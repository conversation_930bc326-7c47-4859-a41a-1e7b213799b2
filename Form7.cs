﻿using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    public partial class Form7 : Form
    {
        public Form7()
        {
            InitializeComponent();
        }
        public static Form7 form7;
        private Point mPoint;
        int langf = 0;
        int hongf = 0;

        public static Color QQQQ = Color.FromArgb(6, 18, 17);
        private void Form7_Load(object sender, EventArgs e)
        {
            form7 = this;
            this.FormBorderStyle = FormBorderStyle.None; //设置窗体无边框
            this.ShowInTaskbar = false;
            this.TopMost = true;

            SetPenetrate();



            pictureBox1.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox2.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox3.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox4.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox5.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);

            pictureBox6.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox7.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox8.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox9.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);
            pictureBox10.MouseDoubleClick += new MouseEventHandler(pictureBox1_MouseDoubleClick);

        }

        private void pictureBox1_MouseDoubleClick(object sender, EventArgs e)
        {
            var dd = (PictureBox)sender;

            var URIname = "";
            //pictureBox
            switch (dd.Name)
            {
                case "pictureBox1":
                    URIname = label4.Text;
                    break;
                case "pictureBox2":
                    URIname = label9.Text;
                    break;
                case "pictureBox3":
                    URIname = label13.Text;
                    break;
                case "pictureBox4":
                    URIname = label17.Text;
                    break;
                case "pictureBox5":
                    URIname = label21.Text;
                    break;


                case "pictureBox6":
                    URIname = label25.Text;
                    break;
                case "pictureBox7":
                    URIname = label29.Text;
                    break;
                case "pictureBox8":
                    URIname = label33.Text;
                    break;
                case "pictureBox9":
                    URIname = label37.Text;
                    break;
                case "pictureBox10":
                    URIname = label41.Text;
                    break;
                default:
                    break;
            }

            if (URIname != "")
            {
                System.Diagnostics.Process.Start(/*"iexplore.exe",*/ "https://www.op.gg/summoner/userName=" + URIname);
            }
            MessageBox.Show("111");



        }

        private const uint WS_EX_LAYERED = 0x80000;
        private const int WS_EX_TRANSPARENT = 0x20;
        private const int GWL_STYLE = (-16);
        private const int GWL_EXSTYLE = (-20);
        private const int LWA_ALPHA = 0;

        [DllImport("user32", EntryPoint = "SetWindowLong")]
        private static extern uint SetWindowLong(
        IntPtr hwnd,
        int nIndex,
        uint dwNewLong
        );

        [DllImport("user32", EntryPoint = "GetWindowLong")]
        private static extern uint GetWindowLong(
        IntPtr hwnd,
        int nIndex
        );

        [DllImport("user32", EntryPoint = "SetLayeredWindowAttributes")]
        private static extern int SetLayeredWindowAttributes(
        IntPtr hwnd,
        int crKey,
        int bAlpha,
        int dwFlags
        );


        public static void ceshi123()
        {
            ssss();
        }
        /// <summary>
        　　/// 设置窗体具有鼠标穿透效果
        　　/// </summary>
        　　/// <param name="flag">true穿透，false不穿透</param>
        public void SetPenetrate(bool flag = true)
        {
            uint style = GetWindowLong(this.Handle, GWL_EXSTYLE);
            if (flag)
                SetWindowLong(this.Handle, GWL_EXSTYLE, style | WS_EX_TRANSPARENT | WS_EX_LAYERED);
            else
                SetWindowLong(this.Handle, GWL_EXSTYLE, style & ~(WS_EX_TRANSPARENT | WS_EX_LAYERED));
            SetLayeredWindowAttributes(this.Handle, 0, 100, LWA_ALPHA);
        }
        public static void ssss()
        {

            //6, 18, 17
            form7.panel1.BackColor = QQQQ;
            form7.panel2.BackColor = QQQQ;
            form7.panel3.BackColor = QQQQ;
            form7.panel4.BackColor = QQQQ;
            form7.panel5.BackColor = QQQQ;

            form7.panel6.BackColor = QQQQ;
            form7.panel7.BackColor = QQQQ;
            form7.panel8.BackColor = QQQQ;
            form7.panel9.BackColor = QQQQ;
            form7.panel10.BackColor = QQQQ;
            //pictureBox1.Image = null;
            //pictureBox2.Image = null;
            //pictureBox3.Image = null;
            //pictureBox4.Image = null;
            //pictureBox5.Image = null;


            //pictureBox6.Image = null;
            //pictureBox7.Image = null;
            //pictureBox8.Image = null;
            //pictureBox9.Image = null;
            //pictureBox10.Image = null;

            form7.label17.Text = "小伟老淫魔";
            form7.label21.Text = "小伟老淫魔";
            form7.label4.Text = "小伟老淫魔";
            form7.label9.Text = "小伟老淫魔";
            form7.label13.Text = "小伟老淫魔";

            form7.label25.Text = "小伟老淫魔";
            form7.label29.Text = "小伟老淫魔";
            form7.label33.Text = "小伟老淫魔";
            form7.label37.Text = "小伟老淫魔";
            form7.label41.Text = "小伟老淫魔";



            form7.label5.Text = "最强青铜";
            form7.label11.Text = "最强青铜";
            form7.label15.Text = "最强青铜";
            form7.label19.Text = "最强青铜";
            form7.label23.Text = "最强青铜";


            form7.label27.Text = "最强青铜";
            form7.label31.Text = "最强青铜";
            form7.label35.Text = "最强青铜";
            form7.label39.Text = "最强青铜";
            form7.label43.Text = "最强青铜";


            form7.label7.Text = "100";
            form7.label10.Text = "100";
            form7.label14.Text = "100";
            form7.label18.Text = "100";
            form7.label22.Text = "100";


            form7.label26.Text = "100";
            form7.label30.Text = "100";
            form7.label34.Text = "100";
            form7.label38.Text = "100";
            form7.label42.Text = "100";


            form7.label12.Text = "[100] 100";
            form7.label20.Text = "[100] 100";
            form7.label24.Text = "[100] 100";
            form7.label28.Text = "[100] 100";
            form7.label32.Text = "[100] 100";


            form7.label36.Text = "[100] 100";
            form7.label40.Text = "[100] 100";
            form7.label44.Text = "[100] 100";
            form7.label45.Text = "[100] 100";
            form7.label46.Text = "[100] 100";

            form7.label3.Text = Util.Jinengjiadian1;
            form7.label6.Text = Util.Jinengjiadian2;

            var ddd = Form1.form1.BlueSquareData.Count;
            form7.langf = 0;
            form7.hongf = 0;

            foreach (var item in Form1.form1.BlueSquareData)
            {


                form7.langf++;
                if (form7.langf == 1)
                {
                    form7.pictureBox1.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label4.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label5.Text = item.Rank + item.RankLevel+ item.RWLevel;
                    form7.label7.Text = item.RankPoints;
                    form7.label12.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel1.BackColor = Color.FromArgb(16, 43, 46);

                }
                if (form7.langf == 2)
                {
                    form7.pictureBox2.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label9.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label11.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label10.Text = item.RankPoints;
                    form7.label20.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel2.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.langf == 3)
                {
                    form7.pictureBox3.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label13.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label15.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label14.Text = item.RankPoints;
                    form7.label24.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel3.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.langf == 4)
                {
                    form7.pictureBox4.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label17.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label19.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label18.Text = item.RankPoints;
                    form7.label28.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel4.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.langf == 5)
                {
                    form7.pictureBox5.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label21.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label23.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label22.Text = item.RankPoints;
                    form7.label32.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel5.BackColor = Color.FromArgb(16, 43, 46);
                }

            }

            foreach (var item in Form1.form1.RedSquareData)
            {

                form7.hongf++;
                if (form7.hongf == 1)
                {
                    form7.pictureBox6.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label25.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label27.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label26.Text = item.RankPoints;
                    form7.label36.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel6.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.hongf == 2)
                {
                    form7.pictureBox7.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label29.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label31.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label30.Text = item.RankPoints;
                    form7.label40.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel7.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.hongf == 3)
                {
                    form7.pictureBox8.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label33.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label35.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label34.Text = item.RankPoints;
                    form7.label44.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel8.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.hongf == 4)
                {
                    form7.pictureBox9.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label37.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label39.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label38.Text = item.RankPoints;
                    form7.label45.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel9.BackColor = Color.FromArgb(16, 43, 46);
                }
                if (form7.hongf == 5)
                {
                    form7.pictureBox10.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

                    form7.label41.Text = item.summonerName + "-[" + item.summonerLevel + "]";
                    form7.label43.Text = item.Rank + item.RankLevel + item.RWLevel;
                    form7.label42.Text = item.RankPoints;
                    form7.label46.Text = item.RWSHENGLV;
                    if (item.accountId == Util.accountId)
                        form7.panel10.BackColor = Color.FromArgb(16, 43, 46);
                }

            }
        }

        public void QQQQQ()
        {

            if (label17.InvokeRequired)
            {
                label17.Invoke(new Action<int>(n =>
                {
                    ssss();
                }), 1);


            }
            else
            {
                ssss();
            }



            //foreach (var item in Form1.form1.BlueSquareData)
            //{

            //    langf++;
            //    if (langf == 1)
            //    {
            //        touxiang.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        dengji.Text =item.summonerLevel;
            //        duanwei.Text =  item.Rank + item.RankLevel;
            //        shengdian.Text =  item.RankPoints;
            //    }
            //    if (langf == 2)
            //    {
            //        pictureBox1.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label3.Text =item.summonerLevel;
            //        label2.Text =  item.Rank + item.RankLevel;
            //        label1.Text =  item.RankPoints;
            //    }
            //    if (langf == 3)
            //    {
            //        pictureBox2.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label6.Text =item.summonerLevel;
            //        label5.Text =  item.Rank + item.RankLevel;
            //        label4.Text =  item.RankPoints;
            //    }
            //    if (langf == 4)
            //    {
            //        pictureBox3.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label9.Text =item.summonerLevel;
            //        label8.Text =  item.Rank + item.RankLevel;
            //        label7.Text =  item.RankPoints;
            //    }
            //    if (langf == 5)
            //    {
            //        pictureBox4.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label12.Text =item.summonerLevel;
            //        label11.Text =  item.Rank + item.RankLevel;
            //        label10.Text =  item.RankPoints;
            //    }

            //}

            //foreach (var item in Form1.form1.RedSquareData)
            //{

            //    hongf++;
            //    if (hongf == 1)
            //    {
            //        pictureBox9.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label27.Text =item.summonerLevel;
            //        label26.Text =  item.Rank + item.RankLevel;
            //        label25.Text =  item.RankPoints;
            //    }
            //    if (hongf == 2)
            //    {
            //        pictureBox8.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label24.Text =item.summonerLevel;
            //        label23.Text =  item.Rank + item.RankLevel;
            //        label22.Text =  item.RankPoints;
            //    }
            //    if (hongf == 3)
            //    {
            //        pictureBox7.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label21.Text =item.summonerLevel;
            //        label20.Text =  item.Rank + item.RankLevel;
            //        label19.Text =  item.RankPoints;
            //    }
            //    if (hongf == 4)
            //    {
            //        pictureBox6.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label18.Text =item.summonerLevel;
            //        label17.Text =  item.Rank + item.RankLevel;
            //        label16.Text =  item.RankPoints;
            //    }
            //    if (hongf == 5)
            //    {
            //        pictureBox5.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + item.HeroID + ".png");

            //        label15.Text =item.summonerLevel;
            //        label14.Text =  item.Rank + item.RankLevel;
            //        label13.Text =  item.RankPoints;
            //    }

            //}
        }
        private void Form7_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.Location = new Point(this.Location.X + e.X - mPoint.X, this.Location.Y + e.Y - mPoint.Y);
            }

        }

        private void Form7_MouseDown(object sender, MouseEventArgs e)
        {
            mPoint = new Point(e.X, e.Y);
        }

    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    public partial class Form5 : Form
    {
        public Form5()
        {
            InitializeComponent();
        }

        private void Form5_Load(object sender, EventArgs e)
        {
            int width = System.Windows.Forms.SystemInformation.WorkingArea.Width;   //屏幕宽度
            int height = System.Windows.Forms.SystemInformation.WorkingArea.Height; //屏幕高度（不包括系统任务栏）
            this.FormBorderStyle = FormBorderStyle.None;
            this.Location = new Point(width-200, Convert.ToInt32(height / 3.75f));
            this.Width = 50;
            this.Height = 50;
            this.labelex.AutoSize = false;
            this.labelex.Left = 0;
            this.labelex.Top = 0;
            this.labelex.Width = this.Width;
            this.labelex.Height = this.Height;
        }
        const int WM_NCHITTEST = 0x0084;
        const int HTCLIENT = 0x0001;
        const int HTCAPTION = 0x0002;
       // private Point ptMouseCurrrnetPos, ptMouseNewPos, ptFormPos, ptFormNewPos;
        public bool blnMouseDown = false;


        [DllImport("user32.dll")]
        public static extern bool ReleaseCapture();

        [DllImport("user32.dll")]
        public static extern bool SendMessage(IntPtr hwnd, int wMsg, int wParam, int lParam);

        public const int WM_SYSCOMMAND = 0x0112;
        public const int SC_MOVE = 0xF010;
        //public const int HTCAPTION = 0x0002;

        const int WM_NCLBUTTONDBLCLK = 0xA3;
        public const int WM_RBUTTONDOWN = 0x0204;
        public const int WM_LBUTTONDOWN = 0x0201;
        private void labelex_DoubleClick(object sender, EventArgs e)
        {

            if (Form1.form1.Visible)
                return;

            Form1.form1.Visible = true;
            //Form1.form1.Show();
            Form1.form1.WindowState = FormWindowState.Normal;
            Form1.form1.Activate();
            
            Form1.form1.TopMost = true;
            Form1.form1.TopMost = false;
            //var kesho = Form1.form1.Visible;
           
            //var dddd = Form1.form1.WindowState;

            //var ShowInTaskbar = Form1.form1.ShowInTaskbar;
            //if (dddd != FormWindowState.Normal && ShowInTaskbar)
            //{

            //    Form1.form1.WindowState = FormWindowState.Normal;
            //    this.TopMost = true;

            //    this.TopMost = false;

            //}
            //else if (kesho)
            //{
            //    Form1.form1.Visible = false;
            //    Form1.form1.ShowInTaskbar = false;
            //}
            //else
            //{
            //    Form1.form1.Visible = true;
            //    Form1.form1.ShowInTaskbar = true;
            //    Form1.form1.WindowState = FormWindowState.Normal;
            //    this.TopMost = true;

            //    this.TopMost = false;
            //}


        }

        private void 退出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //if (Program.JIEJIEJIE.InvokeRequired)
            //{
            //    Program.JIEJIEJIE.Invoke(new Action<int>(n =>
            //    {
            //        Program.JIEJIEJIE.Close();


            //    }), 1);
            //}
            Util.BulkSave(Form1.form1, "checkBox10");//保存
            Form1.IsQuit = true;
            Form1.form1.QQQQQQQQQ = true;
            Form1.form1.Close();
        }
        private Point mPoint;

        private void Form5_FormClosing(object sender, FormClosingEventArgs e)
        {
            
            Form1.form1.QQQQQQQQQ = true;
            this.Hide();
           
        }

        private void labelex_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                mPoint = new Point(e.X, e.Y);

            }

        }

        private void labelex_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                contextMenuStrip1.Show(labelex, new Point(e.X, e.Y));
            }


        }

        private void labelex_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.Location = new Point(this.Location.X + e.X - mPoint.X, this.Location.Y + e.Y - mPoint.Y);

            }
        }

       
    }
}

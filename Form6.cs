﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.IO;


namespace WindowsFormsApp1
{
    public partial class Form6 : Form
    {
        public Form6()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {

            if(comboBox1.Text=="GET")
            {
                Fanhui.Text = Util.HttpRequest_0Async("https://127.0.0.1:" + Util.duank + Uri.Text, "Basic " + Util.token, Tijiao.Text, comboBox1.Text);
            }
            else if (comboBox1.Text == "POST")
            {
                Fanhui.Text=Util.zi_Sendshuju(Uri.Text, Tijiao.Text);
            }
          
        }
        string urlDD = "";
        string TTTT = "";
        private void Form6_Load(object sender, EventArgs e)
        {
            comboBox1.SelectedIndex = 0;

            System.Diagnostics.Process[] processList = System.Diagnostics.Process.GetProcesses();

            foreach (System.Diagnostics.Process process in processList)
            {
                if (process.ProcessName == "RiotClientUx")
                {

                    var LineArgs = Util.GetCommandLineArgs(process);


                    var port = Util.GetMiddleText(LineArgs, "port=", " ");

                    var token1 = Util.GetMiddleText(LineArgs, "token=", " ");

                    var token = Util.EncodeBase64("utf-8", "riot:" + token1);

                    urlDD = "https://127.0.0.1:" + port;

                    TTTT = "Basic " + token;
                }

            }
        }
        private async void button2_Click(object sender, EventArgs e)
        {
            Fanhui.Text = Util.zi_Getwenb_0("/lol-summoner/v1/current-summoner");
            // Fanhui.Text = await Util.SendRequestWithExternalParamAsync(@"https://lol-api-champion.op.gg/api/tw/champions/aram/neeko/None");
        }

        private void button3_Click(object sender, EventArgs e)
        {

            Fanhui.Text = Util.zi_Getwenb_0("/lol-gameflow/v1/session");
            //Fanhui.Text = Util.zi_Getwenb_0("/lol-summoner/v1/current-summoner");
            //Fanhui.Text = Util.HttpRequest_0(urlDD + Uri.Text, TTTT, Tijiao.Text, listBox1.Text).retstring;
            //Console.WriteLine("{0} {1} {2} {3}",urlDD + Uri.Text, TTTT, Tijiao.Text, listBox1.Text);
        }

        private void button4_Click(object sender, EventArgs e)
        {
            Fanhui.Text = Util.zi_Getwenb_0("/lol-champ-select/v1/session");
        }

        private void button5_Click(object sender, EventArgs e)
        {
            Fanhui.Text= "https://127.0.0.1:" + Util.duank + Uri.Text+"\r\n" + "Authorization: Basic " + Util.token;
        }

        private void button6_Click(object sender, EventArgs e)
        {
           //Fanhui.Text = Util.HttpRequest_0Async("https://127.0.0.1:" + Util.duank + "/lol-summoner/v1/summoners?name=" + textBox1.Text, "Basic " + Util.token, Tijiao.Text, comboBox1.Text);
           Fanhui.Text = Util.HttpRequest_0Async("https://127.0.0.1:" + Util.duank + "/lol-summoner/v1/summoners/" + textBox1.Text, "Basic " + Util.token, Tijiao.Text, comboBox1.Text);
        }

        private void button7_Click(object sender, EventArgs e)
        {
            var d3 = Fanhui.Text;

            if (d3.IndexOf("远程服务器") == -1)
            {

                var d33 = JSON.Jiexun(d3);

                var d13 = d33["games"]["games"];

                float Winning = 0;

                float Failure = 0;

                for (int i = 0; i < d13.Count(); i++)
                {
                    var WE = d13[i]["gameMode"].ToString().ToLower();
                    if (WE == "classic")
                    {
                        var winn = d13[i]["participants"][0]["stats"]["win"].ToString().ToLower();

                        if(winn=="true")
                        {
                            Winning++;
                        }
                        else
                        {
                            Failure++;
                        }

                        
                    }
                  

                }

                int Zwin = ((int)(Winning / (Winning + Failure)*100));
                Console.WriteLine("赢{0},输{1},胜率:{2}%", Winning, Failure, Zwin);











            }
        }

        private void button8_Click(object sender, EventArgs e)
        {
            Fanhui.Text = Util.HttpRequest_0Async("https://127.0.0.1:" + Util.duank + "/lol-match-history/v1/products/lol/" + textBox1.Text+ "/matches?begIndex=0&endIndex=30", "Basic " + Util.token, Tijiao.Text, comboBox1.Text);
        }
    }
}

﻿namespace WindowsFormsApp1
{
    partial class WebDAV
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripFiles; // 添加 ContextMenuStrip 变量
        private System.Windows.Forms.ToolStripMenuItem uploadToolStripMenuItem; // 添加上传菜单项变量
        private System.Windows.Forms.ToolStripMenuItem uploadFolderToolStripMenuItem; // 添加上传文件夹菜单项变量
        private System.Windows.Forms.ToolStripMenuItem downloadToolStripMenuItem; // 添加下载菜单项变量
        private System.Windows.Forms.ToolStripMenuItem deleteToolStripMenuItem; // 添加删除菜单项变量
        private System.Windows.Forms.ToolStripMenuItem newFolderToolStripMenuItem; // 添加新建文件夹菜单项变量
        private System.Windows.Forms.ToolStripMenuItem refreshToolStripMenuItem; // 添加刷新菜单项变量
        protected System.Windows.Forms.ProgressBar progressBarTransfer; // 添加进度条变量
        protected System.Windows.Forms.Label labelSpeed; // 添加速度标签变量
        protected System.Windows.Forms.Label labelTime; // 添加时间标签变量
        private System.Windows.Forms.TextBox textBoxOutput; // 添加输出编辑框变量
        private System.Windows.Forms.Button buttonUploadFolder; // 添加上传文件夹按钮变量
        private System.Windows.Forms.ComboBox comboBoxHistory; // 添加历史连接下拉框变量
        private System.Windows.Forms.Label labelHistory; // 添加历史连接标签变量

        // 添加拖放所需的字段
        private bool isDragging = false;
        private System.Collections.Specialized.StringCollection dragFileCollection;
        private System.Windows.Forms.Timer timerDownload;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.labelServerAddress = new System.Windows.Forms.Label();
            this.textBoxServerAddress = new System.Windows.Forms.TextBox();
            this.labelUsername = new System.Windows.Forms.Label();
            this.textBoxUsername = new System.Windows.Forms.TextBox();
            this.labelPassword = new System.Windows.Forms.Label();
            this.textBoxPassword = new System.Windows.Forms.TextBox();
            this.buttonConnect = new System.Windows.Forms.Button();
            this.checkBoxRememberConnection = new System.Windows.Forms.CheckBox();
            this.labelPath = new System.Windows.Forms.Label();
            this.listViewFiles = new System.Windows.Forms.ListView();
            this.columnHeaderName = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderType = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderSize = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeaderModifiedDate = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.contextMenuStripFiles = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.uploadToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.uploadFolderToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.downloadToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.deleteToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.newFolderToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.refreshToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.labelStatus = new System.Windows.Forms.Label();
            this.buttonUploadFolder = new System.Windows.Forms.Button();
            this.buttonUpload = new System.Windows.Forms.Button();
            this.comboBoxHistory = new System.Windows.Forms.ComboBox();
            this.labelHistory = new System.Windows.Forms.Label();
            this.buttonDownload = new System.Windows.Forms.Button();
            this.buttonDelete = new System.Windows.Forms.Button();
            this.buttonNewFolder = new System.Windows.Forms.Button();
            this.buttonRefresh = new System.Windows.Forms.Button();
            this.progressBarTransfer = new System.Windows.Forms.ProgressBar();
            this.labelSpeed = new System.Windows.Forms.Label();
            this.labelTime = new System.Windows.Forms.Label();
            this.textBoxOutput = new System.Windows.Forms.TextBox();
            this.buttonDeleteHistory = new System.Windows.Forms.Button();
            this.contextMenuStripFiles.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelServerAddress
            // 
            this.labelServerAddress.AutoSize = true;
            this.labelServerAddress.Location = new System.Drawing.Point(20, 25);
            this.labelServerAddress.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelServerAddress.Name = "labelServerAddress";
            this.labelServerAddress.Size = new System.Drawing.Size(107, 18);
            this.labelServerAddress.TabIndex = 0;
            this.labelServerAddress.Text = "服务器地址:";
            // 
            // textBoxServerAddress
            // 
            this.textBoxServerAddress.Location = new System.Drawing.Point(140, 22);
            this.textBoxServerAddress.Margin = new System.Windows.Forms.Padding(5);
            this.textBoxServerAddress.Name = "textBoxServerAddress";
            this.textBoxServerAddress.Size = new System.Drawing.Size(440, 28);
            this.textBoxServerAddress.TabIndex = 1;
            // 
            // labelUsername
            // 
            this.labelUsername.AutoSize = true;
            this.labelUsername.Location = new System.Drawing.Point(20, 60);
            this.labelUsername.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelUsername.Name = "labelUsername";
            this.labelUsername.Size = new System.Drawing.Size(71, 18);
            this.labelUsername.TabIndex = 2;
            this.labelUsername.Text = "用户名:";
            // 
            // textBoxUsername
            // 
            this.textBoxUsername.Location = new System.Drawing.Point(140, 57);
            this.textBoxUsername.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxUsername.Name = "textBoxUsername";
            this.textBoxUsername.Size = new System.Drawing.Size(220, 28);
            this.textBoxUsername.TabIndex = 3;
            // 
            // labelPassword
            // 
            this.labelPassword.AutoSize = true;
            this.labelPassword.Location = new System.Drawing.Point(20, 95);
            this.labelPassword.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelPassword.Name = "labelPassword";
            this.labelPassword.Size = new System.Drawing.Size(53, 18);
            this.labelPassword.TabIndex = 4;
            this.labelPassword.Text = "密码:";
            // 
            // textBoxPassword
            // 
            this.textBoxPassword.Location = new System.Drawing.Point(140, 92);
            this.textBoxPassword.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxPassword.Name = "textBoxPassword";
            this.textBoxPassword.Size = new System.Drawing.Size(220, 28);
            this.textBoxPassword.TabIndex = 5;
            this.textBoxPassword.UseSystemPasswordChar = true;
            // 
            // buttonConnect
            // 
            this.buttonConnect.Location = new System.Drawing.Point(140, 130);
            this.buttonConnect.Margin = new System.Windows.Forms.Padding(4);
            this.buttonConnect.Name = "buttonConnect";
            this.buttonConnect.Size = new System.Drawing.Size(110, 30);
            this.buttonConnect.TabIndex = 6;
            this.buttonConnect.Text = "连接";
            this.buttonConnect.UseVisualStyleBackColor = true;
            this.buttonConnect.Click += new System.EventHandler(this.buttonConnect_Click);
            // 
            // checkBoxRememberConnection
            // 
            this.checkBoxRememberConnection.AutoSize = true;
            this.checkBoxRememberConnection.Location = new System.Drawing.Point(260, 135);
            this.checkBoxRememberConnection.Margin = new System.Windows.Forms.Padding(4);
            this.checkBoxRememberConnection.Name = "checkBoxRememberConnection";
            this.checkBoxRememberConnection.Size = new System.Drawing.Size(106, 22);
            this.checkBoxRememberConnection.TabIndex = 7;
            this.checkBoxRememberConnection.Text = "保存连接";
            this.checkBoxRememberConnection.UseVisualStyleBackColor = true;
            // 
            // labelPath
            // 
            this.labelPath.AutoSize = true;
            this.labelPath.Location = new System.Drawing.Point(18, 188);
            this.labelPath.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelPath.Name = "labelPath";
            this.labelPath.Size = new System.Drawing.Size(53, 18);
            this.labelPath.TabIndex = 8;
            this.labelPath.Text = "路径:";
            // 
            // listViewFiles
            // 
            this.listViewFiles.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewFiles.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderName,
            this.columnHeaderType,
            this.columnHeaderSize,
            this.columnHeaderModifiedDate});
            this.listViewFiles.ContextMenuStrip = this.contextMenuStripFiles;
            this.listViewFiles.FullRowSelect = true;
            this.listViewFiles.HideSelection = false;
            this.listViewFiles.Location = new System.Drawing.Point(8, 210);
            this.listViewFiles.Margin = new System.Windows.Forms.Padding(4);
            this.listViewFiles.Name = "listViewFiles";
            this.listViewFiles.Size = new System.Drawing.Size(1179, 416);
            this.listViewFiles.TabIndex = 9;
            this.listViewFiles.UseCompatibleStateImageBehavior = false;
            this.listViewFiles.View = System.Windows.Forms.View.Details;
            this.listViewFiles.KeyDown += new System.Windows.Forms.KeyEventHandler(this.listViewFiles_KeyDown);
            this.listViewFiles.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewFiles_MouseDoubleClick);
            // 
            // columnHeaderName
            // 
            this.columnHeaderName.Text = "名称";
            this.columnHeaderName.Width = 200;
            // 
            // columnHeaderType
            // 
            this.columnHeaderType.Text = "类型";
            this.columnHeaderType.Width = 100;
            // 
            // columnHeaderSize
            // 
            this.columnHeaderSize.Text = "大小";
            this.columnHeaderSize.Width = 100;
            // 
            // columnHeaderModifiedDate
            // 
            this.columnHeaderModifiedDate.Text = "修改日期";
            this.columnHeaderModifiedDate.Width = 150;
            // 
            // contextMenuStripFiles
            // 
            this.contextMenuStripFiles.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStripFiles.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.uploadToolStripMenuItem,
            this.uploadFolderToolStripMenuItem,
            this.downloadToolStripMenuItem,
            this.deleteToolStripMenuItem,
            this.newFolderToolStripMenuItem,
            this.refreshToolStripMenuItem});
            this.contextMenuStripFiles.Name = "contextMenuStripFiles";
            this.contextMenuStripFiles.Size = new System.Drawing.Size(171, 184);
            // 
            // uploadToolStripMenuItem
            // 
            this.uploadToolStripMenuItem.Name = "uploadToolStripMenuItem";
            this.uploadToolStripMenuItem.Size = new System.Drawing.Size(170, 30);
            this.uploadToolStripMenuItem.Text = "上传";
            this.uploadToolStripMenuItem.Click += new System.EventHandler(this.uploadToolStripMenuItem_Click);
            // 
            // uploadFolderToolStripMenuItem
            // 
            this.uploadFolderToolStripMenuItem.Name = "uploadFolderToolStripMenuItem";
            this.uploadFolderToolStripMenuItem.Size = new System.Drawing.Size(170, 30);
            this.uploadFolderToolStripMenuItem.Text = "上传文件夹";
            this.uploadFolderToolStripMenuItem.Click += new System.EventHandler(this.uploadFolderToolStripMenuItem_Click);
            // 
            // downloadToolStripMenuItem
            // 
            this.downloadToolStripMenuItem.Name = "downloadToolStripMenuItem";
            this.downloadToolStripMenuItem.Size = new System.Drawing.Size(170, 30);
            this.downloadToolStripMenuItem.Text = "下载";
            this.downloadToolStripMenuItem.Click += new System.EventHandler(this.downloadToolStripMenuItem_Click);
            // 
            // deleteToolStripMenuItem
            // 
            this.deleteToolStripMenuItem.Name = "deleteToolStripMenuItem";
            this.deleteToolStripMenuItem.Size = new System.Drawing.Size(170, 30);
            this.deleteToolStripMenuItem.Text = "删除";
            this.deleteToolStripMenuItem.Click += new System.EventHandler(this.deleteToolStripMenuItem_Click);
            // 
            // newFolderToolStripMenuItem
            // 
            this.newFolderToolStripMenuItem.Name = "newFolderToolStripMenuItem";
            this.newFolderToolStripMenuItem.Size = new System.Drawing.Size(170, 30);
            this.newFolderToolStripMenuItem.Text = "新建文件夹";
            this.newFolderToolStripMenuItem.Click += new System.EventHandler(this.newFolderToolStripMenuItem_Click);
            // 
            // refreshToolStripMenuItem
            // 
            this.refreshToolStripMenuItem.Name = "refreshToolStripMenuItem";
            this.refreshToolStripMenuItem.Size = new System.Drawing.Size(170, 30);
            this.refreshToolStripMenuItem.Text = "刷新";
            this.refreshToolStripMenuItem.Click += new System.EventHandler(this.refreshToolStripMenuItem_Click);
            // 
            // labelStatus
            // 
            this.labelStatus.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.labelStatus.AutoSize = true;
            this.labelStatus.Location = new System.Drawing.Point(20, 654);
            this.labelStatus.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelStatus.Name = "labelStatus";
            this.labelStatus.Size = new System.Drawing.Size(188, 18);
            this.labelStatus.TabIndex = 10;
            this.labelStatus.Text = "已加载保存的连接设置";
            // 
            // buttonUploadFolder
            // 
            this.buttonUploadFolder.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonUploadFolder.Location = new System.Drawing.Point(540, 645);
            this.buttonUploadFolder.Margin = new System.Windows.Forms.Padding(4);
            this.buttonUploadFolder.Name = "buttonUploadFolder";
            this.buttonUploadFolder.Size = new System.Drawing.Size(105, 34);
            this.buttonUploadFolder.TabIndex = 18;
            this.buttonUploadFolder.Text = "上传文件夹";
            this.buttonUploadFolder.UseVisualStyleBackColor = true;
            this.buttonUploadFolder.Click += new System.EventHandler(this.buttonUploadFolder_Click);
            // 
            // buttonUpload
            // 
            this.buttonUpload.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonUpload.Location = new System.Drawing.Point(650, 645);
            this.buttonUpload.Margin = new System.Windows.Forms.Padding(4);
            this.buttonUpload.Name = "buttonUpload";
            this.buttonUpload.Size = new System.Drawing.Size(105, 34);
            this.buttonUpload.TabIndex = 11;
            this.buttonUpload.Text = "上传";
            this.buttonUpload.UseVisualStyleBackColor = true;
            this.buttonUpload.Click += new System.EventHandler(this.buttonUpload_Click);
            // 
            // comboBoxHistory
            // 
            this.comboBoxHistory.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxHistory.FormattingEnabled = true;
            this.comboBoxHistory.Location = new System.Drawing.Point(690, 22);
            this.comboBoxHistory.Margin = new System.Windows.Forms.Padding(4);
            this.comboBoxHistory.Name = "comboBoxHistory";
            this.comboBoxHistory.Size = new System.Drawing.Size(280, 26);
            this.comboBoxHistory.TabIndex = 20;
            this.comboBoxHistory.SelectedIndexChanged += new System.EventHandler(this.comboBoxHistory_SelectedIndexChanged);
            // 
            // labelHistory
            // 
            this.labelHistory.AutoSize = true;
            this.labelHistory.Location = new System.Drawing.Point(600, 25);
            this.labelHistory.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelHistory.Name = "labelHistory";
            this.labelHistory.Size = new System.Drawing.Size(89, 18);
            this.labelHistory.TabIndex = 19;
            this.labelHistory.Text = "历史连接:";
            // 
            // buttonDownload
            // 
            this.buttonDownload.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonDownload.Location = new System.Drawing.Point(760, 645);
            this.buttonDownload.Margin = new System.Windows.Forms.Padding(4);
            this.buttonDownload.Name = "buttonDownload";
            this.buttonDownload.Size = new System.Drawing.Size(105, 34);
            this.buttonDownload.TabIndex = 12;
            this.buttonDownload.Text = "下载";
            this.buttonDownload.UseVisualStyleBackColor = true;
            this.buttonDownload.Click += new System.EventHandler(this.buttonDownload_Click);
            // 
            // buttonDelete
            // 
            this.buttonDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonDelete.Location = new System.Drawing.Point(870, 645);
            this.buttonDelete.Margin = new System.Windows.Forms.Padding(4);
            this.buttonDelete.Name = "buttonDelete";
            this.buttonDelete.Size = new System.Drawing.Size(105, 34);
            this.buttonDelete.TabIndex = 13;
            this.buttonDelete.Text = "删除";
            this.buttonDelete.UseVisualStyleBackColor = true;
            this.buttonDelete.Click += new System.EventHandler(this.buttonDelete_Click);
            // 
            // buttonNewFolder
            // 
            this.buttonNewFolder.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonNewFolder.Location = new System.Drawing.Point(980, 645);
            this.buttonNewFolder.Margin = new System.Windows.Forms.Padding(4);
            this.buttonNewFolder.Name = "buttonNewFolder";
            this.buttonNewFolder.Size = new System.Drawing.Size(105, 34);
            this.buttonNewFolder.TabIndex = 14;
            this.buttonNewFolder.Text = "新建文件夹";
            this.buttonNewFolder.UseVisualStyleBackColor = true;
            this.buttonNewFolder.Click += new System.EventHandler(this.newFolderToolStripMenuItem_Click);
            // 
            // buttonRefresh
            // 
            this.buttonRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonRefresh.Location = new System.Drawing.Point(1090, 645);
            this.buttonRefresh.Margin = new System.Windows.Forms.Padding(4);
            this.buttonRefresh.Name = "buttonRefresh";
            this.buttonRefresh.Size = new System.Drawing.Size(105, 34);
            this.buttonRefresh.TabIndex = 15;
            this.buttonRefresh.Text = "刷新";
            this.buttonRefresh.UseVisualStyleBackColor = true;
            this.buttonRefresh.Click += new System.EventHandler(this.buttonRefresh_Click);
            // 
            // progressBarTransfer
            // 
            this.progressBarTransfer.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progressBarTransfer.Location = new System.Drawing.Point(250, 645);
            this.progressBarTransfer.Margin = new System.Windows.Forms.Padding(4);
            this.progressBarTransfer.Name = "progressBarTransfer";
            this.progressBarTransfer.Size = new System.Drawing.Size(282, 30);
            this.progressBarTransfer.TabIndex = 16;
            // 
            // labelSpeed
            // 
            this.labelSpeed.Location = new System.Drawing.Point(403, 130);
            this.labelSpeed.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelSpeed.Name = "labelSpeed";
            this.labelSpeed.Size = new System.Drawing.Size(230, 34);
            this.labelSpeed.TabIndex = 2;
            // 
            // labelTime
            // 
            this.labelTime.Location = new System.Drawing.Point(641, 130);
            this.labelTime.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.labelTime.Name = "labelTime";
            this.labelTime.Size = new System.Drawing.Size(150, 34);
            this.labelTime.TabIndex = 1;
            this.labelTime.Text = "已用时间: 0秒";
            // 
            // textBoxOutput
            // 
            this.textBoxOutput.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBoxOutput.Location = new System.Drawing.Point(20, 690);
            this.textBoxOutput.Margin = new System.Windows.Forms.Padding(4);
            this.textBoxOutput.Multiline = true;
            this.textBoxOutput.Name = "textBoxOutput";
            this.textBoxOutput.ReadOnly = true;
            this.textBoxOutput.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.textBoxOutput.Size = new System.Drawing.Size(1160, 150);
            this.textBoxOutput.TabIndex = 17;
            // 
            // buttonDeleteHistory
            // 
            this.buttonDeleteHistory.Location = new System.Drawing.Point(980, 22);
            this.buttonDeleteHistory.Name = "buttonDeleteHistory";
            this.buttonDeleteHistory.Size = new System.Drawing.Size(100, 28);
            this.buttonDeleteHistory.TabIndex = 21;
            this.buttonDeleteHistory.Text = "删除记录";
            this.buttonDeleteHistory.UseVisualStyleBackColor = true;
            this.buttonDeleteHistory.Click += new System.EventHandler(this.buttonDeleteHistory_Click);
            // 
            // WebDAV
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 861);
            this.Controls.Add(this.buttonDeleteHistory);
            this.Controls.Add(this.comboBoxHistory);
            this.Controls.Add(this.labelHistory);
            this.Controls.Add(this.buttonUploadFolder);
            this.Controls.Add(this.labelTime);
            this.Controls.Add(this.labelSpeed);
            this.Controls.Add(this.progressBarTransfer);
            this.Controls.Add(this.buttonRefresh);
            this.Controls.Add(this.buttonNewFolder);
            this.Controls.Add(this.buttonDelete);
            this.Controls.Add(this.buttonDownload);
            this.Controls.Add(this.buttonUpload);
            this.Controls.Add(this.labelStatus);
            this.Controls.Add(this.listViewFiles);
            this.Controls.Add(this.labelPath);
            this.Controls.Add(this.checkBoxRememberConnection);
            this.Controls.Add(this.buttonConnect);
            this.Controls.Add(this.textBoxPassword);
            this.Controls.Add(this.labelPassword);
            this.Controls.Add(this.textBoxUsername);
            this.Controls.Add(this.labelUsername);
            this.Controls.Add(this.textBoxServerAddress);
            this.Controls.Add(this.textBoxOutput);
            this.Controls.Add(this.labelServerAddress);
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "WebDAV";
            this.Text = "WebDAV客户端";
            this.Load += new System.EventHandler(this.WebDAV_Load);
            this.contextMenuStripFiles.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label labelServerAddress;
        private System.Windows.Forms.TextBox textBoxServerAddress;
        private System.Windows.Forms.Label labelUsername;
        private System.Windows.Forms.TextBox textBoxUsername;
        private System.Windows.Forms.Label labelPassword;
        private System.Windows.Forms.TextBox textBoxPassword;
        private System.Windows.Forms.Button buttonConnect;
        private System.Windows.Forms.CheckBox checkBoxRememberConnection;
        private System.Windows.Forms.Label labelPath;
        private System.Windows.Forms.ListView listViewFiles;
        private System.Windows.Forms.ColumnHeader columnHeaderName;
        private System.Windows.Forms.ColumnHeader columnHeaderType;
        private System.Windows.Forms.ColumnHeader columnHeaderSize;
        private System.Windows.Forms.ColumnHeader columnHeaderModifiedDate;
        private System.Windows.Forms.Label labelStatus;
        private System.Windows.Forms.Button buttonUpload;
        private System.Windows.Forms.Button buttonDownload;
        private System.Windows.Forms.Button buttonDelete;
        private System.Windows.Forms.Button buttonNewFolder;
        private System.Windows.Forms.Button buttonRefresh;
        private System.Windows.Forms.Button buttonDeleteHistory;
    }
}

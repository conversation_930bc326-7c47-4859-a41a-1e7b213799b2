﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>HttpToSocks5Proxy</id>
    <version>1.4.0</version>
    <authors><PERSON><PERSON><PERSON><PERSON><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/MihaZupan/HttpToSocks5Proxy</projectUrl>
    <iconUrl>https://telegram.org/img/tl_card_decentralized.gif</iconUrl>
    <description>This is a class that implements the IWebProxy interface to act as an HTTP(S) proxy while connecting to a SOCKS5 server behind the scenes.</description>
    <copyright>Copyright © Miha Zupan 2019</copyright>
    <tags>Proxy Socks5 Socks Http Telegram Bot</tags>
    <repository url="https://github.com/MihaZupan/HttpToSocks5Proxy.git" />
    <dependencies>
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETStandard2.0" />
    </dependencies>
  </metadata>
  <files>
    <file src="D:\项目\WindowsFormsApp1\HttpToSocks5Proxy-master\src\HttpToSocks5Proxy\bin\Debug\net45\MihaZupan.HttpToSocks5Proxy.dll" target="lib\net45\MihaZupan.HttpToSocks5Proxy.dll" />
    <file src="D:\项目\WindowsFormsApp1\HttpToSocks5Proxy-master\src\HttpToSocks5Proxy\bin\Debug\netstandard2.0\MihaZupan.HttpToSocks5Proxy.dll" target="lib\netstandard2.0\MihaZupan.HttpToSocks5Proxy.dll" />
  </files>
</package>
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using Microsoft.VisualBasic;
using WindowsFormsApp1; // Ensure the namespace is included for ShellIconHelper

namespace WindowsFormsApp1
{
    public partial class WebDAV : Form
    {
        // 连接历史记录项类
        private class ConnectionHistoryItem
        {
            public string ServerAddress { get; set; }
            public string Username { get; set; }
            public string Password { get; set; }
            public DateTime LastConnected { get; set; }

            public ConnectionHistoryItem(string serverAddress, string username, string password)
            {
                ServerAddress = serverAddress;
                Username = username;
                Password = password;
                LastConnected = DateTime.Now;
            }

            public override string ToString()
            {
                // 在下拉框中显示的文本
                return $"{ServerAddress} - {Username}";
            }
        }

        private string currentUrl = string.Empty;
        private NetworkCredential credential;
        private HttpClient httpClient;
        private HttpClientHandler httpClientHandler;
        private List<WebDAVItem> currentItems = new List<WebDAVItem>();
        private System.Diagnostics.Stopwatch transferStopwatch = new System.Diagnostics.Stopwatch();
        private long totalBytesToTransfer = 0;
        private long bytesTransferred = 0;
        private List<ConnectionHistoryItem> connectionHistory = new List<ConnectionHistoryItem>();

        // 上传队列相关变量
        private System.Collections.Concurrent.ConcurrentQueue<UploadTask> uploadQueue = new System.Collections.Concurrent.ConcurrentQueue<UploadTask>();
        private bool isUploading = false;
        private object uploadLock = new object(); // 用于同步的锁对象

        // 上传任务类
        private class UploadTask
        {
            public string FilePath { get; set; }        // 本地文件路径
            public string TargetUrl { get; set; }       // 目标URL
            public bool IsFolder { get; set; }          // 是否为文件夹
            public string FolderTargetUrl { get; set; } // 文件夹上传的目标URL，仅在IsFolder=true时使用

            // 文件上传任务构造函数
            public UploadTask(string filePath, string targetUrl)
            {
                FilePath = filePath;
                TargetUrl = targetUrl;
                IsFolder = false;
            }

            // 文件夹上传任务构造函数
            public UploadTask(string folderPath, string targetUrl, bool isFolder)
            {
                FilePath = folderPath;
                FolderTargetUrl = targetUrl;
                IsFolder = isFolder;
            }
        }

        // 添加支持的文本文件扩展名集合
        private static readonly HashSet<string> _textFileExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            ".txt",
            ".ini",
            ".log",
            ".csv",
            ".xml",
            ".json",
            ".md",
            ".config",
            ".yml",
            ".yaml"
        };

        // 添加一个标志变量来跟踪窗体是否正在关闭
        private bool isFormClosing = false;

        // 排序相关变量
        private int currentSortColumn = -1; // 当前排序列
        private bool isAscending = true;    // 是否升序排序
        private ListViewColumnSorter lvwColumnSorter; // 列表排序器

        // 列表排序器类
        public class ListViewColumnSorter : IComparer
        {
            private int columnToSort;        // 要排序的列
            private SortOrder orderOfSort;   // 排序顺序
            private CaseInsensitiveComparer objectCompare;

            public ListViewColumnSorter()
            {
                // 默认按第一列升序排序
                columnToSort = 0;
                orderOfSort = SortOrder.Ascending;
                objectCompare = new CaseInsensitiveComparer();
            }

            public int Compare(object x, object y)
            {
                int compareResult;
                ListViewItem listViewX = (ListViewItem)x;
                ListViewItem listViewY = (ListViewItem)y;

                // 特殊情况：始终将返回上级目录".."保持在列表顶部
                if (listViewX.Text == "..")
                    return -1;
                if (listViewY.Text == "..")
                    return 1;

                // 比较两个ListViewItem
                string textX = listViewX.SubItems[columnToSort].Text;
                string textY = listViewY.SubItems[columnToSort].Text;

                // 列2是大小列，需要特殊处理
                if (columnToSort == 2)
                {
                    // 文件夹显示为空，应该排在最上面
                    if (string.IsNullOrEmpty(textX))
                        return -1;
                    if (string.IsNullOrEmpty(textY))
                        return 1;

                    // 尝试将文件大小转换为数字进行比较
                    double sizeX = ConvertFileSizeToBytes(textX);
                    double sizeY = ConvertFileSizeToBytes(textY);
                    compareResult = sizeX.CompareTo(sizeY);
                }
                // 列3是日期列，需要特殊处理
                else if (columnToSort == 3)
                {
                    DateTime dateX, dateY;
                    if (!DateTime.TryParse(textX, out dateX))
                        dateX = DateTime.MinValue;
                    if (!DateTime.TryParse(textY, out dateY))
                        dateY = DateTime.MinValue;
                    compareResult = dateX.CompareTo(dateY);
                }
                else
                {
                    // 一般文本比较
                    compareResult = objectCompare.Compare(textX, textY);
                }

                // 根据排序顺序返回结果
                if (orderOfSort == SortOrder.Ascending)
                {
                    return compareResult;
                }
                else if (orderOfSort == SortOrder.Descending)
                {
                    return -compareResult;
                }
                else
                {
                    return 0;
                }
            }

            // 将文件大小字符串转换为字节数值进行比较
            private double ConvertFileSizeToBytes(string fileSize)
            {
                fileSize = fileSize.Trim();
                if (string.IsNullOrEmpty(fileSize))
                    return 0;

                string[] parts = fileSize.Split(' ');
                if (parts.Length != 2)
                    return 0;

                double size;
                if (!double.TryParse(parts[0], out size))
                    return 0;

                string unit = parts[1].ToUpper();
                switch (unit)
                {
                    case "B": return size;
                    case "KB": return size * 1024;
                    case "MB": return size * 1024 * 1024;
                    case "GB": return size * 1024 * 1024 * 1024;
                    case "TB": return size * 1024 * 1024 * 1024 * 1024;
                    default: return 0;
                }
            }

            // 属性访问器
            public int SortColumn
            {
                set { columnToSort = value; }
                get { return columnToSort; }
            }

            public SortOrder Order
            {
                set { orderOfSort = value; }
                get { return orderOfSort; }
            }
        }

        // WebDAV项目类，用于存储文件或目录信息
        private class WebDAVItem
        {
            public string Name { get; set; }
            public string Path { get; set; }
            public bool IsDirectory { get; set; }
            public long Size { get; set; }
            public DateTime LastModified { get; set; }

            public override string ToString()
            {
                return Name;
            }
        }

        public WebDAV()
        {
            InitializeComponent();

            // 初始化排序器
            lvwColumnSorter = new ListViewColumnSorter();
            listViewFiles.ListViewItemSorter = lvwColumnSorter;

            // 初始化HttpClient
            httpClientHandler = new HttpClientHandler
            {
                UseDefaultCredentials = false,
                PreAuthenticate = true,
                AllowAutoRedirect = true
            };

            httpClient = new HttpClient(httpClientHandler);

            // 设置默认超时
            httpClient.Timeout = TimeSpan.FromMinutes(10);

            // 添加窗体关闭事件处理程序
            this.FormClosing += WebDAV_FormClosing;
        }

        // 添加窗体关闭事件处理程序
        private void WebDAV_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 设置窗体正在关闭标志
            isFormClosing = true;

            // 只有在有正在进行的传输时才提示用户是否真的要关闭
            // 检查是否有正在进行的上传或下载任务
            if (isUploading || (totalBytesToTransfer > 0 && bytesTransferred < totalBytesToTransfer))
            {
                string taskType = "";
                if (isUploading)
                {
                    taskType = "上传";
                }
                else if (totalBytesToTransfer > 0 && bytesTransferred < totalBytesToTransfer)
                {
                    taskType = "下载";
                }
                // 如果同时有上传和下载（尽管当前设计不太可能），可以显示更通用的信息
                if (isUploading && (totalBytesToTransfer > 0 && bytesTransferred < totalBytesToTransfer))
                {
                    taskType = "上传或下载";
                }

                string message = $"有正在进行的{taskType}任务";

                if (MessageBox.Show($"{message}，确定要关闭吗？", "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                {
                    // 用户选择不关闭，取消关闭事件
                    e.Cancel = true;
                    isFormClosing = false;
                    return;
                }
            }

            // 安全关闭和清理资源
            try
            {
                if (httpClient != null)
                {
                    httpClient.CancelPendingRequests();
                    httpClient.Dispose();
                }

                if (httpClientHandler != null)
                {
                    httpClientHandler.Dispose();
                }
            }
            catch (Exception ex)
            {
                // 记录但不显示异常，因为窗体正在关闭
                LogMessage($"Cleanup during form closing encountered an error: {ex.Message}");
            }
        }

        // 将 private 改为 protected，使设计器能够访问此方法
        protected void WebDAV_Load(object sender, EventArgs e)
        {
            // 初始化UI设置
            InitializeUI();

            // 加载保存的连接设置
            LoadSavedConnection();

            // 隐藏传输进度相关控件
            progressBarTransfer.Visible = false;
            labelSpeed.Visible = false;
            labelTime.Visible = false;

            // 创建图片进度条控件
            pictureBoxProgress = new PictureBox();
            pictureBoxProgress.Size = new Size(progressBarTransfer.Width, progressImageHeight);
            pictureBoxProgress.Location = progressBarTransfer.Location;
            pictureBoxProgress.SizeMode = PictureBoxSizeMode.Normal;
            pictureBoxProgress.Visible = false;
            this.Controls.Add(pictureBoxProgress);

            // 绑定列标题点击事件
            listViewFiles.ColumnClick += new ColumnClickEventHandler(listViewFiles_ColumnClick);

            // 启用ListView的拖放功能
            listViewFiles.AllowDrop = true;
            listViewFiles.DragEnter += new DragEventHandler(listViewFiles_DragEnter);
            listViewFiles.DragDrop += new DragEventHandler(listViewFiles_DragDrop);
        }

        // 处理拖动进入事件
        private void listViewFiles_DragEnter(object sender, DragEventArgs e)
        {
            // 如果拖入的是文件，显示可以接受的图标
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effect = DragDropEffects.Copy;
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        // 处理拖放完成事件
        private async void listViewFiles_DragDrop(object sender, DragEventArgs e)
        {
            // 确保当前已连接到服务器
            if (string.IsNullOrEmpty(currentUrl))
            {
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 获取拖放的文件和文件夹
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] items = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (items.Length > 0)
                {
                    // 分离文件和文件夹
                    List<string> files = new List<string>();
                    List<string> folders = new List<string>();

                    foreach (string path in items)
                    {
                        if (File.Exists(path))
                            files.Add(path);
                        else if (Directory.Exists(path))
                            folders.Add(path);
                    }

                    // 计算总大小和文件数量
                    long totalSize = 0;
                    int totalFiles = 0;

                    // 计算文件大小
                    foreach (string file in files)
                    {
                        FileInfo fileInfo = new FileInfo(file);
                        totalSize += fileInfo.Length;
                        totalFiles++;
                    }

                    // 计算文件夹大小和内部文件数量
                    if (folders.Count > 0)
                    {
                        labelStatus.Text = "正在计算文件大小...";
                        Application.DoEvents();

                        foreach (string folder in folders)
                        {
                            DirectoryInfo dir = new DirectoryInfo(folder);
                            await Task.Run(() => CalculateFolderSize(dir, ref totalSize, ref totalFiles));
                        }
                    }

                    if (totalSize > 0 && totalFiles > 0)
                    {
                        // 不再显示确认对话框，直接继续上传
                        AppendOutputText($"开始上传 {totalFiles} 个文件和 {folders.Count} 个文件夹，总大小：{FormatFileSize(totalSize)}");
                        LogMessage($"Starting upload of {totalFiles} files and {folders.Count} folders, total size: {FormatFileSize(totalSize)}");

                        totalBytesToTransfer = totalSize;
                        bytesTransferred = 0;
                        _lastBytesTransferred = 0; // 重置最后传输字节数
                        ShowTransferProgress(true);

                        // 将所有文件和文件夹添加到队列
                        foreach (string file in files)
                        {
                            string fileName = Path.GetFileName(file);
                            string targetUrl = currentUrl + fileName;
                            lock (uploadLock)
                            {
                                uploadQueue.Enqueue(new UploadTask(file, targetUrl));
                            }
                        }

                        foreach (string folder in folders)
                        {
                            DirectoryInfo dirInfo = new DirectoryInfo(folder);
                            string folderName = dirInfo.Name;
                            string targetUrl = currentUrl + folderName;
                            lock (uploadLock)
                            {
                                uploadQueue.Enqueue(new UploadTask(folder, targetUrl, true));
                            }
                        }

                        // 更新队列状态显示
                        UpdateQueueStatus();

                        // 如果当前没有正在进行的上传，启动队列处理
                        lock (uploadLock)
                        {
                            if (!isUploading)
                            {
                                isUploading = true;
                                ProcessNextUpload();
                            }
                            else
                            {
                                AppendOutputText($"所有拖放的项目已加入上传队列，等待上传...");
                                LogMessage($"All dropped items added to upload queue, waiting...");
                            }
                        }
                    }
                }
            }
        }

        // 处理列标题点击事件
        private void listViewFiles_ColumnClick(object sender, ColumnClickEventArgs e)
        {
            // 判断是否是同一列被点击
            if (e.Column == lvwColumnSorter.SortColumn)
            {
                // 如果是同一列，切换排序顺序
                if (lvwColumnSorter.Order == SortOrder.Ascending)
                {
                    lvwColumnSorter.Order = SortOrder.Descending;
                }
                else
                {
                    lvwColumnSorter.Order = SortOrder.Ascending;
                }
            }
            else
            {
                // 如果是新列，默认为升序
                lvwColumnSorter.SortColumn = e.Column;
                lvwColumnSorter.Order = SortOrder.Ascending;
            }

            // 更新当前排序列和排序顺序
            currentSortColumn = e.Column;
            isAscending = (lvwColumnSorter.Order == SortOrder.Ascending);

            // 添加视觉指示器
            UpdateColumnHeaders();

            // 执行排序
            listViewFiles.Sort();
        }

        // 更新列标题显示排序指示器
        private void UpdateColumnHeaders()
        {
            // 先清除所有列的排序标记
            for (int i = 0; i < listViewFiles.Columns.Count; i++)
            {
                listViewFiles.Columns[i].Text = listViewFiles.Columns[i].Text.Replace(" ▲", "").Replace(" ▼", "");
            }

            // 为当前排序列添加排序标记
            if (currentSortColumn >= 0)
            {
                string sortMark = isAscending ? " ▲" : " ▼";
                string columnText = listViewFiles.Columns[currentSortColumn].Text;
                listViewFiles.Columns[currentSortColumn].Text = columnText + sortMark;
            }
        }

        // 初始化UI设置
        private void InitializeUI()
        {
            // 为ListView设置图标
            ImageList imgList = new ImageList();
            imgList.ColorDepth = ColorDepth.Depth32Bit; // 提高颜色深度以获得更好的图标质量
            imgList.ImageSize = new Size(16, 16); // 设置图标大小

            // 添加默认图标（例如，一个通用的文件图标和一个文件夹图标）
            // 这些将在无法获取特定系统图标时使用
            Icon defaultFileIcon = ShellIconHelper.GetSmallIcon(".txt", false); // 获取一个通用文件图标
            if (defaultFileIcon != null)
            {
                imgList.Images.Add("DefaultFile", defaultFileIcon);
            }
            else
            {
                // 如果获取失败，使用一个备用图标
                imgList.Images.Add("DefaultFile", System.Drawing.SystemIcons.WinLogo);
            }


            Icon defaultFolderIcon = ShellIconHelper.GetSmallIcon("C:\\", true); // 获取文件夹图标
            if (defaultFolderIcon != null)
            {
                imgList.Images.Add("DefaultFolder", defaultFolderIcon);
            }
            else
            {
                // 如果获取失败，使用一个备用图标
                imgList.Images.Add("DefaultFolder", System.Drawing.SystemIcons.Application);
            }

            listViewFiles.SmallImageList = imgList;
        }

        // Method to get image index based on item type/extension
        private int GetImageIndex(WebDAVItem item)
        {
            ImageList imgList = listViewFiles.SmallImageList;

            if (item.IsDirectory)
            {
                // 尝试获取文件夹图标
                Icon folderIcon = ShellIconHelper.GetSmallIcon("C:\\", true);
                if (folderIcon != null)
                {
                    // 检查 ImageList 中是否已存在此图标
                    string iconKey = "Folder"; // 使用一个固定的键
                    if (!imgList.Images.ContainsKey(iconKey))
                    {
                        imgList.Images.Add(iconKey, folderIcon);
                    }
                    return imgList.Images.IndexOfKey(iconKey);
                }
                else
                {
                    // 如果获取失败，返回默认文件夹图标的索引
                    return imgList.Images.IndexOfKey("DefaultFolder");
                }
            }
            else
            {
                // 尝试获取文件图标
                string extension = Path.GetExtension(item.Name).ToLower();
                Icon fileIcon = ShellIconHelper.GetSmallIcon(extension, false);

                if (fileIcon != null)
                {
                    // 检查 ImageList 中是否已存在此图标
                    string iconKey = "File_" + extension; // 使用扩展名作为键
                    if (!imgList.Images.ContainsKey(iconKey))
                    {
                        imgList.Images.Add(iconKey, fileIcon);
                    }
                    return imgList.Images.IndexOfKey(iconKey);
                }
                else
                {
                    // 如果获取失败，返回默认文件图标的索引
                    return imgList.Images.IndexOfKey("DefaultFile");
                }
            }
        }

        // 加载保存的连接设置和历史记录
        private void LoadSavedConnection()
        {
            try
            {
                // 加载单个保存的连接设置
                var settings = Properties.Settings.Default;
                if (settings.ServerAddress != null &&
                    !string.IsNullOrEmpty(settings.ServerAddress))
                {
                    textBoxServerAddress.Text = settings.ServerAddress;
                    textBoxUsername.Text = settings.Username;
                    textBoxPassword.Text = settings.Password;
                    checkBoxRememberConnection.Checked = true;

                    labelStatus.Text = "已加载保存的连接设置";
                }
                else
                {
                    // 添加默认连接数据
                    textBoxServerAddress.Text = "http://www1.movemama.cn/%E5%B7%A5%E4%BD%9C";
                    textBoxUsername.Text = "movemama";
                    textBoxPassword.Text = "qq123456";
                    checkBoxRememberConnection.Checked = false; // 不记住默认连接

                    labelStatus.Text = "已加载默认连接数据";
                }

                // 加载连接历史记录
                LoadConnectionHistory();
            }
            catch (Exception ex)
            {
                AppendOutputText($"加载连接设置失败: {ex.Message}");
                LogMessage($"Failed to load connection settings: {ex.Message}");
                labelStatus.Text = "准备就绪";
            }
        }

        // 加载连接历史记录
        private void LoadConnectionHistory()
        {
            try
            {
                // 清空当前历史记录列表
                connectionHistory.Clear();
                comboBoxHistory.Items.Clear();

                var settings = Properties.Settings.Default;
                string historyJson = settings.ConnectionHistory;

                if (!string.IsNullOrEmpty(historyJson))
                {
                    // 从JSON字符串反序列化连接历史记录
                    connectionHistory = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ConnectionHistoryItem>>(historyJson);

                    // 更新UI
                    foreach (var item in connectionHistory)
                    {
                        comboBoxHistory.Items.Add(item.ToString());
                    }

                    AppendOutputText($"已加载 {connectionHistory.Count} 个历史连接记录");
                    LogMessage($"Loaded {connectionHistory.Count} connection history items");
                }
            }
            catch (Exception ex)
            {
                AppendOutputText($"加载连接历史记录失败: {ex.Message}");
                LogMessage($"Failed to load connection history: {ex.Message}");
                // 如果加载失败，确保使用空列表
                connectionHistory = new List<ConnectionHistoryItem>();
            }
        }

        // 保存连接设置
        private void SaveConnectionSettings()
        {
            AppendOutputText("尝试保存连接设置...");
            LogMessage("Attempting to save connection settings...");
            try
            {
                var settings = Properties.Settings.Default;
                if (checkBoxRememberConnection.Checked)
                {
                    settings.ServerAddress = textBoxServerAddress.Text;
                    settings.Username = textBoxUsername.Text;
                    settings.Password = textBoxPassword.Text;
                    settings.Save();
                    AppendOutputText("连接设置已保存");
                    LogMessage("Connection settings saved.");
                }
                else
                {
                    settings.ServerAddress = "";
                    settings.Username = "";
                    settings.Password = "";
                    settings.Save();
                    AppendOutputText("连接设置已清除");
                    LogMessage("Connection settings cleared.");
                }
            }
            catch (Exception ex)
            {
                AppendOutputText($"保存连接设置失败: {ex.Message}");
                LogMessage($"Failed to save connection settings: {ex.Message}");
            }
        }

        // 连接按钮点击事件
        private async void buttonConnect_Click(object sender, EventArgs e)
        {
            AppendOutputText("尝试连接...");
            LogMessage("Attempting to connect...");
            try
            {
                labelStatus.Text = "正在连接...";
                Application.DoEvents();

                if (string.IsNullOrEmpty(textBoxServerAddress.Text))
                {
                    AppendOutputText("连接失败：缺少服务器地址");
                    LogMessage("Connection failed: Server address is empty.");
                    MessageBox.Show("请输入服务器地址", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    labelStatus.Text = "连接失败：缺少服务器地址";
                    return;
                }

                // 确保URL以/结尾
                currentUrl = textBoxServerAddress.Text.TrimEnd('/') + '/';
                AppendOutputText($"服务器地址: {currentUrl}");
                LogMessage($"Server address: {currentUrl}");

                // 重新创建HttpClient和HttpClientHandler实例以避免"This instance has already started one or more requests"错误
                if (httpClient != null)
                {
                    httpClient.Dispose();
                }
                if (httpClientHandler != null)
                {
                    httpClientHandler.Dispose();
                }

                // 初始化新的HttpClientHandler和HttpClient
                httpClientHandler = new HttpClientHandler
                {
                    UseDefaultCredentials = false,
                    PreAuthenticate = true,
                    AllowAutoRedirect = true
                };

                httpClient = new HttpClient(httpClientHandler);

                // 设置默认超时
                httpClient.Timeout = TimeSpan.FromMinutes(10);

                // 设置认证信息
                if (!string.IsNullOrEmpty(textBoxUsername.Text))
                {
                    credential = new NetworkCredential(textBoxUsername.Text, textBoxPassword.Text);
                    httpClientHandler.Credentials = credential;
                    AppendOutputText($"使用用户名: {textBoxUsername.Text}");
                    LogMessage($"Using username: {textBoxUsername.Text}");
                }
                else
                {
                    AppendOutputText("匿名连接");
                    LogMessage("Anonymous connection.");
                }

                // 保存连接设置
                SaveConnectionSettings();
                AppendOutputText("连接设置已保存");
                LogMessage("Connection settings saved.");

                // 设置 HttpClient 的 BaseAddress
                httpClient.BaseAddress = new Uri(currentUrl);
                AppendOutputText($"HttpClient BaseAddress 已设置为: {httpClient.BaseAddress}");
                LogMessage($"HttpClient BaseAddress set to: {httpClient.BaseAddress}");

                // 尝试连接并获取根目录文件
                await RefreshFileList(currentUrl);

                labelPath.Text = "路径: " + currentUrl;
                labelStatus.Text = "已连接到 " + currentUrl;
                AppendOutputText($"成功连接到 {currentUrl}");
                LogMessage($"Successfully connected to {currentUrl}");

                // 添加到历史记录
                SaveToConnectionHistory(textBoxServerAddress.Text, textBoxUsername.Text, textBoxPassword.Text);
            }
            catch (Exception ex)
            {
                AppendOutputText($"连接失败: {ex.Message}");
                LogMessage($"Connection failed: {ex.Message}");
                MessageBox.Show($"连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                labelStatus.Text = "连接失败: " + ex.Message;
            }
        }

        // 刷新文件列表
        private async Task RefreshFileList(string url)
        {
            AppendOutputText($"正在获取文件列表: {url}");
            LogMessage($"Fetching file list for: {url}");
            try
            {
                listViewFiles.Items.Clear();
                currentItems.Clear();

                // 创建PROPFIND请求
                var request = new HttpRequestMessage(new HttpMethod("PROPFIND"), url);
                request.Headers.Add("Depth", "1"); // 只获取当前目录和直接子项
                LogMessage($"Sending PROPFIND request to {url} with Depth: 1");

                // 发送请求
                HttpResponseMessage response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                LogMessage($"Received successful response for PROPFIND {url}");

                // 解析XML响应
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                string responseContent = Encoding.UTF8.GetString(responseBytes);
                XDocument doc = XDocument.Parse(responseContent);
                LogMessage("Parsed XML response.");

                // 定义命名空间
                XNamespace dav = "DAV:";

                // 解析每个资源
                var responses = doc.Descendants(dav + "response").ToList();
                AppendOutputText($"找到 {responses.Count} 个项目");
                LogMessage($"Found {responses.Count} items in the response.");

                // 添加返回上级目录选项（如果不是根目录）
                if (url != textBoxServerAddress.Text.TrimEnd('/') + '/' &&
                    !url.Equals(textBoxServerAddress.Text, StringComparison.OrdinalIgnoreCase))
                {
                    Uri uri = new Uri(url);
                    string parentUrl = uri.AbsoluteUri.Substring(0, uri.AbsoluteUri.TrimEnd('/').LastIndexOf('/') + 1);

                    var parentItem = new WebDAVItem
                    {
                        Name = "..",
                        Path = parentUrl,
                        IsDirectory = true,
                        Size = 0,
                        LastModified = DateTime.Now
                    };

                    currentItems.Add(parentItem);

                    var listItem = new ListViewItem("..");
                    listItem.SubItems.Add("文件夹");
                    listItem.SubItems.Add("");
                    listItem.SubItems.Add("");
                    listItem.ImageIndex = 0;
                    listItem.Tag = parentItem;

                    listViewFiles.Items.Add(listItem);
                    AppendOutputText("添加 '..' 返回上级目录");
                    LogMessage("Added '..' item for parent directory.");
                }

                // 处理服务器返回的每个项目
                foreach (var res in responses)
                {
                    string href = res.Element(dav + "href")?.Value;
                    if (string.IsNullOrEmpty(href)) continue;

                    // 解码URL
                    href = Uri.UnescapeDataString(href);

                    // 跳过当前目录
                    if (IsCurrentDirectory(url, href)) continue;

                    // 获取属性
                    var propstat = res.Element(dav + "propstat");
                    var prop = propstat?.Element(dav + "prop");

                    bool isCollection = prop?.Element(dav + "resourcetype")?.Element(dav + "collection") != null;
                    string contentLength = prop?.Element(dav + "getcontentlength")?.Value ?? "0";
                    string lastModified = prop?.Element(dav + "getlastmodified")?.Value ?? DateTime.Now.ToString();

                    // 解析名称
                    string name = Path.GetFileName(href.TrimEnd('/'));
                    if (string.IsNullOrEmpty(name)) name = href.TrimEnd('/');

                    // 创建项目
                    var item = new WebDAVItem
                    {
                        Name = name,
                        Path = href,
                        IsDirectory = isCollection,
                        Size = long.TryParse(contentLength, out long size) ? size : 0,
                        LastModified = DateTime.TryParse(lastModified, out DateTime dt) ? dt : DateTime.Now
                    };

                    currentItems.Add(item);

                    // 添加到ListView
                    var listItem = new ListViewItem(item.Name);
                    // 修改类型列：文件夹显示"文件夹"，文件显示文件扩展名
                    string fileType = item.IsDirectory ? "文件夹" : Path.GetExtension(item.Name).TrimStart('.');
                    listItem.SubItems.Add(fileType);
                    listItem.SubItems.Add(item.IsDirectory ? "" : FormatFileSize(item.Size));
                    listItem.SubItems.Add(item.LastModified.ToString());
                    listItem.Tag = item;

                    // 设置图标
                    listItem.ImageIndex = GetImageIndex(item);

                    listViewFiles.Items.Add(listItem);
                    LogMessage($"Added item: {item.Name} (IsDirectory: {item.IsDirectory})");
                }

                // 如果之前已经有排序设置，应用当前的排序
                if (currentSortColumn >= 0)
                {
                    lvwColumnSorter.SortColumn = currentSortColumn;
                    lvwColumnSorter.Order = isAscending ? SortOrder.Ascending : SortOrder.Descending;
                    listViewFiles.Sort();
                    UpdateColumnHeaders(); // 更新列标题显示排序标记
                }

                AppendOutputText("文件列表刷新完成");
                LogMessage("File list refresh completed.");
            }
            catch (Exception ex)
            {
                AppendOutputText($"获取文件列表失败: {ex.Message}");
                LogMessage($"Failed to get file list: {ex.Message}");
                MessageBox.Show($"获取文件列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                labelStatus.Text = "获取文件列表失败: " + ex.Message;
            }
        }

        // 格式化文件大小显示 - 适应网络传输速度显示
        private string FormatFileSize(long bytes, bool isNetworkSpeed = false)
        {
            // 如果是网络速度，应用一个调整系数使其更稳定
            if (isNetworkSpeed)
            {
                // 网络速度一般会比系统计算的快，所以我们需要调低些
                bytes = (long)(bytes * 0.7); // 应用一个合理的降速系数
            }
            
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number = number / 1024;
                counter++;
            }

            // 对于网络速度，输出更精细的小数
            string format = isNetworkSpeed ? "n2" : "n1";
            return $"{number.ToString(format)} {suffixes[counter]}";
        }

        // 修改AppendOutputText方法，添加安全检查
        private void AppendOutputText(string text)
        {
            // 如果窗体正在关闭，不要尝试更新UI
            if (isFormClosing)
                return;

            if (textBoxOutput.InvokeRequired)
            {
                try
                {
                    textBoxOutput.Invoke(new Action<string>(AppendOutputText), text);
                }
                catch (ObjectDisposedException)
                {
                    // 控件已被释放，忽略此错误
                    return;
                }
                catch (InvalidOperationException)
                {
                    // 窗体可能正在关闭中，忽略此错误
                    return;
                }
            }
            else
            {
                try
                {
                    // 检查TextBox是否已被释放
                    if (!textBoxOutput.IsDisposed && textBoxOutput.IsHandleCreated)
                    {
                        textBoxOutput.AppendText(text + Environment.NewLine);
                    }
                }
                catch (ObjectDisposedException)
                {
                    // 控件已被释放，忽略此错误
                }
                catch (InvalidOperationException)
                {
                    // 窗体可能正在关闭中，忽略此错误
                }
            }
        }

        // 修改LogMessage方法，确保在窗体关闭时也能正常工作
        private void LogMessage(string message)
        {
            try
            {
                string logFilePath = Path.Combine(Application.StartupPath, "webdav_log.txt");
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}";
                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // 忽略日志记录失败，不要尝试向UI输出，因为可能窗体已关闭
            }
        }

        // 存储最近的传输数据，用于计算实时速度
        private readonly Queue<(DateTime time, long bytes)> _recentTransfers = new Queue<(DateTime, long)>();
        private long _lastBytesTransferred = 0;
        private DateTime _lastSpeedUpdateTime = DateTime.MinValue;
        private const int SPEED_SAMPLE_WINDOW = 5; // 保留最近5秒的采样用于计算速度，增加数据点以提高准确度

        // 添加图片进度条相关变量
        private PictureBox pictureBoxProgress;
        private Color progressFillColor = Color.FromArgb(0, 200, 0); // 绿色进度条
        private Color progressBackColor = Color.FromArgb(240, 240, 240); // 浅灰色背景
        private int progressImageHeight = 24; // 进度条图片高度

        // 更新传输进度
        private void UpdateTransferProgress(long bytesTransferred, long totalBytes)
        {
            // 如果窗体正在关闭，不要尝试更新UI
            if (isFormClosing)
                return;

            if (this.InvokeRequired)
            {
                try
                {
                    this.Invoke(new Action<long, long>(UpdateTransferProgress), bytesTransferred, totalBytes);
                }
                catch (ObjectDisposedException)
                {
                    // 窗体已被释放，忽略此错误
                    return;
                }
                catch (InvalidOperationException)
                {
                    // 窗体可能正在关闭中，忽略此错误
                    return;
                }
            }
            else
            {
                try
                {
                    // 检查窗体是否已释放
                    if (!this.IsDisposed && this.IsHandleCreated)
                    {
                        // 记录详细日志用于调试
                        if ((DateTime.Now - _lastSpeedUpdateTime).TotalMilliseconds >= 1000)
                        {
                            LogMessage($"进度更新: {bytesTransferred}/{totalBytes} 字节 ({bytesTransferred * 100.0 / totalBytes:F1}%)");
                        }

                        // 计算新传输的字节数
                        long newBytes = bytesTransferred - _lastBytesTransferred;
                        if (newBytes > 0)
                        {
                            // 添加传输采样点，添加时间戳以提高精确度
                            DateTime now = DateTime.Now;
                            _recentTransfers.Enqueue((now, newBytes));
                            _lastBytesTransferred = bytesTransferred;

                            // 移除旧的采样点（超过采样窗口）
                            while (_recentTransfers.Count > 0 &&
                                   (DateTime.Now - _recentTransfers.Peek().time).TotalSeconds > SPEED_SAMPLE_WINDOW)
                            {
                                _recentTransfers.Dequeue();
                            }
                        }

                        this.bytesTransferred = bytesTransferred;

                        // 智能处理总大小调整 - 当实际下载超出预期时
                        if (bytesTransferred > totalBytes && totalBytes > 0)
                        {
                            // 计算超出的百分比
                            double overagePercentage = ((double)(bytesTransferred - totalBytes) / totalBytes) * 100;
                            
                            // 如果超出不太多（小于50%），动态调整总大小以保持进度条的有用性
                            if (overagePercentage <= 50)
                            {
                                // 动态调整总大小，增加一些缓冲
                                long adjustedTotal = (long)(bytesTransferred * 1.2); // 增加20%缓冲
                                this.totalBytesToTransfer = adjustedTotal;
                                totalBytes = adjustedTotal;
                                LogMessage($"动态调整总大小: 原{FormatFileSize(this.totalBytesToTransfer)}，现{FormatFileSize(totalBytes)} (超出{overagePercentage:F1}%)");
                            }
                            else
                            {
                                // 超出太多，记录警告但不调整
                                LogMessage($"警告: 传输字节 {bytesTransferred} 大幅超出预期 {totalBytes} ({overagePercentage:F1}%)，可能估算严重不准确");
                            }
                        }
                        else
                        {
                            this.totalBytesToTransfer = totalBytes;
                        }
                        
                        // 计算进度百分比，防止除0错误
                        int progressPercentage = totalBytes > 0 ? (int)((double)bytesTransferred / totalBytes * 100) : 0;

                        // 限制百分比在合理范围内
                        if (progressPercentage > 100)
                        {
                            LogMessage($"进度仍超过100%: {progressPercentage}%，可能需要进一步调整估算");
                            progressPercentage = 100; // 显示时限制为100%
                        }
                        
                        // 更新进度条
                        progressBarTransfer.Value = Math.Min(progressPercentage, 100);

                        // 更新图片进度条
                        UpdateProgressImage(Math.Min(progressPercentage, 100));

                        // 每250毫秒更新一次速度显示，提高响应度但不会过于频繁
                        if ((DateTime.Now - _lastSpeedUpdateTime).TotalMilliseconds >= 250)
                        {
                            _lastSpeedUpdateTime = DateTime.Now;

                            // 计算实时传输速度
                            double realTimeSpeed = CalculateCurrentSpeed();
                            
                            // 应用速度校准因子 - 根据实际测试结果调整显示速度
                            double calibrationFactor = 0.6; // 使显示的速度接近实际网络速度
                            realTimeSpeed *= calibrationFactor;

                            // 优化速度显示格式，使用特殊的网络速度格式化
                            string formattedSpeed = FormatFileSize((long)realTimeSpeed, true) + "/s";
                            string formattedTotal = FormatFileSize(totalBytes);
                            string formattedTransferred = FormatFileSize(bytesTransferred);

                            // 显示格式: "XXX MB/s | YYY MB / ZZZ MB (PP%)"
                            string speedText = $"{formattedSpeed} | {formattedTransferred} / {formattedTotal} ({progressPercentage}%)";
                            labelSpeed.Text = speedText;

                            // 计算已用时间和估计剩余时间
                            double elapsedSeconds = transferStopwatch.ElapsedMilliseconds / 1000.0;
                            TimeSpan elapsedTime = TimeSpan.FromSeconds(elapsedSeconds);

                            // 估算剩余时间
                            string remainingTimeText = "";
                            if (realTimeSpeed > 0 && bytesTransferred > 0 && bytesTransferred < totalBytes)
                            {
                                double remainingSeconds = (totalBytes - bytesTransferred) / realTimeSpeed;
                                if (remainingSeconds > 0 && !double.IsInfinity(remainingSeconds))
                                {
                                    TimeSpan remainingTime = TimeSpan.FromSeconds(remainingSeconds);
                                    remainingTimeText = $" | 剩余: {FormatTimeSpan(remainingTime)}";
                                }
                            }

                            labelTime.Text = $"已用时间: {FormatTimeSpan(elapsedTime)}{remainingTimeText}";
                        }
                    }
                }
                catch (ObjectDisposedException)
                {
                    // 控件已被释放，忽略此错误
                }
                catch (InvalidOperationException)
                {
                    // 窗体可能正在关闭中，忽略此错误
                }
                catch (Exception ex)
                {
                    // 捕获其他可能的异常
                    LogMessage($"更新进度时出错: {ex.Message}");
                }
            }
        }

        // 格式化TimeSpan显示
        private string FormatTimeSpan(TimeSpan time)
        {
            if (time.TotalHours >= 1)
                return $"{(int)time.TotalHours}时{time.Minutes}分{time.Seconds}秒";
            else if (time.TotalMinutes >= 1)
                return $"{time.Minutes}分{time.Seconds}秒";
            else
                return $"{time.Seconds}秒";
        }

        // 更新进度图片
        private void UpdateProgressImage(int percentage)
        {
            try
            {
                // 确保pictureBoxProgress已创建
                if (pictureBoxProgress == null || pictureBoxProgress.IsDisposed)
                    return;

                // 创建进度条图片并设置到PictureBox
                using (Bitmap progressBitmap = new Bitmap(pictureBoxProgress.Width, progressImageHeight))
                {
                    using (Graphics g = Graphics.FromImage(progressBitmap))
                    {
                        // 绘制背景
                        g.Clear(progressBackColor);

                        // 计算进度条宽度
                        int progressWidth = (int)(pictureBoxProgress.Width * percentage / 100.0);

                        // 绘制进度
                        if (progressWidth > 0)
                        {
                            using (SolidBrush brush = new SolidBrush(progressFillColor))
                            {
                                g.FillRectangle(brush, 0, 0, progressWidth, progressImageHeight);
                            }
                        }

                        // 添加边框
                        using (Pen pen = new Pen(Color.LightGray, 1))
                        {
                            g.DrawRectangle(pen, 0, 0, pictureBoxProgress.Width - 1, progressImageHeight - 1);
                        }

                        // 添加进度百分比文本和文件大小信息
                        string percentText = $"{percentage}%";
                        string sizeText = $"{FormatFileSize(bytesTransferred)}/{FormatFileSize(totalBytesToTransfer)}";

                        // 绘制进度百分比
                        using (Font font = new Font("Arial", 10, FontStyle.Bold))
                        using (SolidBrush textBrush = new SolidBrush(Color.Black))
                        using (StringFormat sf = new StringFormat())
                        {
                            sf.Alignment = StringAlignment.Center;
                            sf.LineAlignment = StringAlignment.Center;
                            g.DrawString(percentText, font, textBrush,
                                new RectangleF(0, 0, pictureBoxProgress.Width, progressImageHeight), sf);
                        }
                    }

                    // 如果已有图片，释放它
                    if (pictureBoxProgress.Image != null)
                    {
                        Image oldImg = pictureBoxProgress.Image;
                        pictureBoxProgress.Image = null;
                        oldImg.Dispose();
                    }

                    // 设置新图片
                    pictureBoxProgress.Image = new Bitmap(progressBitmap);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不显示，防止影响上传过程
                LogMessage($"更新进度图片时出错: {ex.Message}");
            }
        }

        // 显示传输进度
        private void ShowTransferProgress(bool show)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<bool>(ShowTransferProgress), show);
            }
            else
            {
                // 确保创建了pictureBoxProgress
                if (pictureBoxProgress == null || pictureBoxProgress.IsDisposed)
                {
                    // 如果PictureBox不存在或已释放，创建一个新的
                    pictureBoxProgress = new PictureBox();
                    pictureBoxProgress.Size = new Size(progressBarTransfer.Width, progressImageHeight);
                    pictureBoxProgress.Location = progressBarTransfer.Location;
                    pictureBoxProgress.SizeMode = PictureBoxSizeMode.Normal;
                    pictureBoxProgress.Visible = false;
                    this.Controls.Add(pictureBoxProgress);
                    pictureBoxProgress.BringToFront();
                }

                // 隐藏标准进度条，显示图片进度条
                progressBarTransfer.Visible = false;
                pictureBoxProgress.Visible = show;
                labelSpeed.Visible = show;
                labelTime.Visible = show;

                if (show)
                {
                    progressBarTransfer.Value = 0;
                    transferStopwatch.Reset();
                    transferStopwatch.Start();
                    bytesTransferred = 0;
                    _lastBytesTransferred = 0;
                    _recentTransfers.Clear();
                    _lastSpeedUpdateTime = DateTime.MinValue;

                    // 创建初始进度图片(0%)
                    UpdateProgressImage(0);
                }
                else
                {
                    transferStopwatch.Stop();

                    // 释放图片资源
                    if (pictureBoxProgress.Image != null)
                    {
                        Image oldImg = pictureBoxProgress.Image;
                        pictureBoxProgress.Image = null;
                        oldImg.Dispose();
                    }
                }
            }
        }

        // 调整窗体大小时重设pictureBoxProgress大小
        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);

            // 如果pictureBoxProgress已创建，调整其大小
            if (pictureBoxProgress != null && !pictureBoxProgress.IsDisposed)
            {
                pictureBoxProgress.Size = new Size(progressBarTransfer.Width, progressImageHeight);
                pictureBoxProgress.Location = progressBarTransfer.Location;

                // 如果正在显示进度，更新图片
                if (pictureBoxProgress.Visible && totalBytesToTransfer > 0)
                {
                    int progressPercentage = (int)((double)bytesTransferred / totalBytesToTransfer * 100);
                    UpdateProgressImage(progressPercentage);
                }
            }
        }

        // 计算当前实时速度（字节/秒）- 优化计算逻辑
        private double CalculateCurrentSpeed()
        {
            if (_recentTransfers.Count == 0)
                return 0;

            // 使用所有样本点计算速度
            long totalBytes = 0;
            foreach (var sample in _recentTransfers)
            {
                totalBytes += sample.bytes;
            }

            // 计算从第一个点到当前的时间跨度
            double timeSpan = (DateTime.Now - _recentTransfers.First().time).TotalSeconds;

            // 防止除以太小的数
            if (timeSpan <= 0.1)
                timeSpan = 0.1;

            // 计算平滑的速度值
            double instantSpeed = totalBytes / timeSpan;
            
            // 限制瞬时速度，避免突发峰值
            const double MAX_SPEED_FACTOR = 2.0; // 最高速度不能超过平均速度的2倍
            
            // 始终计算整体平均速度作为基准
            double overallSpeed = 0;
            if (transferStopwatch.ElapsedMilliseconds > 1000) // 至少传输1秒后再计算
            {
                overallSpeed = bytesTransferred / (transferStopwatch.ElapsedMilliseconds / 1000.0);
                
                // 对瞬时速度进行限制，避免速度显示波动过大
                if (instantSpeed > overallSpeed * MAX_SPEED_FACTOR)
                {
                    instantSpeed = overallSpeed * MAX_SPEED_FACTOR;
                }
            }
            
            // 增强的权重算法，更注重实际速度而非瞬时速度
            if (_recentTransfers.Count < 3)
            {
                // 样本太少时主要依赖整体速度
                return (instantSpeed * 0.2 + overallSpeed * 0.8);
            }
            else if (_recentTransfers.Count < 5)
            {
                // 样本适中时仍然更依赖整体速度
                return (instantSpeed * 0.3 + overallSpeed * 0.7);
            }
            else
            {
                // 即使样本充足时也使用平衡的权重
                return (instantSpeed * 0.4 + overallSpeed * 0.6);
            }
            
        }

        // 上传按钮点击事件
        private async void buttonUpload_Click(object sender, EventArgs e)
        {
            await UploadFiles();
        }

        // 上传菜单项点击事件
        private async void uploadToolStripMenuItem_Click(object sender, EventArgs e)
        {
            await UploadFiles();
        }

        // 上传文件夹按钮点击事件
        private async void buttonUploadFolder_Click(object sender, EventArgs e)
        {
            await UploadFolder();
        }

        // 上传文件夹菜单项点击事件
        private async void uploadFolderToolStripMenuItem_Click(object sender, EventArgs e)
        {
            await UploadFolder();
        }

        // 上传文件夹方法 - 添加到队列
        private async Task UploadFolder()
        {
            if (string.IsNullOrEmpty(currentUrl))
            {
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            using (FolderBrowserDialog dlg = new FolderBrowserDialog())
            {
                dlg.Description = "选择要上传的文件夹";

                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    string folderPath = dlg.SelectedPath;
                    string folderName = new DirectoryInfo(folderPath).Name;

                    // 计算总大小和文件数量
                    long totalSize = 0;
                    int totalFiles = 0;
                    DirectoryInfo dir = new DirectoryInfo(folderPath);

                    // 显示计算中提示
                    labelStatus.Text = "正在计算文件大小...";
                    Application.DoEvents();

                    // 计算文件夹大小和文件数量
                    await Task.Run(() => CalculateFolderSize(dir, ref totalSize, ref totalFiles));

                    if (totalSize > 0 && totalFiles > 0)
                    {
                        // 不再显示确认对话框，直接继续上传
                        AppendOutputText($"开始上传文件夹：{folderName}，共 {totalFiles} 个文件，总大小：{FormatFileSize(totalSize)}");
                        LogMessage($"Starting upload of folder {folderName} with {totalFiles} files, total size: {FormatFileSize(totalSize)}");

                        totalBytesToTransfer = totalSize;
                        bytesTransferred = 0;
                        _lastBytesTransferred = 0; // 重置最后传输字节数
                        ShowTransferProgress(true);

                        // 将文件夹上传任务添加到队列
                        string targetUrl = currentUrl + folderName;
                        lock (uploadLock)
                        {
                            uploadQueue.Enqueue(new UploadTask(folderPath, targetUrl, true));
                        }

                        // 更新队列状态显示
                        UpdateQueueStatus();

                        // 如果当前没有正在进行的上传，启动队列处理
                        lock (uploadLock)
                        {
                            if (!isUploading)
                            {
                                isUploading = true;
                                ProcessNextUpload();
                            }
                            else
                            {
                                AppendOutputText($"文件夹 {folderName} 已加入上传队列，等待上传...");
                                LogMessage($"Folder {folderName} added to upload queue, waiting...");
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show("选择的文件夹为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
        }

        // 修正文件夹大小计算方法
        private void CalculateFolderSize(DirectoryInfo directory, ref long totalSize, ref int fileCount)
        {
            try
            {
                // 计算所有文件大小
                FileInfo[] files = directory.GetFiles();
                foreach (FileInfo file in files)
                {
                    try
                    {
                        // 确保获取准确的文件长度
                        long fileLength = file.Length;
                        totalSize += fileLength;
                        fileCount++;

                        // 记录日志以便调试
                        LogMessage($"文件: {file.Name}, 大小: {fileLength} 字节, 累计: {totalSize} 字节");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"获取文件大小失败: {file.FullName} - {ex.Message}");
                    }
                }

                // 递归处理所有子目录
                DirectoryInfo[] subDirs = directory.GetDirectories();
                foreach (DirectoryInfo dir in subDirs)
                {
                    CalculateFolderSize(dir, ref totalSize, ref fileCount);
                }
            }
            catch (Exception ex)
            {
                AppendOutputText($"计算文件夹大小时出错：{ex.Message}");
                LogMessage($"Error calculating folder size: {ex.Message}");
            }
        }

        // 带进度的文件上传 - 修正进度跟踪
        private async Task UploadFileWithProgress(string filePath, string targetUrl)
        {
            string fileName = Path.GetFileName(filePath);
            AppendOutputText($"正在上传文件: {fileName} 到 {targetUrl}");
            LogMessage($"Uploading file: {fileName} to {targetUrl}, 准备上传");

            try
            {
                labelStatus.Text = $"正在上传: {fileName}...";

                // 获取文件大小
                FileInfo fileInfo = new FileInfo(filePath);
                long fileSize = fileInfo.Length;
                LogMessage($"文件 {fileName} 实际大小: {fileSize} 字节");

                // 记录当前文件的起始位置，用于计算该文件的进度
                long fileStartPosition = bytesTransferred;
                long lastReportedProgress = 0;

                // 创建可以报告进度的内容
                var progressContent = new ProgressStreamContent(
                    File.OpenRead(filePath),
                    (bytesRead, totalBytes) =>
                    {
                        try
                        {
                            // 计算增量进度，避免重复计算
                            long incrementalProgress = bytesRead - lastReportedProgress;
                            if (incrementalProgress > 0)
                            {
                                // 更新最后报告的进度
                                lastReportedProgress = bytesRead;

                                // 更新总进度
                                long overallProgress = fileStartPosition + bytesRead;

                                // 避免超出总大小
                                if (overallProgress > totalBytesToTransfer)
                                    overallProgress = totalBytesToTransfer;

                                // 记录详细的进度信息
                                LogMessage($"文件: {fileName}, 已读取: {bytesRead}/{fileSize}, 总进度: {overallProgress}/{totalBytesToTransfer}");

                                // 更新UI
                                UpdateTransferProgress(overallProgress, totalBytesToTransfer);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录但不中断传输
                            LogMessage($"更新进度时出错: {ex.Message}");
                        }
                    }
                );

                // 发送PUT请求
                // u8bbeu7f6eHTTPu5934u4ee5u4f18u5316u5927u6587u4ef6u4f20u8f93
                httpClient.DefaultRequestHeaders.ExpectContinue = true;
                
                // u4f7fu7528TransferEncodingChunkedu5934u6765u5206u5757u4f20u8f93u5927u6587u4ef6
                var request = new HttpRequestMessage(HttpMethod.Put, targetUrl);
                request.Content = progressContent;
                request.Headers.TransferEncodingChunked = true;
                
                // u53d1u9001PUTu8bf7u6c42
                HttpResponseMessage response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                // 更新已传输的总字节数
                bytesTransferred = fileStartPosition + fileSize;
                LogMessage($"文件 {fileName} 上传完成，大小: {fileSize} 字节，总计: {bytesTransferred} 字节");

                AppendOutputText($"文件 {fileName} ({FormatFileSize(fileSize)}) 上传成功");
                LogMessage($"File {fileName} uploaded successfully.");
            }
            catch (Exception ex)
            {
                // 检查是否是413错误（请求实体过大）
                if (ex is HttpRequestException httpEx && httpEx.Message.Contains("413"))
                {
                    AppendOutputText($"上传失败: {fileName} - 文件大小超过服务器限制。服务器返回413错误(请求实体过大)。");
                    LogMessage($"Upload failed for {fileName}: File size exceeds server limit. Server returned 413 error (Request Entity Too Large).");
                    labelStatus.Text = $"上传失败: {fileName} - 文件过大，服务器拒绝接收";
                }
                else
                {
                    AppendOutputText($"上传失败: {fileName} - {ex.Message}");
                    LogMessage($"Upload failed for {fileName}: {ex.Message}");
                }
            }
        }

        // 上传文件 - 修正大小跟踪
        private async Task UploadFiles()
        {
            if (string.IsNullOrEmpty(currentUrl))
            {
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            using (OpenFileDialog dlg = new OpenFileDialog())
            {
                dlg.Title = "选择要上传的文件";
                dlg.Multiselect = true;

                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    // 计算总大小
                    long totalSize = 0;
                    foreach (string file in dlg.FileNames)
                    {
                        try
                        {
                            FileInfo fileInfo = new FileInfo(file);
                            totalSize += fileInfo.Length;
                            LogMessage($"文件: {fileInfo.Name}, 大小: {fileInfo.Length} 字节");
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"获取文件大小失败: {file} - {ex.Message}");
                        }
                    }

                    // 记录总大小信息
                    LogMessage($"上传文件总大小: {totalSize} 字节 ({FormatFileSize(totalSize)})");

                    // 不再显示确认对话框，直接继续上传
                    AppendOutputText($"开始上传 {dlg.FileNames.Length} 个文件，总大小：{FormatFileSize(totalSize)}");
                    LogMessage($"Starting upload of {dlg.FileNames.Length} files, total size: {FormatFileSize(totalSize)}");

                    totalBytesToTransfer = totalSize;
                    bytesTransferred = 0;
                    _lastBytesTransferred = 0; // 重置最后传输字节数
                    ShowTransferProgress(true);

                    // 上传多个文件，但不在每个文件上传后刷新目录
                    for (int i = 0; i < dlg.FileNames.Length; i++)
                    {
                        string file = dlg.FileNames[i];
                        string fileName = Path.GetFileName(file);
                        string targetUrl = currentUrl + fileName;
                        // 传入false参数，表示不在每个文件上传后刷新，提高批量上传性能
                        await UploadFileDirectly(file, targetUrl, false);
                    }

                    ShowTransferProgress(false);
                    labelStatus.Text = $"上传完成，共 {dlg.FileNames.Length} 个文件，大小：{FormatFileSize(totalSize)}";

                    // 所有文件上传完成后，只刷新一次目录
                    try
                    {
                        await RefreshFileList(currentUrl);
                        LogMessage($"批量上传完成后刷新目录: {currentUrl}");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"批量上传完成后刷新目录失败: {ex.Message}");
                        AppendOutputText("刷新文件列表失败，可能需要手动刷新");
                    }
                }
            }
        }

        // 直接上传单个文件（带进度）
        private async Task UploadFileDirectly(string filePath, string targetUrl, bool refreshAfterUpload = true)
        {
            string fileName = Path.GetFileName(filePath);

            try
            {
                // 获取文件大小
                FileInfo fileInfo = new FileInfo(filePath);
                long fileSize = fileInfo.Length;

                // 检查文件大小是否超过200MB（大约服务器的限制）
                if (fileSize > 200 * 1024 * 1024) // 200MB
                {
                    AppendOutputText($"上传失败: {fileName} - 文件大小({FormatFileSize(fileSize)})超过服务器限制(200MB)。");
                    LogMessage($"Upload failed: {fileName} - File size ({fileSize} bytes) exceeds server limit (200MB).");
                    labelStatus.Text = $"上传失败: {fileName} - 文件过大，超过服务器限制";
                    return;
                }

                AppendOutputText($"正在上传文件: {fileName} ({FormatFileSize(fileSize)}) 到 {targetUrl}");
                LogMessage($"Uploading file: {fileName} to {targetUrl}, 大小: {fileSize} 字节");

                labelStatus.Text = $"正在上传: {fileName} ({FormatFileSize(fileSize)})...";

                // 记录当前文件的起始位置，用于计算该文件的进度
                long fileStartPosition = bytesTransferred;
                long lastReportedProgress = 0;

                // 创建可以报告进度的内容
                var progressContent = new ProgressStreamContent(
                    File.OpenRead(filePath),
                    (bytesRead, totalBytes) =>
                    {
                        try
                        {
                            // 计算增量进度，避免重复计算
                            long incrementalProgress = bytesRead - lastReportedProgress;
                            if (incrementalProgress > 0)
                            {
                                // 更新最后报告的进度
                                lastReportedProgress = bytesRead;

                                // 更新总进度
                                long overallProgress = fileStartPosition + bytesRead;

                                // 避免超出总大小
                                if (overallProgress > totalBytesToTransfer)
                                    overallProgress = totalBytesToTransfer;

                                // 更新UI
                                UpdateTransferProgress(overallProgress, totalBytesToTransfer);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录但不中断传输
                            LogMessage($"更新进度时出错: {ex.Message}");
                        }
                    }
                    );

                // 设置HTTP头以优化大文件传输
                httpClient.DefaultRequestHeaders.ExpectContinue = true;
                
                // 使用TransferEncodingChunked头来分块传输大文件
                var request = new HttpRequestMessage(HttpMethod.Put, targetUrl);
                request.Content = progressContent;
                request.Headers.TransferEncodingChunked = true;
                
                // 发送PUT请求
                HttpResponseMessage response = await httpClient.SendAsync(request);
                
                // 检查响应状态码
                if ((int)response.StatusCode == 413) // 413 Request Entity Too Large
                {
                    AppendOutputText($"上传失败: {fileName} - 响应状态码表明请求过大(413)。服务器拒绝接收此大小的文件。");
                    LogMessage($"Upload failed: {fileName} - Response status code 413 (Request Entity Too Large). Server refused the file size.");
                    labelStatus.Text = $"上传失败: {fileName} - 文件过大，服务器拒绝接收";
                    throw new Exception($"服务器拒绝接收此大小的文件 ({FormatFileSize(fileSize)})");
                }
                
                response.EnsureSuccessStatusCode();

                // 更新已传输的总字节数
                bytesTransferred = fileStartPosition + fileSize;

                labelStatus.Text = $"文件 {fileName} ({FormatFileSize(fileSize)}) 上传成功";
                AppendOutputText($"文件 {fileName} 上传成功");
                LogMessage($"File {fileName} uploaded successfully. 大小: {fileSize} 字节，总计上传: {bytesTransferred} 字节");

                // 根据参数决定是否刷新目录
                if (refreshAfterUpload)
                {
                    try
                    {
                        await RefreshFileList(currentUrl);
                        LogMessage($"已刷新目录: {currentUrl}");
                    }
                    catch (Exception refreshEx)
                    {
                        LogMessage($"刷新目录失败: {refreshEx.Message}");
                        // 不抛出异常，因为文件上传已成功
                    }
                }
            }
            catch (Exception ex)
            {
                // 检查是否是413错误（请求实体过大）
                if (ex is HttpRequestException httpEx && httpEx.Message.Contains("413"))
                {
                    string msg = $"上传失败: {fileName} - 文件大小超过服务器限制。服务器返回413错误(请求实体过大)。";
                    AppendOutputText(msg);
                    LogMessage($"Upload failed for {fileName}: File size exceeds server limit. Server returned 413 error (Request Entity Too Large).");
                    labelStatus.Text = $"上传失败: {fileName} - 文件过大，服务器拒绝接收";
                    MessageBox.Show(msg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    AppendOutputText($"上传失败: {fileName} - {ex.Message}");
                    LogMessage($"Upload failed for {fileName}: {ex.Message}");
                    MessageBox.Show($"上传失败: {fileName} - {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    labelStatus.Text = "上传失败: " + ex.Message;
                }
                throw; // 重新抛出异常，以便调用者知道上传失败
            }
        }

        // 处理队列中的下一个上传任务
        private async void ProcessNextUpload()
        {
            UploadTask task = null;

            bool shouldRefresh = false; // 标记是否需要刷新
            lock (uploadLock)
            {
                if (!uploadQueue.TryDequeue(out task))
                {
                    // 队列为空，标记上传结束
                    isUploading = false;
                    shouldRefresh = true; // 标记需要在lock外部刷新
                }
                else
                {
                    UpdateQueueStatus(); // 如果成功取出任务，更新队列状态
                }
            }

            // 如果标记了需要刷新（即队列处理完成），则在lock外部执行刷新和UI更新
            if (shouldRefresh)
            {
                // 这些UI更新和日志记录也应该在lock外部执行
                ShowTransferProgress(false);
                labelStatus.Text = "上传队列已完成";
                AppendOutputText("上传队列中的所有任务已完成");
                LogMessage("All tasks in upload queue completed");
                try
                {
                    // 在lock外部执行await
                    await RefreshFileList(currentUrl);
                    LogMessage($"队列完成后刷新目录: {currentUrl}");
                }
                catch (Exception ex)
                {
                    LogMessage($"队列完成后刷新目录失败: {ex.Message}");
                    AppendOutputText("刷新文件列表失败，可能需要手动刷新");
                }
                return; // 结束处理
            }

            // 如果shouldRefresh为false，说明成功取出了任务，继续处理任务
            // （下面的代码块保持不变，处理取出的task）

            try
            {
                if (task.IsFolder)
                {
                    // 处理文件夹上传
                    DirectoryInfo dirInfo = new DirectoryInfo(task.FilePath);
                    string folderName = dirInfo.Name;

                    await CreateFolderIfNotExists(task.FolderTargetUrl);
                    // 文件夹上传时，内部文件上传不刷新，整个文件夹上传完成后也不刷新（由队列结束时统一刷新）
                    await UploadFolderContents(task.FilePath, task.FolderTargetUrl + "/", false);
                }
                else
                {
                    // 处理单个文件上传，但在队列模式下不刷新
                    await UploadFileDirectly(task.FilePath, task.TargetUrl, false);
                }
            }
            catch (Exception ex)
            {
                AppendOutputText($"上传失败: {Path.GetFileName(task.FilePath)} - {ex.Message}");
                LogMessage($"Upload failed for {Path.GetFileName(task.FilePath)}: {ex.Message}");
            }
            finally
            {
                // 无论成功或失败，都处理下一个任务
                ProcessNextUpload();
            }
        }

        // 更新队列状态显示
        private void UpdateQueueStatus()
        {
            if (this.InvokeRequired)
            {
                try
                {
                    this.Invoke(new Action(UpdateQueueStatus));
                }
                catch (ObjectDisposedException)
                {
                    // 窗体已关闭，忽略
                    return;
                }
            }
            else
            {
                if (uploadQueue.Count > 0)
                {
                    labelStatus.Text = $"当前队列中有 {uploadQueue.Count} 个文件等待上传";
                }
                else
                {
                    labelStatus.Text = "准备就绪"; // 或者其他默认状态文字
                }
            }
        }

        // 下载按钮点击事件
        private async void buttonDownload_Click(object sender, EventArgs e)
        {
            await DownloadSelectedItem();
        }

        // 下载菜单项点击事件
        private async void downloadToolStripMenuItem_Click(object sender, EventArgs e)
        {
            await DownloadSelectedItem();
        }

        // 下载选中项
        private async Task DownloadSelectedItem()
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("请选择要下载的文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查是否有多个选中项
            if (listViewFiles.SelectedItems.Count > 1)
            {
                await DownloadMultipleItems();
                return;
            }

            // 单个项目的下载逻辑
            var selectedItem = listViewFiles.SelectedItems[0].Tag as WebDAVItem;
            if (selectedItem == null) return;

            if (selectedItem.Name == "..")
            {
                // 如果是返回上级目录，则不下载
                return;
            }
            else if (selectedItem.IsDirectory)
            {
                // 如果是文件夹，询问是否下载整个文件夹
                if (MessageBox.Show("您选择了文件夹。是否要下载整个文件夹？", "下载文件夹",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    await DownloadFolder(selectedItem);
                }
            }
            else
            {
                // 如果是文件，直接下载
                await DownloadFile(selectedItem);
            }
        }

        // 多文件下载处理
        private async Task DownloadMultipleItems()
        {
            // 计算有效项目数量（排除".."返回上级目录项）
            List<WebDAVItem> itemsToDownload = new List<WebDAVItem>();
            int folderCount = 0;
            int fileCount = 0;
            long totalFileSize = 0; // 添加变量跟踪总文件大小

            foreach (ListViewItem item in listViewFiles.SelectedItems)
            {
                var webDavItem = item.Tag as WebDAVItem;
                if (webDavItem != null && webDavItem.Name != "..")
                {
                    itemsToDownload.Add(webDavItem);
                    if (webDavItem.IsDirectory)
                        folderCount++;
                    else {
                        fileCount++;
                        totalFileSize += webDavItem.Size; // 累加文件大小
                    }
                }
            }

            if (itemsToDownload.Count == 0)
            {
                return;
            }

            // 如果包含文件夹，询问用户是否确定下载
            string confirmMessage;
            string fileSizeInfo = totalFileSize > 0 ? $"，文件总大小：{FormatFileSize(totalFileSize)}" : "";
            
            if (folderCount > 0 && fileCount > 0)
            {
                confirmMessage = $"您选择了 {fileCount} 个文件和 {folderCount} 个文件夹{fileSizeInfo}。是否下载所有选中项？";
            }
            else if (folderCount > 0)
            {
                confirmMessage = $"您选择了 {folderCount} 个文件夹。是否下载所有选中的文件夹？";
            }
            else
            {
                confirmMessage = $"您选择了 {fileCount} 个文件{fileSizeInfo}。是否下载所有选中的文件？";
            }

            if (MessageBox.Show(confirmMessage, "批量下载",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
            {
                return;
            }

            // 选择保存目录
            using (FolderBrowserDialog dlg = new FolderBrowserDialog())
            {
                dlg.Description = "选择保存下载项目的位置";

                if (dlg.ShowDialog() != DialogResult.OK)
                {
                    return;
                }

                string localPath = dlg.SelectedPath;

                try
                {
                    // 直接计算准确的总大小而不使用安全因子
                    long totalSize = 0;
                    int totalFiles = 0;
                    int totalFolders = 0;
                    AppendOutputText("计算多文件下载总大小...");
                    labelStatus.Text = "正在计算文件大小...";
                    Application.DoEvents();
                    
                    // 首先计算文件和文件夹总数
                    foreach (var item in itemsToDownload)
                    {
                        if (item.IsDirectory)
                            totalFolders++;
                        else
                            totalFiles++;
                    }
                    
                    // 然后计算准确的文件大小
                    foreach (var item in itemsToDownload)
                    {
                        if (item.IsDirectory)
                        {
                            // 文件夹需要从PROPFIND获取实际大小
                            AppendOutputText($"获取文件夹 {item.Name} 大小...");
                            labelStatus.Text = $"正在计算文件夹 {item.Name} 大小...";
                            Application.DoEvents();
                            long folderSize = await GetFolderSize(item.Path);
                            totalSize += folderSize;
                            AppendOutputText($"文件夹 {item.Name} 大小: {FormatFileSize(folderSize)}");
                        }
                        else
                        {
                            // 使用准确的文件大小
                            totalSize += item.Size;
                        }
                    }
                    
                    labelStatus.Text = $"计算完成，总大小: {FormatFileSize(totalSize)}";
                    Application.DoEvents();
                    
                    AppendOutputText($"下载总大小: {FormatFileSize(totalSize)} (文件: {totalFiles}, 文件夹: {totalFolders})");

                    // 显示进度条
                    totalBytesToTransfer = totalSize > 0 ? totalSize : 1024 * 1024; // 至少设置1MB
                    bytesTransferred = 0;
                    _lastBytesTransferred = 0; // 重置最后传输字节数
                    _recentTransfers.Clear(); // 清除速度采样点
                    ShowTransferProgress(true);

                    // 下载每个项目
                    for (int i = 0; i < itemsToDownload.Count; i++)
                    {
                        var item = itemsToDownload[i];
                        if (item.IsDirectory)
                        {
                            string subPath = Path.Combine(localPath, item.Name);
                            if (!Directory.Exists(subPath))
                            {
                                Directory.CreateDirectory(subPath);
                            }
                            AppendOutputText($"下载文件夹 {item.Name} ({i+1}/{itemsToDownload.Count})");
                            await DownloadFolderContents(item.Path, subPath);
                        }
                        else
                        {
                            string filePath = Path.Combine(localPath, item.Name);
                            AppendOutputText($"下载文件 {item.Name} ({i+1}/{itemsToDownload.Count})");
                            await DownloadFileToPath(item, filePath);
                        }
                    }
                    
                    // 检查下载完成情况，不强制设置100%
                    if (bytesTransferred < totalBytesToTransfer)
                    {
                        LogMessage($"批量下载可能未完全完成，实际下载: {bytesTransferred}, 预期: {totalBytesToTransfer}");
                        // 计算实际完成百分比
                        double actualProgress = totalBytesToTransfer > 0 ? (double)bytesTransferred / totalBytesToTransfer * 100 : 100;
                        LogMessage($"实际完成进度: {actualProgress:F1}%");
                    }
                    else
                    {
                        LogMessage($"批量下载完成，最终进度: {bytesTransferred}/{totalBytesToTransfer}");
                    }
                    
                    // 最后更新一次进度显示，使用实际数据
                    UpdateTransferProgress(bytesTransferred, totalBytesToTransfer);
                    await Task.Delay(500); // 延迟半秒确保进度显示正确

                    ShowTransferProgress(false);
                    labelStatus.Text = $"批量下载完成，共 {itemsToDownload.Count} 个项目";
                }
                catch (Exception ex)
                {
                    ShowTransferProgress(false);
                    AppendOutputText($"批量下载出错: {ex.Message}");
                    LogMessage($"Batch download error: {ex.Message}");
                    labelStatus.Text = $"批量下载失败: {ex.Message}";
                }
            }
        }

        // 下载文件到指定路径
        private async Task DownloadFileToPath(WebDAVItem item, string localPath)
        {
            try
            {
                labelStatus.Text = $"正在下载: {item.Name}...";
                AppendOutputText($"正在下载文件: {item.Name} 到 {localPath}");
                LogMessage($"Downloading file: {item.Name} to {localPath}");

                // 下载文件
                HttpResponseMessage response = await httpClient.GetAsync(item.Path, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();

                using (var fileStream = new FileStream(localPath, FileMode.Create, FileAccess.Write))
                {
                    using (var downloadStream = await response.Content.ReadAsStreamAsync())
                    {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        long totalBytesRead = 0;

                        // 预取文件实际大小
                        var contentLength = response.Content.Headers.ContentLength ?? item.Size;
                        
                        // 文件大小为0的特殊处理
                        if (contentLength == 0 && item.Size == 0)
                        {
                            LogMessage($"警告: 文件 {item.Name} 大小为0，可能是空文件或服务器未返回大小");
                            // 假设文件至少有1KB，以避免除零错误
                            contentLength = 1024;
                            // 增加总字节数
                            totalBytesToTransfer += contentLength;
                            this.totalBytesToTransfer = totalBytesToTransfer;
                        }
                        // 正常大小差异处理
                        else if (contentLength > 0 && contentLength != item.Size)
                        {
                            // 记录文件实际大小与预计大小的差异
                            LogMessage($"文件 {item.Name} 实际大小 {contentLength} 与预计大小 {item.Size} 不一致");
                            // 调整总大小，避免进度计算偏差
                            if (contentLength > item.Size)
                            {
                                // 增加总字节数以反映更大的文件
                                long difference = contentLength - item.Size;
                                totalBytesToTransfer += difference;
                                // 同步更新全局变量
                                this.totalBytesToTransfer = totalBytesToTransfer;
                                LogMessage($"调整总传输大小为: {totalBytesToTransfer} (增加了 {difference} 字节)");
                            }
                        }
                        
                        while ((bytesRead = await downloadStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                        {
                            await fileStream.WriteAsync(buffer, 0, bytesRead);
                            totalBytesRead += bytesRead;
                            bytesTransferred += bytesRead;
                            UpdateTransferProgress(bytesTransferred, totalBytesToTransfer);
                        }
                        
                        // 确保文件完整性，检查实际下载大小与预期大小是否匹配
                        if (totalBytesRead != contentLength && contentLength > 0)
                        {
                            LogMessage($"警告: 文件 {item.Name} 下载大小 {totalBytesRead} 与内容长度 {contentLength} 不一致");
                        }
                    }
                }

                // 确保文件下载完成后显示正确进度
                if (item.Size > 0) // 使用项目大小而不是内部变量
                {
                    // 直接更新进度，确保文件完成后进度正确
                    UpdateTransferProgress(bytesTransferred, totalBytesToTransfer);
                    LogMessage($"文件 {item.Name} 下载完成，总进度: {bytesTransferred}/{totalBytesToTransfer}");
                }
                
                AppendOutputText($"文件 {item.Name} 下载完成");
                LogMessage($"File {item.Name} downloaded successfully.");
            }
            catch (Exception ex)
            {
                AppendOutputText($"下载失败: {item.Name} - {ex.Message}");
                LogMessage($"Download failed for {item.Name}: {ex.Message}");
            }
        }

        // 下载文件
        private async Task DownloadFile(WebDAVItem item)
        {
            using (SaveFileDialog dlg = new SaveFileDialog())
            {
                dlg.FileName = item.Name;
                dlg.Filter = "所有文件 (*.*)|*.*";

                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        labelStatus.Text = $"正在下载: {item.Name}...";

                        // 显示进度条
                        totalBytesToTransfer = item.Size;
                        bytesTransferred = 0;
                        ShowTransferProgress(true);

                        // 下载文件
                        HttpResponseMessage response = await httpClient.GetAsync(item.Path, HttpCompletionOption.ResponseHeadersRead);
                        response.EnsureSuccessStatusCode();

                        using (var fileStream = new FileStream(dlg.FileName, FileMode.Create, FileAccess.Write))
                        {
                            using (var downloadStream = await response.Content.ReadAsStreamAsync())
                            {
                                byte[] buffer = new byte[8192];
                                int bytesRead;
                                long totalBytesRead = 0;

                                while ((bytesRead = await downloadStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                                {
                                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                                    totalBytesRead += bytesRead;
                                    UpdateTransferProgress(totalBytesRead, totalBytesToTransfer);
                                }
                            }
                        }

                        ShowTransferProgress(false);
                        labelStatus.Text = $"文件 {item.Name} 下载成功";
                    }
                    catch (Exception ex)
                    {
                        ShowTransferProgress(false);
                        MessageBox.Show($"下载失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        labelStatus.Text = "下载失败: " + ex.Message;
                    }
                }
            }
        }

        // 下载文件夹
        private async Task DownloadFolder(WebDAVItem folderItem, string localPath = null)
        {
            // 重置已处理路径集合
            _processedPaths.Clear();

            if (localPath == null)
            {
                using (FolderBrowserDialog dlg = new FolderBrowserDialog())
                {
                    dlg.Description = "选择保存文件夹的位置";
                    if (dlg.ShowDialog() != DialogResult.OK)
                    {
                        return;
                    }
                    localPath = Path.Combine(dlg.SelectedPath, folderItem.Name);
                }
            }

            // 创建目标文件夹
            if (!Directory.Exists(localPath))
            {
                Directory.CreateDirectory(localPath);
            }

            // 获取文件夹总大小
            long folderSize = await GetFolderSize(folderItem.Path);
            totalBytesToTransfer = folderSize;
            bytesTransferred = 0;
            ShowTransferProgress(true);

            try
            {
                // 下载文件夹内容
                await DownloadFolderContents(folderItem.Path, localPath);
                
                // 检查是否真正完成下载，只有在实际完成时才显示100%
                if (bytesTransferred < totalBytesToTransfer)
                {
                    LogMessage($"下载可能未完全完成，实际下载: {bytesTransferred}, 预期: {totalBytesToTransfer}");
                    // 不强制设置为100%，保持实际进度
                    UpdateTransferProgress(bytesTransferred, totalBytesToTransfer);
                }
                
                LogMessage($"文件夹下载完成，最终进度: {bytesTransferred}/{totalBytesToTransfer}");
                await Task.Delay(500); // 延迟半秒确保进度显示正确
                
                ShowTransferProgress(false);
                labelStatus.Text = $"文件夹 {folderItem.Name} 下载完成";
            }
            catch (Exception ex)
            {
                ShowTransferProgress(false);
                AppendOutputText($"文件夹下载出错: {ex.Message}");
                LogMessage($"Folder download error: {ex.Message}");
                labelStatus.Text = $"文件夹下载失败: {ex.Message}";
            }
        }

        // 获取文件夹大小 - 用于进度显示
        private async Task<long> GetFolderSize(string folderUrl)
        {
            long size = 0;
            const long DEFAULT_FOLDER_SIZE = 1024 * 1024; // 1MB
            
            try
            {
                AppendOutputText($"正在计算文件夹总大小...");
                
                // 创建PROPFIND请求
                var request = new HttpRequestMessage(new HttpMethod("PROPFIND"), folderUrl);
                request.Headers.Add("Depth", "infinity"); // 获取所有子项

                // 发送请求
                HttpResponseMessage response = await httpClient.SendAsync(request);
                
                // 检查是否是403错误
                if (response.StatusCode == HttpStatusCode.Forbidden)
                {
                    LogMessage($"服务器禁止访问文件夹大小信息 (403)，将使用智能估算");
                    AppendOutputText($"无法获取精确文件夹大小 (服务器限制)，使用智能估算...");
                    
                    // 使用更大的默认值，因为实际文件夹通常比1MB大
                    size = DEFAULT_FOLDER_SIZE * 50; // 50MB作为更合理的估算
                    LogMessage($"使用智能估算大小: {FormatFileSize(size)}");
                    return size;
                }
                
                response.EnsureSuccessStatusCode();

                // 解析XML响应
                string responseContent = await response.Content.ReadAsStringAsync();
                XDocument doc = XDocument.Parse(responseContent);

                // 定义命名空间
                XNamespace dav = "DAV:";

                // 计算所有文件大小并记录文件数量
                int fileCount = 0;
                int folderCount = 0;
                foreach (var propstat in doc.Descendants(dav + "propstat"))
                {
                    var prop = propstat.Element(dav + "prop");
                    bool isCollection = prop?.Element(dav + "resourcetype")?.Element(dav + "collection") != null;

                    if (!isCollection)
                    {
                        string contentLength = prop?.Element(dav + "getcontentlength")?.Value ?? "0";
                        if (long.TryParse(contentLength, out long fileSize))
                        {
                            size += fileSize;
                            fileCount++;
                        }
                        else
                        {
                            LogMessage($"无法解析文件大小: {contentLength}");
                        }
                    }
                    else
                    {
                        folderCount++;
                    }
                }
                
                // 记录详细的计算结果
                AppendOutputText($"文件夹大小计算完成: {FormatFileSize(size)}，包含 {fileCount} 个文件，{folderCount} 个子文件夹");
                LogMessage($"Folder size calculation completed: {size} bytes ({FormatFileSize(size)}), contains {fileCount} files, {folderCount} folders");
                
                // 如果没有找到任何文件，给出警告并使用更大的估算值
                if (fileCount == 0 && folderCount <= 1)
                {
                    LogMessage($"警告: 文件夹似乎为空或无法获取文件信息");
                    AppendOutputText($"警告: 文件夹似乎为空，将使用默认大小进行进度估算");
                    size = DEFAULT_FOLDER_SIZE * 20; // 20MB作为空文件夹的估算
                }
            }
            catch(Exception ex)
            {
                // 如果无法获取文件夹大小，则返回一个更大的默认值
                AppendOutputText($"无法计算文件夹大小: {ex.Message}，使用智能估算值");
                LogMessage($"Failed to get folder size: {ex.Message}");
                
                // 根据异常类型使用不同的估算策略
                if (ex.Message.Contains("403") || ex.Message.Contains("Forbidden"))
                {
                    size = DEFAULT_FOLDER_SIZE * 50; // 50MB for 403 errors
                    LogMessage($"403错误，使用50MB估算值");
                }
                else
                {
                    size = DEFAULT_FOLDER_SIZE * 30; // 30MB for other errors
                    LogMessage($"其他错误，使用30MB估算值");
                }
            }

            // 确保至少返回合理的最小大小
            return size > 0 ? size : DEFAULT_FOLDER_SIZE * 20;
        }

        // 下载文件夹内容
        // 存储已处理的路径
        private HashSet<string> _processedPaths = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        private async Task DownloadFolderContents(string folderUrl, string localPath)
        {
            // 标准化并验证路径
            string normalizedUrl = NormalizeUrl(folderUrl);
            if (!IsValidPath(normalizedUrl))
            {
                LogMessage($"无效的文件夹路径: {normalizedUrl}");
                return;
            }

            // 检查是否已处理过该路径
            if (_processedPaths.Contains(normalizedUrl))
            {
                LogMessage($"跳过已处理的文件夹: {normalizedUrl}");
                return;
            }

            // 添加到已处理路径集合
            _processedPaths.Add(normalizedUrl);

            LogMessage($"开始下载文件夹内容: {normalizedUrl} 到 {localPath}");
            try
            {
                // 创建PROPFIND请求
                var request = new HttpRequestMessage(new HttpMethod("PROPFIND"), folderUrl);
                request.Headers.Add("Depth", "1"); // 只获取当前目录和直接子项
                LogMessage($"Sending PROPFIND request to {folderUrl} with Depth: 1 for contents.");

                // 发送请求
                HttpResponseMessage response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                LogMessage($"Received successful response for PROPFIND {folderUrl} contents.");

                // 解析XML响应
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                string responseContent = Encoding.UTF8.GetString(responseBytes);
                XDocument doc = XDocument.Parse(responseContent);
                LogMessage("Parsed XML response for folder contents.");

                // 定义命名空间
                XNamespace dav = "DAV:";

                // 解析每个资源
                var responses = doc.Descendants(dav + "response").ToList();
                LogMessage($"Found {responses.Count} items in folder contents.");

                foreach (var res in responses)
                {
                    string href = res.Element(dav + "href")?.Value;
                    if (string.IsNullOrEmpty(href)) continue;

                    // 记录原始href以便调试
                    LogMessage($"处理路径: {href}");

                    // 确保href只解码一次，避免重复解码
                    string decodedHref = Uri.UnescapeDataString(href);
                    LogMessage($"解码后路径: {decodedHref}");

                    // 检查是否为当前目录或重复目录
                    if (IsCurrentDirectory(folderUrl, decodedHref) ||
                        _processedPaths.Contains(decodedHref.TrimEnd('/')))
                    {
                        LogMessage($"跳过当前目录或已处理目录: {decodedHref}");
                        continue;
                    }

                    // 获取属性
                    var propstat = res.Element(dav + "propstat");
                    var prop = propstat?.Element(dav + "prop");
                    bool isCollection = prop?.Element(dav + "resourcetype")?.Element(dav + "collection") != null;

                    // 从解码的href解析名称
                    string name = Path.GetFileName(decodedHref.TrimEnd('/'));
                    if (string.IsNullOrEmpty(name)) name = decodedHref.TrimEnd('/');

                    if (isCollection)
                    {
                        // 创建子文件夹使用解码后的名称
                        string validSubfolderName = MakeValidFileName(name);
                        string subfolderPath = Path.Combine(localPath, validSubfolderName);
                        AppendOutputText($"创建本地子文件夹: {subfolderPath}");
                        LogMessage($"Creating local subfolder: {subfolderPath}");

                        if (!Directory.Exists(subfolderPath))
                        {
                            Directory.CreateDirectory(subfolderPath);
                        }

                        // 构建子文件夹完整URI - 改进处理中文和特殊字符的能力
                        string targetUrl;

                        try
                        {
                            // 记录调试信息
                            LogMessage($"构建子文件夹URI，基础路径: {folderUrl}，文件夹名: {name}");

                            // 方法1: 如果href已经是完整的绝对路径，直接使用
                            if (href.StartsWith("http"))
                            {
                                targetUrl = href;
                                if (!targetUrl.EndsWith("/")) targetUrl += "/";
                                LogMessage($"使用绝对路径href: {targetUrl}");
                            }
                            else
                            {
                                // 方法2: 构建相对路径
                                // 确保folderUrl是完整的URL
                                string baseUrl = folderUrl;
                                if (!baseUrl.StartsWith("http"))
                                {
                                    // 如果folderUrl不是完整URL，需要与currentUrl组合
                                    baseUrl = new Uri(new Uri(currentUrl), folderUrl).ToString();
                                }
                                
                                // 确保baseUrl以斜杠结尾
                                if (!baseUrl.EndsWith("/")) baseUrl += "/";
                                
                                // 直接拼接文件夹名，不需要额外编码
                                targetUrl = baseUrl + name;
                                if (!targetUrl.EndsWith("/")) targetUrl += "/";
                                
                                LogMessage($"使用基础URL拼接: {baseUrl} + {name} = {targetUrl}");
                            }

                            // 验证构建的URL是否有效
                            bool isValidUrl = Uri.IsWellFormedUriString(targetUrl, UriKind.Absolute);
                            if (!isValidUrl)
                            {
                                LogMessage($"警告：构建的URL可能无效: {targetUrl}");
                                // 尝试使用Uri类进行修复
                                try
                                {
                                    Uri baseUri = new Uri(folderUrl.StartsWith("http") ? folderUrl : new Uri(new Uri(currentUrl), folderUrl).ToString());
                                    Uri targetUri = new Uri(baseUri, name + "/");
                                    targetUrl = targetUri.ToString();
                                    LogMessage($"使用Uri类修复后的URL: {targetUrl}");
                                }
                                catch (Exception uriEx)
                                {
                                    LogMessage($"Uri类修复失败: {uriEx.Message}");
                                    // 最后的回退方案
                                    targetUrl = currentUrl.TrimEnd('/') + "/" + name + "/";
                                    LogMessage($"使用最终回退URL: {targetUrl}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 如果所有方法都失败，使用最基本的拼接
                            LogMessage($"URI构建出错: {ex.Message}，使用基本拼接");
                            targetUrl = currentUrl.TrimEnd('/') + "/" + name + "/";
                            LogMessage($"回退到基本URL拼接: {targetUrl}");
                        }

                        // 最终日志记录
                        AppendOutputText($"构建子文件夹URI: {targetUrl}");
                        LogMessage($"最终子文件夹URI: {targetUrl}");

                        AppendOutputText($"递归下载子文件夹: {targetUrl} 到 {subfolderPath}");
                        LogMessage($"开始递归下载子文件夹: {targetUrl} 到 {subfolderPath}");

                        try
                        {
                            // 递归下载子文件夹内容
                            await DownloadFolderContents(targetUrl, subfolderPath);
                        }
                        catch (Exception ex)
                        {
                            AppendOutputText($"处理子文件夹失败: {name} - {ex.Message}");
                            LogMessage($"Failed to process subfolder {name}: {ex.Message}");
                            LogMessage($"Exception stack trace: {ex.StackTrace}");
                            // 继续处理其他项目而不是直接失败
                            continue;
                        }
                    }
                    else
                    {
                        // 下载文件
                        string validFileName = MakeValidFileName(name);
                        string filePath = Path.Combine(localPath, validFileName);
                        string contentLength = prop?.Element(dav + "getcontentlength")?.Value ?? "0";
                        long fileSize = long.TryParse(contentLength, out long size) ? size : 0;

                        labelStatus.Text = $"正在下载: {name}...";
                        AppendOutputText($"正在下载文件: {name} 到 {filePath}");
                        LogMessage($"Downloading file: {name} to {filePath}");

                        // 下载文件使用原始href
                        HttpResponseMessage fileResponse = await httpClient.GetAsync(href, HttpCompletionOption.ResponseHeadersRead);
                        fileResponse.EnsureSuccessStatusCode();
                        LogMessage($"GET request successful for file {name} in folder.");

                        using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                        {
                            using (var downloadStream = await fileResponse.Content.ReadAsStreamAsync())
                            {
                                byte[] buffer = new byte[8192];
                                int bytesRead;
                                long totalBytesRead = 0;
                                
                                // 获取实际文件大小
                                var actualContentLength = fileResponse.Content.Headers.ContentLength ?? fileSize;
                                
                                // 如果实际大小与预期不同，调整总大小
                                if (actualContentLength != fileSize && actualContentLength > 0)
                                {
                                    long sizeDifference = actualContentLength - fileSize;
                                    totalBytesToTransfer += sizeDifference;
                                    LogMessage($"文件 {name} 实际大小 {actualContentLength} 与预期 {fileSize} 不同，调整总大小差异: {sizeDifference}");
                                }

                                while ((bytesRead = await downloadStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                                {
                                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                                    totalBytesRead += bytesRead;
                                    bytesTransferred += bytesRead;
                                    UpdateTransferProgress(bytesTransferred, totalBytesToTransfer);
                                }
                                
                                // 验证下载完整性
                                if (actualContentLength > 0 && totalBytesRead != actualContentLength)
                                {
                                    LogMessage($"警告: 文件 {name} 下载大小 {totalBytesRead} 与实际内容长度 {actualContentLength} 不匹配");
                                }
                            }
                        }
                        AppendOutputText($"文件 {name} 下载完成");
                        LogMessage($"File {name} downloaded successfully.");
                    }
                }
                AppendOutputText($"文件夹内容下载完成: {folderUrl}");
                LogMessage($"Finished downloading contents for folder: {folderUrl}");
            }
            catch (Exception ex)
            {
                AppendOutputText($"下载文件夹内容失败: {folderUrl} - {ex.Message}");
                LogMessage($"Failed to download folder contents for {folderUrl}: {ex.Message}");
                LogMessage($"Exception stack trace: {ex.StackTrace}");
                MessageBox.Show($"下载文件夹内容失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 标准化URL的辅助方法
        private string NormalizeUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            try
            {
                // 确保url是绝对路径
                if (!url.StartsWith("http", StringComparison.OrdinalIgnoreCase))
                {
                    url = new Uri(new Uri(currentUrl), url).AbsoluteUri;
                }

                // 解码URL
                url = Uri.UnescapeDataString(url);

                // 移除末尾的斜杠
                return url.TrimEnd('/');
            }
            catch (Exception ex)
            {
                LogMessage($"NormalizeUrl error: {ex.Message} for url={url}");
                return url.TrimEnd('/');
            }
        }

        // 路径验证方法
        private bool IsValidPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            try
            {
                // 检查是否为有效的URI
                if (!Uri.TryCreate(path, UriKind.Absolute, out Uri uri))
                    return false;

                // 检查是否包含无效字符
                if (path.IndexOfAny(Path.GetInvalidPathChars()) >= 0)
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        // 辅助方法：判断是否为当前目录或父子目录关系
        private bool IsCurrentDirectory(string folderUrl, string href)
        {
            try
            {
                // 标准化两个URL
                string normalizedBase = NormalizeUrl(folderUrl);
                string normalizedHref = NormalizeUrl(href);

                LogMessage($"Comparing URLs - Base: {normalizedBase}, Href: {normalizedHref}");

                // 检查是否完全匹配
                if (normalizedBase.Equals(normalizedHref, StringComparison.OrdinalIgnoreCase))
                {
                    LogMessage("Found exact URL match");
                    return true;
                }

                // 检查是否为父子目录关系
                if (normalizedHref.StartsWith(normalizedBase, StringComparison.OrdinalIgnoreCase))
                {
                    string relativePath = normalizedHref.Substring(normalizedBase.Length).Trim('/');
                    string[] parts = relativePath.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);

                    // 如果存在子目录且与父目录同名，则可能是重复目录
                    if (parts.Length > 0)
                    {
                        string parentDirName = Path.GetFileName(normalizedBase);
                        if (parts[0].Equals(parentDirName, StringComparison.OrdinalIgnoreCase))
                        {
                            LogMessage($"Found duplicate directory name: {parts[0]}");
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogMessage($"IsCurrentDirectory error: {ex.Message} for folderUrl={folderUrl}, href={href}");
                return false;
            }
        }

        // 删除按钮点击事件
        private async void buttonDelete_Click(object sender, EventArgs e)
        {
            await DeleteSelectedItem();
        }

        // 删除菜单项点击事件
        private async void deleteToolStripMenuItem_Click(object sender, EventArgs e)
        {
            await DeleteSelectedItem();
        }

        // 删除选中项
        private async Task DeleteSelectedItem()
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("请选择要删除的项目", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 计算选中的有效项目数量（排除".."返回上级目录项）
            int validItemCount = 0;
            foreach (ListViewItem item in listViewFiles.SelectedItems)
            {
                var webDavItem = item.Tag as WebDAVItem;
                if (webDavItem != null && webDavItem.Name != "..")
                {
                    validItemCount++;
                }
            }

            if (validItemCount == 0)
            {
                return; // 没有有效项目可删除
            }

            // 根据选中数量显示不同的确认消息
            string confirmMessage = validItemCount == 1
                ? $"确定要删除选中的项目吗？"
                : $"确定要删除选中的 {validItemCount} 个项目吗？";

            if (MessageBox.Show(confirmMessage, "确认删除",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // 显示进度条
                if (validItemCount > 1)
                {
                    progressBarTransfer.Value = 0;
                    progressBarTransfer.Maximum = validItemCount;
                    progressBarTransfer.Visible = true;
                    labelSpeed.Visible = false;
                    labelTime.Text = "正在删除...";
                    labelTime.Visible = true;
                }

                int successCount = 0;
                int failCount = 0;

                // 处理每个选中的项目
                foreach (ListViewItem item in listViewFiles.SelectedItems)
                {
                    var webDavItem = item.Tag as WebDAVItem;
                    if (webDavItem == null || webDavItem.Name == "..") continue;

                    try
                    {
                        await DeleteItem(webDavItem);
                        successCount++;

                        // 更新进度条
                        if (validItemCount > 1)
                        {
                            progressBarTransfer.Value = successCount + failCount;
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        AppendOutputText($"删除失败: {webDavItem.Name} - {ex.Message}");
                        LogMessage($"Delete failed for {webDavItem.Name}: {ex.Message}");

                        // 更新进度条
                        if (validItemCount > 1)
                        {
                            progressBarTransfer.Value = successCount + failCount;
                        }
                    }
                }

                // 隐藏进度条
                if (validItemCount > 1)
                {
                    progressBarTransfer.Visible = false;
                    labelTime.Visible = false;
                }

                // 显示结果消息
                if (failCount == 0)
                {
                    labelStatus.Text = validItemCount == 1
                        ? "删除成功"
                        : $"成功删除 {successCount} 个项目";
                }
                else
                {
                    labelStatus.Text = $"已删除 {successCount} 个项目，{failCount} 个项目删除失败";
                }

                // 刷新文件列表
                await RefreshFileList(currentUrl);
            }
        }

        // 删除项目
        private async Task DeleteItem(WebDAVItem item)
        {
            string itemType = item.IsDirectory ? "文件夹" : "文件";
            AppendOutputText($"正在删除 {itemType}: {item.Name} 从 {item.Path}");
            LogMessage($"Deleting {itemType}: {item.Name} from {item.Path}");

            labelStatus.Text = $"正在删除{itemType}: {item.Name}...";
            Application.DoEvents();

            // 发送DELETE请求
            HttpResponseMessage response = await httpClient.DeleteAsync(item.Path);
            response.EnsureSuccessStatusCode();
            LogMessage($"DELETE request successful for {item.Name}.");

            AppendOutputText($"{itemType} {item.Name} 删除成功");
            LogMessage($"{itemType} {item.Name} deleted successfully.");
        }

        // 新建文件夹按钮点击事件
        private async void buttonNewFolder_Click(object sender, EventArgs e)
        {
            await CreateNewFolder();
        }

        // 新建文件夹菜单项点击事件
        private async void newFolderToolStripMenuItem_Click(object sender, EventArgs e)
        {
            await CreateNewFolder();
        }

        // 创建新文件夹
        private async Task CreateNewFolder()
        {
            if (string.IsNullOrEmpty(currentUrl))
            {
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string folderName = Interaction.InputBox("请输入文件夹名称:", "创建文件夹", "");
            if (string.IsNullOrEmpty(folderName)) return;

            try
            {
                labelStatus.Text = $"正在创建文件夹: {folderName}...";
                Application.DoEvents();

                string newFolderUrl = currentUrl + folderName;

                // 创建MKCOL请求
                var request = new HttpRequestMessage(new HttpMethod("MKCOL"), newFolderUrl);
                HttpResponseMessage response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                labelStatus.Text = $"文件夹 {folderName} 创建成功";
                await RefreshFileList(currentUrl);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建文件夹失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                labelStatus.Text = "创建文件夹失败: " + ex.Message;
            }
        }

        // 刷新按钮点击事件
        private async void buttonRefresh_Click(object sender, EventArgs e)
        {
            await RefreshCurrentFolder();
        }

        // 刷新菜单项点击事件
        private async void refreshToolStripMenuItem_Click(object sender, EventArgs e)
        {
            await RefreshCurrentFolder();
        }

        // 刷新当前文件夹
        private async Task RefreshCurrentFolder()
        {
            if (string.IsNullOrEmpty(currentUrl))
            {
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            labelStatus.Text = "正在刷新...";
            Application.DoEvents();
            await RefreshFileList(currentUrl);
            labelStatus.Text = "已刷新";
        }

        // 文件列表双击事件
        private async void listViewFiles_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listViewFiles.SelectedItems.Count > 0)
            {
                var selectedItem = listViewFiles.SelectedItems[0].Tag as WebDAVItem;
                if (selectedItem != null && selectedItem.IsDirectory)
                {
                    // 如果是目录，进入该目录
                    labelStatus.Text = $"正在打开文件夹: {selectedItem.Name}...";
                    AppendOutputText($"双击打开文件夹: {selectedItem.Name}");
                    LogMessage($"Double-clicked to open folder: {selectedItem.Name}");
                    Application.DoEvents();

                    // 确保 currentUrl 是一个有效的绝对 URI
                    Uri baseUri = new Uri(currentUrl);
                    // 使用 Uri.TryCreate 结合 baseUri 和 selectedItem.Path 来创建新的 URI
                    if (Uri.TryCreate(baseUri, selectedItem.Path, out Uri newUri))
                    {
                        currentUrl = newUri.AbsoluteUri;
                        // 确保新的 URL 以斜杠结尾，这对于 WebDAV 目录是必需的
                        if (!currentUrl.EndsWith("/"))
                        {
                            currentUrl += "/";
                        }
                        await RefreshFileList(currentUrl);

                        labelPath.Text = "路径: " + currentUrl;
                        labelStatus.Text = "已打开文件夹: " + selectedItem.Name;
                        AppendOutputText($"已进入文件夹: {selectedItem.Name}");
                        LogMessage($"Entered folder: {selectedItem.Name}");
                    }
                    else
                    {
                        // 处理 URI 创建失败的情况，例如记录日志或显示错误消息
                        AppendOutputText($"错误: 无法创建有效的 URI 从 {currentUrl} 和 {selectedItem.Path}");
                        LogMessage($"Error: Could not create valid URI from {currentUrl} and {selectedItem.Path}");
                        MessageBox.Show($"无法打开文件夹: 无效的路径 {selectedItem.Path}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        labelStatus.Text = "打开文件夹失败: 无效路径";
                    }
                    LogMessage($"Entered folder: {selectedItem.Name}");
                }
                else if (selectedItem != null && selectedItem.Name != "..")
                {
                    // 检查是否是支持的文本文件类型
                    string fileExtension = System.IO.Path.GetExtension(selectedItem.Name).ToLower();
                    if (_textFileExtensions.Contains(fileExtension))
                    {
                        // 如果是文本文件，打开文本编辑器
                        AppendOutputText($"双击编辑文本文件: {selectedItem.Name}");
                        LogMessage($"Double-clicked to edit text file: {selectedItem.Name}");

                        // 创建并显示文本编辑窗口
                        TextEditorForm editorForm = new TextEditorForm(selectedItem.Path, credential, httpClient);
                        editorForm.FileSaved += EditorForm_FileSaved; // 订阅保存事件
                        editorForm.ShowDialog(this); // 以模态方式显示
                    }
                    else
                    {
                        // 如果是其他文件，下载该文件
                        AppendOutputText($"双击下载文件: {selectedItem.Name}");
                        LogMessage($"Double-clicked to download file: {selectedItem.Name}");
                        await DownloadFile(selectedItem);
                    }
                }
            }
        }

        // TextEditorForm保存文件后触发的事件处理方法
        private async void EditorForm_FileSaved(object sender, EventArgs e)
        {
            // 文件保存成功后刷新当前文件列表
            AppendOutputText("文本文件已保存，正在刷新文件列表...");
            LogMessage("Text file saved, refreshing file list...");
            await RefreshFileList(currentUrl);
            labelStatus.Text = "文件列表已刷新";
        }

        // 处理ListView的KeyDown事件，实现Delete键删除功能和Backspace键返回上级目录功能
        private async void listViewFiles_KeyDown(object sender, KeyEventArgs e)
        {
            // 检查是否按下Delete键
            if (e.KeyCode == Keys.Delete)
            {
                // 调用已有的删除方法
                await DeleteSelectedItem();

                // 标记事件已处理
                e.Handled = true;
            }
            // 增加对Backspace键的处理，实现返回上级目录功能
            else if (e.KeyCode == Keys.Back)
            {
                await NavigateToParentDirectory();

                // 标记事件已处理
                e.Handled = true;
            }
        }

        // 返回上级目录方法
        private async Task NavigateToParentDirectory()
        {
            // 检查是否已连接到服务器
            if (string.IsNullOrEmpty(currentUrl))
            {
                return;
            }

            // 检查是否已在根目录
            if (currentUrl == textBoxServerAddress.Text.TrimEnd('/') + '/' ||
                currentUrl.Equals(textBoxServerAddress.Text, StringComparison.OrdinalIgnoreCase))
            {
                // 已在根目录，无法返回上级
                return;
            }

            try
            {
                labelStatus.Text = "正在返回上级目录...";
                Application.DoEvents();

                // 构建上级目录URL
                Uri uri = new Uri(currentUrl);
                string parentUrl = uri.AbsoluteUri.Substring(0, uri.AbsoluteUri.TrimEnd('/').LastIndexOf('/') + 1);

                // 更新当前URL并刷新
                currentUrl = parentUrl;
                await RefreshFileList(currentUrl);

                labelPath.Text = "路径: " + currentUrl;
                labelStatus.Text = "已返回上级目录";
                AppendOutputText("已返回上级目录: " + currentUrl);
                LogMessage("Navigated to parent directory: " + currentUrl);
            }
            catch (Exception ex)
            {
                AppendOutputText($"返回上级目录失败: {ex.Message}");
                LogMessage($"Failed to navigate to parent directory: {ex.Message}");
                labelStatus.Text = "返回上级目录失败: " + ex.Message;
            }
        }

        // Helper method to make file/folder names valid for the local file system
        private string MakeValidFileName(string name)
        {
            string invalidChars = System.Text.RegularExpressions.Regex.Escape(new string(System.IO.Path.GetInvalidFileNameChars()) + new string(System.IO.Path.GetInvalidPathChars()));
            string invalidRegStr = string.Format(@"[{0}]", invalidChars);
            return System.Text.RegularExpressions.Regex.Replace(name, invalidRegStr, "_");
        }

        /// <summary>
        /// 当历史连接下拉框选择改变时触发
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param e="事件参数"></param>
        private void comboBoxHistory_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (comboBoxHistory.SelectedIndex != -1 && comboBoxHistory.SelectedItem != null)
            {
                int selectedIndex = comboBoxHistory.SelectedIndex;

                // 确保索引在有效范围内且connectionHistory已加载
                if (selectedIndex >= 0 && selectedIndex < connectionHistory.Count)
                {
                    // 直接从connectionHistory列表中获取选中的连接项
                    var selectedConnection = connectionHistory[selectedIndex];

                    // 填充表单
                    textBoxServerAddress.Text = selectedConnection.ServerAddress;
                    textBoxUsername.Text = selectedConnection.Username;
                    textBoxPassword.Text = selectedConnection.Password;

                    // 输出日志
                    AppendToLog($"已加载连接设置: {selectedConnection.ServerAddress}");
                }
                else
                {
                    // 如果索引不匹配（可能是UI和数据不同步），回退到使用文本解析
                    string selectedItem = comboBoxHistory.SelectedItem.ToString();
                    LoadConnectionSettings(selectedItem);
                }
            }
        }

        /// <summary>
        /// 根据所选历史连接加载连接设置
        /// </summary>
        /// <param name="connectionName">连接名称</param>
        private void LoadConnectionSettings(string connectionName)
        {
            try
            {
                // 这里应该实现从配置文件或数据库中读取连接详情的逻辑
                // 例如从应用程序的设置、注册表或XML文件中获取

                // 示例实现，假设使用格式 "serverUrl - username"
                string[] parts = connectionName.Split(new string[] { " - " }, StringSplitOptions.None);
                if (parts.Length >= 2)
                {
                    string serverUrl = parts[0];
                    string username = parts[1];

                    // 从存储中获取对应的密码
                    string password = GetSavedPassword(serverUrl, username);

                    // 填充表单
                    textBoxServerAddress.Text = serverUrl;
                    textBoxUsername.Text = username;
                    textBoxPassword.Text = password;

                    // 输出日志
                    AppendToLog($"已加载连接设置: {serverUrl}");
                }
            }
            catch (Exception ex)
            {
                AppendToLog($"加载连接设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取保存的密码
        /// </summary>
        /// <param name="serverUrl">服务器URL</param>
        /// <param name="username">用户名</param>
        /// <returns>保存的密码，如果未找到则返回空字符串</returns>
        private string GetSavedPassword(string serverUrl, string username)
        {
            try
            {
                // 首先从connectionHistory列表中查找匹配的连接项并返回其密码
                var connectionItem = connectionHistory.FirstOrDefault(
                    item => item.ServerAddress == serverUrl && item.Username == username);

                // 如果找到匹配项，返回其密码
                if (connectionItem != null)
                {
                    return connectionItem.Password;
                }

                // 如果在列表中没有找到，尝试从设置中获取（兼容旧版本）
                return Properties.Settings.Default[serverUrl + "_" + username] as string ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 保存连接到历史记录
        /// </summary>
        private void SaveToConnectionHistory(string serverAddress, string username, string password)
        {
            try
            {
                // 检查是否已存在相同地址和用户名的记录
                var existingItem = connectionHistory.FirstOrDefault(
                    item => item.ServerAddress == serverAddress && item.Username == username);

                if (existingItem != null)
                {
                    // 更新现有记录
                    existingItem.Password = password;
                    existingItem.LastConnected = DateTime.Now;

                    // 更新UI（如果需要）
                    int index = connectionHistory.IndexOf(existingItem);
                    if (index >= 0 && index < comboBoxHistory.Items.Count)
                    {
                        comboBoxHistory.Items[index] = existingItem.ToString();
                    }
                }
                else
                {
                    // 添加新记录
                    var newItem = new ConnectionHistoryItem(serverAddress, username, password);
                    connectionHistory.Add(newItem);
                    comboBoxHistory.Items.Add(newItem.ToString());
                }

                // 按最近连接时间排序
                connectionHistory = connectionHistory
                    .OrderByDescending(item => item.LastConnected)
                    .ToList();

                // 更新下拉框顺序
                comboBoxHistory.Items.Clear();
                foreach (var item in connectionHistory)
                {
                    comboBoxHistory.Items.Add(item.ToString());
                }

                // 序列化并保存
                var settings = Properties.Settings.Default;
                settings.ConnectionHistory = Newtonsoft.Json.JsonConvert.SerializeObject(connectionHistory);
                settings.Save();

                AppendOutputText("连接信息已保存到历史记录");
                LogMessage("Connection info saved to history");
            }
            catch (Exception ex)
            {
                AppendOutputText($"保存到历史记录失败: {ex.Message}");
                LogMessage($"Failed to save to connection history: {ex.Message}");
            }
        }

        /// <summary>
        /// 从历史记录中删除选中项
        /// </summary>
        private void DeleteFromConnectionHistory()
        {
            if (comboBoxHistory.SelectedIndex == -1)
            {
                MessageBox.Show("请先选择要删除的历史记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            string selectedItem = comboBoxHistory.SelectedItem.ToString();

            if (MessageBox.Show($"确定要删除选中的历史记录: {selectedItem}?", "确认删除",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    int index = comboBoxHistory.SelectedIndex;

                    // 从列表和UI中移除
                    if (index >= 0 && index < connectionHistory.Count)
                    {
                        connectionHistory.RemoveAt(index);
                        comboBoxHistory.Items.RemoveAt(index);

                        // 保存更改
                        var settings = Properties.Settings.Default;
                        settings.ConnectionHistory = Newtonsoft.Json.JsonConvert.SerializeObject(connectionHistory);
                        settings.Save();

                        AppendOutputText($"历史记录已删除: {selectedItem}");
                        LogMessage($"History item deleted: {selectedItem}");
                    }
                }
                catch (Exception ex)
                {
                    AppendOutputText($"删除历史记录失败: {ex.Message}");
                    LogMessage($"Failed to delete history item: {ex.Message}");
                    MessageBox.Show($"删除历史记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 删除历史记录按钮点击事件
        /// </summary>
        private void buttonDeleteHistory_Click(object sender, EventArgs e)
        {
            DeleteFromConnectionHistory();
        }

        /// <summary>
        /// 向日志文本框追加消息
        /// </summary>
        /// <param name="message">要追加的消息</param>
        private void AppendToLog(string message)
        {
            if (textBoxOutput.InvokeRequired)
            {
                textBoxOutput.Invoke(new Action<string>(AppendToLog), message);
            }
            else
            {
                textBoxOutput.AppendText(DateTime.Now.ToString("[yyyy-MM-dd HH:mm:ss] ") + message + Environment.NewLine);
                textBoxOutput.ScrollToCaret();
            }
        }

        // 上传文件夹内容 - 增加刷新控制参数
        private async Task UploadFolderContents(string localFolderPath, string remoteFolderUrl, bool refreshAfterComplete = true)
        {
            try
            {
                // 获取目录信息
                DirectoryInfo directory = new DirectoryInfo(localFolderPath);

                // 上传当前文件夹中的所有文件，但不刷新
                FileInfo[] files = directory.GetFiles();
                foreach (FileInfo file in files)
                {
                    string targetUrl = remoteFolderUrl + file.Name;
                    // 传递false，避免在上传每个文件后刷新
                    await UploadFileDirectly(file.FullName, targetUrl, false);
                }

                // 递归处理所有子文件夹
                DirectoryInfo[] subDirs = directory.GetDirectories();
                foreach (DirectoryInfo dir in subDirs)
                {
                    string subFolderUrl = remoteFolderUrl + dir.Name;
                    await CreateFolderIfNotExists(subFolderUrl);
                    // 递归调用时也传递false，避免在子文件夹上传完成后刷新
                    await UploadFolderContents(dir.FullName, subFolderUrl + "/", false);
                }

                // 仅在整个顶层文件夹内容上传完成后，根据参数决定是否刷新
                if (refreshAfterComplete)
                {
                    try
                    {
                        await RefreshFileList(currentUrl);
                        LogMessage($"文件夹上传完成后刷新目录: {currentUrl}");
                    }
                    catch (Exception refreshEx)
                    {
                        LogMessage($"文件夹上传完成后刷新目录失败: {refreshEx.Message}");
                        AppendOutputText("刷新文件列表失败，可能需要手动刷新");
                    }
                }
            }
            catch (Exception ex)
            {
                AppendOutputText($"上传文件夹内容失败: {localFolderPath} - {ex.Message}");
                LogMessage($"Failed to upload folder contents: {localFolderPath} - {ex.Message}");
            }
        }

        // 检查文件夹是否存在，不存在则创建 - 添加缺失的方法实现
        private async Task CreateFolderIfNotExists(string folderUrl)
        {
            try
            {
                // 检查文件夹是否存在
                var request = new HttpRequestMessage(new HttpMethod("PROPFIND"), folderUrl);
                request.Headers.Add("Depth", "0");

                HttpResponseMessage response = await httpClient.SendAsync(request);

                // 如果返回状态码不是2xx，说明文件夹不存在，需要创建
                if (!response.IsSuccessStatusCode)
                {
                    // 创建文件夹
                    var createRequest = new HttpRequestMessage(new HttpMethod("MKCOL"), folderUrl);
                    HttpResponseMessage createResponse = await httpClient.SendAsync(createRequest);
                    createResponse.EnsureSuccessStatusCode();
                    AppendOutputText($"创建远程文件夹: {folderUrl}");
                    LogMessage($"Created remote folder: {folderUrl}");
                }
            }
            catch (Exception ex)
            {
                // 如果捕获到异常，尝试创建文件夹
                try
                {
                    var createRequest = new HttpRequestMessage(new HttpMethod("MKCOL"), folderUrl);
                    HttpResponseMessage createResponse = await httpClient.SendAsync(createRequest);
                    createResponse.EnsureSuccessStatusCode();
                    AppendOutputText($"创建远程文件夹: {folderUrl}");
                    LogMessage($"Created remote folder: {folderUrl}");
                }
                catch (Exception innerEx)
                {
                    AppendOutputText($"创建文件夹失败: {folderUrl} - {innerEx.Message}");
                    LogMessage($"Failed to create folder: {folderUrl} - {innerEx.Message}");
                    throw; // 重新抛出异常，让调用者处理
                }
            }
        }
    }
}

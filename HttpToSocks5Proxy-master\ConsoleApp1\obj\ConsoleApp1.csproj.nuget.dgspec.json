{"format": 1, "restore": {"D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\ConsoleApp1.csproj": {}}, "projects": {"D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\ConsoleApp1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\ConsoleApp1.csproj", "projectName": "ConsoleApp1", "projectPath": "D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\ConsoleApp1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"projectPath": "D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.402\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"version": "1.4.0", "restore": {"projectUniqueName": "D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj", "projectName": "HttpToSocks5Proxy", "projectPath": "D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net45": {"targetAlias": "net45", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.402\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.402\\RuntimeIdentifierGraph.json"}}}}}
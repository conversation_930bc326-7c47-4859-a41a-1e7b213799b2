{"version": 3, "targets": {".NETCoreApp,Version=v2.2": {"Microsoft.NETCore.App/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.0", "Microsoft.NETCore.Platforms": "2.2.0", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3"}, "compile": {"ref/netcoreapp2.2/Microsoft.CSharp.dll": {"related": ".xml"}, "ref/netcoreapp2.2/Microsoft.VisualBasic.dll": {"related": ".xml"}, "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.AppContext.dll": {}, "ref/netcoreapp2.2/System.Buffers.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Concurrent.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Immutable.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.NonGeneric.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.Specialized.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Collections.dll": {"related": ".Concurrent.xml;.Immutable.xml;.NonGeneric.xml;.Specialized.xml;.xml"}, "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.ComponentModel.dll": {"related": ".Annotations.xml;.EventBasedAsync.xml;.Primitives.xml;.TypeConverter.xml;.xml"}, "ref/netcoreapp2.2/System.Configuration.dll": {}, "ref/netcoreapp2.2/System.Console.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Core.dll": {}, "ref/netcoreapp2.2/System.Data.Common.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Data.dll": {"related": ".Common.xml"}, "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Debug.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Process.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Tools.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Drawing.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Drawing.dll": {"related": ".Primitives.xml"}, "ref/netcoreapp2.2/System.Dynamic.Runtime.dll": {}, "ref/netcoreapp2.2/System.Globalization.Calendars.dll": {}, "ref/netcoreapp2.2/System.Globalization.Extensions.dll": {}, "ref/netcoreapp2.2/System.Globalization.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.Compression.dll": {"related": ".xml;.ZipFile.xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.FileSystem.dll": {"related": ".DriveInfo.xml;.Watcher.xml;.xml"}, "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.Pipes.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll": {}, "ref/netcoreapp2.2/System.IO.dll": {"related": ".Compression.xml;.Compression.ZipFile.xml;.FileSystem.DriveInfo.xml;.FileSystem.Watcher.xml;.FileSystem.xml;.IsolatedStorage.xml;.MemoryMappedFiles.xml;.Pipes.xml"}, "ref/netcoreapp2.2/System.Linq.Expressions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.Parallel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.Queryable.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Linq.dll": {"related": ".Expressions.xml;.Parallel.xml;.Queryable.xml;.xml"}, "ref/netcoreapp2.2/System.Memory.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Http.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.HttpListener.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Mail.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.NameResolution.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.NetworkInformation.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Ping.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Requests.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Security.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.ServicePoint.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.Sockets.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebClient.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebProxy.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Net.WebSockets.dll": {"related": ".Client.xml;.xml"}, "ref/netcoreapp2.2/System.Net.dll": {"related": ".Http.xml;.HttpListener.xml;.Mail.xml;.NameResolution.xml;.NetworkInformation.xml;.Ping.xml;.Primitives.xml;.Requests.xml;.Security.xml;.ServicePoint.xml;.Sockets.xml;.WebClient.xml;.WebHeaderCollection.xml;.WebProxy.xml;.WebSockets.Client.xml;.WebSockets.xml"}, "ref/netcoreapp2.2/System.Numerics.Vectors.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Numerics.dll": {"related": ".Vectors.xml"}, "ref/netcoreapp2.2/System.ObjectModel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Emit.dll": {"related": ".ILGeneration.xml;.Lightweight.xml;.xml"}, "ref/netcoreapp2.2/System.Reflection.Extensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.Metadata.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Reflection.dll": {"related": ".DispatchProxy.xml;.Emit.ILGeneration.xml;.Emit.Lightweight.xml;.Emit.xml;.Metadata.xml;.Primitives.xml;.TypeExtensions.xml"}, "ref/netcoreapp2.2/System.Resources.Reader.dll": {}, "ref/netcoreapp2.2/System.Resources.ResourceManager.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Resources.Writer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Handles.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.InteropServices.dll": {"related": ".RuntimeInformation.xml;.WindowsRuntime.xml;.xml"}, "ref/netcoreapp2.2/System.Runtime.Loader.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Numerics.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Runtime.Serialization.dll": {"related": ".Formatters.xml;.Json.xml;.Primitives.xml;.Xml.xml"}, "ref/netcoreapp2.2/System.Runtime.dll": {"related": ".CompilerServices.VisualC.xml;.Extensions.xml;.InteropServices.RuntimeInformation.xml;.InteropServices.WindowsRuntime.xml;.InteropServices.xml;.Loader.xml;.Numerics.xml;.Serialization.Formatters.xml;.Serialization.Json.xml;.Serialization.Primitives.xml;.Serialization.Xml.xml;.xml"}, "ref/netcoreapp2.2/System.Security.Claims.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.Principal.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Security.SecureString.dll": {}, "ref/netcoreapp2.2/System.Security.dll": {"related": ".Claims.xml;.Cryptography.Algorithms.xml;.Cryptography.Csp.xml;.Cryptography.Encoding.xml;.Cryptography.Primitives.xml;.Cryptography.X509Certificates.xml;.Principal.xml"}, "ref/netcoreapp2.2/System.ServiceModel.Web.dll": {}, "ref/netcoreapp2.2/System.ServiceProcess.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Text.Encoding.dll": {"related": ".Extensions.xml"}, "ref/netcoreapp2.2/System.Text.RegularExpressions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Overlapped.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Tasks.dll": {"related": ".Dataflow.xml;.Extensions.xml;.Parallel.xml;.xml"}, "ref/netcoreapp2.2/System.Threading.Thread.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.ThreadPool.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.Timer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Threading.dll": {"related": ".Overlapped.xml;.Tasks.Dataflow.xml;.Tasks.Extensions.xml;.Tasks.Parallel.xml;.Tasks.xml;.Thread.xml;.ThreadPool.xml;.Timer.xml;.xml"}, "ref/netcoreapp2.2/System.Transactions.Local.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Transactions.dll": {"related": ".Local.xml"}, "ref/netcoreapp2.2/System.ValueTuple.dll": {}, "ref/netcoreapp2.2/System.Web.HttpUtility.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Web.dll": {"related": ".HttpUtility.xml"}, "ref/netcoreapp2.2/System.Windows.dll": {}, "ref/netcoreapp2.2/System.Xml.Linq.dll": {}, "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.Serialization.dll": {}, "ref/netcoreapp2.2/System.Xml.XDocument.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.XPath.dll": {"related": ".XDocument.xml;.xml"}, "ref/netcoreapp2.2/System.Xml.XmlDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll": {"related": ".xml"}, "ref/netcoreapp2.2/System.Xml.dll": {"related": ".ReaderWriter.xml;.XDocument.xml;.XmlSerializer.xml;.XPath.XDocument.xml;.XPath.xml"}, "ref/netcoreapp2.2/System.dll": {"related": ".Buffers.xml;.Collections.Concurrent.xml;.Collections.Immutable.xml;.Collections.NonGeneric.xml;.Collections.Specialized.xml;.Collections.xml;.ComponentModel.Annotations.xml;.ComponentModel.EventBasedAsync.xml;.ComponentModel.Primitives.xml;.ComponentModel.TypeConverter.xml;.ComponentModel.xml;.Console.xml;.Data.Common.xml;.Diagnostics.Contracts.xml;.Diagnostics.Debug.xml;.Diagnostics.DiagnosticSource.xml;.Diagnostics.FileVersionInfo.xml;.Diagnostics.Process.xml;.Diagnostics.StackTrace.xml;.Diagnostics.TextWriterTraceListener.xml;.Diagnostics.Tools.xml;.Diagnostics.TraceSource.xml;.Diagnostics.Tracing.xml;.Drawing.Primitives.xml;.IO.Compression.xml;.IO.Compression.ZipFile.xml;.IO.FileSystem.DriveInfo.xml;.IO.FileSystem.Watcher.xml;.IO.FileSystem.xml;.IO.IsolatedStorage.xml;.IO.MemoryMappedFiles.xml;.IO.Pipes.xml;.Linq.Expressions.xml;.Linq.Parallel.xml;.Linq.Queryable.xml;.Linq.xml;.Memory.xml;.Net.Http.xml;.Net.HttpListener.xml;.Net.Mail.xml;.Net.NameResolution.xml;.Net.NetworkInformation.xml;.Net.Ping.xml;.Net.Primitives.xml;.Net.Requests.xml;.Net.Security.xml;.Net.ServicePoint.xml;.Net.Sockets.xml;.Net.WebClient.xml;.Net.WebHeaderCollection.xml;.Net.WebProxy.xml;.Net.WebSockets.Client.xml;.Net.WebSockets.xml;.Numerics.Vectors.xml;.ObjectModel.xml;.Reflection.DispatchProxy.xml;.Reflection.Emit.ILGeneration.xml;.Reflection.Emit.Lightweight.xml;.Reflection.Emit.xml;.Reflection.Metadata.xml;.Reflection.Primitives.xml;.Reflection.TypeExtensions.xml;.Resources.ResourceManager.xml;.Resources.Writer.xml;.Runtime.CompilerServices.VisualC.xml;.Runtime.Extensions.xml;.Runtime.InteropServices.RuntimeInformation.xml;.Runtime.InteropServices.WindowsRuntime.xml;.Runtime.InteropServices.xml;.Runtime.Loader.xml;.Runtime.Numerics.xml;.Runtime.Serialization.Formatters.xml;.Runtime.Serialization.Json.xml;.Runtime.Serialization.Primitives.xml;.Runtime.Serialization.Xml.xml;.Runtime.xml;.Security.Claims.xml;.Security.Cryptography.Algorithms.xml;.Security.Cryptography.Csp.xml;.Security.Cryptography.Encoding.xml;.Security.Cryptography.Primitives.xml;.Security.Cryptography.X509Certificates.xml;.Security.Principal.xml;.Text.Encoding.Extensions.xml;.Text.RegularExpressions.xml;.Threading.Overlapped.xml;.Threading.Tasks.Dataflow.xml;.Threading.Tasks.Extensions.xml;.Threading.Tasks.Parallel.xml;.Threading.Tasks.xml;.Threading.Thread.xml;.Threading.ThreadPool.xml;.Threading.Timer.xml;.Threading.xml;.Transactions.Local.xml;.Web.HttpUtility.xml;.Xml.ReaderWriter.xml;.Xml.XDocument.xml;.Xml.XmlSerializer.xml;.Xml.XPath.XDocument.xml;.Xml.XPath.xml"}, "ref/netcoreapp2.2/WindowsBase.dll": {}, "ref/netcoreapp2.2/mscorlib.dll": {}, "ref/netcoreapp2.2/netstandard.dll": {}}, "build": {"build/netcoreapp2.2/Microsoft.NETCore.App.props": {}, "build/netcoreapp2.2/Microsoft.NETCore.App.targets": {}}}, "Microsoft.NETCore.DotNetAppHost/2.2.0": {"type": "package"}, "Microsoft.NETCore.DotNetHostPolicy/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.0"}}, "Microsoft.NETCore.DotNetHostResolver/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.0"}}, "Microsoft.NETCore.Platforms/2.2.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "NETStandard.Library/2.0.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"build/netstandard2.0/NETStandard.Library.targets": {}}}, "HttpToSocks5Proxy/1.4.0": {"type": "project", "framework": ".NETStandard,Version=v2.0", "compile": {"bin/placeholder/HttpToSocks5Proxy.dll": {}}, "runtime": {"bin/placeholder/HttpToSocks5Proxy.dll": {}}}}}, "libraries": {"Microsoft.NETCore.App/2.2.0": {"sha512": "7z5l8Jp324S8bU8+yyWeYHXUFYvKyiI5lqS1dXgTzOx1H69Qbf6df12kCKlNX45LpMfCMd4U3M6p7Rl5Zk7SLA==", "type": "package", "path": "microsoft.netcore.app/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "Microsoft.NETCore.App.versions.txt", "THIRD-PARTY-NOTICES.TXT", "build/netcoreapp2.2/Microsoft.NETCore.App.PlatformManifest.txt", "build/netcoreapp2.2/Microsoft.NETCore.App.props", "build/netcoreapp2.2/Microsoft.NETCore.App.targets", "microsoft.netcore.app.2.2.0.nupkg.sha512", "microsoft.netcore.app.nuspec", "ref/netcoreapp2.2/Microsoft.CSharp.dll", "ref/netcoreapp2.2/Microsoft.CSharp.xml", "ref/netcoreapp2.2/Microsoft.VisualBasic.dll", "ref/netcoreapp2.2/Microsoft.VisualBasic.xml", "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll", "ref/netcoreapp2.2/Microsoft.Win32.Primitives.xml", "ref/netcoreapp2.2/System.AppContext.dll", "ref/netcoreapp2.2/System.Buffers.dll", "ref/netcoreapp2.2/System.Buffers.xml", "ref/netcoreapp2.2/System.Collections.Concurrent.dll", "ref/netcoreapp2.2/System.Collections.Concurrent.xml", "ref/netcoreapp2.2/System.Collections.Immutable.dll", "ref/netcoreapp2.2/System.Collections.Immutable.xml", "ref/netcoreapp2.2/System.Collections.NonGeneric.dll", "ref/netcoreapp2.2/System.Collections.NonGeneric.xml", "ref/netcoreapp2.2/System.Collections.Specialized.dll", "ref/netcoreapp2.2/System.Collections.Specialized.xml", "ref/netcoreapp2.2/System.Collections.dll", "ref/netcoreapp2.2/System.Collections.xml", "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll", "ref/netcoreapp2.2/System.ComponentModel.Annotations.xml", "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll", "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll", "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.xml", "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll", "ref/netcoreapp2.2/System.ComponentModel.Primitives.xml", "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll", "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.xml", "ref/netcoreapp2.2/System.ComponentModel.dll", "ref/netcoreapp2.2/System.ComponentModel.xml", "ref/netcoreapp2.2/System.Configuration.dll", "ref/netcoreapp2.2/System.Console.dll", "ref/netcoreapp2.2/System.Console.xml", "ref/netcoreapp2.2/System.Core.dll", "ref/netcoreapp2.2/System.Data.Common.dll", "ref/netcoreapp2.2/System.Data.Common.xml", "ref/netcoreapp2.2/System.Data.dll", "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll", "ref/netcoreapp2.2/System.Diagnostics.Contracts.xml", "ref/netcoreapp2.2/System.Diagnostics.Debug.dll", "ref/netcoreapp2.2/System.Diagnostics.Debug.xml", "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll", "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.xml", "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll", "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.xml", "ref/netcoreapp2.2/System.Diagnostics.Process.dll", "ref/netcoreapp2.2/System.Diagnostics.Process.xml", "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll", "ref/netcoreapp2.2/System.Diagnostics.StackTrace.xml", "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll", "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.xml", "ref/netcoreapp2.2/System.Diagnostics.Tools.dll", "ref/netcoreapp2.2/System.Diagnostics.Tools.xml", "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll", "ref/netcoreapp2.2/System.Diagnostics.TraceSource.xml", "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll", "ref/netcoreapp2.2/System.Diagnostics.Tracing.xml", "ref/netcoreapp2.2/System.Drawing.Primitives.dll", "ref/netcoreapp2.2/System.Drawing.Primitives.xml", "ref/netcoreapp2.2/System.Drawing.dll", "ref/netcoreapp2.2/System.Dynamic.Runtime.dll", "ref/netcoreapp2.2/System.Globalization.Calendars.dll", "ref/netcoreapp2.2/System.Globalization.Extensions.dll", "ref/netcoreapp2.2/System.Globalization.dll", "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll", "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll", "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll", "ref/netcoreapp2.2/System.IO.Compression.ZipFile.xml", "ref/netcoreapp2.2/System.IO.Compression.dll", "ref/netcoreapp2.2/System.IO.Compression.xml", "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll", "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.xml", "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll", "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll", "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.xml", "ref/netcoreapp2.2/System.IO.FileSystem.dll", "ref/netcoreapp2.2/System.IO.FileSystem.xml", "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll", "ref/netcoreapp2.2/System.IO.IsolatedStorage.xml", "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll", "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.xml", "ref/netcoreapp2.2/System.IO.Pipes.dll", "ref/netcoreapp2.2/System.IO.Pipes.xml", "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll", "ref/netcoreapp2.2/System.IO.dll", "ref/netcoreapp2.2/System.Linq.Expressions.dll", "ref/netcoreapp2.2/System.Linq.Expressions.xml", "ref/netcoreapp2.2/System.Linq.Parallel.dll", "ref/netcoreapp2.2/System.Linq.Parallel.xml", "ref/netcoreapp2.2/System.Linq.Queryable.dll", "ref/netcoreapp2.2/System.Linq.Queryable.xml", "ref/netcoreapp2.2/System.Linq.dll", "ref/netcoreapp2.2/System.Linq.xml", "ref/netcoreapp2.2/System.Memory.dll", "ref/netcoreapp2.2/System.Memory.xml", "ref/netcoreapp2.2/System.Net.Http.dll", "ref/netcoreapp2.2/System.Net.Http.xml", "ref/netcoreapp2.2/System.Net.HttpListener.dll", "ref/netcoreapp2.2/System.Net.HttpListener.xml", "ref/netcoreapp2.2/System.Net.Mail.dll", "ref/netcoreapp2.2/System.Net.Mail.xml", "ref/netcoreapp2.2/System.Net.NameResolution.dll", "ref/netcoreapp2.2/System.Net.NameResolution.xml", "ref/netcoreapp2.2/System.Net.NetworkInformation.dll", "ref/netcoreapp2.2/System.Net.NetworkInformation.xml", "ref/netcoreapp2.2/System.Net.Ping.dll", "ref/netcoreapp2.2/System.Net.Ping.xml", "ref/netcoreapp2.2/System.Net.Primitives.dll", "ref/netcoreapp2.2/System.Net.Primitives.xml", "ref/netcoreapp2.2/System.Net.Requests.dll", "ref/netcoreapp2.2/System.Net.Requests.xml", "ref/netcoreapp2.2/System.Net.Security.dll", "ref/netcoreapp2.2/System.Net.Security.xml", "ref/netcoreapp2.2/System.Net.ServicePoint.dll", "ref/netcoreapp2.2/System.Net.ServicePoint.xml", "ref/netcoreapp2.2/System.Net.Sockets.dll", "ref/netcoreapp2.2/System.Net.Sockets.xml", "ref/netcoreapp2.2/System.Net.WebClient.dll", "ref/netcoreapp2.2/System.Net.WebClient.xml", "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll", "ref/netcoreapp2.2/System.Net.WebHeaderCollection.xml", "ref/netcoreapp2.2/System.Net.WebProxy.dll", "ref/netcoreapp2.2/System.Net.WebProxy.xml", "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll", "ref/netcoreapp2.2/System.Net.WebSockets.Client.xml", "ref/netcoreapp2.2/System.Net.WebSockets.dll", "ref/netcoreapp2.2/System.Net.WebSockets.xml", "ref/netcoreapp2.2/System.Net.dll", "ref/netcoreapp2.2/System.Numerics.Vectors.dll", "ref/netcoreapp2.2/System.Numerics.Vectors.xml", "ref/netcoreapp2.2/System.Numerics.dll", "ref/netcoreapp2.2/System.ObjectModel.dll", "ref/netcoreapp2.2/System.ObjectModel.xml", "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll", "ref/netcoreapp2.2/System.Reflection.DispatchProxy.xml", "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll", "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.xml", "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll", "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.xml", "ref/netcoreapp2.2/System.Reflection.Emit.dll", "ref/netcoreapp2.2/System.Reflection.Emit.xml", "ref/netcoreapp2.2/System.Reflection.Extensions.dll", "ref/netcoreapp2.2/System.Reflection.Metadata.dll", "ref/netcoreapp2.2/System.Reflection.Metadata.xml", "ref/netcoreapp2.2/System.Reflection.Primitives.dll", "ref/netcoreapp2.2/System.Reflection.Primitives.xml", "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll", "ref/netcoreapp2.2/System.Reflection.TypeExtensions.xml", "ref/netcoreapp2.2/System.Reflection.dll", "ref/netcoreapp2.2/System.Resources.Reader.dll", "ref/netcoreapp2.2/System.Resources.ResourceManager.dll", "ref/netcoreapp2.2/System.Resources.ResourceManager.xml", "ref/netcoreapp2.2/System.Resources.Writer.dll", "ref/netcoreapp2.2/System.Resources.Writer.xml", "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll", "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.xml", "ref/netcoreapp2.2/System.Runtime.Extensions.dll", "ref/netcoreapp2.2/System.Runtime.Extensions.xml", "ref/netcoreapp2.2/System.Runtime.Handles.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.xml", "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcoreapp2.2/System.Runtime.InteropServices.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.xml", "ref/netcoreapp2.2/System.Runtime.Loader.dll", "ref/netcoreapp2.2/System.Runtime.Loader.xml", "ref/netcoreapp2.2/System.Runtime.Numerics.dll", "ref/netcoreapp2.2/System.Runtime.Numerics.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Json.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.dll", "ref/netcoreapp2.2/System.Runtime.dll", "ref/netcoreapp2.2/System.Runtime.xml", "ref/netcoreapp2.2/System.Security.Claims.dll", "ref/netcoreapp2.2/System.Security.Claims.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Csp.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.xml", "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll", "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.xml", "ref/netcoreapp2.2/System.Security.Principal.dll", "ref/netcoreapp2.2/System.Security.Principal.xml", "ref/netcoreapp2.2/System.Security.SecureString.dll", "ref/netcoreapp2.2/System.Security.dll", "ref/netcoreapp2.2/System.ServiceModel.Web.dll", "ref/netcoreapp2.2/System.ServiceProcess.dll", "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll", "ref/netcoreapp2.2/System.Text.Encoding.Extensions.xml", "ref/netcoreapp2.2/System.Text.Encoding.dll", "ref/netcoreapp2.2/System.Text.RegularExpressions.dll", "ref/netcoreapp2.2/System.Text.RegularExpressions.xml", "ref/netcoreapp2.2/System.Threading.Overlapped.dll", "ref/netcoreapp2.2/System.Threading.Overlapped.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.xml", "ref/netcoreapp2.2/System.Threading.Tasks.dll", "ref/netcoreapp2.2/System.Threading.Tasks.xml", "ref/netcoreapp2.2/System.Threading.Thread.dll", "ref/netcoreapp2.2/System.Threading.Thread.xml", "ref/netcoreapp2.2/System.Threading.ThreadPool.dll", "ref/netcoreapp2.2/System.Threading.ThreadPool.xml", "ref/netcoreapp2.2/System.Threading.Timer.dll", "ref/netcoreapp2.2/System.Threading.Timer.xml", "ref/netcoreapp2.2/System.Threading.dll", "ref/netcoreapp2.2/System.Threading.xml", "ref/netcoreapp2.2/System.Transactions.Local.dll", "ref/netcoreapp2.2/System.Transactions.Local.xml", "ref/netcoreapp2.2/System.Transactions.dll", "ref/netcoreapp2.2/System.ValueTuple.dll", "ref/netcoreapp2.2/System.Web.HttpUtility.dll", "ref/netcoreapp2.2/System.Web.HttpUtility.xml", "ref/netcoreapp2.2/System.Web.dll", "ref/netcoreapp2.2/System.Windows.dll", "ref/netcoreapp2.2/System.Xml.Linq.dll", "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll", "ref/netcoreapp2.2/System.Xml.ReaderWriter.xml", "ref/netcoreapp2.2/System.Xml.Serialization.dll", "ref/netcoreapp2.2/System.Xml.XDocument.dll", "ref/netcoreapp2.2/System.Xml.XDocument.xml", "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll", "ref/netcoreapp2.2/System.Xml.XPath.XDocument.xml", "ref/netcoreapp2.2/System.Xml.XPath.dll", "ref/netcoreapp2.2/System.Xml.XPath.xml", "ref/netcoreapp2.2/System.Xml.XmlDocument.dll", "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll", "ref/netcoreapp2.2/System.Xml.XmlSerializer.xml", "ref/netcoreapp2.2/System.Xml.dll", "ref/netcoreapp2.2/System.dll", "ref/netcoreapp2.2/WindowsBase.dll", "ref/netcoreapp2.2/mscorlib.dll", "ref/netcoreapp2.2/netstandard.dll", "runtime.json"]}, "Microsoft.NETCore.DotNetAppHost/2.2.0": {"sha512": "DrhaKInRKKvN6Ns2VNIlC7ZffLOp9THf8cO6X4fytPRJovJUbF49/zzx4WfgX9E44FMsw9hT8hrKiIqDSHvGvA==", "type": "package", "path": "microsoft.netcore.dotnetapphost/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnetapphost.2.2.0.nupkg.sha512", "microsoft.netcore.dotnetapphost.nuspec", "runtime.json"]}, "Microsoft.NETCore.DotNetHostPolicy/2.2.0": {"sha512": "FJie7IoPZFaPgNDxhZGmDBQP/Bs5vPdfca/G2Wf9gd6LIvMYkZcibtmJwB4tcf4KXkaOYfIOo4Cl9sEPMsSzkw==", "type": "package", "path": "microsoft.netcore.dotnethostpolicy/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnethostpolicy.2.2.0.nupkg.sha512", "microsoft.netcore.dotnethostpolicy.nuspec", "runtime.json"]}, "Microsoft.NETCore.DotNetHostResolver/2.2.0": {"sha512": "spDm3AJYmebthDNhzY17YLPtvbc+Y1lCLVeiIH1uLJ/hZaM+40pBiPefFR8J1u66Ndkqi8ipR2tEbqPnYnjRhw==", "type": "package", "path": "microsoft.netcore.dotnethostresolver/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnethostresolver.2.2.0.nupkg.sha512", "microsoft.netcore.dotnethostresolver.nuspec", "runtime.json"]}, "Microsoft.NETCore.Platforms/2.2.0": {"sha512": "T/J+XZo+YheFTJh8/4uoeJDdz5qOmOMkjg6/VL8mHJ9AnP8+fmV/kcbxeXsob0irRNiChf+V0ig1MCRLp/+Kog==", "type": "package", "path": "microsoft.netcore.platforms/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.2.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/2.0.0": {"sha512": "odP/tJj1z6GylFpNo7pMtbd/xQgTC3Ex2If63dRTL38bBNMwsBnJ+RceUIyHdRBC0oik/3NehYT+oECwBhIM3Q==", "type": "package", "path": "microsoft.netcore.targets/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.targets.2.0.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "NETStandard.Library/2.0.3": {"sha512": "st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "type": "package", "path": "netstandard.library/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/NETStandard.Library.targets", "build/netstandard2.0/ref/Microsoft.Win32.Primitives.dll", "build/netstandard2.0/ref/System.AppContext.dll", "build/netstandard2.0/ref/System.Collections.Concurrent.dll", "build/netstandard2.0/ref/System.Collections.NonGeneric.dll", "build/netstandard2.0/ref/System.Collections.Specialized.dll", "build/netstandard2.0/ref/System.Collections.dll", "build/netstandard2.0/ref/System.ComponentModel.Composition.dll", "build/netstandard2.0/ref/System.ComponentModel.EventBasedAsync.dll", "build/netstandard2.0/ref/System.ComponentModel.Primitives.dll", "build/netstandard2.0/ref/System.ComponentModel.TypeConverter.dll", "build/netstandard2.0/ref/System.ComponentModel.dll", "build/netstandard2.0/ref/System.Console.dll", "build/netstandard2.0/ref/System.Core.dll", "build/netstandard2.0/ref/System.Data.Common.dll", "build/netstandard2.0/ref/System.Data.dll", "build/netstandard2.0/ref/System.Diagnostics.Contracts.dll", "build/netstandard2.0/ref/System.Diagnostics.Debug.dll", "build/netstandard2.0/ref/System.Diagnostics.FileVersionInfo.dll", "build/netstandard2.0/ref/System.Diagnostics.Process.dll", "build/netstandard2.0/ref/System.Diagnostics.StackTrace.dll", "build/netstandard2.0/ref/System.Diagnostics.TextWriterTraceListener.dll", "build/netstandard2.0/ref/System.Diagnostics.Tools.dll", "build/netstandard2.0/ref/System.Diagnostics.TraceSource.dll", "build/netstandard2.0/ref/System.Diagnostics.Tracing.dll", "build/netstandard2.0/ref/System.Drawing.Primitives.dll", "build/netstandard2.0/ref/System.Drawing.dll", "build/netstandard2.0/ref/System.Dynamic.Runtime.dll", "build/netstandard2.0/ref/System.Globalization.Calendars.dll", "build/netstandard2.0/ref/System.Globalization.Extensions.dll", "build/netstandard2.0/ref/System.Globalization.dll", "build/netstandard2.0/ref/System.IO.Compression.FileSystem.dll", "build/netstandard2.0/ref/System.IO.Compression.ZipFile.dll", "build/netstandard2.0/ref/System.IO.Compression.dll", "build/netstandard2.0/ref/System.IO.FileSystem.DriveInfo.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Primitives.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Watcher.dll", "build/netstandard2.0/ref/System.IO.FileSystem.dll", "build/netstandard2.0/ref/System.IO.IsolatedStorage.dll", "build/netstandard2.0/ref/System.IO.MemoryMappedFiles.dll", "build/netstandard2.0/ref/System.IO.Pipes.dll", "build/netstandard2.0/ref/System.IO.UnmanagedMemoryStream.dll", "build/netstandard2.0/ref/System.IO.dll", "build/netstandard2.0/ref/System.Linq.Expressions.dll", "build/netstandard2.0/ref/System.Linq.Parallel.dll", "build/netstandard2.0/ref/System.Linq.Queryable.dll", "build/netstandard2.0/ref/System.Linq.dll", "build/netstandard2.0/ref/System.Net.Http.dll", "build/netstandard2.0/ref/System.Net.NameResolution.dll", "build/netstandard2.0/ref/System.Net.NetworkInformation.dll", "build/netstandard2.0/ref/System.Net.Ping.dll", "build/netstandard2.0/ref/System.Net.Primitives.dll", "build/netstandard2.0/ref/System.Net.Requests.dll", "build/netstandard2.0/ref/System.Net.Security.dll", "build/netstandard2.0/ref/System.Net.Sockets.dll", "build/netstandard2.0/ref/System.Net.WebHeaderCollection.dll", "build/netstandard2.0/ref/System.Net.WebSockets.Client.dll", "build/netstandard2.0/ref/System.Net.WebSockets.dll", "build/netstandard2.0/ref/System.Net.dll", "build/netstandard2.0/ref/System.Numerics.dll", "build/netstandard2.0/ref/System.ObjectModel.dll", "build/netstandard2.0/ref/System.Reflection.Extensions.dll", "build/netstandard2.0/ref/System.Reflection.Primitives.dll", "build/netstandard2.0/ref/System.Reflection.dll", "build/netstandard2.0/ref/System.Resources.Reader.dll", "build/netstandard2.0/ref/System.Resources.ResourceManager.dll", "build/netstandard2.0/ref/System.Resources.Writer.dll", "build/netstandard2.0/ref/System.Runtime.CompilerServices.VisualC.dll", "build/netstandard2.0/ref/System.Runtime.Extensions.dll", "build/netstandard2.0/ref/System.Runtime.Handles.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.RuntimeInformation.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.dll", "build/netstandard2.0/ref/System.Runtime.Numerics.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Formatters.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Json.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Primitives.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Xml.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.dll", "build/netstandard2.0/ref/System.Runtime.dll", "build/netstandard2.0/ref/System.Security.Claims.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Algorithms.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Csp.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Encoding.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Primitives.dll", "build/netstandard2.0/ref/System.Security.Cryptography.X509Certificates.dll", "build/netstandard2.0/ref/System.Security.Principal.dll", "build/netstandard2.0/ref/System.Security.SecureString.dll", "build/netstandard2.0/ref/System.ServiceModel.Web.dll", "build/netstandard2.0/ref/System.Text.Encoding.Extensions.dll", "build/netstandard2.0/ref/System.Text.Encoding.dll", "build/netstandard2.0/ref/System.Text.RegularExpressions.dll", "build/netstandard2.0/ref/System.Threading.Overlapped.dll", "build/netstandard2.0/ref/System.Threading.Tasks.Parallel.dll", "build/netstandard2.0/ref/System.Threading.Tasks.dll", "build/netstandard2.0/ref/System.Threading.Thread.dll", "build/netstandard2.0/ref/System.Threading.ThreadPool.dll", "build/netstandard2.0/ref/System.Threading.Timer.dll", "build/netstandard2.0/ref/System.Threading.dll", "build/netstandard2.0/ref/System.Transactions.dll", "build/netstandard2.0/ref/System.ValueTuple.dll", "build/netstandard2.0/ref/System.Web.dll", "build/netstandard2.0/ref/System.Windows.dll", "build/netstandard2.0/ref/System.Xml.Linq.dll", "build/netstandard2.0/ref/System.Xml.ReaderWriter.dll", "build/netstandard2.0/ref/System.Xml.Serialization.dll", "build/netstandard2.0/ref/System.Xml.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.dll", "build/netstandard2.0/ref/System.Xml.XmlDocument.dll", "build/netstandard2.0/ref/System.Xml.XmlSerializer.dll", "build/netstandard2.0/ref/System.Xml.dll", "build/netstandard2.0/ref/System.dll", "build/netstandard2.0/ref/mscorlib.dll", "build/netstandard2.0/ref/netstandard.dll", "build/netstandard2.0/ref/netstandard.xml", "lib/netstandard1.0/_._", "netstandard.library.2.0.3.nupkg.sha512", "netstandard.library.nuspec"]}, "HttpToSocks5Proxy/1.4.0": {"type": "project", "path": "../../src/HttpToSocks5Proxy/HttpToSocks5Proxy.csproj", "msbuildProject": "../../src/HttpToSocks5Proxy/HttpToSocks5Proxy.csproj"}}, "projectFileDependencyGroups": {".NETCoreApp,Version=v2.2": ["HttpToSocks5Proxy >= 1.4.0", "Microsoft.NETCore.App >= 2.2.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\HttpToSocks5Proxy.Tests.csproj", "projectName": "HttpToSocks5Proxy.Tests", "projectPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\HttpToSocks5Proxy.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"projectPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'Microsoft.NETCore.App' 2.2.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-2xjx-v99w-gqf3", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.2"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'Microsoft.NETCore.App' 2.2.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-6px8-22w5-w334", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.2"]}, {"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Package 'Microsoft.NETCore.App' 2.2.0 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-x5qj-9vmx-7g6g", "libraryId": "Microsoft.NETCore.App", "targetGraphs": [".NETCoreApp,Version=v2.2"]}]}
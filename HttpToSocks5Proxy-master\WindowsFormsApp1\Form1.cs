﻿using MihaZupan;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            var qwe = Test();
        }

        async Task<string> Test()
        {
            var proxy = new HttpToSocks5Proxy("127.0.0.1", 2801);
            var handler = new HttpClientHandler { Proxy = proxy };
            HttpClient httpClient = new HttpClient(handler, false);

            var result = await httpClient.SendAsync(
                new HttpRequestMessage(HttpMethod.Get, "https://httpbin.org/ip"));
            // aresult.Content.Headers.Add("1", "1");
            //result.Headers.Add("Authorization", "Basic cmlvdDpWX2dKNmNWWWJncVZXNVlCWjhkNkd3");

            Console.WriteLine("HTTPS GET: " + await result.Content.ReadAsStringAsync());
            return await result.Content.ReadAsStringAsync();
            //Console.WriteLine("HTTPS GET: " + result.Content.Headers.ContentRange);
            //MessageBox.Show(await result.Content.ReadAsStringAsync());
        }
    }
}

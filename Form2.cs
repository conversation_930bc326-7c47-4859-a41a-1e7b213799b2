﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

using System.Diagnostics;
using System.Text.RegularExpressions;

namespace WindowsFormsApp1
{
    public partial class Form2 : Form
    {
        public Form2()
        {
            InitializeComponent();
        }

        private void Form2_Load(object sender, EventArgs e)
        {

            if (Form1.C_state == "增加数据")
            {
                button1.Text = "增加数据";
                this.Text = "增加数据";
                Gamename.Text = "无";
                serverkj.SelectedIndex = 0;
            }
            else
            {
                button1.Text = "修改数据";

                this.Text = "修改数据";

                account.Text = Form1.Getaccount;

                password.Text = Form1.Getpassword;

                Remarkkj.Text = Form1.GetRemark;

                Gamename.Text = Form1.Getsname;

                string Fuwuqi = Form1.Getserver;

                int Xu = 0;

                if (Fuwuqi == "韩服")
                {
                    Xu = 0;
                }
                else if (<PERSON>wuqi == "日服")
                {
                    Xu = 1;
                }
                else if (<PERSON><PERSON><PERSON> == "澳服")
                {
                    Xu = 2;
                }
                else if (<PERSON><PERSON><PERSON> == "欧服")
                {
                    Xu = 3;
                }
                else if (<PERSON><PERSON><PERSON> == "美服")
                {
                    Xu = 4;
                }

                serverkj.SelectedIndex = Xu;


            }



        }

        private void button1_Click(object sender, EventArgs e)
        {
            var UserName = account.Text;
            UserName = Regex.Replace(UserName, @"\s", "");
            var PassWord = password.Text;
            PassWord = Regex.Replace(PassWord, @"\s", "");
            var Remark = Remarkkj.Text;
            int moshi = 0;

            var Server = serverkj.SelectedItem.ToString();

            if (UserName == "" || PassWord == "" || Remark == "")
            {
                MessageBox.Show("账号或密码或备注不能为空!");
                return;
            }

            if (Form1.C_state == "增加数据")
            {
                moshi = 0;
            }
            else
            {
                moshi = 1;
            }
            Form1.Xieshujukun(UserName, PassWord, Remark, Gamename.Text, Server, moshi);
            this.Close();
        }
    }
}

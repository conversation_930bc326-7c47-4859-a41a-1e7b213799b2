{"format": 1, "restore": {"d:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {}}, "projects": {"d:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"version": "1.4.0", "restore": {"projectUniqueName": "d:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj", "projectName": "HttpToSocks5Proxy", "projectPath": "d:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net45": {"targetAlias": "net45", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}
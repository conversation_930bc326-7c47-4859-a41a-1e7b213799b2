﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="button1.Location" type="System.Drawing.Point, System.Drawing">
    <value>131, 161</value>
  </data>
  <data name="button1.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 37</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="button1.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="button1.Text" xml:space="preserve">
    <value>修改数据</value>
  </data>
  <data name="&gt;&gt;button1.Name" xml:space="preserve">
    <value>button1</value>
  </data>
  <data name="&gt;&gt;button1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button1.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="account.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 21</value>
  </data>
  <data name="account.Size" type="System.Drawing.Size, System.Drawing">
    <value>191, 21</value>
  </data>
  <data name="account.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;account.Name" xml:space="preserve">
    <value>account</value>
  </data>
  <data name="&gt;&gt;account.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;account.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;account.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 23</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>账号:</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="Remarkkj.Location" type="System.Drawing.Point, System.Drawing">
    <value>50, 132</value>
  </data>
  <data name="Remarkkj.Size" type="System.Drawing.Size, System.Drawing">
    <value>191, 21</value>
  </data>
  <data name="Remarkkj.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;Remarkkj.Name" xml:space="preserve">
    <value>Remarkkj</value>
  </data>
  <data name="&gt;&gt;Remarkkj.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Remarkkj.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;Remarkkj.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="password.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 57</value>
  </data>
  <data name="password.Size" type="System.Drawing.Size, System.Drawing">
    <value>191, 21</value>
  </data>
  <data name="password.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;password.Name" xml:space="preserve">
    <value>password</value>
  </data>
  <data name="&gt;&gt;password.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;password.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;password.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 61</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>密码:</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 135</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>备注:</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="serverkj.Items" xml:space="preserve">
    <value>韩服</value>
  </data>
  <data name="serverkj.Items1" xml:space="preserve">
    <value>日服</value>
  </data>
  <data name="serverkj.Items2" xml:space="preserve">
    <value>澳服</value>
  </data>
  <data name="serverkj.Items3" xml:space="preserve">
    <value>欧服</value>
  </data>
  <data name="serverkj.Items4" xml:space="preserve">
    <value>美服</value>
  </data>
  <data name="serverkj.Items5" xml:space="preserve">
    <value>台服</value>
  </data>
  <data name="serverkj.Location" type="System.Drawing.Point, System.Drawing">
    <value>50, 170</value>
  </data>
  <data name="serverkj.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 20</value>
  </data>
  <data name="serverkj.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;serverkj.Name" xml:space="preserve">
    <value>serverkj</value>
  </data>
  <data name="&gt;&gt;serverkj.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;serverkj.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;serverkj.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 101</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>名称:</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="Gamename.Location" type="System.Drawing.Point, System.Drawing">
    <value>50, 96</value>
  </data>
  <data name="Gamename.Size" type="System.Drawing.Size, System.Drawing">
    <value>191, 21</value>
  </data>
  <data name="Gamename.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;Gamename.Name" xml:space="preserve">
    <value>Gamename</value>
  </data>
  <data name="&gt;&gt;Gamename.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Gamename.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;Gamename.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 173</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>区服:</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.AutoSizeMode" type="System.Windows.Forms.AutoSizeMode, System.Windows.Forms">
    <value>GrowAndShrink</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>248, 206</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>`</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>Form2</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;

namespace WindowsFormsApp1
{
    public partial class PaiweiGET : Form
    {

        public PaiweiGET()
        {

            InitializeComponent();
        }
       
        private void PaiweiGET_Load(object sender, EventArgs e)
        {
            if (Paiwei.XINHAO.IndexOf("TOP") != -1)
            {
                tabControl1.SelectedIndex = 0;
            }
            if (Paiwei.XINHAO.IndexOf("JG") != -1)
            {
                tabControl1.SelectedIndex = 1;
            }
            if (Paiwei.XINHAO.IndexOf("MID") != -1)
            {
                tabControl1.SelectedIndex = 2;
            }
            if (Paiwei.XINHAO.IndexOf("ADC") != -1)
            {
                tabControl1.SelectedIndex = 3;
            }
            if (Pa<PERSON>wei.XINHAO.IndexOf("SUP")!=-1)
            {
                tabControl1.SelectedIndex = 4;
            }

            listView1.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView2.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView3.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView4.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView5.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView6.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
        }


        List<string> HUHU = new List<string>();


        Image TOP1 = null;
        Image TOP2 = null;
        Image TOP3 = null;

        Image JG1 = null;
        Image JG2 = null;
        Image JG3 = null;

        Image MID1 = null;
        Image MID2 = null;
        Image MID3 = null;

        Image ADC1 = null;
        Image ADC2 = null;
        Image ADC3 = null;

        Image SUP1 = null;
        Image SUP2 = null;
        Image SUP3 = null;
        int chaog = 0;
        public bool dddddddd()
        {



            Form1.paiwei.TOP1.Image = Resources._1111;
            Form1.paiwei.TOP2.Image = Resources._1111;
            Form1.paiwei.TOP3.Image = Resources._1111;

            Form1.paiwei.JG1.Image = Resources._1111;
            Form1.paiwei.JG2.Image = Resources._1111;
            Form1.paiwei.JG3.Image = Resources._1111;

            Form1.paiwei.MID1.Image = Resources._1111;
            Form1.paiwei.MID2.Image = Resources._1111;
            Form1.paiwei.MID3.Image = Resources._1111;

            Form1.paiwei.ADC1.Image = Resources._1111;
            Form1.paiwei.ADC2.Image = Resources._1111;
            Form1.paiwei.ADC3.Image = Resources._1111;

            Form1.paiwei.SUP1.Image = Resources._1111;
            Form1.paiwei.SUP2.Image = Resources._1111;
            Form1.paiwei.SUP3.Image = Resources._1111;




            Form1.form1.Rizhi("正在获取英雄数据中!:"+ Form1.PweiDataKun.Count);

 
            if (Form1.PweiDataKun.Count > 0)
            {

                imageList1.Images.Clear();
                listView1.Items.Clear();

                imageList2.Images.Clear();
                listView2.Items.Clear();

                imageList3.Images.Clear();
                listView3.Items.Clear();

                imageList4.Images.Clear();
                listView4.Items.Clear();

                imageList5.Images.Clear();
                listView5.Items.Clear();

                imageList6.Images.Clear();
                listView6.Items.Clear();


                HUHU.Clear();
                for (var i = 0; i < Form1.PweiDataKun.Count; i++)
                {
                    if (Form1.PweiDataKun[i].owned == "true")
                    {


                        var HeroID = Form1.PweiDataKun[i].HeroID;

                        HUHU.Add(HeroID);

                        var HeroLEIXING = Form1.PweiDataKun[i].Weizi;

                        var Heroname = Form1.PweiDataKun[i].Heroname;

                        var icon = Form1.PweiDataKun[i].icon;

                        if (HeroLEIXING=="TOP")
                        {



                            this.listView1.BeginUpdate();
                            this.imageList1.Images.Add(HeroID, icon);

                            this.listView1.Items.Add(Heroname, HeroID);
                            this.listView1.EndUpdate();
                        }

                        if (HeroLEIXING== "JUNGLE")
                        {
                  


                            this.listView2.BeginUpdate();
                            this.imageList2.Images.Add(HeroID, icon);

                            this.listView2.Items.Add(Heroname, HeroID);
                            this.listView2.EndUpdate();
                        }

                        if (HeroLEIXING== "MID")
                        {

                      


                            this.listView3.BeginUpdate();
                            this.imageList3.Images.Add(HeroID, icon);

                            this.listView3.Items.Add(Heroname, HeroID);
                            this.listView3.EndUpdate();
                        }

                        if (HeroLEIXING== "ADC")
                        {

                        


                            this.listView4.BeginUpdate();
                            this.imageList4.Images.Add(HeroID, icon);

                            this.listView4.Items.Add(Heroname, HeroID);
                            this.listView4.EndUpdate();
                        }

                        if (HeroLEIXING== "SUPPORT")
                        {

                         


                            this.listView5.BeginUpdate();
                            this.imageList5.Images.Add(HeroID, icon);

                            this.listView5.Items.Add(Heroname, HeroID);
                            this.listView5.EndUpdate();
                        }
                        if (HeroLEIXING=="QITA")
                        {

                            

                            this.listView6.BeginUpdate();
                            this.imageList6.Images.Add(HeroID, icon);

                            this.listView6.Items.Add(Heroname, HeroID);
                            this.listView6.EndUpdate();
                        }
                    }





                }




                Form1.form1.T_msg("排位英雄数据初始化完毕!", 3, 5);
                Form1.form1.Rizhi("排位英雄数据初始化完毕");
                Form1.form1.PPWWEE = true;

                var Top1 = Util.ReadIniData("AutomaticHero", "WeiTOP1", "123", @".\Data\Cofing.ini");
                var Top2 = Util.ReadIniData("AutomaticHero", "WeiTOP2", "123", @".\Data\Cofing.ini");
                var Top3 = Util.ReadIniData("AutomaticHero", "WeiTOP3", "123", @".\Data\Cofing.ini");

                var Jg1 = Util.ReadIniData("AutomaticHero", "WeiJG1", "123", @".\Data\Cofing.ini");
                var Jg2 = Util.ReadIniData("AutomaticHero", "WeiJG2", "123", @".\Data\Cofing.ini");
                var Jg3 = Util.ReadIniData("AutomaticHero", "WeiJG3", "123", @".\Data\Cofing.ini");

                var Mid1 = Util.ReadIniData("AutomaticHero", "WeiMID1", "123", @".\Data\Cofing.ini");
                var Mid2 = Util.ReadIniData("AutomaticHero", "WeiMID2", "123", @".\Data\Cofing.ini");
                var Mid3 = Util.ReadIniData("AutomaticHero", "WeiMID3", "123", @".\Data\Cofing.ini");

                var Adc1 = Util.ReadIniData("AutomaticHero", "WeiADC1", "123", @".\Data\Cofing.ini");
                var Adc2 = Util.ReadIniData("AutomaticHero", "WeiADC2", "123", @".\Data\Cofing.ini");
                var Adc3 = Util.ReadIniData("AutomaticHero", "WeiADC3", "123", @".\Data\Cofing.ini");

                var Sup1 = Util.ReadIniData("AutomaticHero", "WeiSUP1", "123", @".\Data\Cofing.ini");
                var Sup2 = Util.ReadIniData("AutomaticHero", "WeiSUP2", "123", @".\Data\Cofing.ini");
                var Sup3 = Util.ReadIniData("AutomaticHero", "WeiSUP3", "123", @".\Data\Cofing.ini");

                if (HUHU.Count > 0)
                {
                    for (var i = 0; i < HUHU.Count; i++)
                    {

                        if (HUHU[i] == Top1)
                        {
                            Form1.WeiTOP[0] = Top1;
                            Form1.paiwei.TOP1.Image = TOP1;

                        }

                        if (HUHU[i] == Top2)
                        {
                            Form1.WeiTOP[1] = Top2;
                            Form1.paiwei.TOP2.Image = TOP2;

                        }
                        if (HUHU[i] == Top3)
                        {
                            Form1.WeiTOP[2] = Top3;
                            Form1.paiwei.TOP3.Image = TOP3;

                        }
                        //=========================================================================================================================
                        if (HUHU[i] == Jg1)
                        {
                            Form1.WeiJG[0] = Jg1;
                            Form1.paiwei.JG1.Image = JG1;

                        }

                        if (HUHU[i] == Jg2)
                        {
                            Form1.WeiJG[1] = Jg2;
                            Form1.paiwei.JG2.Image = JG2;

                        }
                        if (HUHU[i] == Jg3)
                        {
                            Form1.WeiJG[2] = Jg3;
                            Form1.paiwei.JG3.Image = JG3;

                        }
                        //=========================================================================================================================
                        if (HUHU[i] == Mid1)
                        {
                            Form1.WeiMID[0] = Mid1;
                            Form1.paiwei.MID1.Image = MID1;

                        }

                        if (HUHU[i] == Mid2)
                        {
                            Form1.WeiMID[1] = Mid2;
                            Form1.paiwei.MID2.Image = MID2;

                        }
                        if (HUHU[i] == Mid3)
                        {
                            Form1.WeiMID[2] = Mid3;
                            Form1.paiwei.MID3.Image = MID3;

                        }
                        //=========================================================================================================================
                        if (HUHU[i] == Adc1)
                        {
                            Form1.WeiADC[0] = Adc1;
                            Form1.paiwei.ADC1.Image = ADC1;

                        }

                        if (HUHU[i] == Adc2)
                        {
                            Form1.WeiADC[1] = Adc2;
                            Form1.paiwei.ADC2.Image = ADC2;

                        }
                        if (HUHU[i] == Adc3)
                        {
                            Form1.WeiADC[2] = Adc3;
                            Form1.paiwei.ADC3.Image = ADC3;

                        }
                        //=========================================================================================================================
                        if (HUHU[i] == Sup1)
                        {
                            Form1.WeiSUP[0] = Sup1;
                            Form1.paiwei.SUP1.Image = SUP1;

                        }

                        if (HUHU[i] == Sup2)
                        {
                            Form1.WeiSUP[1] = Sup2;
                            Form1.paiwei.SUP2.Image = SUP2;

                        }
                        if (HUHU[i] == Sup3)
                        {
                            Form1.WeiSUP[2] = Sup3;
                            Form1.paiwei.SUP3.Image = SUP3;

                        }
                    }
                }



                Form1.form1.InitializationG = 4;

                return true;
            }
            else
            {
                chaog++;
                if (chaog > 2)
                {
                    Form1.form1.Rizhi("排位英雄数据初始化失败");
                    chaog = 0;
                    return true;
                }
                    

                return false;
            }
        }
        public void Getrunninginitialization()
        {
            bool wanbi = false;
            while (true)
            {


                var HeroData = Util.zi_Getwenb("/lol-champions/v1/owned-champions-minimal");

                var HeroDataJX = JSON.Jiexun_1(HeroData);

                try
                {
                    if (Form1.form1.QQQQQQQQQ || Form1.form1.EEEEEEEEEEEE)
                        break;


                    Form1.PweiDataKun.Clear();

                    if (HeroDataJX.Count > 0)
                    {

                        HUHU.Clear();

                        for (var i = 0; i < HeroDataJX.Count; i++)
                        {

                            var owned = HeroDataJX[i]["ownership"]["owned"].ToString().ToLower();
                            var freeToPlay = HeroDataJX[i]["freeToPlay"].ToString().ToLower();
                            var xboxGPReward = HeroDataJX[i]["ownership"]["xboxGPReward"].ToString().ToLower();
                            Console.WriteLine("排位初始化owned:" + owned);
                            Console.WriteLine("排位初始化freeToPlay:" + freeToPlay);
                            Console.WriteLine("排位初始化xboxGPReward:" + xboxGPReward);
                            if (owned == "true"|| freeToPlay == "true"|| xboxGPReward == "true")
                            {


                                var HeroID = HeroDataJX[i]["id"].ToString();

                                HUHU.Add(HeroID);

                                var HeroLEIXING = Util.GetheronameWEI(HeroID);

                                var Heroname = HeroDataJX[i]["name"].ToString();

                                var icon = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + HeroID + ".png");

                                if (HeroLEIXING.Contains("TOP"))
                                {
                                    var JUCS = new Form1.BanData();
                                    JUCS.Weizi = "TOP";
                                    JUCS.icon = icon;
                                    JUCS.HeroID = HeroID;
                                    JUCS.Heroname = Heroname;
                                    JUCS.owned = "true";
                                    Form1.PweiDataKun.Add(JUCS);

                                }

                                if (HeroLEIXING.Contains("JUNGLE"))
                                {
                                    var JUCS = new Form1.BanData();
                                    JUCS.Weizi = "JUNGLE";
                                    JUCS.icon = icon;
                                    JUCS.HeroID = HeroID;
                                    JUCS.Heroname = Heroname;
                                    JUCS.owned = "true";
                                    Form1.PweiDataKun.Add(JUCS);
                                }

                                if (HeroLEIXING.Contains("MID"))
                                {

                                    var JUCS = new Form1.BanData();
                                    JUCS.Weizi = "MID";
                                    JUCS.icon = icon;
                                    JUCS.HeroID = HeroID;
                                    JUCS.Heroname = Heroname;
                                    JUCS.owned = "true";
                                    Form1.PweiDataKun.Add(JUCS);
                                }

                                if (HeroLEIXING.Contains("ADC"))
                                {

                                    var JUCS = new Form1.BanData();
                                    JUCS.Weizi = "ADC";
                                    JUCS.icon = icon;
                                    JUCS.HeroID = HeroID;
                                    JUCS.Heroname = Heroname;
                                    JUCS.owned = "true";
                                    Form1.PweiDataKun.Add(JUCS);
                                }

                                if (HeroLEIXING.Contains("SUPPORT"))
                                {

                                    var JUCS = new Form1.BanData();
                                    JUCS.Weizi = "SUPPORT";
                                    JUCS.icon = icon;
                                    JUCS.HeroID = HeroID;
                                    JUCS.Heroname = Heroname;
                                    JUCS.owned = "true";
                                    Form1.PweiDataKun.Add(JUCS);
                                }
                                if (HeroLEIXING.Count() == 0)
                                {

                                    var JUCS = new Form1.BanData();
                                    JUCS.Weizi = "QITA";
                                    JUCS.icon = icon;
                                    JUCS.HeroID = HeroID;
                                    JUCS.Heroname = Heroname;
                                    JUCS.owned = "true";
                                    Form1.PweiDataKun.Add(JUCS);

                                }
                            }





                        }


                        

                        var Top1 = Util.ReadIniData("AutomaticHero", "WeiTOP1", "123", @".\Data\Cofing.ini");
                        var Top2 = Util.ReadIniData("AutomaticHero", "WeiTOP2", "123", @".\Data\Cofing.ini");
                        var Top3 = Util.ReadIniData("AutomaticHero", "WeiTOP3", "123", @".\Data\Cofing.ini");

                        var Jg1 = Util.ReadIniData("AutomaticHero", "WeiJG1", "123", @".\Data\Cofing.ini");
                        var Jg2 = Util.ReadIniData("AutomaticHero", "WeiJG2", "123", @".\Data\Cofing.ini");
                        var Jg3 = Util.ReadIniData("AutomaticHero", "WeiJG3", "123", @".\Data\Cofing.ini");

                        var Mid1 = Util.ReadIniData("AutomaticHero", "WeiMID1", "123", @".\Data\Cofing.ini");
                        var Mid2 = Util.ReadIniData("AutomaticHero", "WeiMID2", "123", @".\Data\Cofing.ini");
                        var Mid3 = Util.ReadIniData("AutomaticHero", "WeiMID3", "123", @".\Data\Cofing.ini");

                        var Adc1 = Util.ReadIniData("AutomaticHero", "WeiADC1", "123", @".\Data\Cofing.ini");
                        var Adc2 = Util.ReadIniData("AutomaticHero", "WeiADC2", "123", @".\Data\Cofing.ini");
                        var Adc3 = Util.ReadIniData("AutomaticHero", "WeiADC3", "123", @".\Data\Cofing.ini");

                        var Sup1 = Util.ReadIniData("AutomaticHero", "WeiSUP1", "123", @".\Data\Cofing.ini");
                        var Sup2 = Util.ReadIniData("AutomaticHero", "WeiSUP2", "123", @".\Data\Cofing.ini");
                        var Sup3 = Util.ReadIniData("AutomaticHero", "WeiSUP3", "123", @".\Data\Cofing.ini");

                        if (HUHU.Count > 0)
                        {
                            for (var i = 0; i < HUHU.Count; i++)
                            {

                                if (HUHU[i] == Top1)
                                {
                                  
                                    TOP1 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Top1 + ".png");

                                   

                                }

                                if (HUHU[i] == Top2)
                                {
                                  
                                    TOP2 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Top2 + ".png");
  

                                }
                                if (HUHU[i] == Top3)
                                {

                                    TOP3 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Top3 + ".png");
        

                                }
                                //=========================================================================================================================
                                if (HUHU[i] == Jg1)
                                {
      
                                    JG1 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Jg1 + ".png");
                                  

                                }

                                if (HUHU[i] == Jg2)
                                {
   
                                    JG2 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Jg2 + ".png");


                                }
                                if (HUHU[i] == Jg3)
                                {

                                    JG3 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Jg3 + ".png");

                                }
                                //=========================================================================================================================
                                if (HUHU[i] == Mid1)
                                {

                                    MID1 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Mid1 + ".png");
  

                                }

                                if (HUHU[i] == Mid2)
                                {
                                    MID2 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Mid2 + ".png");

                                }
                                if (HUHU[i] == Mid3)
                                {
                                    MID3 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Mid3 + ".png");

                                }
                                //=========================================================================================================================
                                if (HUHU[i] == Adc1)
                                {
                                    ADC1 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Adc1 + ".png");

                                }

                                if (HUHU[i] == Adc2)
                                {
                                    ADC2 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Adc2 + ".png");
                                }
                                if (HUHU[i] == Adc3)
                                {
                                    ADC3 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Adc3 + ".png");

                                }
                                //=========================================================================================================================
                                if (HUHU[i] == Sup1)
                                {
                                    SUP1 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Sup1 + ".png");

                                }

                                if (HUHU[i] == Sup2)
                                {
                                    SUP2 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Sup2 + ".png");

                                }
                                if (HUHU[i] == Sup3)
                                {
                                    SUP3 = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Sup3 + ".png");

                                }
                            }
                        }




                        if (Form1.form1.pictureBox3.InvokeRequired)
                        {

                            Form1.form1.pictureBox3.Invoke(new Action<int>(n =>
                            {
                                if (dddddddd())
                                    wanbi = true;






                            }), 1);

                        }
                        else
                        {
                            Form1.form1.pictureBox3.Enabled = false;
                            if (dddddddd())
                                wanbi = true;


                        }

                    }
                    


                    


                    if (wanbi)
                        break;



                }
                catch (Exception e)
                {

                    Console.WriteLine(e.Message);
                }


                Thread.Sleep(100);




            }




        }


        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {


            InitializationHero(((ListView)sender));



        }


        private void CAO_TOP(int XQ, string HAHAID, Image QQQWW)
        {
            if (XQ == 1 && Form1.WeiTOP[1] == HAHAID)//第一个和第二个
            {
                var Banid = Form1.WeiTOP[0];

                var BanTP = Form1.paiwei.TOP1.Image;


                Form1.WeiTOP[1] = Banid;

                Form1.paiwei.TOP2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiTOP2", Banid, @".\Data\Cofing.ini");

            }
            else if (XQ == 1 && Form1.WeiTOP[2] == HAHAID)//第一个和第三个
            {
                var Banid = Form1.WeiTOP[0];

                var BanTP = Form1.paiwei.TOP1.Image;


                Form1.WeiTOP[2] = Banid;

                Form1.paiwei.TOP3.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiTOP3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiTOP[0] == HAHAID)//第二个和第一个
            {
                var Banid = Form1.WeiTOP[1];

                var BanTP = Form1.paiwei.TOP2.Image;


                Form1.WeiTOP[0] = Banid;

                Form1.paiwei.TOP1.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiTOP1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiTOP[2] == HAHAID)//第二个和第三个
            {
                var Banid = Form1.WeiTOP[1];

                var BanTP = Form1.paiwei.TOP2.Image;


                Form1.WeiTOP[2] = Banid;

                Form1.paiwei.TOP3.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiTOP3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiTOP[0] == HAHAID)//第三个和第一个
            {
                var Banid = Form1.WeiTOP[2];

                var BanTP = Form1.paiwei.TOP3.Image;


                Form1.WeiTOP[0] = Banid;

                Form1.paiwei.TOP1.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiTOP1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiTOP[1] == HAHAID)//第三个和第二个
            {
                var Banid = Form1.WeiTOP[2];

                var BanTP = Form1.paiwei.TOP3.Image;


                Form1.WeiTOP[1] = Banid;

                Form1.paiwei.TOP2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiTOP2", Banid, @".\Data\Cofing.ini");
            }


            if (XQ == 1)
            {
                Form1.WeiTOP[0] = HAHAID;
                Form1.paiwei.TOP1.Image = QQQWW;
            }
            else if (XQ == 2)
            {

                Form1.WeiTOP[1] = HAHAID;
                Form1.paiwei.TOP2.Image = QQQWW;
            }
            else if (XQ == 3)
            {

                Form1.WeiTOP[2] = HAHAID;
                Form1.paiwei.TOP3.Image = QQQWW;
            }


            Util.WriteIniData("AutomaticHero", "WeiTOP" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");

            this.Close();
        }


        private void CAO_JG(int XQ, string HAHAID, Image QQQWW)
        {
            if (XQ == 1 && Form1.WeiJG[1] == HAHAID)//第一个和第二个
            {
                var Banid = Form1.WeiJG[0];

                var BanTP = Form1.paiwei.JG1.Image;


                Form1.WeiJG[1] = Banid;

                Form1.paiwei.JG2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiJG2", Banid, @".\Data\Cofing.ini");

            }
            else if (XQ == 1 && Form1.WeiJG[2] == HAHAID)//第一个和第三个
            {
                var Banid = Form1.WeiJG[0];

                var BanTP = Form1.paiwei.JG1.Image;


                Form1.WeiJG[2] = Banid;

                Form1.paiwei.JG3.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiJG3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiJG[0] == HAHAID)//第二个和第一个
            {
                var Banid = Form1.WeiJG[1];

                var BanTP = Form1.paiwei.JG2.Image;


                Form1.WeiJG[0] = Banid;

                Form1.paiwei.JG1.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiJG1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiJG[2] == HAHAID)//第二个和第三个
            {
                var Banid = Form1.WeiJG[1];

                var BanTP = Form1.paiwei.JG2.Image;


                Form1.WeiJG[2] = Banid;

                Form1.paiwei.JG3.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiJG3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiJG[0] == HAHAID)//第三个和第一个
            {
                var Banid = Form1.WeiJG[2];

                var BanTP = Form1.paiwei.JG3.Image;


                Form1.WeiJG[0] = Banid;

                Form1.paiwei.JG1.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiJG1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiJG[1] == HAHAID)//第三个和第二个
            {
                var Banid = Form1.WeiJG[2];

                var BanTP = Form1.paiwei.JG3.Image;


                Form1.WeiJG[1] = Banid;

                Form1.paiwei.JG2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiJG2", Banid, @".\Data\Cofing.ini");
            }


            if (XQ == 1)
            {
                Form1.WeiJG[0] = HAHAID;
                Form1.paiwei.JG1.Image = QQQWW;
            }
            else if (XQ == 2)
            {

                Form1.WeiJG[1] = HAHAID;
                Form1.paiwei.JG2.Image = QQQWW;
            }
            else if (XQ == 3)
            {

                Form1.WeiJG[2] = HAHAID;
                Form1.paiwei.JG3.Image = QQQWW;
            }


            Util.WriteIniData("AutomaticHero", "WeiJG" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");

            this.Close();
        }


        private void CAO_MID(int XQ, string HAHAID, Image QQQWW)
        {
            if (XQ == 1 && Form1.WeiMID[1] == HAHAID)//第一个和第二个
            {
                var Banid = Form1.WeiMID[0];

                var BanTP = Form1.paiwei.MID1.Image;


                Form1.WeiMID[1] = Banid;

                Form1.paiwei.MID2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiMID2", Banid, @".\Data\Cofing.ini");

            }
            else if (XQ == 1 && Form1.WeiMID[2] == HAHAID)//第一个和第三个
            {
                var Banid = Form1.WeiMID[0];

                var BanTP = Form1.paiwei.MID1.Image;


                Form1.WeiMID[2] = Banid;

                Form1.paiwei.MID3.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiMID3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiMID[0] == HAHAID)//第二个和第一个
            {
                var Banid = Form1.WeiMID[1];

                var BanTP = Form1.paiwei.MID2.Image;


                Form1.WeiMID[0] = Banid;

                Form1.paiwei.MID1.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiMID1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiMID[2] == HAHAID)//第二个和第三个
            {
                var Banid = Form1.WeiMID[1];

                var BanTP = Form1.paiwei.MID2.Image;


                Form1.WeiMID[2] = Banid;

                Form1.paiwei.MID3.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiMID3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiMID[0] == HAHAID)//第三个和第一个
            {
                var Banid = Form1.WeiMID[2];

                var BanTP = Form1.paiwei.MID3.Image;


                Form1.WeiMID[0] = Banid;

                Form1.paiwei.MID1.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiMID1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiMID[1] == HAHAID)//第三个和第二个
            {
                var Banid = Form1.WeiMID[2];

                var BanTP = Form1.paiwei.MID3.Image;


                Form1.WeiMID[1] = Banid;

                Form1.paiwei.MID2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiMID2", Banid, @".\Data\Cofing.ini");
            }


            if (XQ == 1)
            {
                Form1.WeiMID[0] = HAHAID;
                Form1.paiwei.MID1.Image = QQQWW;
            }
            else if (XQ == 2)
            {

                Form1.WeiMID[1] = HAHAID;
                Form1.paiwei.MID2.Image = QQQWW;
            }
            else if (XQ == 3)
            {

                Form1.WeiMID[2] = HAHAID;
                Form1.paiwei.MID3.Image = QQQWW;
            }


            Util.WriteIniData("AutomaticHero", "WeiMID" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");

            Form1.paiweiget.Close();
        }

        private void CAO_ADC(int XQ, string HAHAID, Image QQQWW)
        {
            if (XQ == 1 && Form1.WeiADC[1] == HAHAID)//第一个和第二个
            {
                var Banid = Form1.WeiADC[0];

                var BanTP = Form1.paiwei.ADC1.Image;


                Form1.WeiADC[1] = Banid;

                Form1.paiwei.ADC2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiADC2", Banid, @".\Data\Cofing.ini");

            }
            else if (XQ == 1 && Form1.WeiADC[2] == HAHAID)//第一个和第三个
            {
                var Banid = Form1.WeiADC[0];

                var BanTP = Form1.paiwei.ADC1.Image;


                Form1.WeiADC[2] = Banid;

                Form1.paiwei.ADC3.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiADC3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiADC[0] == HAHAID)//第二个和第一个
            {
                var Banid = Form1.WeiADC[1];

                var BanTP = Form1.paiwei.ADC2.Image;


                Form1.WeiADC[0] = Banid;

                Form1.paiwei.ADC1.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiADC1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiADC[2] == HAHAID)//第二个和第三个
            {
                var Banid = Form1.WeiADC[1];

                var BanTP = Form1.paiwei.ADC2.Image;


                Form1.WeiADC[2] = Banid;

                Form1.paiwei.ADC3.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiADC3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiADC[0] == HAHAID)//第三个和第一个
            {
                var Banid = Form1.WeiADC[2];

                var BanTP = Form1.paiwei.ADC3.Image;


                Form1.WeiADC[0] = Banid;

                Form1.paiwei.ADC1.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiADC1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiADC[1] == HAHAID)//第三个和第二个
            {
                var Banid = Form1.WeiADC[2];

                var BanTP = Form1.paiwei.ADC3.Image;


                Form1.WeiADC[1] = Banid;

                Form1.paiwei.ADC2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiADC2", Banid, @".\Data\Cofing.ini");
            }


            if (XQ == 1)
            {
                Form1.WeiADC[0] = HAHAID;
                Form1.paiwei.ADC1.Image = QQQWW;
            }
            else if (XQ == 2)
            {

                Form1.WeiADC[1] = HAHAID;
                Form1.paiwei.ADC2.Image = QQQWW;
            }
            else if (XQ == 3)
            {

                Form1.WeiADC[2] = HAHAID;
                Form1.paiwei.ADC3.Image = QQQWW;
            }


            Util.WriteIniData("AutomaticHero", "WeiADC" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");

            this.Close();
        }

        private void CAO_SUP(int XQ, string HAHAID, Image QQQWW)
        {
            if (XQ == 1 && Form1.WeiSUP[1] == HAHAID)//第一个和第二个
            {
                var Banid = Form1.WeiSUP[0];

                var BanTP = Form1.paiwei.SUP1.Image;


                Form1.WeiSUP[1] = Banid;

                Form1.paiwei.SUP2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiSUP2", Banid, @".\Data\Cofing.ini");

            }
            else if (XQ == 1 && Form1.WeiSUP[2] == HAHAID)//第一个和第三个
            {
                var Banid = Form1.WeiSUP[0];

                var BanTP = Form1.paiwei.SUP1.Image;


                Form1.WeiSUP[2] = Banid;

                Form1.paiwei.SUP3.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiSUP3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiSUP[0] == HAHAID)//第二个和第一个
            {
                var Banid = Form1.WeiSUP[1];

                var BanTP = Form1.paiwei.SUP2.Image;


                Form1.WeiSUP[0] = Banid;

                Form1.paiwei.SUP1.Image = BanTP;
                Util.WriteIniData("AutomaticHero", "WeiSUP1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 2 && Form1.WeiSUP[2] == HAHAID)//第二个和第三个
            {
                var Banid = Form1.WeiSUP[1];

                var BanTP = Form1.paiwei.SUP2.Image;


                Form1.WeiSUP[2] = Banid;

                Form1.paiwei.SUP3.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiSUP3", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiSUP[0] == HAHAID)//第三个和第一个
            {
                var Banid = Form1.WeiSUP[2];

                var BanTP = Form1.paiwei.SUP3.Image;


                Form1.WeiSUP[0] = Banid;

                Form1.paiwei.SUP1.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiSUP1", Banid, @".\Data\Cofing.ini");
            }
            else if (XQ == 3 && Form1.WeiSUP[1] == HAHAID)//第三个和第二个
            {
                var Banid = Form1.WeiSUP[2];

                var BanTP = Form1.paiwei.SUP3.Image;


                Form1.WeiSUP[1] = Banid;

                Form1.paiwei.SUP2.Image = BanTP;

                Util.WriteIniData("AutomaticHero", "WeiSUP2", Banid, @".\Data\Cofing.ini");
            }


            if (XQ == 1)
            {
                Form1.WeiSUP[0] = HAHAID;
                Form1.paiwei.SUP1.Image = QQQWW;
            }
            else if (XQ == 2)
            {

                Form1.WeiSUP[1] = HAHAID;
                Form1.paiwei.SUP2.Image = QQQWW;
            }
            else if (XQ == 3)
            {

                Form1.WeiSUP[2] = HAHAID;
                Form1.paiwei.SUP3.Image = QQQWW;
            }


            Util.WriteIniData("AutomaticHero", "WeiSUP" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");

            this.Close();
        }

        private void InitializationHero(ListView listViewYY /*ImageList GO*/)
        {

            if (listViewYY.SelectedItems[0].Index != -1)
            {

                var index1 = listViewYY.SelectedItems[0].Index;
                Image QQQWW = null;
                switch (listViewYY.Name)
                {

                    case "listView1":
                        QQQWW = imageList1.Images[index1];
                        break;
                    case "listView2":
                        QQQWW = imageList2.Images[index1];
                        break;
                    case "listView3":
                        QQQWW = imageList3.Images[index1];
                        break;
                    case "listView4":
                        QQQWW = imageList4.Images[index1];
                        break;
                    case "listView5":
                        QQQWW = imageList5.Images[index1];
                        break;
                    case "listView6":
                        QQQWW = imageList6.Images[index1];
                        break;
                    default:
                        break;
                }
                if (QQQWW != null)
                {


                    var HAHAID = listViewYY.Items[index1].ImageKey;

                    string XQNAKE = Paiwei.XINHAO;

                    int XQ = Paiwei.XINHAOINT;

                    if (XQNAKE.IndexOf("TOP") != -1)
                    {
                        CAO_TOP(XQ, HAHAID, QQQWW);
                    }

                    if (XQNAKE.IndexOf("JG") != -1)
                    {
                        CAO_JG(XQ, HAHAID, QQQWW);
                    }

                    if (XQNAKE.IndexOf("MID") != -1)
                    {
                        CAO_MID(XQ, HAHAID, QQQWW);
                    }

                    if (XQNAKE.IndexOf("ADC") != -1)
                    {
                        CAO_ADC(XQ, HAHAID, QQQWW);
                    }

                    if (XQNAKE.IndexOf("SUP") != -1)
                    {
                        CAO_SUP(XQ, HAHAID, QQQWW);
                    }



                }


            }


        }
      

    }
}

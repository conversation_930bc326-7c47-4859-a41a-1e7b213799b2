                    else if (_screenCaptureMethod == "ddagrab") // 直接转到ddagrab
                    {
                        //https://ayosec.github.io/ffmpeg-filters-docs/7.1/Sources/Video/ddagrab.html
                        ffmpegArgs = $"-hide_banner -loglevel error -f lavfi " +
                                      $"-hwaccel cuda -hwaccel_output_format cuda " + // 添加CUDA硬件加速和输出格式
                                      $"-probesize 32M -analyzeduration 16M " + // 增加探测缓冲区和分析时长
                                      $"-i \"ddagrab=framerate={_frameRate}:output_fmt=auto:video_size={_captureWidth}x{_captureHeight}:offset_x={_offsetX}:offset_y={_offsetY}\" " + // 使用DirectX捕获方式，配置帧率、输出格式、尺寸和偏移
                                      $"-vsync 0 " + // 防止帧丢失错误
                                      $"-c:v {selectedEncoder} {qualityParam} " + // 使用传入的质量参数
                                      (selectedEncoder.Contains("nvenc") ? $"-preset p7 -tune ll " : $"-preset {presetToUse} ") + // 为NVENC添加特定预设
                                      $"-g {_frameRate * 10} -keyint_min {_frameRate * 3} -sc_threshold 0 " + // 配置关键帧参数
                                      $"-b:v {(int)(int.Parse(GetMaxBitrate(_captureWidth, _captureHeight, _frameRate).TrimEnd('k')) * 0.7)}k " + // 增加比特率
                                      $"-maxrate {(int)(int.Parse(GetMaxBitrate(_captureWidth, _captureHeight, _frameRate).TrimEnd('k')) * 0.8)}k " + // 增加最大比特率
                                      $"-bufsize {(int)(int.Parse(GetBufferSize(_captureWidth, _captureHeight, _frameRate).TrimEnd('k')) * 2)}k " + // 增加缓冲区大小提高稳定性
                                      $"-profile:v high -movflags +faststart -r {_frameRate} -y \"{safeFilePath}\""; // 指定输出文件路径，覆盖已有文件
                        
                        OnLogMessage($"使用优化后的ddagrab DirectX捕获方式。已添加CUDA硬件加速和vsync参数修复AcquireNextFrame错误。");
                        
                        OnLogMessage($"[调试 ddagrab with optimized output] FFmpeg 参数: {ffmpegArgs}");
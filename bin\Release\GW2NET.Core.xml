<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GW2NET.Core</name>
    </assembly>
    <members>
        <member name="T:GW2NET.Builds.Build">
            <summary>Represents the current build of the game.</summary>
            <remarks>See <a href="http://wiki.guildwars2.com/wiki/API:1/build" /> for more information.</remarks>
        </member>
        <member name="P:GW2NET.Builds.Build.BuildId">
            <summary>Gets or sets the current build identifier of the game.</summary>
        </member>
        <member name="P:GW2NET.Builds.Build.Timestamp">
            <summary>Gets or sets a timestamp for this build.</summary>
        </member>
        <member name="M:GW2NET.Builds.Build.op_Equality(GW2NET.Builds.Build,GW2NET.Builds.Build)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.op_GreaterThan(GW2NET.Builds.Build,GW2NET.Builds.Build)">
            <summary>Indicates whether a build is greater than another.</summary>
            <param name="left">The build on the left side.</param>
            <param name="right">The build on the right side.</param>
            <returns>true if the <paramref name="left" /> build is greater than the <paramref name="right" /> build; otherwise, false</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.op_GreaterThanOrEqual(GW2NET.Builds.Build,GW2NET.Builds.Build)">
            <summary>Indicates whether a build is greater or equal to another.</summary>
            <param name="left">The build on the left side.</param>
            <param name="right">The build on the right side.</param>
            <returns>true if the <paramref name="left" /> build is greater or equal to the <paramref name="right" /> build; otherwise, false</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.op_Inequality(GW2NET.Builds.Build,GW2NET.Builds.Build)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.op_LessThan(GW2NET.Builds.Build,GW2NET.Builds.Build)">
            <summary>Indicates whether a build is greater than another.</summary>
            <param name="left">The build on the left side.</param>
            <param name="right">The build on the right side.</param>
            <returns>true if the <paramref name="left" /> build is smaller than the <paramref name="right" /> build; otherwise, false</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.op_LessThanOrEqual(GW2NET.Builds.Build,GW2NET.Builds.Build)">
            <summary>Indicates whether a build is smaller or equal to another.</summary>
            <param name="left">The build on the left side.</param>
            <param name="right">The build on the right side.</param>
            <returns>true if the <paramref name="left" /> build is smaller or equal to the <paramref name="right" /> build; otherwise, false</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.CompareTo(GW2NET.Builds.Build)">
            <summary>Compares the current object with another object of the same type.</summary>
            <returns>A value that indicates the relative order of the objects being compared. The return value has the following meanings: Value Meaning Less than zero This object is less than the <paramref name="other"/> parameter.Zero This object is equal to <paramref name="other"/>. Greater than zero This object is greater than<paramref name="other"/>.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Builds.Build.Equals(GW2NET.Builds.Build)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Builds.Build.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Builds.Build.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Builds.Build.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.ChatLinks.ChatLink">
            <summary>Provides the base class for chat links.</summary>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLink.#cctor">
            <summary>Initializes static members of the <see cref="T:GW2NET.ChatLinks.ChatLink"/> class.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.ChatLink.Factory">
            <summary>
            Gets a reference to the factory class that provides chat link factory methods.
            </summary>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLink.CopyTo(GW2NET.ChatLinks.Interop.ChatLinkStruct)">
            <summary>Copies the current instance to a data structure for serialization. A return value indicates the number of bytes copied.</summary>
            <param name="value">An object to copy values to.</param>
            <returns>The number of significant bytes in the data structure after copying.</returns>
        </member>
        <member name="T:GW2NET.ChatLinks.ChatLinkFactory">
            <summary>Factory class. Provides factory methods for creating <see cref="T:GW2NET.ChatLinks.ChatLink"/> instances.</summary>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.Decode(System.String)">
            <summary>Decodes chat links.</summary>
            <param name="input">A chat link.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="input"/> is a null reference.</exception>
            <returns>A decoded <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.Decode``1(System.String)">
            <summary>Decodes chat links of the specified type.</summary>
            <param name="input">A chat link.</param>
            <typeparam name="T">The chat link type.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="input"/> is a null reference.</exception>
            <returns>A decoded <see cref="T:GW2NET.ChatLinks.ChatLink"/> of the specified type.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeCoins(System.Int32)">
            <summary>Encodes an amount of coins.</summary>
            <param name="quantity">The quantity.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeDialog(System.Int32)">
            <summary>Encodes a dialog.</summary>
            <param name="dialogId">The dialog identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeItem(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>Encodes an item.</summary>
            <param name="itemId">The item identifier.</param>
            <param name="quantity">The quantity.</param>
            <param name="suffixItemId">The suffix item identifier.</param>
            <param name="secondarySuffixItemId">The secondary suffix item identifier.</param>
            <param name="skinId">The skin identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeOutfit(System.Int32)">
            <summary>Encodes an outfit.</summary>
            <param name="outfitId">The outfit identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodePointOfInterest(System.Int32)">
            <summary>Encodes a point of interest.</summary>
            <param name="pointOfInterestId">The point of interest identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeRecipe(System.Int32)">
            <summary>Encodes a recipe.</summary>
            <param name="recipeId">The recipe identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeSkill(System.Int32)">
            <summary>Encodes a skill.</summary>
            <param name="skillId">The skill identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeSkin(System.Int32)">
            <summary>Encodes a skin.</summary>
            <param name="skinId">The skin identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.ChatLinks.ChatLinkFactory.EncodeTrait(System.Int32)">
            <summary>Encodes a trait.</summary>
            <param name="traitId">The trait identifier.</param>
            <returns>A <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="T:GW2NET.ChatLinks.CoinChatLink">
            <summary>Represents a chat link that links to an amount of coins.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.CoinChatLink.Quantity">
            <summary>Gets or sets the quantity.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.DialogChatLink">
            <summary>Represents a chat link that links to a dialog.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.DialogChatLink.DialogId">
            <summary>Gets or sets the dialog identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.ItemChatLink">
            <summary>Represents a chat link that links to an item.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.ItemChatLink.ItemId">
            <summary>Gets or sets the item identifier.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.ItemChatLink.Quantity">
            <summary>Gets or sets an item quantity between 1 and 255, both inclusive.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.ItemChatLink.SecondarySuffixItemId">
            <summary>Gets or sets the secondary upgrade identifier.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.ItemChatLink.SkinId">
            <summary>Gets or sets the skin identifier.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.ItemChatLink.SuffixItemId">
            <summary>Gets or sets the upgrade identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.OutfitChatLink">
            <summary>Represents a chat link that links to an outfit.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.OutfitChatLink.OutfitId">
            <summary>Gets or sets the outfit identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.PointOfInterestChatLink">
            <summary>Represents a chat link that links to a point of interest.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.PointOfInterestChatLink.PointOfInterestId">
            <summary>Gets or sets the point of interest identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.RecipeChatLink">
            <summary>Represents a chat link that links to a recipe.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.RecipeChatLink.RecipeId">
            <summary>Gets or sets the recipe identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.SkillChatLink">
            <summary>Represents a chat link that links to a skill.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.SkillChatLink.SkillId">
            <summary>Gets or sets the skill identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.SkinChatLink">
            <summary>Represents a chat link that links to a wardrobe skin.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.SkinChatLink.SkinId">
            <summary>Gets or sets the skin identifier.</summary>
        </member>
        <member name="T:GW2NET.ChatLinks.TraitChatLink">
            <summary>Represents a chat link that links to a trait.</summary>
        </member>
        <member name="P:GW2NET.ChatLinks.TraitChatLink.TraitId">
            <summary>Gets or sets the trait identifier.</summary>
        </member>
        <member name="T:GW2NET.Colors.Color">
            <summary>Represents an RGB color.</summary>
        </member>
        <member name="M:GW2NET.Colors.Color.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Colors.Color"/> struct.</summary>
            <param name="r">The red component value.</param>
            <param name="g">The green component value.</param>
            <param name="b">The blue component value.</param>
        </member>
        <member name="P:GW2NET.Colors.Color.B">
            <summary>Gets or sets the blue component value.</summary>
        </member>
        <member name="P:GW2NET.Colors.Color.G">
            <summary>Gets or sets the green component value.</summary>
        </member>
        <member name="P:GW2NET.Colors.Color.R">
            <summary>Gets or sets the red component value.</summary>
        </member>
        <member name="T:GW2NET.Colors.ColorModel">
            <summary>Represents a color's component information.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorModel.Brightness">
            <summary>Gets or sets the brightness.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorModel.Contrast">
            <summary>Gets or sets the contrast.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorModel.Hue">
            <summary>Gets or sets the hue in the HSL color space.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorModel.Lightness">
            <summary>Gets or sets the lightness in the HSL color space.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorModel.Rgb">
            <summary>Gets or sets the color.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorModel.Saturation">
            <summary>Gets or sets the saturation in the HSL color space.</summary>
        </member>
        <member name="T:GW2NET.Colors.ColorPalette">
            <summary>Represents a named color and its color component information for cloth, leather and metal materials.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.BaseRgb">
            <summary>Gets or sets the base RGB values.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.Cloth">
            <summary>Gets or sets the color model for cloth armor.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.ColorId">
            <summary>Gets or sets the color identifier.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.Leather">
            <summary>Gets or sets the color model for leather armor.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.Metal">
            <summary>Gets or sets the color model for metal armor.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.Name">
            <summary>Gets or sets the name of the color.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.ItemId">
            <summary>Gets or sets the item identifier of the item that unlocks this color.</summary>
        </member>
        <member name="P:GW2NET.Colors.ColorPalette.item">
            <summary>Gets or sets the item that unlocks this color.</summary>
        </member>
        <member name="M:GW2NET.Colors.ColorPalette.op_Equality(GW2NET.Colors.ColorPalette,GW2NET.Colors.ColorPalette)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Colors.ColorPalette.op_Inequality(GW2NET.Colors.ColorPalette,GW2NET.Colors.ColorPalette)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Colors.ColorPalette.Equals(GW2NET.Colors.ColorPalette)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Colors.ColorPalette.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The object to compare with the current object. </param>
        </member>
        <member name="M:GW2NET.Colors.ColorPalette.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:GW2NET.Colors.ColorPalette.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Colors.IColorRepository">
            <summary>Provides the interface for repositories that provide localized color data.</summary>
        </member>
        <member name="T:GW2NET.Commerce.AggregateListing">
            <summary>Represents aggregate buy or sell offer listing information.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateListing.BuyOffers">
            <summary>Gets or sets the buy offers.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateListing.Item">
            <summary>Gets or sets the item. This is a navigation property. Use the value of <see cref="P:GW2NET.Commerce.AggregateListing.ItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateListing.ItemId">
            <summary>Gets or sets the item identifier.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateListing.Whitelisted">
            <summary>Gets or sets a value indicating whether this item can be acquired in the free version of the game.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateListing.SellOffers">
            <summary>Gets or sets the sell offers.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateListing.Timestamp">
            <summary>Gets or sets the timestamp.</summary>
        </member>
        <member name="M:GW2NET.Commerce.AggregateListing.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Commerce.AggregateOffer">
            <summary>Represents aggregated buy or sell offer information.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateOffer.Quantity">
            <summary>Gets or sets the total number of items listed.</summary>
        </member>
        <member name="P:GW2NET.Commerce.AggregateOffer.UnitPrice">
            <summary>Gets or sets the highest buy order or lowest sell order.</summary>
        </member>
        <member name="M:GW2NET.Commerce.AggregateOffer.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Commerce.GemQuotation">
            <summary>Represents the gems from/to gold exchange rate.</summary>
        </member>
        <member name="P:GW2NET.Commerce.GemQuotation.CoinsPerGem">
            <summary>Gets or sets the coins per gem.</summary>
        </member>
        <member name="P:GW2NET.Commerce.GemQuotation.Id">
            <summary>Gets or sets the identifier.</summary>
        </member>
        <member name="P:GW2NET.Commerce.GemQuotation.Receive">
            <summary>Gets or sets the number of gems/coins to receive.</summary>
        </member>
        <member name="P:GW2NET.Commerce.GemQuotation.Send">
            <summary>Gets or sets the number of gems/coins to send.</summary>
        </member>
        <member name="P:GW2NET.Commerce.GemQuotation.Timestamp">
            <summary>Gets or sets the timestamp.</summary>
        </member>
        <member name="M:GW2NET.Commerce.GemQuotation.GetCoinsPerGemChatLink">
            <summary>Gets a coin chat link for the amount of coins per gem.</summary>
        </member>
        <member name="T:GW2NET.Commerce.IAggregateListingRepository">
            <summary>Provides the interface for repositories that provide aggregate market listings.</summary>
        </member>
        <member name="T:GW2NET.Commerce.IExchangeBroker">
            <summary>Provides the interface for brokers that that provide gem quotations.</summary>
        </member>
        <member name="T:GW2NET.Commerce.IListingRepository">
            <summary>Provides the interface for repositories that provide market listings.</summary>
        </member>
        <member name="T:GW2NET.Commerce.Listing">
            <summary>Represents buy or sell offer listing information.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Listing.BuyOffers">
            <summary>Gets or sets the buy offers.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Listing.Item">
            <summary>Gets or sets the item. This is a navigation property. Use the value of <see cref="P:GW2NET.Commerce.Listing.ItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Listing.ItemId">
            <summary>Gets or sets the item identifier.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Listing.SellOffers">
            <summary>Gets or sets the sell offers.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Listing.Timestamp">
            <summary>Gets or sets the timestamp.</summary>
        </member>
        <member name="M:GW2NET.Commerce.Listing.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Commerce.Offer">
            <summary>Represents buy or sell offer information.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Offer.Listings">
            <summary>Gets or sets the number of offers for this <see cref="P:GW2NET.Commerce.Offer.UnitPrice"/>.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Offer.Quantity">
            <summary>Gets or sets the total number of items listed.</summary>
        </member>
        <member name="P:GW2NET.Commerce.Offer.UnitPrice">
            <summary>Gets or sets the price per unit.</summary>
        </member>
        <member name="M:GW2NET.Commerce.Offer.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Common.BulkRequest">
            <summary>Provides the base class for bulk resource details requests.</summary>
        </member>
        <member name="P:GW2NET.Common.BulkRequest.Identifiers">
            <summary>Gets or sets the identifiers.</summary>
        </member>
        <member name="P:GW2NET.Common.BulkRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Common.BulkRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Common.BulkRequest.GetPathSegments">
            <summary>Gets additional path segments for the targeted resource.</summary>
            <returns>A collection of path segments.</returns>
        </member>
        <member name="T:GW2NET.Common.CollectionPage`1">
            <summary>Represents a subset of values.</summary>
            <typeparam name="T">The type of elements in the subset.</typeparam>
        </member>
        <member name="M:GW2NET.Common.CollectionPage`1.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.CollectionPage`1"/> class that is empty and has the default initial capacity.</summary>
        </member>
        <member name="M:GW2NET.Common.CollectionPage`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.CollectionPage`1"/> class that contains elements copied from the specified collection and has sufficient capacity to accommodate the number of elements copied.</summary>
            <param name="collection">The collection whose elements are copied to the new list.</param>
        </member>
        <member name="M:GW2NET.Common.CollectionPage`1.#ctor(System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.CollectionPage`1"/> class that is empty and has the specified initial capacity.</summary>
            <param name="capacity">The number of elements that the new list can initially store.</param>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.FirstPageIndex">
            <summary>Gets or sets the page index of the first page.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.LastPageIndex">
            <summary>Gets or sets the page index of the last page.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.NextPageIndex">
            <summary>Gets or sets the page index of the next page.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.PageCount">
            <summary>Gets or sets the number of pages.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.PageIndex">
            <summary>Gets or sets the page index of the current page.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.PageSize">
            <summary>Gets or sets the maximum number of items per page.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.PreviousPageIndex">
            <summary>Gets or sets the page index of the previous page.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.SubtotalCount">
            <summary>Gets or sets the number of values in this subset.</summary>
        </member>
        <member name="P:GW2NET.Common.CollectionPage`1.TotalCount">
            <summary>Gets or sets the number of values in the collection.</summary>
        </member>
        <member name="T:GW2NET.Common.ConverterForCollectionPageResponse`2">
            <summary>Converts objects of type <see cref="T:GW2NET.Common.IResponse`1"/> to objects of type <see cref="T:GW2NET.Common.ICollectionPage`1"/>.</summary>
            <typeparam name="TDataContract">The type of data contracts in the response content.</typeparam>
            <typeparam name="TValue">The type of the converted values.</typeparam>
        </member>
        <member name="F:GW2NET.Common.ConverterForCollectionPageResponse`2.converterForDataContract">
            <summary>Infrastructure. Holds a reference to a type converter.</summary>
        </member>
        <member name="M:GW2NET.Common.ConverterForCollectionPageResponse`2.#ctor(GW2NET.Common.IConverter{`0,`1})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ConverterForCollectionPageResponse`2"/> class.</summary>
            <param name="converterForDataContract">The converter for <typeparamref name="TDataContract"/>.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="converterForDataContract"/> is a null reference.</exception>
        </member>
        <member name="M:GW2NET.Common.ConverterForCollectionPageResponse`2.GW2NET#Common#IConverter{GW2NET#Common#IResponse{System#Collections#Generic#ICollection{TDataContract}},GW2NET#Common#ICollectionPage{TValue}}#Convert(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{`0}})">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.ConverterForCollectionResponse`2">
            <summary>Converts objects of type <see cref="T:GW2NET.Common.IResponse`1"/> to objects of type <see cref="T:System.Collections.Generic.ICollection`1"/>.</summary>
            <typeparam name="TDataContract">The type of data contracts in the response content.</typeparam>
            <typeparam name="TValue">The type of the converted values.</typeparam>
        </member>
        <member name="F:GW2NET.Common.ConverterForCollectionResponse`2.converterForDataContract">
            <summary>Infrastructure. Holds a reference to a type converter.</summary>
        </member>
        <member name="M:GW2NET.Common.ConverterForCollectionResponse`2.#ctor(GW2NET.Common.IConverter{`0,`1})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ConverterForCollectionResponse`2"/> class.</summary>
            <param name="converterForDataContract">The converter for <typeparamref name="TDataContract"/>.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="converterForDataContract"/> is a null reference.</exception>
        </member>
        <member name="M:GW2NET.Common.ConverterForCollectionResponse`2.GW2NET#Common#IConverter{GW2NET#Common#IResponse{System#Collections#Generic#ICollection{TDataContract}},System#Collections#Generic#ICollection{TValue}}#Convert(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{`0}})">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.ConverterForDictionaryRangeResponse`3">
            <summary>Converts objects of type <see cref="T:GW2NET.Common.IResponse`1"/> to objects of type <see cref="T:IDictionaryRange&lt;TKey, TValue&gt;"/>.</summary>
            <typeparam name="TDataContract">The type of data contracts in the response content.</typeparam>
            <typeparam name="TKey">The type of the key values.</typeparam>
            <typeparam name="TValue">The type of the converted values.</typeparam>
        </member>
        <member name="M:GW2NET.Common.ConverterForDictionaryRangeResponse`3.#ctor(GW2NET.Common.IConverter{`0,`2},System.Func{`2,`1})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ConverterForDictionaryRangeResponse`3"/> class.</summary>
            <param name="converterForDataContract">The converter for <typeparamref name="TValue"/>.</param>
            <param name="keySelector">The key selector expression.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="converterForDataContract"/> or <paramref name="keySelector"/> is a null reference.</exception>
        </member>
        <member name="M:GW2NET.Common.ConverterForDictionaryRangeResponse`3.GW2NET#Common#IConverter{GW2NET#Common#IResponse{System#Collections#Generic#ICollection{TDataContract}},GW2NET#Common#IDictionaryRange{TKey,TValue}}#Convert(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{`0}})">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.ConverterForResponse`2">
            <summary>Converts objects of type <see cref="T:GW2NET.Common.IResponse`1"/> to objects of type <see cref="!:TValue"/>.</summary>
            <typeparam name="TDataContract">The type of data contracts in the response content.</typeparam>
            <typeparam name="TValue">The type of the converted value.</typeparam>
        </member>
        <member name="M:GW2NET.Common.ConverterForResponse`2.#ctor(GW2NET.Common.IConverter{`0,`1})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ConverterForResponse`2"/> class.</summary>
            <param name="converterForDataContract">The converter for <typeparamref name="TDataContract"/>.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="converterForDataContract"/> is a null reference.</exception>
        </member>
        <member name="M:GW2NET.Common.ConverterForResponse`2.GW2NET#Common#IConverter{GW2NET#Common#IResponse{TDataContract},TValue}#Convert(GW2NET.Common.IResponse{`0})">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.Converters.ConverterAdapter`1">
            <summary>Represents an adapter for the <see cref="T:GW2NET.Common.IConverter`2"/> interface that does not do any conversions.</summary>
            <typeparam name="T">The type of the value that needs to be adapted.</typeparam>
        </member>
        <member name="M:GW2NET.Common.Converters.ConverterAdapter`1.GW2NET#Common#IConverter{T,T}#Convert(`0)">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.Converters.ConverterForCollection`2">
            <summary>Converts objects of type <see cref="T:System.Collections.Generic.ICollection`1"/> to objects of type <see cref="T:System.Collections.Generic.ICollection`1"/>.</summary>
            <typeparam name="TInput">The type of the input.</typeparam>
            <typeparam name="TOutput">The type of the output.</typeparam>
        </member>
        <member name="M:GW2NET.Common.Converters.ConverterForCollection`2.#ctor(GW2NET.Common.IConverter{`0,`1})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Converters.ConverterForCollection`2"/> class.</summary>
            <param name="converterForOutput">The converter for <typeparamref name="TOutput"/>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="converterForOutput"/> is null.</exception>
        </member>
        <member name="M:GW2NET.Common.Converters.ConverterForCollection`2.Convert(System.Collections.Generic.ICollection{`0})">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.Converters.ConverterForIDictionary`4">
            <summary>Converts objects of type <see cref="T:System.Collections.Generic.IDictionary`2"/> to objects of type <see cref="T:System.Collections.Generic.IDictionary`2"/>.</summary>
            <typeparam name="TKeyInput">The type of keys in the input dictionary.</typeparam>
            <typeparam name="TValueInput">The type of values in the input dictionary.</typeparam>
            <typeparam name="TKeyOutput">The type of keys in the output dictionary.</typeparam>
            <typeparam name="TValueOutput">The type of values in the output dictionary.</typeparam>
        </member>
        <member name="M:GW2NET.Common.Converters.ConverterForIDictionary`4.#ctor(GW2NET.Common.IConverter{System.Collections.Generic.KeyValuePair{`0,`1},System.Collections.Generic.KeyValuePair{`2,`3}})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Converters.ConverterForIDictionary`4"/> class.</summary>
            <param name="converterForKeyValuePair">The converter for <see cref="T:System.Collections.Generic.KeyValuePair`2"/>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="converterForKeyValuePair"/> is null.</exception>
        </member>
        <member name="M:GW2NET.Common.Converters.ConverterForIDictionary`4.Convert(System.Collections.Generic.IDictionary{`0,`1})">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.Converters.ConverterForObject`1">
            <summary>Converts objects of type <see cref="T:object"/> to objects of type <see cref="!:T"/>.</summary>
            <typeparam name="T">The type to activate.</typeparam>
        </member>
        <member name="M:GW2NET.Common.Converters.ConverterForObject`1.GW2NET#Common#IConverter{System#Object,T}#Convert(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.DetailsRequest">
            <summary>Provides the base class for resource details requests.</summary>
        </member>
        <member name="P:GW2NET.Common.DetailsRequest.Identifier">
            <summary>Gets or sets the resource identifier.</summary>
        </member>
        <member name="P:GW2NET.Common.DetailsRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Common.DetailsRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Common.DetailsRequest.GetPathSegments">
            <summary>Gets additional path segments for the targeted resource.</summary>
            <returns>A collection of path segments.</returns>
        </member>
        <member name="T:GW2NET.Common.DictionaryRange`2">
            <summary>Represents a subset of keys and values.</summary>
            <typeparam name="TKey">The type of the keys in the subset.</typeparam>
            <typeparam name="TValue">The type of the values in the subset.</typeparam>
        </member>
        <member name="M:GW2NET.Common.DictionaryRange`2.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class.
            Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class that is empty, has the default initial capacity, and uses the default equality comparer for the key type.</summary>
        </member>
        <member name="M:GW2NET.Common.DictionaryRange`2.#ctor(System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class. Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class that is empty, has the specified initial capacity, and uses the default equality comparer for the key type.</summary>
            <param name="capacity">The initial number of elements that the <see cref="T:GW2NET.Common.DictionaryRange`2"/> can contain.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than 0.</exception>
        </member>
        <member name="M:GW2NET.Common.DictionaryRange`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class. Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class that is empty, has the specified initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.</summary>
            <param name="capacity">The initial number of elements that the <see cref="T:GW2NET.Common.DictionaryRange`2"/> can contain.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1"/> for the type of the key.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than 0.</exception>
        </member>
        <member name="M:GW2NET.Common.DictionaryRange`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class. Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class that is empty, has the default initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.</summary>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1"/> for the type of the key.</param>
        </member>
        <member name="M:GW2NET.Common.DictionaryRange`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class. Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2"/> and uses the default equality comparer for the key type.</summary>
            <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2"/> whose elements are copied to the new <see cref="T:GW2NET.Common.DictionaryRange`2"/>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dictionary"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="dictionary"/> contains one or more duplicate keys.</exception>
        </member>
        <member name="M:GW2NET.Common.DictionaryRange`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class. Initializes a new instance of the <see cref="T:GW2NET.Common.DictionaryRange`2"/> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2"/> and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.</summary>
            <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2"/> whose elements are copied to the new <see cref="T:GW2NET.Common.DictionaryRange`2"/>.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1"/> for the type of the key.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dictionary"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="dictionary"/> contains one or more duplicate keys.</exception>
        </member>
        <member name="P:GW2NET.Common.DictionaryRange`2.SubtotalCount">
            <summary>Gets or sets the number of values in this subset.</summary>
        </member>
        <member name="P:GW2NET.Common.DictionaryRange`2.TotalCount">
            <summary>Gets or sets the number of values in the collection.</summary>
        </member>
        <member name="T:GW2NET.Common.DiscoveryRequest">
            <summary>Provides the base class for discovery requests.</summary>
        </member>
        <member name="P:GW2NET.Common.DiscoveryRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Common.DiscoveryRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Common.DiscoveryRequest.GetPathSegments">
            <summary>Gets additional path segments for the targeted resource.</summary>
            <returns>A collection of path segments.</returns>
        </member>
        <member name="T:GW2NET.Common.Drawing.Rectangle">
            <summary>Represents a rectangle.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Rectangle.#ctor(GW2NET.Common.Drawing.Vector2D,GW2NET.Common.Drawing.Vector2D)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Drawing.Rectangle"/> struct.</summary>
            <param name="upperLeft">The coordinates of the upper-left corner.</param>
            <param name="lowerRight">The coordinates of the lower-right corner.</param>
        </member>
        <member name="P:GW2NET.Common.Drawing.Rectangle.Height">
            <summary>Gets or sets the height.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Rectangle.Width">
            <summary>Gets or sets the width.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Rectangle.X">
            <summary>Gets or sets the X-coordinate.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Rectangle.Y">
            <summary>Gets or sets the Y-coordinate.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Rectangle.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>Returns a <see cref="T:System.String" />.</returns>
        </member>
        <member name="T:GW2NET.Common.Drawing.Size2D">
            <summary>Represents the size of a two-dimensional object.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Size2D.#ctor(System.Double,System.Double)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Drawing.Size2D"/> struct.</summary>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
        </member>
        <member name="P:GW2NET.Common.Drawing.Size2D.Height">
            <summary>Gets or sets the height.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Size2D.Width">
            <summary>Gets or sets the width.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Size2D.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>Returns a <see cref="T:System.String" />.</returns>
        </member>
        <member name="T:GW2NET.Common.Drawing.Vector2D">
            <summary>Represents a vector in two-dimensional space.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Vector2D.#ctor(System.Double,System.Double)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Drawing.Vector2D"/> struct.</summary>
            <param name="x">The X-component value.</param>
            <param name="y">The Y-component value.</param>
        </member>
        <member name="P:GW2NET.Common.Drawing.Vector2D.X">
            <summary>Gets or sets the X-component value.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Vector2D.Y">
            <summary>Gets or sets the Y-component value.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Vector2D.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>Returns a <see cref="T:System.String" />.</returns>
        </member>
        <member name="T:GW2NET.Common.Drawing.Vector3D">
            <summary>Represents a vector in three-dimensional space.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Vector3D.#ctor(System.Double,System.Double,System.Double)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Drawing.Vector3D"/> struct.</summary>
            <param name="x">The X-component value.</param>
            <param name="y">The Y-component value.</param>
            <param name="z">The Z-component value.</param>
        </member>
        <member name="P:GW2NET.Common.Drawing.Vector3D.X">
            <summary>Gets or sets the X-coordinate.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Vector3D.Y">
            <summary>Gets or sets the Y-coordinate.</summary>
        </member>
        <member name="P:GW2NET.Common.Drawing.Vector3D.Z">
            <summary>Gets or sets the Z-coordinate.</summary>
        </member>
        <member name="M:GW2NET.Common.Drawing.Vector3D.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>Returns a <see cref="T:System.String" />.</returns>
        </member>
        <member name="T:GW2NET.Common.ErrorResult">
            <summary>Represents the result that is returned when an error occurs.</summary>
            <remarks>See <a href="http://wiki.guildwars2.com/wiki/API:1" /> for more information.</remarks>
        </member>
        <member name="P:GW2NET.Common.ErrorResult.Error">
            <summary>Gets or sets a number that indicates the error kind.</summary>
        </member>
        <member name="P:GW2NET.Common.ErrorResult.Code">
            <summary>Gets or sets a number that indicates the error code.</summary>
        </member>
        <member name="P:GW2NET.Common.ErrorResult.Line">
            <summary>Gets or sets the line number on which the error occurred.</summary>
        </member>
        <member name="P:GW2NET.Common.ErrorResult.Module">
            <summary>Gets or sets a number that represents the module in which the error occurred.</summary>
        </member>
        <member name="P:GW2NET.Common.ErrorResult.Product">
            <summary>Gets or sets a number that represents the product in which the error occurred.</summary>
        </member>
        <member name="P:GW2NET.Common.ErrorResult.Text">
            <summary>Gets or sets the error message.</summary>
        </member>
        <member name="T:GW2NET.Common.HttpStatusCodeExtensions">
            <summary>Provides extension methods for the <see cref="T:System.Net.HttpStatusCode" /> type.</summary>
        </member>
        <member name="M:GW2NET.Common.HttpStatusCodeExtensions.IsSuccessStatusCode(System.Net.HttpStatusCode)">
            <summary>Gets whether the specified status code indicates success.</summary>
            <param name="value">The status code.</param>
            <returns>True if the status code indicates success; otherwise false.</returns>
        </member>
        <member name="T:GW2NET.Common.IBroker`1">
            <summary>Provides the interface for agents that provide exchange services.</summary>
            <typeparam name="TQuotation">The type of quotation.</typeparam>
        </member>
        <member name="M:GW2NET.Common.IBroker`1.GetQuotation(System.Int64)">
            <summary>Gets a quotation for the specified number of commodities.</summary>
            <param name="quantity">The quantity.</param>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A quotation.</returns>
        </member>
        <member name="M:GW2NET.Common.IBroker`1.GetQuotationAsync(System.Int64)">
            <summary>Gets a quotation for the specified number of commodities.</summary>
            <param name="quantity">The quantity.</param>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A quotation.</returns>
        </member>
        <member name="M:GW2NET.Common.IBroker`1.GetQuotationAsync(System.Int64,System.Threading.CancellationToken)">
            <summary>Gets a quotation for the specified number of commodities.</summary>
            <param name="quantity">The quantity.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>A quotation.</returns>
        </member>
        <member name="T:GW2NET.Common.IBuildService">
            <summary>Provides the interface for the build service.</summary>
        </member>
        <member name="M:GW2NET.Common.IBuildService.GetBuild">
            <summary>Gets the current game build.</summary>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving the current game build.</exception>
            <returns>The current game build.</returns>
        </member>
        <member name="M:GW2NET.Common.IBuildService.GetBuildAsync">
            <summary>Gets the current build.</summary>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving the current game build.</exception>
            <returns>The current game build.</returns>
        </member>
        <member name="M:GW2NET.Common.IBuildService.GetBuildAsync(System.Threading.CancellationToken)">
            <summary>Gets the current build.</summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving the current game build.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>The current game build.</returns>
        </member>
        <member name="T:GW2NET.Common.IBulkRequest">
            <summary>Provides the interface for bulk resource details requests.</summary>
        </member>
        <member name="P:GW2NET.Common.IBulkRequest.Identifiers">
            <summary>Gets or sets the collection of resource identifiers.</summary>
        </member>
        <member name="T:GW2NET.Common.ICollectionPage`1">
            <summary>Provides the interface for collections that represent a page.</summary>
            <typeparam name="T">The type of values in the collection.</typeparam>
        </member>
        <member name="T:GW2NET.Common.IConverter`2">
            <summary>Provides the interface for classes that convert one type to another type.</summary>
            <typeparam name="TInput">The type of the input.</typeparam>
            <typeparam name="TOutput">The type of the output.</typeparam>
        </member>
        <member name="M:GW2NET.Common.IConverter`2.Convert(`0)">
            <summary>Converts the given object of type <typeparamref name="TInput"/> to an object of type <typeparamref name="TOutput"/>.</summary>
            <param name="value">The value to convert.</param>
            <exception cref="T:System.ArgumentNullException">The value is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The value can't be converted by the current converter.</exception>
            <returns>The converted value.</returns>
        </member>
        <member name="T:GW2NET.Common.IDetailsRequest">
            <summary>Provides the interface for resource details requests.</summary>
        </member>
        <member name="P:GW2NET.Common.IDetailsRequest.Identifier">
            <summary>Gets or sets the resource identifier.</summary>
        </member>
        <member name="T:GW2NET.Common.IDictionaryRange`2">
            <summary>Provides the interface for <see cref="T:System.Collections.Generic.IDictionary`2"/> types that represent a range.</summary>
            <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
            <typeparam name="TValue">The type of values in the dictionary.</typeparam>
        </member>
        <member name="T:GW2NET.Common.IDiscoverable`1">
            <summary>Provides the interface for data sources that support enumerating object identifiers.</summary>
            <typeparam name="T">The type of the identifiers.</typeparam>
        </member>
        <member name="M:GW2NET.Common.IDiscoverable`1.Discover">
            <summary>Discovers identifiers of objects in the data source.</summary>
            <exception cref="T:System.NotSupportedException">The data source does not support the discovery of object identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of object identifiers.</returns>
        </member>
        <member name="M:GW2NET.Common.IDiscoverable`1.DiscoverAsync">
            <summary>Discovers identifiers of objects in the data source.</summary>
            <exception cref="T:System.NotSupportedException">The data source does not support the discovery of object identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of object identifiers.</returns>
        </member>
        <member name="M:GW2NET.Common.IDiscoverable`1.DiscoverAsync(System.Threading.CancellationToken)">
            <summary>Discovers identifiers of objects in the data source.</summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support the discovery of object identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>A collection of object identifiers.</returns>
        </member>
        <member name="T:GW2NET.Common.ILocalizable">
            <summary>Provides the interface for locale-aware types.</summary>
        </member>
        <member name="P:GW2NET.Common.ILocalizable.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="T:GW2NET.Common.IPageContext">
            <summary>Provides contextual information for paginated collections.</summary>
        </member>
        <member name="P:GW2NET.Common.IPageContext.FirstPageIndex">
            <summary>Gets or sets the page index of the first page.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is greater than <see cref="P:GW2NET.Common.IPageContext.LastPageIndex"/>.</exception>
        </member>
        <member name="P:GW2NET.Common.IPageContext.LastPageIndex">
            <summary>Gets or sets the page index of the last page.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than <see cref="P:GW2NET.Common.IPageContext.FirstPageIndex"/>.</exception>
        </member>
        <member name="P:GW2NET.Common.IPageContext.NextPageIndex">
            <summary>Gets or sets the page index of the next page.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than <see cref="P:GW2NET.Common.IPageContext.FirstPageIndex"/> or greater than <see cref="P:GW2NET.Common.IPageContext.LastPageIndex"/>.</exception>
        </member>
        <member name="P:GW2NET.Common.IPageContext.PageCount">
            <summary>Gets or sets the number of pages.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than 0.</exception>
        </member>
        <member name="P:GW2NET.Common.IPageContext.PageIndex">
            <summary>Gets or sets the page index of the current page.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than <see cref="P:GW2NET.Common.IPageContext.FirstPageIndex"/> or greater than <see cref="P:GW2NET.Common.IPageContext.LastPageIndex"/>.</exception>
        </member>
        <member name="P:GW2NET.Common.IPageContext.PageSize">
            <summary>Gets or sets the maximum number of items per page.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than 0.</exception>
        </member>
        <member name="P:GW2NET.Common.IPageContext.PreviousPageIndex">
            <summary>Gets or sets the page index of the previous page.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than <see cref="P:GW2NET.Common.IPageContext.FirstPageIndex"/> or greater than <see cref="P:GW2NET.Common.IPageContext.LastPageIndex"/>.</exception>
        </member>
        <member name="T:GW2NET.Common.IPageRequest">
            <summary>Provides the interface for paginated resource details requests.</summary>
        </member>
        <member name="P:GW2NET.Common.IPageRequest.Page">
            <summary>Gets or sets the page number.</summary>
        </member>
        <member name="P:GW2NET.Common.IPageRequest.PageSize">
            <summary>Gets or sets the number of entries per page.</summary>
        </member>
        <member name="T:GW2NET.Common.IPaginator`1">
            <summary>Provides the interface for types that perform pagination.</summary>
            <typeparam name="T">The type of elements on the page.</typeparam>
        </member>
        <member name="M:GW2NET.Common.IPaginator`1.FindPage(System.Int32)">
            <summary>Finds the page with the specified page index.</summary>
            <param name="pageIndex">The page index to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support pagination.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="pageIndex"/> is less than 0.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The page.</returns>
        </member>
        <member name="M:GW2NET.Common.IPaginator`1.FindPage(System.Int32,System.Int32)">
            <summary>Finds the page with the specified page number and maximum size.</summary>
            <param name="pageIndex">The page index to find.</param>
            <param name="pageSize">The maximum number of page elements.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support pagination.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="pageIndex"/> is less than 0 or <paramref name="pageSize"/> is less than 0.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The page.</returns>
        </member>
        <member name="M:GW2NET.Common.IPaginator`1.FindPageAsync(System.Int32)">
            <summary>Finds the page with the specified page index.</summary>
            <param name="pageIndex">The page index to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support pagination.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="pageIndex"/> is less than 0.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The page.</returns>
        </member>
        <member name="M:GW2NET.Common.IPaginator`1.FindPageAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>Finds the page with the specified page index.</summary>
            <param name="pageIndex">The page index to find.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support pagination.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="pageIndex"/> is less than 0.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>The page.</returns>
        </member>
        <member name="M:GW2NET.Common.IPaginator`1.FindPageAsync(System.Int32,System.Int32)">
            <summary>Finds the page with the specified page index.</summary>
            <param name="pageIndex">The page index to find.</param>
            <param name="pageSize">The maximum number of page elements.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support pagination.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="pageIndex"/> is less than 0 or <paramref name="pageSize"/> is less than 0.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The page.</returns>
        </member>
        <member name="M:GW2NET.Common.IPaginator`1.FindPageAsync(System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>Finds the page with the specified page index.</summary>
            <param name="pageIndex">The page index to find.</param>
            <param name="pageSize">The maximum number of page elements.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support pagination.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="pageIndex"/> is less than 0 or <paramref name="pageSize"/> is less than 0.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>The page.</returns>
        </member>
        <member name="T:GW2NET.Common.IRenderable">
            <summary>Provides the interface for objects for which a graphical representation exists.</summary>
        </member>
        <member name="P:GW2NET.Common.IRenderable.FileId">
            <summary>Gets the file identifier.</summary>
        </member>
        <member name="P:GW2NET.Common.IRenderable.FileSignature">
            <summary>Gets the file signature.</summary>
        </member>
        <member name="T:GW2NET.Common.IRenderService">
            <summary>Provides the interface for the render service.</summary>
            <remarks>See <a href="http://wiki.guildwars2.com/wiki/API:Render_service">wiki</a> for more information.</remarks>
        </member>
        <member name="M:GW2NET.Common.IRenderService.GetImage(GW2NET.Common.IRenderable,System.String)">
            <summary>Gets binary image data for the given file identifier and image format.</summary>
            <param name="file">The file identifier.</param>
            <param name="imageFormat">The image file format.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="file"/> or <paramref name="imageFormat"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="imageFormat"/> is not "jpg" or "png".</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving the image data.</exception>
            <returns>The binary image data.</returns>
        </member>
        <member name="M:GW2NET.Common.IRenderService.GetImageAsync(GW2NET.Common.IRenderable,System.String)">
            <summary>Gets binary image data for the given file identifier and image format.</summary>
            <param name="file">The file identifier.</param>
            <param name="imageFormat">The image format.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="file"/> or <paramref name="imageFormat"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="imageFormat"/> is not "jpg" or "png".</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving the image data.</exception>
            <returns>The binary image data.</returns>
        </member>
        <member name="M:GW2NET.Common.IRenderService.GetImageAsync(GW2NET.Common.IRenderable,System.String,System.Threading.CancellationToken)">
            <summary>Gets binary image data for the given file identifier and image format.</summary>
            <param name="file">The file identifier.</param>
            <param name="imageFormat">The image format.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="file"/> or <paramref name="imageFormat"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="imageFormat"/> is not "jpg" or "png".</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving the image data.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>The binary image data.</returns>
        </member>
        <member name="T:GW2NET.Common.IRepository`2">
            <summary>Provides the interface for data sources.</summary>
            <typeparam name="TKey">The type of the key values that uniquely identify the entities in the repository.</typeparam>
            <typeparam name="TValue">The type of the entities in the repository.</typeparam>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.Find(`0)">
            <summary>Finds the object with the given identifier.</summary>
            <param name="identifier">The identifier of the object to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by identifier.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The object with the given identifier, or a null reference.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAll">
            <summary>Finds every object.</summary>
            <exception cref="T:System.NotSupportedException">The data source does not support searching for all objects.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of objects.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAll(System.Collections.Generic.ICollection{`0})">
            <summary>Finds every object with one of the given identifiers.</summary>
            <param name="identifiers">The identifiers of the objects to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching for a range of objects.</exception>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="identifiers"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The value of <paramref name="identifiers"/> is an empty collection.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of objects with one of the given identifiers.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAllAsync">
            <summary>Finds every object.</summary>
            <exception cref="T:System.NotSupportedException">The data source does not support searching for all objects.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of objects.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAllAsync(System.Threading.CancellationToken)">
            <summary>Finds every object.</summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching for all objects.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>A collection of objects.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAllAsync(System.Collections.Generic.ICollection{`0})">
            <summary>Finds every object with one of the given identifiers.</summary>
            <param name="identifiers">The identifiers of the objects to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching for a range of objects.</exception>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="identifiers"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The value of <paramref name="identifiers"/> is an empty collection.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of objects with one of the given identifiers.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAllAsync(System.Collections.Generic.ICollection{`0},System.Threading.CancellationToken)">
            <summary>Finds every object with one of the given identifiers.</summary>
            <param name="identifiers">The identifiers of the objects to find.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching for a range of objects.</exception>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="identifiers"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The value of <paramref name="identifiers"/> is an empty collection.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>A collection of objects with one of the given identifiers.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAsync(`0)">
            <summary>Finds the object with the given identifier.</summary>
            <param name="identifier">The identifier of the object to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by identifier.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The object with the given identifier, or a null reference.</returns>
        </member>
        <member name="M:GW2NET.Common.IRepository`2.FindAsync(`0,System.Threading.CancellationToken)">
            <summary>Finds the object with the given identifier.</summary>
            <param name="identifier">The identifier of the object to find.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by identifier.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>The object with the given identifier, or a null reference.</returns>
        </member>
        <member name="T:GW2NET.Common.IRequest">
            <summary>Provides the interface for service requests.</summary>
        </member>
        <member name="P:GW2NET.Common.IRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Common.IRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Common.IRequest.GetPathSegments">
            <summary>Gets additional path segments for the targeted resource.</summary>
            <returns>A collection of path segments.</returns>
        </member>
        <member name="T:GW2NET.Common.IResponse`1">
            <summary>Provides the interface for service responses.</summary>
            <typeparam name="T">The type of the response content.</typeparam>
        </member>
        <member name="P:GW2NET.Common.IResponse`1.Content">
            <summary>Gets or sets the response content.</summary>
        </member>
        <member name="P:GW2NET.Common.IResponse`1.Date">
            <summary>Gets or sets the <see cref="T:System.DateTimeOffset"/> at which the message originated.</summary>
        </member>
        <member name="P:GW2NET.Common.IResponse`1.ExtensionData">
            <summary>Gets or sets a collection of custom response headers.</summary>
            <exception cref="T:System.ArgumentNullException">The value is a null reference.</exception>
        </member>
        <member name="T:GW2NET.Common.IServiceClient">
            <summary>Provides the interface for service clients.</summary>
        </member>
        <member name="M:GW2NET.Common.IServiceClient.Send``1(GW2NET.Common.IRequest)">
            <summary>Sends a request and returns the response.</summary>
            <param name="request">The service request.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="request"/> is a null reference.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.IServiceClient.SendAsync``1(GW2NET.Common.IRequest)">
            <summary>Sends a request and returns the response.</summary>
            <param name="request">The service request.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="request"/> is a null reference.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.IServiceClient.SendAsync``1(GW2NET.Common.IRequest,System.Threading.CancellationToken)">
            <summary>Sends a request and returns the response.</summary>
            <param name="request">The service request.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="request"/> is a null reference.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="T:GW2NET.Common.ISubsetContext">
            <summary>Provides contextual information for collections that are a subset of a larger collection.</summary>
        </member>
        <member name="P:GW2NET.Common.ISubsetContext.SubtotalCount">
            <summary>Gets or sets the number of values in this subset.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than 0.</exception>
        </member>
        <member name="P:GW2NET.Common.ISubsetContext.TotalCount">
            <summary>Gets or sets the number of values in the collection.</summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value is less than 0.</exception>
        </member>
        <member name="T:GW2NET.Common.ITimeSensitive">
            <summary>Provides the interface for types whose value is time sensitive.</summary>
        </member>
        <member name="P:GW2NET.Common.ITimeSensitive.Timestamp">
            <summary>Gets or sets a timestamp.</summary>
        </member>
        <member name="T:GW2NET.Common.LocalizableRequest">
            <summary>Represents a request, targeting any the v2/ endpoint.</summary>
        </member>
        <member name="P:GW2NET.Common.LocalizableRequest.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Common.LocalizableRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Common.LocalizableRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Common.LocalizableRequest.GetPathSegments">
            <summary>The get path segments.</summary>
            <returns>The <see cref="T:System.Collections.Generic.IEnumerable`1"/>.</returns>
        </member>
        <member name="T:GW2NET.Common.PageContextPatchUtility">
            <summary>
            The API embeds page context in HTTP Link headers, but we currently do not have a parser for that header.
            This class calculates the missing page context information for a given page index.
            This class should eventually be replaced by a class that can parse Link headers.
            </summary>
        </member>
        <member name="M:GW2NET.Common.PageContextPatchUtility.Patch``1(GW2NET.Common.ICollectionPage{``0},System.Int32)">
            <summary>Patches missing page context for the given collection and page index.</summary>
            <param name="collection">The collection.</param>
            <param name="pageIndex">The page index.</param>
            <typeparam name="T">The type of values in the collection.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="collection"/> is a null reference.</exception>
        </member>
        <member name="T:GW2NET.Common.PageRequest">
            <summary>Provides the base class for paginated resource details requests.</summary>
        </member>
        <member name="P:GW2NET.Common.PageRequest.Page">
            <summary>Gets or sets the page number.</summary>
        </member>
        <member name="P:GW2NET.Common.PageRequest.PageSize">
            <summary>Gets or sets the number of entries per page.</summary>
        </member>
        <member name="P:GW2NET.Common.PageRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Common.PageRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Common.PageRequest.GetPathSegments">
            <summary>Gets additional path segments for the targeted resource.</summary>
            <returns>A collection of path segments.</returns>
        </member>
        <member name="T:GW2NET.Common.Paginator">
            <summary>Provides static extension methods for types that implement the <see cref="T:GW2NET.Common.IPaginator`1"/> interface.</summary>
        </member>
        <member name="P:GW2NET.Common.Paginator.MaxRetryCount">
            <summary>Gets or sets the maximum number of retry attempts per page. The default value is 3.</summary>
        </member>
        <member name="M:GW2NET.Common.Paginator.FindAllPages``1(GW2NET.Common.IPaginator{``0},System.Int32)">
            <summary>Finds a collection of all pages.</summary>
            <param name="instance">The instance of <see cref="T:GW2NET.Common.IPaginator`1"/> that provides the pages.</param>
            <param name="pageCount">The number of pages to get.</param>
            <typeparam name="T">The type of elements on the page.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>A collection of pages.</returns>
        </member>
        <member name="M:GW2NET.Common.Paginator.FindAllPages``1(GW2NET.Common.IPaginator{``0},System.Int32,System.Int32)">
            <summary>Finds a collection of all pages.</summary>
            <param name="instance">The instance of <see cref="T:GW2NET.Common.IPaginator`1"/> that provides the pages.</param>
            <param name="pageSize">The maximum number of page elements.</param>
            <param name="pageCount">The number of pages to get.</param>
            <typeparam name="T">The type of elements on the page.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>A collection of pages.</returns>
        </member>
        <member name="M:GW2NET.Common.Paginator.FindAllPagesAsync``1(GW2NET.Common.IPaginator{``0},System.Int32)">
            <summary>Finds a collection of all pages.</summary>
            <param name="instance">The instance of <see cref="T:GW2NET.Common.IPaginator`1"/> that provides the pages.</param>
            <param name="pageCount">The number of pages to get.</param>
            <typeparam name="T">The type of elements on the page.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>A collection of pages.</returns>
        </member>
        <member name="M:GW2NET.Common.Paginator.FindAllPagesAsync``1(GW2NET.Common.IPaginator{``0},System.Int32,System.Threading.CancellationToken)">
            <summary>Finds a collection of all pages.</summary>
            <param name="instance">The instance of <see cref="T:GW2NET.Common.IPaginator`1"/> that provides the pages.</param>
            <param name="pageCount">The number of pages to get.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <typeparam name="T">The type of elements on the page.</typeparam>
            <returns>A collection of pages.</returns>
        </member>
        <member name="M:GW2NET.Common.Paginator.FindAllPagesAsync``1(GW2NET.Common.IPaginator{``0},System.Int32,System.Int32)">
            <summary>Finds a collection of all pages.</summary>
            <param name="instance">The instance of <see cref="T:GW2NET.Common.IPaginator`1"/> that provides the pages.</param>
            <param name="pageSize">The maximum number of page elements.</param>
            <param name="pageCount">The number of pages to get.</param>
            <typeparam name="T">The type of elements on the page.</typeparam>
            <returns>A collection of pages.</returns>
        </member>
        <member name="M:GW2NET.Common.Paginator.FindAllPagesAsync``1(GW2NET.Common.IPaginator{``0},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>Finds a collection of all pages.</summary>
            <param name="instance">The instance of <see cref="T:GW2NET.Common.IPaginator`1"/> that provides the pages.</param>
            <param name="pageSize">The maximum number of page elements.</param>
            <param name="pageCount">The number of pages to get.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <typeparam name="T">The type of elements on the page.</typeparam>
            <returns>A collection of pages.</returns>
        </member>
        <member name="T:GW2NET.Common.Profession">
            <summary>
            Enumerates the known professions.
            </summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Unknown">
            <summary>Indicates an unknown profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Guardian">
            <summary>The 'Guardian' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Warrior">
            <summary>The 'Warrior' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Engineer">
            <summary>The 'Engineer' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Ranger">
            <summary>The 'Ranger' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Thief">
            <summary>The 'Thief' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Elementalist">
            <summary>The 'Elementalist' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Mesmer">
            <summary>The 'Mesmer' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Necromancer">
            <summary>The 'Necromancer' profession.</summary>
        </member>
        <member name="F:GW2NET.Common.Profession.Revenant">
            <summary>The 'Revenant' profession.</summary>
        </member>
        <member name="T:GW2NET.Common.Race">
            <summary>
            Enumerates the known races.
            </summary>
        </member>
        <member name="F:GW2NET.Common.Race.Asura">
            <summary>The 'Asura' race.</summary>
        </member>
        <member name="F:GW2NET.Common.Race.Charr">
            <summary>The 'Charr' race.</summary>
        </member>
        <member name="F:GW2NET.Common.Race.Human">
            <summary>The 'Human' race.</summary>
        </member>
        <member name="F:GW2NET.Common.Race.Norn">
            <summary>The 'Norn' race.</summary>
        </member>
        <member name="F:GW2NET.Common.Race.Sylvari">
            <summary>The 'Sylvari' race.</summary>
        </member>
        <member name="T:GW2NET.Common.RepositoryFactoryBase`1">
            <summary>Provides methods for creating repository objects.</summary>
            <typeparam name="TRepository">The type of repository to create.</typeparam>
        </member>
        <member name="P:GW2NET.Common.RepositoryFactoryBase`1.Item(System.String)">
            <summary>Creates an instance for the given language.</summary>
            <param name="language">The two-letter language code.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="language"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The length of <paramref name="language"/> is not 2.</exception>
            <returns>A repository.</returns>
        </member>
        <member name="P:GW2NET.Common.RepositoryFactoryBase`1.Item(System.Globalization.CultureInfo)">
            <summary>Creates an instance for the given language.</summary>
            <param name="culture">The culture.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="culture"/> is a null reference.</exception>
            <returns>A repository.</returns>
        </member>
        <member name="M:GW2NET.Common.RepositoryFactoryBase`1.ForDefaultCulture">
            <summary>Creates an instance for the default language.</summary>
            <returns>A repository.</returns>
        </member>
        <member name="M:GW2NET.Common.RepositoryFactoryBase`1.ForCulture(System.Globalization.CultureInfo)">
            <summary>Creates an instance for the given language.</summary>
            <param name="culture">The culture.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="culture"/> is a null reference.</exception>
            <returns>A repository.</returns>
        </member>
        <member name="M:GW2NET.Common.RepositoryFactoryBase`1.ForCurrentCulture">
            <summary>Creates an instance for the current system language.</summary>
            <returns>A repository.</returns>
        </member>
        <member name="M:GW2NET.Common.RepositoryFactoryBase`1.ForCurrentUICulture">
            <summary>Creates an instance for the current UI language.</summary>
            <returns>A repository.</returns>
        </member>
        <member name="T:GW2NET.Common.Response`1">
            <summary>Provides the default implementation of the <see cref="T:GW2NET.Common.IResponse`1"/> interface.</summary>
            <typeparam name="T">The type of the response content.</typeparam>
        </member>
        <member name="P:GW2NET.Common.Response`1.Content">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Common.Response`1.Culture">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Common.Response`1.Date">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Common.Response`1.ExtensionData">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Common.ResponseExtensions">
            <summary>Provides extension methods for <see cref="T:GW2NET.Common.IResponse`1" /> types.</summary>
        </member>
        <member name="M:GW2NET.Common.ResponseExtensions.GetPageSize``1(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{``0}})">
            <summary>Gets the maximum number of values in a subset.</summary>
            <param name="instance">The response.</param>
            <typeparam name="T">The response content type.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>The maximum number of values in a subset.</returns>
        </member>
        <member name="M:GW2NET.Common.ResponseExtensions.GetPageTotal``1(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{``0}})">
            <summary>Gets the number of subsets in a collection.</summary>
            <param name="instance">The response.</param>
            <typeparam name="T">The response content type.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>The number of subsets in a collection.</returns>
        </member>
        <member name="M:GW2NET.Common.ResponseExtensions.GetResultCount``1(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{``0}})">
            <summary>Gets the number of values in a subset.</summary>
            <param name="instance">The response.</param>
            <typeparam name="T">The response content type.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>The number of values in a subset.</returns>
        </member>
        <member name="M:GW2NET.Common.ResponseExtensions.GetResultTotal``1(GW2NET.Common.IResponse{System.Collections.Generic.ICollection{``0}})">
            <summary>Gets the number of values in a collection.</summary>
            <param name="instance">The response.</param>
            <typeparam name="T">The response content type.</typeparam>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="instance"/> is a null reference.</exception>
            <returns>The number of values in a collection.</returns>
        </member>
        <member name="T:GW2NET.Common.SerializationException">
            <summary>Represents errors that occur during (de-)serialization of types.</summary>
        </member>
        <member name="M:GW2NET.Common.SerializationException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.SerializationException" /> class.</summary>
        </member>
        <member name="M:GW2NET.Common.SerializationException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.SerializationException" /> class.</summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:GW2NET.Common.SerializationException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.SerializationException" /> class.</summary>
            <param name="message">The message that describes the error.</param>
            <param name="inner">
                The exception that is the cause of the current exception, or a null reference (Nothing in Visual
                Basic) if no inner exception is specified.
            </param>
        </member>
        <member name="T:GW2NET.Common.Serializers.BinarySerializer">
            <summary>Provides methods for serializing binary data.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.BinarySerializer.Deserialize(System.IO.Stream)">
            <summary>Converts the input stream to the specified type.</summary>
            <param name="stream">The input stream.</param>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.Serializers.BinarySerializer.Serialize(System.Byte[],System.IO.Stream)">
            <summary>Converts the specified value to an output stream.</summary>
            <param name="value">An instance of the specified type.</param>
            <param name="stream">The output stream.</param>
        </member>
        <member name="T:GW2NET.Common.Serializers.BinarySerializerFactory">
            <summary>Provides factory methods for the binary serialization engine.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.BinarySerializerFactory.GetSerializer``1">
            <summary>Gets a serializer for the specified type.</summary>
            <typeparam name="T">The serialization type.</typeparam>
            <returns>The <see cref="T:GW2NET.Common.Serializers.ISerializer`1"/>.</returns>
        </member>
        <member name="T:GW2NET.Common.Serializers.DataContractJsonSerializer`1">
            <summary>Provides methods for serializing JSON data contracts.</summary>
            <typeparam name="T">The type of the data contract.</typeparam>
        </member>
        <member name="F:GW2NET.Common.Serializers.DataContractJsonSerializer`1.serializer">
            <summary>Infrastructure. Holds a reference to the data contract serializer.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractJsonSerializer`1.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Serializers.DataContractJsonSerializer`1"/> class.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractJsonSerializer`1.Deserialize(System.IO.Stream)">
            <summary>Converts the input stream to the specified type.</summary>
            <param name="stream">The input stream.</param>
            <exception cref="T:GW2NET.Common.SerializationException">A serialization error occurred.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractJsonSerializer`1.Serialize(`0,System.IO.Stream)">
            <summary>Converts the specified value to an output stream.</summary>
            <param name="value">An instance of the specified type.</param>
            <param name="stream">The output stream.</param>
            <exception cref="T:GW2NET.Common.SerializationException">A serialization error occurred.</exception>
        </member>
        <member name="T:GW2NET.Common.Serializers.DataContractJsonSerializerFactory">
            <summary>Provides factory methods for the JSON data contract serialization engine.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractJsonSerializerFactory.GetSerializer``1">
            <summary>Gets a serializer for the specified type.</summary>
            <typeparam name="T">The serialization type.</typeparam>
            <returns>The <see cref="T:GW2NET.Common.Serializers.ISerializer`1"/>.</returns>
        </member>
        <member name="T:GW2NET.Common.Serializers.DataContractSerializer`1">
            <summary>Provides methods for serializing data contracts.</summary>
            <typeparam name="T">The type of the data contract.</typeparam>
        </member>
        <member name="F:GW2NET.Common.Serializers.DataContractSerializer`1.serializer">
            <summary>Infrastructure. Holds a reference to the JSON.NET serializer.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractSerializer`1.#ctor(System.Runtime.Serialization.XmlObjectSerializer)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Serializers.DataContractSerializer`1"/> class.</summary>
            <param name="serializer">The serializer.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="serializer"/> is null.</exception>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractSerializer`1.Deserialize(System.IO.Stream)">
            <summary>Converts the input stream to the specified type.</summary>
            <param name="stream">The input stream.</param>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractSerializer`1.Serialize(`0,System.IO.Stream)">
            <summary>Converts the specified value to an output stream.</summary>
            <param name="value">An instance of the specified type.</param>
            <param name="stream">The output stream.</param>
        </member>
        <member name="T:GW2NET.Common.Serializers.DataContractXmlSerializerFactory">
            <summary>Provides factory methods for the XML data contract serialization engine.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.DataContractXmlSerializerFactory.GetSerializer``1">
            <summary>Gets a serializer for the specified type.</summary>
            <typeparam name="T">The serialization type.</typeparam>
            <returns>The <see cref="T:GW2NET.Common.Serializers.ISerializer`1"/>.</returns>
        </member>
        <member name="T:GW2NET.Common.Serializers.ISerializer`1">
            <summary>Provides the interface for serialization engines.</summary>
            <typeparam name="T">The serialization type.</typeparam>
        </member>
        <member name="M:GW2NET.Common.Serializers.ISerializer`1.Deserialize(System.IO.Stream)">
            <summary>Converts the input stream to the specified type.</summary>
            <param name="stream">The input stream.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="stream"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The input stream is not readable.</exception>
            <exception cref="T:GW2NET.Common.SerializationException">A serialization error occurred.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.Serializers.ISerializer`1.Serialize(`0,System.IO.Stream)">
            <summary>Converts the specified value to an output stream.</summary>
            <param name="value">An instance of the specified type.</param>
            <param name="stream">The output stream.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="value"/> or the value of <paramref name="stream"/> is a null reference.</exception>
            <exception cref="T:System.ArgumentException">The output stream is not writable.</exception>
            <exception cref="T:GW2NET.Common.SerializationException">A serialization error occurred.</exception>
        </member>
        <member name="T:GW2NET.Common.Serializers.ISerializerFactory">
            <summary>Provides the interface for serialization engine factories.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.ISerializerFactory.GetSerializer``1">
            <summary>Gets a serializer for the specified type.</summary>
            <typeparam name="T">The serialization type.</typeparam>
            <returns>The <see cref="T:GW2NET.Common.Serializers.ISerializer`1"/>.</returns>
        </member>
        <member name="T:GW2NET.Common.Serializers.StringSerializer">
            <summary>Provides methods for serializing strings.</summary>
        </member>
        <member name="F:GW2NET.Common.Serializers.StringSerializer.encoding">
            <summary>Infrastructure. Holds a reference to the character encoding.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializer.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Serializers.StringSerializer"/> class.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializer.#ctor(System.Text.Encoding)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Serializers.StringSerializer"/> class.</summary>
            <param name="encoding">The character encoding.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="encoding"/> is null.</exception>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializer.Deserialize(System.IO.Stream)">
            <summary>Converts the input stream to the specified type.</summary>
            <param name="stream">The input stream.</param>
            <exception cref="T:GW2NET.Common.SerializationException">A serialization error occurred.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializer.Serialize(System.String,System.IO.Stream)">
            <summary>Converts the specified value to an output stream.</summary>
            <param name="value">An instance of the specified type.</param>
            <param name="stream">The output stream.</param>
            <exception cref="T:GW2NET.Common.SerializationException">A serialization error occurred.</exception>
        </member>
        <member name="T:GW2NET.Common.Serializers.StringSerializerFactory">
            <summary>Provides factory methods for the string serialization engine.</summary>
        </member>
        <member name="F:GW2NET.Common.Serializers.StringSerializerFactory.encoding">
            <summary>Infrastructure. Holds a reference to the character encoding.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializerFactory.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Serializers.StringSerializerFactory"/> class.</summary>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializerFactory.#ctor(System.Text.Encoding)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.Serializers.StringSerializerFactory"/> class.</summary>
            <param name="encoding">The character encoding.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="encoding"/> is null.</exception>
        </member>
        <member name="M:GW2NET.Common.Serializers.StringSerializerFactory.GetSerializer``1">
            <summary>Gets a serializer for the specified type.</summary>
            <typeparam name="T">The serialization type.</typeparam>
            <returns>The <see cref="T:GW2NET.Common.Serializers.ISerializer`1"/>.</returns>
        </member>
        <member name="T:GW2NET.Common.ServiceClient">
            <summary>Provides a default implementation for the <see cref="T:GW2NET.Common.IServiceClient" /> interface.</summary>
        </member>
        <member name="F:GW2NET.Common.ServiceClient.baseUri">
            <summary>Infrastructure. Holds a reference to the base URI.</summary>
        </member>
        <member name="F:GW2NET.Common.ServiceClient.errorSerializerFactory">
            <summary>Infrastructure. Holds a reference to a serializer factory.</summary>
        </member>
        <member name="F:GW2NET.Common.ServiceClient.gzipInflator">
            <summary>Infrastructure. Holds a reference to a GZIP inflator.</summary>
        </member>
        <member name="F:GW2NET.Common.ServiceClient.successSerializerFactory">
            <summary>Infrastructure. Holds a reference to a serializer factory.</summary>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.#ctor(System.Uri,GW2NET.Common.Serializers.ISerializerFactory,GW2NET.Common.Serializers.ISerializerFactory,GW2NET.Common.IConverter{System.IO.Stream,System.IO.Stream})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ServiceClient"/> class.</summary>
            <param name="baseUri">The base URI.</param>
            <param name="successSerializerFactory">The serializer factory.</param>
            <param name="errorSerializerFactory">The error serializer Factory.</param>
            <param name="gzipInflator">The GZIP inflator.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="baseUri"/> or <paramref name="successSerializerFactory"/> or <paramref name="errorSerializerFactory"/> or <paramref name="gzipInflator"/> is a null reference.</exception>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.Send``1(GW2NET.Common.IRequest)">
            <summary>Sends a request and returns the response.</summary>
            <param name="request">The service request.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <exception cref="T:GW2NET.Common.ServiceException">The service responded with an error code.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.SendAsync``1(GW2NET.Common.IRequest)">
            <summary>Sends a request and returns the response.</summary>
            <param name="request">The service request.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.SendAsync``1(GW2NET.Common.IRequest,System.Threading.CancellationToken)">
            <summary>Sends a request and returns the response.</summary>
            <param name="request">The service request.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <exception cref="T:GW2NET.Common.ServiceException">The service responded with an error code.</exception>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.BuildUri(System.Uri,System.String,GW2NET.Common.UrlEncodedForm)">
            <summary>Infrastructure. Creates and configures a new instance of the <see cref="T:System.UriBuilder"/> class.</summary>
            <param name="baseUri">The base URI.</param>
            <param name="resource">The resource name.</param>
            <param name="formData">The form data.</param>
            <returns>The <see cref="T:System.Uri"/>.</returns>
            <exception cref="T:System.FormatException">One or more query parameters violate the format for a valid URI as defined by RFC 2396.</exception>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.CreateHttpWebRequest(System.Uri)">
            <summary>Infrastructure. Creates and configures a new instance of the <see cref="T:System.Net.HttpWebRequest"/> class.</summary>
            <param name="uri">The resource <see cref="T:System.Uri"/>.</param>
            <returns>The <see cref="T:System.Net.HttpWebRequest"/>.</returns>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.DeserializeResponse``1(System.Net.HttpWebResponse,GW2NET.Common.Serializers.ISerializerFactory,GW2NET.Common.IConverter{System.IO.Stream,System.IO.Stream})">
            <summary>Infrastructure. Deserializes the response stream.</summary>
            <param name="response">The response.</param>
            <param name="serializerFactory">The serializer factory.</param>
            <param name="gzipInflator">The GZIP inflator.</param>
            <typeparam name="TResult">The type of the response content.</typeparam>
            <returns>An instance of the specified type.</returns>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.GetHttpWebResponse(System.Net.HttpWebRequest)">
            <summary>Infrastructure. Sends a web request and gets the response.</summary>
            <param name="webRequest">The <see cref="T:System.Net.HttpWebRequest"/>.</param>
            <returns>The <see cref="T:System.Net.HttpWebResponse"/>.</returns>
            <exception cref="T:GW2NET.Common.ServiceException">The exception that is thrown when an API error occurs.</exception>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.GetHttpWebResponseAsync(System.Net.HttpWebRequest,System.Threading.CancellationToken)">
            <summary>Infrastructure. Sends a web request and gets the response.</summary>
            <param name="webRequest">The <see cref="T:System.Net.HttpWebRequest"/>.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <returns>The <see cref="T:System.Net.HttpWebResponse"/>.</returns>
            <exception cref="T:GW2NET.Common.ServiceException">The request could not be fulfilled.</exception>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.OnError(System.Net.HttpWebResponse,GW2NET.Common.Serializers.ISerializerFactory,GW2NET.Common.IConverter{System.IO.Stream,System.IO.Stream})">
            <summary>Infrastructure. Throws an exception for error responses.</summary>
            <param name="response">The error response.</param>
            <param name="serializerFactory">The factory class that provides the serialization engine for the response.</param>
            <param name="gzipInflator">The GZIP inflator that decompresses the response.</param>
            <exception cref="T:GW2NET.Common.ServiceException">The exception that represents the error.</exception>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.OnSuccess``1(System.Net.HttpWebResponse,GW2NET.Common.Serializers.ISerializerFactory,GW2NET.Common.IConverter{System.IO.Stream,System.IO.Stream})">
            <summary>Infrastructure. Creates a response object for success responses.</summary>
            <param name="response">The success response.</param>
            <param name="serializerFactory">The factory class that provides the serialization engine for the response.</param>
            <param name="gzipInflator">The GZIP inflator that decompresses the response.</param>
            <typeparam name="T">The type of the response content.</typeparam>
            <returns>The object that represents the response.</returns>
        </member>
        <member name="M:GW2NET.Common.ServiceClient.OnResponse``1(System.Net.HttpWebResponse)">
            <summary>Infrastructure. Handles a response.</summary>
            <param name="response">The response to handle.</param>
            <typeparam name="TResult">The type of the response content</typeparam>
            <returns>The response as an instance of <see cref="T:GW2NET.Common.IResponse`1"/>.</returns>
            <exception cref="T:GW2NET.Common.ServiceException">The request could not be fulfilled.</exception>
        </member>
        <member name="T:GW2NET.Common.ServiceException">
            <summary>The exception that is thrown when a request could not be fulfilled.</summary>
        </member>
        <member name="M:GW2NET.Common.ServiceException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GW2NET.Common.ServiceException"/> class.
            </summary>
        </member>
        <member name="M:GW2NET.Common.ServiceException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ServiceException"/> class with a specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:GW2NET.Common.ServiceException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.ServiceException"/> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="P:GW2NET.Common.ServiceException.Request">
            <summary>Gets or sets the request that is the cause of this exception.</summary>
        </member>
        <member name="T:GW2NET.Common.UrlEncodedForm">
            <summary>Represents a collection of form data that can be URL-encoded.</summary>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GW2NET.Common.UrlEncodedForm"/> class that is empty, has the default initial capacity, and uses the default equality comparer for the key type.
            </summary>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.#ctor(System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.UrlEncodedForm"/> class that is empty, has the specified initial capacity, and uses the default equality comparer for the key type.</summary>
            <param name="capacity">The initial number of elements that the <see cref="T:GW2NET.Common.UrlEncodedForm"/> can contain.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than 0.</exception>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.#ctor(System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.UrlEncodedForm"/> class that is empty, has the default initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.</summary>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1"/> for the type of the key.</param>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.UrlEncodedForm"/> class that is empty, has the specified initial capacity, and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.</summary>
            <param name="capacity">The initial number of elements that the <see cref="T:GW2NET.Common.UrlEncodedForm"/> can contain.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1"/> for the type of the key.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than 0.</exception>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.UrlEncodedForm"/> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2"/> and uses the default equality comparer for the key type.</summary>
            <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2"/> whose elements are copied to the new <see cref="T:GW2NET.Common.UrlEncodedForm"/>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dictionary"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="dictionary"/> contains one or more duplicate keys.</exception>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Common.UrlEncodedForm"/> class that contains elements copied from the specified <see cref="T:System.Collections.Generic.IDictionary`2"/> and uses the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.</summary>
            <param name="dictionary">The <see cref="T:System.Collections.Generic.IDictionary`2"/> whose elements are copied to the new <see cref="T:GW2NET.Common.UrlEncodedForm"/>.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.EqualityComparer`1"/> for the type of the key.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dictionary"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="dictionary"/> contains one or more duplicate keys.</exception>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.GetQueryString">
            <summary>Gets the query string.</summary>
            <returns>The query <see cref="T:System.String" />.</returns>
            <exception cref="T:System.FormatException">One or more query parameters violate the format for a valid URI as defined by RFC 2396.</exception>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.ToString">
            <summary>Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.</summary>
            <returns>A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.</returns>
            <exception cref="T:System.FormatException">One or more query parameters violate the format for a valid URI as defined by RFC 2396.</exception>
        </member>
        <member name="M:GW2NET.Common.UrlEncodedForm.EncodeNameValuePair(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <summary>Encodes a key value pair for safe transportation over HTTP.</summary>
            <param name="keyValuePair">The key value pair.</param>
            <returns>The encoded key value pair.</returns>
            <exception cref="T:System.FormatException">One or more query parameters violate the format for a valid URI as defined by RFC 2396.</exception>
        </member>
        <member name="T:GW2NET.DynamicEvents.CylinderLocation">
            <summary>Represents a cylindrical location of an event on the map.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.CylinderLocation.Height">
            <summary>Gets or sets the location's height.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.CylinderLocation.Radius">
            <summary>Gets or sets the location's radius.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.CylinderLocation.Rotation">
            <summary>Gets or sets the location's rotation.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.DynamicEvent">
            <summary>Represents a dynamic event and its localized details.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.EventId">
            <summary>Gets or sets the event identifier.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.Flags">
            <summary>Gets or sets additional flags.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.Level">
            <summary>Gets or sets the event level.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.Location">
            <summary>Gets or sets the location of the event.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.Map">
            <summary>Gets or sets the map. This is a navigation property. Use the value of <see cref="P:GW2NET.DynamicEvents.DynamicEvent.MapId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.MapId">
            <summary>Gets or sets the map identifier.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEvent.Name">
            <summary>Gets or sets the name of the event.</summary>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEvent.op_Equality(GW2NET.DynamicEvents.DynamicEvent,GW2NET.DynamicEvents.DynamicEvent)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEvent.op_Inequality(GW2NET.DynamicEvents.DynamicEvent,GW2NET.DynamicEvents.DynamicEvent)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEvent.Equals(GW2NET.DynamicEvents.DynamicEvent)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEvent.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEvent.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEvent.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.DynamicEvents.DynamicEventFlags">
            <summary>Enumerates known dynamic event flags.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.DynamicEventFlags.None">
            <summary>Indicates no additional flags.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.DynamicEventFlags.GroupEvent">
            <summary>The 'group event' flag.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.DynamicEventFlags.MapWide">
            <summary>The 'map-wide' event flag.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.DynamicEventName">
            <summary>Represents a dynamic event and its localized name.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventName.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventName.EventId">
            <summary>Gets or sets the event identifier.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventName.Name">
            <summary>Gets or sets the localized name of the event.</summary>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventName.Equals(GW2NET.DynamicEvents.DynamicEventName)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventName.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventName.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventName.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.DynamicEvents.DynamicEventRotation">
            <summary>Represents a dynamic event and its rotation.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventRotation.Event">
            <summary>Gets or sets the event. This is a navigation property. Use the value of <see cref="P:GW2NET.DynamicEvents.DynamicEventRotation.EventId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventRotation.EventId">
            <summary>Gets or sets the event identifier.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventRotation.Shifts">
            <summary>Gets or sets the event shifts.</summary>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventRotation.op_Equality(GW2NET.DynamicEvents.DynamicEventRotation,GW2NET.DynamicEvents.DynamicEventRotation)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventRotation.op_Inequality(GW2NET.DynamicEvents.DynamicEventRotation,GW2NET.DynamicEvents.DynamicEventRotation)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventRotation.Equals(GW2NET.DynamicEvents.DynamicEventRotation)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventRotation.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The object to compare with the current object. </param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventRotation.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventRotation.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.DynamicEvents.DynamicEventState">
            <summary>Represents a dynamic event and its state.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventState.EventId">
            <summary>Gets or sets the event identifier.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventState.Map">
            <summary>Gets or sets the map. This is a navigation property. Use the value of <see cref="P:GW2NET.DynamicEvents.DynamicEventState.MapId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventState.MapId">
            <summary>Gets or sets the map identifier.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventState.State">
            <summary>Gets or sets the current state of the event.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventState.World">
            <summary>Gets or sets the world.  This is a navigation property. Use the value of <see cref="P:GW2NET.DynamicEvents.DynamicEventState.WorldId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.DynamicEventState.WorldId">
            <summary>Gets or sets the world identifier.</summary>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventState.op_Equality(GW2NET.DynamicEvents.DynamicEventState,GW2NET.DynamicEvents.DynamicEventState)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventState.op_Inequality(GW2NET.DynamicEvents.DynamicEventState,GW2NET.DynamicEvents.DynamicEventState)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventState.Equals(GW2NET.DynamicEvents.DynamicEventState)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventState.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.DynamicEvents.DynamicEventState.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="T:GW2NET.DynamicEvents.EventState">
            <summary>Enumerates known dynamic event states.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Unknown">
            <summary>The event state is unknown.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Inactive">
            <summary>The event is not running.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Active">
            <summary>The event is running now. </summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Success">
            <summary>The event has succeeded. </summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Fail">
            <summary>The event has failed. </summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Warmup">
            <summary>The event is inactive and waiting for certain criteria to be met before  becoming Active.</summary>
        </member>
        <member name="F:GW2NET.DynamicEvents.EventState.Preparation">
            <summary>The criteria for the event to start have been met, but certain activities (such as an NPC dialogue) have not completed yet. After the activities have been completed, the event will become Active.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.IEventNameRepository">
            <summary>Provides the interface for repositories that provide localized event names.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.IEventRepository">
            <summary>Provides the interface for repositories that provide localized event details.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.Location">
            <summary>Provides the base class for event locations.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.Location.Center">
            <summary>Gets or sets the center coordinates.</summary>
        </member>
        <member name="M:GW2NET.DynamicEvents.Location.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.DynamicEvents.PolygonLocation">
            <summary>Represents a polygonal location.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.PolygonLocation.Points">
            <summary>Gets or sets a collection that contains the vertex points of the polygon.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.PolygonLocation.ZRange">
            <summary>Gets or sets a vector that describes the minimum and maximum Z values.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.SphereLocation">
            <summary>Represents a spherical location.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.SphereLocation.Radius">
            <summary>Gets or sets the location's radius.</summary>
        </member>
        <member name="P:GW2NET.DynamicEvents.SphereLocation.Rotation">
            <summary>Gets or sets the location's rotation.</summary>
        </member>
        <member name="T:GW2NET.DynamicEvents.UnknownLocation">
            <summary>Represents an unknown location.</summary>
        </member>
        <member name="T:GW2NET.Files.Asset">
            <summary>Represents information about a file that can be retrieved from the render service.</summary>
        </member>
        <member name="P:GW2NET.Files.Asset.FileId">
            <summary>Gets or sets the file identifier to be used with the render service.</summary>
        </member>
        <member name="P:GW2NET.Files.Asset.Identifier">
            <summary>Gets or sets the file identifier.</summary>
        </member>
        <member name="P:GW2NET.Files.Asset.FileSignature">
            <summary>Gets or sets file signature to be used with the render service.</summary>
        </member>
        <member name="P:GW2NET.Files.Asset.IconFileUrl">
            <summary>Gets or sets the icon file URL.</summary>
        </member>
        <member name="M:GW2NET.Files.Asset.op_Equality(GW2NET.Files.Asset,GW2NET.Files.Asset)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Files.Asset.op_Inequality(GW2NET.Files.Asset,GW2NET.Files.Asset)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Files.Asset.Equals(GW2NET.Files.Asset)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Files.Asset.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Files.Asset.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Files.Asset.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Files.IFileRepository">
            <summary>Provides the interface for repositories that provide file details.</summary>
        </member>
        <member name="T:GW2NET.Guilds.Emblem">
            <summary>Represents a guild's emblem.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.BackgroundColor">
            <summary>Gets or sets the background color. This is a navigation property. Use the value of <see cref="P:GW2NET.Guilds.Emblem.BackgroundColorId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.BackgroundColorId">
            <summary>Gets or sets the background color identifier.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.BackgroundId">
            <summary>Gets or sets the background image identifier.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.Flags">
            <summary>Gets or sets the image transformations.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.ForegroundId">
            <summary>Gets or sets the foreground image identifier.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.ForegroundPrimaryColor">
            <summary>Gets or sets the primary foreground color. This is a navigation property. Use the value of <see cref="P:GW2NET.Guilds.Emblem.ForegroundPrimaryColorId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.ForegroundPrimaryColorId">
            <summary>Gets or sets the primary foreground color identifier.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.ForegroundSecondaryColor">
            <summary>Gets or sets the secondary foreground color. This is a navigation property. Use the value of <see cref="P:GW2NET.Guilds.Emblem.ForegroundSecondaryColorId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Emblem.ForegroundSecondaryColorId">
            <summary>Gets or sets the secondary foreground color identifier.</summary>
        </member>
        <member name="T:GW2NET.Guilds.EmblemTransformations">
            <summary>Enumerates the possible transformations for a guild emblem image.</summary>
        </member>
        <member name="F:GW2NET.Guilds.EmblemTransformations.None">
            <summary>Indicates no transformations.</summary>
        </member>
        <member name="F:GW2NET.Guilds.EmblemTransformations.FlipBackgroundHorizontal">
            <summary>Flip the background image horizontally.</summary>
        </member>
        <member name="F:GW2NET.Guilds.EmblemTransformations.FlipBackgroundVertical">
            <summary>Flip the background image vertically.</summary>
        </member>
        <member name="F:GW2NET.Guilds.EmblemTransformations.FlipForegroundHorizontal">
            <summary>Flip the foreground image horizontally.</summary>
        </member>
        <member name="F:GW2NET.Guilds.EmblemTransformations.FlipForegroundVertical">
            <summary>Flip the foreground image vertically.</summary>
        </member>
        <member name="T:GW2NET.Guilds.Guild">
            <summary>Represents a guild and its details.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Guild.Emblem">
            <summary>Gets or sets the guild's emblem.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Guild.GuildId">
            <summary>Gets or sets the guild identifier.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Guild.Name">
            <summary>Gets or sets the name of the guild.</summary>
        </member>
        <member name="P:GW2NET.Guilds.Guild.Tag">
            <summary>Gets or sets the guild's tag.</summary>
        </member>
        <member name="M:GW2NET.Guilds.Guild.op_Equality(GW2NET.Guilds.Guild,GW2NET.Guilds.Guild)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Guilds.Guild.op_Inequality(GW2NET.Guilds.Guild,GW2NET.Guilds.Guild)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Guilds.Guild.Equals(GW2NET.Guilds.Guild)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Guilds.Guild.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Guilds.Guild.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Guilds.Guild.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Guilds.IGuildRepository">
            <summary>Provides the interface for repositories that provide guild details.</summary>
        </member>
        <member name="M:GW2NET.Guilds.IGuildRepository.FindByName(System.String)">
            <summary>Finds the <see cref="T:GW2NET.Guilds.Guild"/> with the given name.</summary>
            <param name="name">The name of the <see cref="T:GW2NET.Guilds.Guild"/> to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by name.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The <see cref="T:System.Guid"/> with the given name, or a null reference.</returns>
        </member>
        <member name="M:GW2NET.Guilds.IGuildRepository.FindByNameAsync(System.String)">
            <summary>Finds the <see cref="T:GW2NET.Guilds.Guild"/> with the given name.</summary>
            <param name="name">The name of the <see cref="T:GW2NET.Guilds.Guild"/> to find.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by name.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>The <see cref="T:System.Guid"/> with the given name, or a null reference.</returns>
        </member>
        <member name="M:GW2NET.Guilds.IGuildRepository.FindByNameAsync(System.String,System.Threading.CancellationToken)">
            <summary>Finds the <see cref="T:GW2NET.Guilds.Guild"/> with the given name.</summary>
            <param name="name">The name of the <see cref="T:GW2NET.Guilds.Guild"/> to find.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by name.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>The <see cref="T:System.Guid"/> with the given name, or a null reference.</returns>
        </member>
        <member name="T:GW2NET.Items.Armor">
            <summary>Provides the base class for armor types.</summary>
        </member>
        <member name="F:GW2NET.Items.Armor.infixUpgrade">
            <summary>Backing field for <see cref="P:GW2NET.Items.Armor.InfixUpgrade"/>.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.DefaultSkin">
            <summary>Gets or sets the default skin. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Armor.DefaultSkinId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.DefaultSkinId">
            <summary>Gets or sets the default skin identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.Defense">
            <summary>Gets or sets the armor's defense stat.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.InfixUpgrade">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Items.Armor.InfusionSlots">
            <summary>Gets or sets the item's infusion slots.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.SecondarySuffixItem">
            <summary>Gets or sets the item's secondary suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Armor.SecondarySuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.SecondarySuffixItemId">
            <summary>Gets or sets the item's secondary suffix item identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.SuffixItem">
            <summary>Gets or sets the item's suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Armor.SuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.SuffixItemId">
            <summary>Gets or sets the item's suffix item identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Armor.WeightClass">
            <summary>Gets or sets the armor's weight class.</summary>
        </member>
        <member name="M:GW2NET.Items.Armor.GetItemChatLink">
            <summary>Gets an item chat link for this item.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="T:GW2NET.Items.Boots">
            <summary>Represents feet protection.</summary>
        </member>
        <member name="T:GW2NET.Items.Coat">
            <summary>Represents chest protection.</summary>
        </member>
        <member name="T:GW2NET.Items.Gloves">
            <summary>Represents arm protection.</summary>
        </member>
        <member name="T:GW2NET.Items.Helm">
            <summary>Represents head protection.</summary>
        </member>
        <member name="T:GW2NET.Items.HelmAquatic">
            <summary>Represents aquatic head protection.</summary>
        </member>
        <member name="T:GW2NET.Items.Leggings">
            <summary>Represents leg protection.</summary>
        </member>
        <member name="T:GW2NET.Items.Shoulders">
            <summary>Represents shoulder protection.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownArmor">
            <summary>Represents an unknown armor piece.</summary>
        </member>
        <member name="T:GW2NET.Items.WeightClass">
            <summary>Enumerates the known armor weight classes.</summary>
        </member>
        <member name="F:GW2NET.Items.WeightClass.Unknown">
            <summary>The 'Unknown' weight class.</summary>
        </member>
        <member name="F:GW2NET.Items.WeightClass.Clothing">
            <summary>The 'Clothing' weight class.</summary>
        </member>
        <member name="F:GW2NET.Items.WeightClass.Light">
            <summary>The 'Light' weight class.</summary>
        </member>
        <member name="F:GW2NET.Items.WeightClass.Medium">
            <summary>The 'Medium' weight class.</summary>
        </member>
        <member name="F:GW2NET.Items.WeightClass.Heavy">
            <summary>The 'Heavy' weight class.</summary>
        </member>
        <member name="T:GW2NET.Items.Backpack">
            <summary>Represents a backpack.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.DefaultSkin">
            <summary>Gets or sets the default skin. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Backpack.DefaultSkinId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.DefaultSkinId">
            <summary>Gets or sets the default skin identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.InfixUpgrade">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Items.Backpack.InfusionSlots">
            <summary>Gets or sets the item's infusion slots.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.SecondarySuffixItem">
            <summary>Gets or sets the item's secondary suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Backpack.SecondarySuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.SecondarySuffixItemId">
            <summary>Gets or sets the item's secondary suffix item identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.SuffixItem">
            <summary>Gets or sets the item's suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Backpack.SuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Backpack.SuffixItemId">
            <summary>Gets or sets the item's suffix item identifier.</summary>
        </member>
        <member name="M:GW2NET.Items.Backpack.GetItemChatLink">
            <summary>Gets an item chat link for this item.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="T:GW2NET.Items.Bag">
            <summary>Represents a bag.</summary>
        </member>
        <member name="P:GW2NET.Items.Bag.NoSellOrSort">
            <summary>Gets or sets a value indicating whether this is an invisible bag.</summary>
        </member>
        <member name="P:GW2NET.Items.Bag.Size">
            <summary>Gets or sets the bag's capacity.</summary>
        </member>
        <member name="T:GW2NET.Items.CombatAttribute">
            <summary>Provides the base class for combat attribute modifiers.</summary>
        </member>
        <member name="P:GW2NET.Items.CombatAttribute.Modifier">
            <summary>Gets or sets the modifier for the attribute.</summary>
        </member>
        <member name="T:GW2NET.Items.CombatBuff">
            <summary>Represents an item's combat buff.</summary>
        </member>
        <member name="P:GW2NET.Items.CombatBuff.Description">
            <summary>Gets or sets the buff's description.</summary>
        </member>
        <member name="P:GW2NET.Items.CombatBuff.SkillId">
            <summary>Gets or sets the buff's skill identifier.</summary>
        </member>
        <member name="M:GW2NET.Items.CombatBuff.GetSkillChatLink">
            <summary>Gets a skill chat link for this item buff.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="T:GW2NET.Items.ConcentrationModifier">
            <summary>The Concentration (boon duration) modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.ConditionDamageModifier">
            <summary>The Condition Damage modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.ExpertiseModifier">
            <summary>The Condition Duration modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.FerocityModifier">
            <summary>The Ferocity modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.HealingModifier">
            <summary>The Healing modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.InfixUpgrade">
            <summary>Represents an item's infixed combat upgrades.</summary>
        </member>
        <member name="P:GW2NET.Items.InfixUpgrade.Attributes">
            <summary>Gets or sets the combat attribute modifiers.</summary>
        </member>
        <member name="P:GW2NET.Items.InfixUpgrade.Buff">
            <summary>Gets or sets the buff. This is used for Boon Duration, Condition Duration, or additional attribute bonuses for ascended trinkets or back items.</summary>
        </member>
        <member name="T:GW2NET.Items.InfusionSlot">
            <summary>Represents one of an item's infusion slots.</summary>
        </member>
        <member name="P:GW2NET.Items.InfusionSlot.Flags">
            <summary>Gets or sets the infusion slot's type(s).</summary>
        </member>
        <member name="P:GW2NET.Items.InfusionSlot.Item">
            <summary>Gets or sets the infusion slot's item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.InfusionSlot.ItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.InfusionSlot.ItemId">
            <summary>Gets or sets the infusion slot's item identifier.</summary>
        </member>
        <member name="M:GW2NET.Items.InfusionSlot.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Items.InfusionSlotFlags">
            <summary>Enumerates the known infusion slot types.</summary>
        </member>
        <member name="F:GW2NET.Items.InfusionSlotFlags.Agony">
            <summary>The 'Agony' infusion slot type.</summary>
        </member>
        <member name="F:GW2NET.Items.InfusionSlotFlags.Defense">
            <summary>The 'Defense' infusion slot type.</summary>
        </member>
        <member name="F:GW2NET.Items.InfusionSlotFlags.Offense">
            <summary>The 'Offense' infusion slot type.</summary>
        </member>
        <member name="F:GW2NET.Items.InfusionSlotFlags.Utility">
            <summary>The 'Utility' infusion slot type.</summary>
        </member>
        <member name="T:GW2NET.Items.ISkinnable">
            <summary>Provides the interface for items that have a visual appearance in the game.</summary>
        </member>
        <member name="P:GW2NET.Items.ISkinnable.DefaultSkin">
            <summary>Gets or sets the default skin. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.ISkinnable.DefaultSkinId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.ISkinnable.DefaultSkinId">
            <summary>Gets or sets the default skin identifier.</summary>
        </member>
        <member name="T:GW2NET.Items.IUpgradable">
            <summary>Provides the interface for items that can be upgraded.</summary>
        </member>
        <member name="P:GW2NET.Items.IUpgradable.InfusionSlots">
            <summary>Gets or sets the item's infusion slots.</summary>
        </member>
        <member name="P:GW2NET.Items.IUpgradable.SecondarySuffixItem">
            <summary>Gets or sets the item's secondary suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.IUpgradable.SecondarySuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.IUpgradable.SecondarySuffixItemId">
            <summary>Gets or sets the item's secondary suffix item identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.IUpgradable.SuffixItem">
            <summary>Gets or sets the item's suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.IUpgradable.SuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.IUpgradable.SuffixItemId">
            <summary>Gets or sets the item's suffix item identifier.</summary>
        </member>
        <member name="T:GW2NET.Items.IUpgrade">
            <summary>Provides the interface for items that provide combat bonuses while equipped.</summary>
        </member>
        <member name="P:GW2NET.Items.IUpgrade.InfixUpgrade">
            <summary>Gets or sets the item's infixed combat upgrades.</summary>
            <exception cref="T:System.ArgumentNullException">The value is a null reference.</exception>
        </member>
        <member name="T:GW2NET.Items.PowerModifier">
            <summary>The Power modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.PrecisionModifier">
            <summary>The Precision modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.ToughnessModifier">
            <summary>The Toughness modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownModifier">
            <summary>A modifier for an unknown combat attribute.</summary>
        </member>
        <member name="T:GW2NET.Items.VitalityModifier">
            <summary>The Vitality modifier.</summary>
        </member>
        <member name="T:GW2NET.Items.Alcohol">
            <summary>Represents a drink.</summary>
        </member>
        <member name="T:GW2NET.Items.AppearanceChanger">
            <summary>Represents an appearance changing consumable item.</summary>
        </member>
        <member name="T:GW2NET.Items.BagSlotUnlocker">
            <summary>Represents a bag slot unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.BankTabUnlocker">
            <summary>Represents a bank tab unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.ChampionUnlocker">
            <summary>Represents a champion unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.CollectibleCapacityUnlocker">
            <summary>Represents a collectible capacity unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.Consumable">
            <summary>Provides the base class for consumable types.</summary>
        </member>
        <member name="T:GW2NET.Items.ContentUnlocker">
            <summary>Represents a content unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.ContractNpc">
            <summary>Represents a contract NPC.</summary>
        </member>
        <member name="T:GW2NET.Items.CraftingRecipeUnlocker">
            <summary>Represents a crafting recipe.</summary>
        </member>
        <member name="P:GW2NET.Items.CraftingRecipeUnlocker.Recipe">
            <summary>Gets or sets the recipe. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.CraftingRecipeUnlocker.RecipeId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.CraftingRecipeUnlocker.RecipeId">
            <summary>Gets or sets the identifier of the recipe that is unlocked by the current item.</summary>
        </member>
        <member name="P:GW2NET.Items.CraftingRecipeUnlocker.ExtraRecipeIds">
            <summary>Gets or sets the identifiers of additional recipes that are unlocked by the current item.</summary>
        </member>
        <member name="T:GW2NET.Items.DyeUnlocker">
            <summary>Represents a dye.</summary>
        </member>
        <member name="P:GW2NET.Items.DyeUnlocker.Color">
            <summary>Gets or sets the color. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.DyeUnlocker.ColorId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.DyeUnlocker.ColorId">
            <summary>Gets or sets the color identifier.</summary>
        </member>
        <member name="T:GW2NET.Items.Food">
            <summary>Represents an edible item.</summary>
        </member>
        <member name="P:GW2NET.Items.Food.Duration">
            <summary>Gets or sets the consumable's effect duration.</summary>
        </member>
        <member name="P:GW2NET.Items.Food.Effect">
            <summary>Gets or sets the consumable's effect.</summary>
        </member>
        <member name="T:GW2NET.Items.GenericConsumable">
            <summary>Represents a generic consumable item.</summary>
        </member>
        <member name="P:GW2NET.Items.GenericConsumable.Duration">
            <summary>Gets or sets the consumable's effect duration.</summary>
        </member>
        <member name="P:GW2NET.Items.GenericConsumable.Effect">
            <summary>Gets or sets the consumable's effect.</summary>
        </member>
        <member name="T:GW2NET.Items.GliderSkinUnlocker">
            <summary>Represents an glider skin unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.HalloweenConsumable">
            <summary>Represents a halloween consumable item.</summary>
        </member>
        <member name="T:GW2NET.Items.ImmediateConsumable">
            <summary>Represents an immediate consumable item.</summary>
        </member>
        <member name="P:GW2NET.Items.ImmediateConsumable.Duration">
            <summary>Gets or sets the consumable's effect duration.</summary>
        </member>
        <member name="P:GW2NET.Items.ImmediateConsumable.Effect">
            <summary>Gets or sets the consumable's effect.</summary>
        </member>
        <member name="T:GW2NET.Items.OutfitUnlocker">
            <summary>Represents an outfit unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.TeleportToFriend">
            <summary>Represents an object that teleports the player to a party member's location.</summary>
        </member>
        <member name="T:GW2NET.Items.Transmutation">
            <summary>Represents a transmutation item.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownConsumable">
            <summary>Represents an unknown consumable item.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownUnlocker">
            <summary>Represents an unknown unlock item.</summary>
        </member>
        <member name="T:GW2NET.Items.Unlocker">
            <summary>Provides the base class for unlock consumable types.</summary>
        </member>
        <member name="T:GW2NET.Items.UnTransmutation">
            <summary>Represents a un-transmutation item.</summary>
        </member>
        <member name="T:GW2NET.Items.UpgradeRemoval">
            <summary>Represents an upgrade removal item.</summary>
        </member>
        <member name="T:GW2NET.Items.Utility">
            <summary>Represents a utility consumable item.</summary>
        </member>
        <member name="P:GW2NET.Items.Utility.Duration">
            <summary>Gets or sets the consumable's effect duration.</summary>
        </member>
        <member name="P:GW2NET.Items.Utility.Effect">
            <summary>Gets or sets the consumable's effect.</summary>
        </member>
        <member name="T:GW2NET.Items.Container">
            <summary>Provides the base class for container types.</summary>
        </member>
        <member name="T:GW2NET.Items.DefaultContainer">
            <summary>Represents a default container.</summary>
        </member>
        <member name="T:GW2NET.Items.GiftBox">
            <summary>Represents a gift box container.</summary>
        </member>
        <member name="T:GW2NET.Items.OpenUiContainer">
            <summary>Represents a container that opens a user interface.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownContainer">
            <summary>Represents an unknown container.</summary>
        </member>
        <member name="T:GW2NET.Items.CraftingMaterial">
            <summary>Represents a crafting material.</summary>
        </member>
        <member name="T:GW2NET.Items.GameTypes">
            <summary>Enumerates known game type restrictions.</summary>
        </member>
        <member name="F:GW2NET.Items.GameTypes.None">
            <summary>Indicates no game type restrictions.</summary>
        </member>
        <member name="F:GW2NET.Items.GameTypes.Activity">
            <summary>The 'Activity' game type restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.GameTypes.Dungeon">
            <summary>The 'Dungeon' game type restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.GameTypes.PvE">
            <summary>The 'Player versus Environment' game type restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.GameTypes.PvP">
            <summary>The 'Player versus Player' game type restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.GameTypes.PvPLobby">
            <summary>The 'Player versus Player Lobby' game type restriction.</summary>
            <remarks>Indicates an item that can be used in 'Heart of the Mists'.</remarks>
        </member>
        <member name="F:GW2NET.Items.GameTypes.WvW">
            <summary>The 'World versus World' game type restriction.</summary>
        </member>
        <member name="T:GW2NET.Items.ForagingTool">
            <summary>Represents a foraging tool.</summary>
        </member>
        <member name="T:GW2NET.Items.GatheringTool">
            <summary>Provides the base class for gathering tool types.</summary>
        </member>
        <member name="P:GW2NET.Items.GatheringTool.DefaultSkin">
            <summary>Gets or sets the default skin. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.GatheringTool.DefaultSkinId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.GatheringTool.DefaultSkinId">
            <summary>Gets or sets the default skin identifier.</summary>
        </member>
        <member name="T:GW2NET.Items.LoggingTool">
            <summary>Represents a logging tool.</summary>
        </member>
        <member name="T:GW2NET.Items.MiningTool">
            <summary>Represents a mining tool.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownGatheringTool">
            <summary>Represents an unknown gathering tool.</summary>
        </member>
        <member name="T:GW2NET.Items.ContainerKey">
            <summary>Represents a key that opens a container.</summary>
        </member>
        <member name="T:GW2NET.Items.DefaultGizmo">
            <summary>Represents a default gizmo.</summary>
        </member>
        <member name="T:GW2NET.Items.Gizmo">
            <summary>Provides the base class for gizmo types.</summary>
        </member>
        <member name="T:GW2NET.Items.RentableContractNpc">
            <summary>Represents a rentable contract NPC.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownGizmo">
            <summary>Represents an unknown gizmo.</summary>
        </member>
        <member name="T:GW2NET.Items.UnlimitedConsumable">
            <summary>Represents an unlimited consumable gizmo.</summary>
        </member>
        <member name="T:GW2NET.Items.IItemRepository">
            <summary>Provides the interface for repositories that provide localized item details.</summary>
        </member>
        <member name="T:GW2NET.Items.Item">
            <summary>Provides the base class for types that represent an in-game item.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.BuildId">
            <summary>Gets or sets the item's build number. Default: 0. Assign a build number for change tracking.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Description">
            <summary>Gets or sets the item's description.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Flags">
            <summary>Gets or sets the item's additional flags.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.GameTypes">
            <summary>Gets or sets the item's game types.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.IconFileId">
            <summary>Gets or sets the item's icon identifier for use with the render service.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.IconFileSignature">
            <summary>Gets or sets the item's icon signature for use with the render service.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.IconFileUrl">
            <summary>Gets or sets the icon file URL.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.ItemId">
            <summary>Gets or sets the item's identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Level">
            <summary>Gets or sets the item's level.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Name">
            <summary>Gets or sets the name of the item.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Rarity">
            <summary>Gets or sets the item's rarity.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.Restrictions">
            <summary>Gets or sets the item's restrictions.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.VendorValue">
            <summary>Gets or sets the item's vendor value.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.ChatLink">
            <summary>Gets or sets a chat code that links to the current item in-game.</summary>
        </member>
        <member name="P:GW2NET.Items.Item.GW2NET#Common#IRenderable#FileId">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Items.Item.GW2NET#Common#IRenderable#FileSignature">
            <inheritdoc />
        </member>
        <member name="M:GW2NET.Items.Item.op_Equality(GW2NET.Items.Item,GW2NET.Items.Item)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Items.Item.op_Inequality(GW2NET.Items.Item,GW2NET.Items.Item)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Items.Item.Equals(GW2NET.Items.Item)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Items.Item.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Items.Item.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Items.Item.GetItemChatLink">
            <summary>Gets an item chat link for this item.</summary>
            <returns>The <see cref="P:GW2NET.Items.Item.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.Items.Item.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Items.ItemFlags">
            <summary>Enumerates the known additional item flags.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.None">
            <summary>Indicates no additional item flags.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.AccountBound">
            <summary>The 'Account Bound' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.HideSuffix">
            <summary>The 'Hide Suffix' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.NoMysticForge">
            <summary>The 'No Mystic Forge' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.NoSalvage">
            <summary>The 'No Salvage' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.NoSell">
            <summary>The 'No Sell' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.NotUpgradeable">
            <summary>The 'Not Upgradeable' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.NoUnderwater">
            <summary>The 'No Underwater' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.SoulBindOnAcquire">
            <summary>The 'Soul Bind On Acquire' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.SoulBindOnUse">
            <summary>The 'Soul Bind On Use' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.Unique">
            <summary>The 'Unique' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.AccountBindOnUse">
            <summary>The 'Account Bind On Use' item flag.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemFlags.MonsterOnly">
            <summary>The 'Monster Only' item flag.</summary>
        </member>
        <member name="T:GW2NET.Items.ItemRarity">
            <summary>Enumerates known item rarities.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Unknown">
            <summary>The 'Unknown' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Junk">
            <summary>The 'Junk' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Basic">
            <summary>The 'Basic' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Fine">
            <summary>The 'Fine' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Masterwork">
            <summary>The 'Masterwork' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Rare">
            <summary>The 'Rare' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Exotic">
            <summary>The 'Exotic' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Ascended">
            <summary>The 'Ascended' item rarity.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRarity.Legendary">
            <summary>The 'Legendary' item rarity.</summary>
        </member>
        <member name="T:GW2NET.Items.ItemRestrictions">
            <summary>Enumerates known item restrictions.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.None">
            <summary>Indicates no restrictions.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Asura">
            <summary>The 'Asura' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Charr">
            <summary>The 'Charr' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Human">
            <summary>The 'Human' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Norn">
            <summary>The 'Norn' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Sylvari">
            <summary>The 'Sylvari' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Elementalist">
            <summary>The 'Elementalist' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Engineer">
            <summary>The 'Engineer' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Guardian">
            <summary>The 'Guardian' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Mesmer">
            <summary>The 'Mesmer' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Necromancer">
            <summary>The 'Necromancer' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Ranger">
            <summary>The 'Ranger' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Thief">
            <summary>The 'Thief' item restriction.</summary>
        </member>
        <member name="F:GW2NET.Items.ItemRestrictions.Warrior">
            <summary>The 'Warrior' item restriction.</summary>
        </member>
        <member name="T:GW2NET.Items.ItemStack">
            <summary>Represents a stack of items.</summary>
        </member>
        <member name="P:GW2NET.Items.ItemStack.Count">
            <summary>Gets or sets the number of items in this stack.</summary>
        </member>
        <member name="P:GW2NET.Items.ItemStack.Item">
            <summary>Gets or sets the item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.ItemStack.ItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.ItemStack.ItemId">
            <summary>Gets or sets the item identifier.</summary>
        </member>
        <member name="M:GW2NET.Items.ItemStack.GetItemChatLink">
            <summary>Gets an item chat link for this item.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.Items.ItemStack.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Items.Miniature">
            <summary>Represents a miniature pet.</summary>
        </member>
        <member name="T:GW2NET.Items.SalvageTool">
            <summary>Represents a salvaging tool.</summary>
        </member>
        <member name="P:GW2NET.Items.SalvageTool.Charges">
            <summary>Gets or sets the tool's charges.</summary>
        </member>
        <member name="T:GW2NET.Items.Tool">
            <summary>Provides the base class for tool types.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownTool">
            <summary>Represents an unknown tool.</summary>
        </member>
        <member name="T:GW2NET.Items.TraitGuide">
            <summary>Represents a trait guide.</summary>
        </member>
        <member name="T:GW2NET.Items.Accessory">
            <summary>Represents an accessory.</summary>
        </member>
        <member name="T:GW2NET.Items.Amulet">
            <summary>Represents an amulet.</summary>
        </member>
        <member name="T:GW2NET.Items.Ring">
            <summary>Represents a ring.</summary>
        </member>
        <member name="T:GW2NET.Items.Trinket">
            <summary>Provides the base class for trinket types.</summary>
        </member>
        <member name="P:GW2NET.Items.Trinket.InfixUpgrade">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Items.Trinket.InfusionSlots">
            <summary>Gets or sets the item's infusion slots.</summary>
        </member>
        <member name="P:GW2NET.Items.Trinket.SecondarySuffixItem">
            <summary>Gets or sets the item's secondary suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Trinket.SecondarySuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Trinket.SecondarySuffixItemId">
            <summary>Gets or sets the item's secondary suffix item identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Trinket.SuffixItem">
            <summary>Gets or sets the item's suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Trinket.SuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Trinket.SuffixItemId">
            <summary>Gets or sets the item's suffix item identifier.</summary>
        </member>
        <member name="M:GW2NET.Items.Trinket.GetItemChatLink">
            <summary>Gets an item chat link for this item.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="T:GW2NET.Items.UnknownTrinket">
            <summary>Represents an unknown trinket.</summary>
        </member>
        <member name="T:GW2NET.Items.Trophy">
            <summary>Represents a trophy.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownItem">
            <summary>Represents an unknown item..</summary>
        </member>
        <member name="T:GW2NET.Items.DefaultUpgradeComponent">
            <summary>Represents a default upgrade component.</summary>
        </member>
        <member name="T:GW2NET.Items.Gem">
            <summary>Represents a gem upgrade component.</summary>
        </member>
        <member name="T:GW2NET.Items.Rune">
            <summary>Represents a rune upgrade component.</summary>
        </member>
        <member name="T:GW2NET.Items.Sigil">
            <summary>Represents a sigil upgrade component.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownUpgradeComponent">
            <summary>Represents an unknown upgrade component.</summary>
        </member>
        <member name="T:GW2NET.Items.UpgradeComponent">
            <summary>Provides the base class for upgrade component types.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponent.infixUpgrade">
            <summary>Backing field for <see cref="P:GW2NET.Items.UpgradeComponent.InfixUpgrade"/>.</summary>
        </member>
        <member name="P:GW2NET.Items.UpgradeComponent.Bonuses">
            <summary>Gets or sets the upgrade component's bonuses.</summary>
        </member>
        <member name="P:GW2NET.Items.UpgradeComponent.InfixUpgrade">
            <summary>Gets or sets the item's infixed combat upgrades.</summary>
        </member>
        <member name="P:GW2NET.Items.UpgradeComponent.InfusionUpgradeFlags">
            <summary>Gets or sets the upgrade component's infusion upgrades.</summary>
        </member>
        <member name="P:GW2NET.Items.UpgradeComponent.Suffix">
            <summary>Gets or sets the upgrade component's suffix.</summary>
        </member>
        <member name="P:GW2NET.Items.UpgradeComponent.UpgradeComponentFlags">
            <summary>Gets or sets the upgrade component's flags.</summary>
        </member>
        <member name="T:GW2NET.Items.UpgradeComponentFlags">
            <summary>Enumerates the possible upgrade component flags.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.None">
            <summary>Indicates no upgrade component flags.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Trinket">
            <summary>The 'Trinket' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.LightArmor">
            <summary>The 'Light Armor' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.MediumArmor">
            <summary>The 'Medium Armor' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.HeavyArmor">
            <summary>The 'Heavy Armor' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Axe">
            <summary>The 'Axe' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Dagger">
            <summary>The 'Dagger' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Focus">
            <summary>The 'Focus' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Greatsword">
            <summary>The 'Great sword' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Hammer">
            <summary>The 'Hammer' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Harpoon">
            <summary>The 'Harpoon' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.LongBow">
            <summary>The 'Long Bow' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Mace">
            <summary>The 'Mace' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Pistol">
            <summary>The 'Pistol' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Rifle">
            <summary>The 'Rifle' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Scepter">
            <summary>The 'Scepter' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Shield">
            <summary>The 'Shield' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.ShortBow">
            <summary>The 'Short Bow' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Speargun">
            <summary>The 'Spear gun' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Staff">
            <summary>The 'Staff' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Sword">
            <summary>The 'Sword' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Torch">
            <summary>The 'Torch' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Trident">
            <summary>The 'Trident' upgrade component flag.</summary>
        </member>
        <member name="F:GW2NET.Items.UpgradeComponentFlags.Warhorn">
            <summary>The 'War horn' upgrade component flag.</summary>
        </member>
        <member name="T:GW2NET.Items.Axe">
            <summary>Represents an axe.</summary>
        </member>
        <member name="T:GW2NET.Items.Dagger">
            <summary>Represents a dagger.</summary>
        </member>
        <member name="T:GW2NET.Items.DamageType">
            <summary>Enumerates the possible weapon damage types.</summary>
        </member>
        <member name="F:GW2NET.Items.DamageType.Unknown">
            <summary>The 'Unknown' damage type.</summary>
        </member>
        <member name="F:GW2NET.Items.DamageType.Fire">
            <summary>The 'Fire' damage type.</summary>
        </member>
        <member name="F:GW2NET.Items.DamageType.Ice">
            <summary>The 'Ice' damage type.</summary>
        </member>
        <member name="F:GW2NET.Items.DamageType.Lightning">
            <summary>The 'Lightning' damage type.</summary>
        </member>
        <member name="F:GW2NET.Items.DamageType.Physical">
            <summary>The 'Physical' damage type.</summary>
        </member>
        <member name="F:GW2NET.Items.DamageType.Choking">
            <summary>The 'Choking' damage type.</summary>
        </member>
        <member name="T:GW2NET.Items.Focus">
            <summary>Represents a focus.</summary>
        </member>
        <member name="T:GW2NET.Items.GreatSword">
            <summary>Represents a great sword.</summary>
        </member>
        <member name="T:GW2NET.Items.Hammer">
            <summary>Represents a hammer.</summary>
        </member>
        <member name="T:GW2NET.Items.Harpoon">
            <summary>Represents a harpoon.</summary>
        </member>
        <member name="T:GW2NET.Items.LargeBundle">
            <summary>Represents a large bundle.</summary>
        </member>
        <member name="T:GW2NET.Items.LongBow">
            <summary>Represents a long bow.</summary>
        </member>
        <member name="T:GW2NET.Items.Mace">
            <summary>Represents a mace.</summary>
        </member>
        <member name="T:GW2NET.Items.Pistol">
            <summary>Represents a pistol.</summary>
        </member>
        <member name="T:GW2NET.Items.Rifle">
            <summary>Represents a rifle.</summary>
        </member>
        <member name="T:GW2NET.Items.Scepter">
            <summary>Represents a scepter.</summary>
        </member>
        <member name="T:GW2NET.Items.Shield">
            <summary>Represents a shield.</summary>
        </member>
        <member name="T:GW2NET.Items.ShortBow">
            <summary>Represents a short bow.</summary>
        </member>
        <member name="T:GW2NET.Items.SmallBundle">
            <summary>Represents a small bundle.</summary>
        </member>
        <member name="T:GW2NET.Items.SpearGun">
            <summary>Represents a spear gun.</summary>
        </member>
        <member name="T:GW2NET.Items.Staff">
            <summary>Represents a staff.</summary>
        </member>
        <member name="T:GW2NET.Items.Sword">
            <summary>Represents a sword.</summary>
        </member>
        <member name="T:GW2NET.Items.Torch">
            <summary>Represents a torch.</summary>
        </member>
        <member name="T:GW2NET.Items.Toy">
            <summary>Represents a toy.</summary>
        </member>
        <member name="T:GW2NET.Items.Trident">
            <summary>Represents a trident.</summary>
        </member>
        <member name="T:GW2NET.Items.TwoHandedToy">
            <summary>Represents a two-handed toy.</summary>
        </member>
        <member name="T:GW2NET.Items.UnknownWeapon">
            <summary>Represents an unknown weapon.</summary>
        </member>
        <member name="T:GW2NET.Items.WarHorn">
            <summary>Represents a war horn.</summary>
        </member>
        <member name="T:GW2NET.Items.Weapon">
            <summary>Provides the base class for weapon types.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.DamageType">
            <summary>Gets or sets the weapon's damage type.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.DefaultSkin">
            <summary>Gets or sets the default skin. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Weapon.DefaultSkinId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.DefaultSkinId">
            <summary>Gets or sets the default skin identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.Defense">
            <summary>Gets or sets the weapon's defense.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.InfixUpgrade">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Items.Weapon.InfusionSlots">
            <summary>Gets or sets the item's infusion slots.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.MaximumPower">
            <summary>Gets or sets the weapon's maximum power.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.MinimumPower">
            <summary>Gets or sets the weapon's minimum power.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.SecondarySuffixItem">
            <summary>Gets or sets the item's secondary suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Weapon.SecondarySuffixItem"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.SecondarySuffixItemId">
            <summary>Gets or sets the item's secondary suffix item identifier.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.SuffixItem">
            <summary>Gets or sets the item's suffix item. This is a navigation property. Use the value of <see cref="P:GW2NET.Items.Weapon.SuffixItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Items.Weapon.SuffixItemId">
            <summary>Gets or sets the item's suffix item identifier.</summary>
        </member>
        <member name="M:GW2NET.Items.Weapon.GetItemChatLink">
            <summary>Gets an item chat link for this item.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="T:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract">
            <summary>Wraps a collection of dynamic event rotations.</summary>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract"/> class that is empty and has the default initial capacity.
            </summary>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract.#ctor(System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract"/> class that is empty and has the specified initial capacity.</summary>
            <param name="capacity">The number of elements that the new list can initially store.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than 0. </exception>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract.#ctor(System.Collections.Generic.IEnumerable{GW2NET.Local.DynamicEvents.Xml.RotationContract})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Local.DynamicEvents.Xml.RotationCollectionContract"/> class that contains elements copied from the specified collection and has sufficient capacity to accommodate the number of elements copied.</summary>
            <param name="collection">The collection whose elements are copied to the new list.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="collection"/> is null.</exception>
        </member>
        <member name="T:GW2NET.Local.DynamicEvents.Xml.RotationContract">
            <summary>Represents a dynamic event rotation.</summary>
        </member>
        <member name="P:GW2NET.Local.DynamicEvents.Xml.RotationContract.EventId">
            <summary>Gets or sets the event identifier.</summary>
        </member>
        <member name="P:GW2NET.Local.DynamicEvents.Xml.RotationContract.Shifts">
            <summary>Gets or sets a collection of rotating shifts.</summary>
        </member>
        <member name="T:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract">
            <summary>Wraps a collection of rotating shifts.</summary>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract"/> class that is empty and has the default initial capacity.
            </summary>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract.#ctor(System.Int32)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract"/> class that is empty and has the specified initial capacity.</summary>
            <param name="capacity">The number of elements that the new list can initially store.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than 0. </exception>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract"/> class that contains elements copied from the specified collection and has sufficient capacity to accommodate the number of elements copied.</summary>
            <param name="collection">The collection whose elements are copied to the new list.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="collection"/> is null.</exception>
        </member>
        <member name="T:GW2NET.Local.DynamicEvents.DynamicEventRotationService">
            <summary>Provides the default implementation of the event rotations service.</summary>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.DynamicEventRotationService.GetDynamicEventRotations">
            <summary>Gets a collection of dynamic events and their rotating shifts</summary>
            <returns>A collection of dynamic events and their rotating shifts.</returns>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.DynamicEventRotationService.ConvertRotationCollectionContract(System.Collections.Generic.ICollection{GW2NET.Local.DynamicEvents.Xml.RotationContract})">
            <summary>Infrastructure. Converts contracts to entities.</summary>
            <param name="content">The content.</param>
            <returns>A collection of entities.</returns>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.DynamicEventRotationService.ConvertRotationContract(GW2NET.Local.DynamicEvents.Xml.RotationContract)">
            <summary>Infrastructure. Converts contracts to entities.</summary>
            <param name="content">The content.</param>
            <returns>An entity.</returns>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.DynamicEventRotationService.ConvertShiftCollectionContract(GW2NET.Local.DynamicEvents.Xml.ShiftCollectionContract)">
            <summary>Infrastructure. Converts contracts to entities.</summary>
            <param name="content">The content.</param>
            <returns>A collection of entities.</returns>
        </member>
        <member name="T:GW2NET.Local.DynamicEvents.IDynamicEventRotationService">
            <summary>Provides the interface for the event rotations service.</summary>
        </member>
        <member name="M:GW2NET.Local.DynamicEvents.IDynamicEventRotationService.GetDynamicEventRotations">
            <summary>Gets a collection of dynamic events and their rotating shifts</summary>
            <returns>A collection of dynamic events and their rotating shifts.</returns>
        </member>
        <member name="T:GW2NET.Maps.Continent">
            <summary>Represents a continent.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.ContinentDimensions">
            <summary>Gets or sets the dimensions of the continent.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.ContinentId">
            <summary>Gets or sets the the continent identifier.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.FloorIds">
            <summary>Gets or sets a collection of floor identifiers.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.Floors">
            <summary>Gets or sets a collection of floors. This is a navigation property. Use the value of <see cref="P:GW2NET.Maps.Continent.FloorIds"/> to obtain references.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.MaximumZoom">
            <summary>Gets or sets the maximum zoom level for use with the map tile service.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.MinimumZoom">
            <summary>Gets or sets the minimum zoom level for use with the map tile service.</summary>
        </member>
        <member name="P:GW2NET.Maps.Continent.Name">
            <summary>Gets or sets the name of the continent.</summary>
        </member>
        <member name="M:GW2NET.Maps.Continent.op_Equality(GW2NET.Maps.Continent,GW2NET.Maps.Continent)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Continent.op_Inequality(GW2NET.Maps.Continent,GW2NET.Maps.Continent)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Continent.Equals(GW2NET.Maps.Continent)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.Continent.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.Continent.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.Continent.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.Dungeon">
            <summary>Represents a dungeon entrance.</summary>
        </member>
        <member name="T:GW2NET.Maps.Floor">
            <summary>Represents a map floor, used to populate a world map. All coordinates are map coordinates.</summary>
            <remarks>The returned data only contains static content. Dynamic content, such as vendors, is not currently available.</remarks>
        </member>
        <member name="P:GW2NET.Maps.Floor.ClampedView">
            <summary>Gets or sets a rectangle of downloadable textures. Every tile coordinate outside of this rectangle is not available on the tile server.</summary>
        </member>
        <member name="P:GW2NET.Maps.Floor.Continent">
            <summary>Gets or sets the continent. This is a navigation property. Use the value of <see cref="P:GW2NET.Maps.Floor.ContinentId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Maps.Floor.ContinentId">
            <summary>Gets or sets the continent identifier.</summary>
        </member>
        <member name="P:GW2NET.Maps.Floor.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Maps.Floor.FloorId">
            <summary>Gets or sets the floor identifier.</summary>
        </member>
        <member name="P:GW2NET.Maps.Floor.Regions">
            <summary>Gets or sets the collection of regions.</summary>
        </member>
        <member name="P:GW2NET.Maps.Floor.TextureDimensions">
            <summary>Gets or sets the texture's dimensions.</summary>
        </member>
        <member name="M:GW2NET.Maps.Floor.Equals(GW2NET.Maps.Floor)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.Floor.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.Floor.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.Floor.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.IContinentRepository">
            <summary>Provides the interface for repositories that provide localized continent details.</summary>
        </member>
        <member name="T:GW2NET.Maps.IFloorRepository">
            <summary>Provides the interface for repositories that provide localized floor details.</summary>
        </member>
        <member name="P:GW2NET.Maps.IFloorRepository.ContinentId">
            <summary>Gets the continent identifier.</summary>
        </member>
        <member name="T:GW2NET.Maps.IMapNameRepository">
            <summary>Provides the interface for repositories that provide localized map names.</summary>
        </member>
        <member name="T:GW2NET.Maps.IMapRepository">
            <summary>Provides the interface for repositories that provide localized map details.</summary>
        </member>
        <member name="T:GW2NET.Maps.Landmark">
            <summary>Represents a landmark.</summary>
        </member>
        <member name="T:GW2NET.Maps.Map">
            <summary>Represents a map and its details, including details about floor and translation data on how to translate between world coordinates and map coordinates.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.Continent">
            <summary>Gets or sets the continent that this map belongs to. This is a navigation property. Use the value of <see cref="P:GW2NET.Maps.Map.ContinentId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.ContinentId">
            <summary>Gets or sets the continent identifier of the continent that this map belongs to.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.ContinentName">
            <summary>Gets or sets the name of the continent that this map belongs to.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.ContinentRectangle">
            <summary>Gets or sets the dimensions of the map within the continent coordinate system.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.DefaultFloor">
            <summary>Gets or sets the default floor.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.Floors">
            <summary>Gets or sets a collection of floor identifiers.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.MapId">
            <summary>Gets or sets the map identifier.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.MapName">
            <summary>Gets or sets the name of the map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.MapRectangle">
            <summary>Gets or sets the dimensions of the map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.MaximumLevel">
            <summary>Gets or sets the maximum level of this map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.MinimumLevel">
            <summary>Gets or sets the minimum level of this map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.Region">
            <summary>Gets or sets the region that this map belongs to. This is a navigation property. Use the value of <see cref="P:GW2NET.Maps.Map.RegionId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.RegionId">
            <summary>Gets or sets the region identifier of the region that this map belongs to.</summary>
        </member>
        <member name="P:GW2NET.Maps.Map.RegionName">
            <summary>Gets or sets the name of the region that this map belongs to.</summary>
        </member>
        <member name="M:GW2NET.Maps.Map.op_Equality(GW2NET.Maps.Map,GW2NET.Maps.Map)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Map.op_Inequality(GW2NET.Maps.Map,GW2NET.Maps.Map)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Map.Equals(GW2NET.Maps.Map)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.Map.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.Map.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.Map.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:GW2NET.Maps.MapName">
            <summary>Represents a map and its localized name.</summary>
        </member>
        <member name="P:GW2NET.Maps.MapName.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Maps.MapName.MapId">
            <summary>Gets or sets the map identifier.</summary>
        </member>
        <member name="P:GW2NET.Maps.MapName.Name">
            <summary>Gets or sets the localized name of the map.</summary>
        </member>
        <member name="M:GW2NET.Maps.MapName.Equals(GW2NET.Maps.MapName)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.MapName.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.MapName.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.MapName.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.PointOfInterest">
            <summary>Represents a Point of Interest (POI) location.</summary>
        </member>
        <member name="P:GW2NET.Maps.PointOfInterest.Coordinates">
            <summary>Gets or sets the coordinates of this Point of Interest.</summary>
        </member>
        <member name="P:GW2NET.Maps.PointOfInterest.Floor">
            <summary>Gets or sets the floor of this Point of Interest.</summary>
        </member>
        <member name="P:GW2NET.Maps.PointOfInterest.Name">
            <summary>Gets or sets the name of this Point of Interest.</summary>
        </member>
        <member name="P:GW2NET.Maps.PointOfInterest.PointOfInterestId">
            <summary>Gets or sets the Point of Interest identifier.</summary>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.op_Equality(GW2NET.Maps.PointOfInterest,GW2NET.Maps.PointOfInterest)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.op_Inequality(GW2NET.Maps.PointOfInterest,GW2NET.Maps.PointOfInterest)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.Equals(GW2NET.Maps.PointOfInterest)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.GetMapChatLink">
            <summary>Gets a map chat link for this Point of Interest</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.Maps.PointOfInterest.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.Region">
            <summary>Represents a region on the map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Region.LabelCoordinates">
            <summary>Gets or sets the coordinates of the region label.</summary>
        </member>
        <member name="P:GW2NET.Maps.Region.Maps">
            <summary>Gets or sets a collection of maps and their details.</summary>
        </member>
        <member name="P:GW2NET.Maps.Region.Name">
            <summary>Gets or sets the name of the region.</summary>
        </member>
        <member name="P:GW2NET.Maps.Region.RegionId">
            <summary>Gets or sets the region identifier.</summary>
        </member>
        <member name="M:GW2NET.Maps.Region.op_Equality(GW2NET.Maps.Region,GW2NET.Maps.Region)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Region.op_Inequality(GW2NET.Maps.Region,GW2NET.Maps.Region)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Region.Equals(GW2NET.Maps.Region)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.Region.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.Region.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.Region.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.RenownTask">
            <summary>Represents a renown heart location.</summary>
        </member>
        <member name="P:GW2NET.Maps.RenownTask.Coordinates">
            <summary>Gets or sets the task's coordinates.</summary>
        </member>
        <member name="P:GW2NET.Maps.RenownTask.Level">
            <summary>Gets or sets the level.</summary>
        </member>
        <member name="P:GW2NET.Maps.RenownTask.Objective">
            <summary>Gets or sets the name or objective.</summary>
        </member>
        <member name="P:GW2NET.Maps.RenownTask.TaskId">
            <summary>Gets or sets the renown task identifier.</summary>
        </member>
        <member name="M:GW2NET.Maps.RenownTask.op_Equality(GW2NET.Maps.RenownTask,GW2NET.Maps.RenownTask)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.RenownTask.op_Inequality(GW2NET.Maps.RenownTask,GW2NET.Maps.RenownTask)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.RenownTask.Equals(GW2NET.Maps.RenownTask)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.RenownTask.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.RenownTask.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.RenownTask.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.Sector">
            <summary>Represents an area within a map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Sector.Coordinates">
            <summary>Gets or sets the sector's coordinates, which is (usually) the center position.</summary>
        </member>
        <member name="P:GW2NET.Maps.Sector.Level">
            <summary>Gets or sets the sector's level.</summary>
        </member>
        <member name="P:GW2NET.Maps.Sector.Name">
            <summary>Gets or sets the name of the sector.</summary>
        </member>
        <member name="P:GW2NET.Maps.Sector.SectorId">
            <summary>Gets or sets the sector identifier.</summary>
        </member>
        <member name="M:GW2NET.Maps.Sector.op_Equality(GW2NET.Maps.Sector,GW2NET.Maps.Sector)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Sector.op_Inequality(GW2NET.Maps.Sector,GW2NET.Maps.Sector)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Sector.Equals(GW2NET.Maps.Sector)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.Sector.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.Sector.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.Sector.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.SkillChallenge">
            <summary>Represents a skill challenge location.</summary>
        </member>
        <member name="P:GW2NET.Maps.SkillChallenge.Coordinates">
            <summary>Gets or sets the skill challenge's coordinates.</summary>
        </member>
        <member name="M:GW2NET.Maps.SkillChallenge.op_Equality(GW2NET.Maps.SkillChallenge,GW2NET.Maps.SkillChallenge)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.SkillChallenge.op_Inequality(GW2NET.Maps.SkillChallenge,GW2NET.Maps.SkillChallenge)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.SkillChallenge.Equals(GW2NET.Maps.SkillChallenge)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.SkillChallenge.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.SkillChallenge.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.SkillChallenge.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.Subregion">
            <summary>Represents a map and its details.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.ContinentRectangle">
            <summary>Gets or sets the dimensions of the map within the continent coordinate system.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.DefaultFloor">
            <summary>Gets or sets the default floor of this map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.Map">
            <summary>Gets or sets the map. This is a navigation property. Use the value of <see cref="P:GW2NET.Maps.Subregion.MapId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.MapId">
            <summary>Gets or sets the map identifier.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.MapRectangle">
            <summary>Gets or sets the dimensions of the map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.MaximumLevel">
            <summary>Gets or sets the maximum level of this map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.MinimumLevel">
            <summary>Gets or sets the minimum level of this map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.Name">
            <summary>Gets or sets the name of the map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.PointsOfInterest">
            <summary>Gets or sets a collection of Points of Interest locations.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.Sectors">
            <summary>Gets or sets a collection of areas within the map.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.SkillChallenges">
            <summary>Gets or sets a collection of skill challenge locations.</summary>
        </member>
        <member name="P:GW2NET.Maps.Subregion.Tasks">
            <summary>Gets or sets a collection of renown heart locations.</summary>
        </member>
        <member name="M:GW2NET.Maps.Subregion.op_Equality(GW2NET.Maps.Subregion,GW2NET.Maps.Subregion)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Subregion.op_Inequality(GW2NET.Maps.Subregion,GW2NET.Maps.Subregion)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Maps.Subregion.Equals(GW2NET.Maps.Subregion)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Maps.Subregion.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Maps.Subregion.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Maps.Subregion.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Maps.UnknownPointOfInterest">
            <summary>Represents an unknown point of interest.</summary>
        </member>
        <member name="T:GW2NET.Maps.Vista">
            <summary>Represents a vista.</summary>
        </member>
        <member name="T:GW2NET.Maps.Waypoint">
            <summary>Represents a waypoint.</summary>
        </member>
        <member name="T:GW2NET.Quaggans.IQuagganRepository">
            <summary>Provides the interface for repositories that provide Quaggan details.</summary>
        </member>
        <member name="T:GW2NET.Quaggans.Quaggan">
            <summary>Represents a Quaggan.</summary>
        </member>
        <member name="P:GW2NET.Quaggans.Quaggan.Id">
            <summary>Gets or sets the Quaggan identifier.</summary>
        </member>
        <member name="P:GW2NET.Quaggans.Quaggan.Url">
            <summary>Gets or sets the resource location.</summary>
        </member>
        <member name="M:GW2NET.Quaggans.Quaggan.op_Equality(GW2NET.Quaggans.Quaggan,GW2NET.Quaggans.Quaggan)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Quaggans.Quaggan.op_Inequality(GW2NET.Quaggans.Quaggan,GW2NET.Quaggans.Quaggan)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Quaggans.Quaggan.Equals(GW2NET.Quaggans.Quaggan)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Quaggans.Quaggan.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The object to compare with the current object. </param>
        </member>
        <member name="M:GW2NET.Quaggans.Quaggan.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:GW2NET.Quaggans.Quaggan.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Recipes.AmuletRecipe">
            <summary>Represents an amulet crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.AxeRecipe">
            <summary>Represents an axe crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.BackpackRecipe">
            <summary>Represents a backpack crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.BagRecipe">
            <summary>Represents a bag crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.BootsRecipe">
            <summary>Represents a foot protection crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.BulkRecipe">
            <summary>Represents a bulk crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.CoatRecipe">
            <summary>Represents a coat crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.ComponentRecipe">
            <summary>Represents a component crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.ConsumableRecipe">
            <summary>Represents a consumable crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.CraftingDisciplines">
            <summary>Enumerates known crafting disciplines.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.None">
            <summary>Indicates no crafting disciplines.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Armorsmith">
            <summary>The 'Armor smith' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Artificer">
            <summary>The 'Artificer' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Chef">
            <summary>The 'Chef' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Huntsman">
            <summary>The 'Huntsman' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Jeweler">
            <summary>The 'Jeweler' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Leatherworker">
            <summary>The 'Leatherworker' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Tailor">
            <summary>The 'Tailor' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Weaponsmith">
            <summary>The 'Weapon smith' crafting discipline.</summary>
        </member>
        <member name="F:GW2NET.Recipes.CraftingDisciplines.Scribe">
            <summary>The 'Scribe' crafting discipline.</summary>
        </member>
        <member name="T:GW2NET.Recipes.DaggerRecipe">
            <summary>Represents a dagger crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.DessertRecipe">
            <summary>Represents a dessert crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.DyeRecipe">
            <summary>Represents a dye crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.EarringRecipe">
            <summary>Represents an earring crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.FeastRecipe">
            <summary>Represents a feast crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.FocusRecipe">
            <summary>Represents a focus crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.GlovesRecipe">
            <summary>Represents an arm protection crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.GreatSwordRecipe">
            <summary>Represents a great sword crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.GuildConsumableWvw">
            <summary>Represents a crafting recipe for components used in crafting WvW guild upgrades.</summary>
        </member>
        <member name="T:GW2NET.Recipes.HammerRecipe">
            <summary>Represents a hammer crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.HarpoonRecipe">
            <summary>Represents a harpoon crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.HelmRecipe">
            <summary>Represents a head protection crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.IngredientCookingRecipe">
            <summary>Represents a cooking ingredient recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.InscriptionRecipe">
            <summary>Represents an inscription crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.InsigniaRecipe">
            <summary>Represents an insignia crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.IRecipeRepository">
            <summary>Provides the interface for repositories that provide localized recipe details.</summary>
        </member>
        <member name="M:GW2NET.Recipes.IRecipeRepository.DiscoverByInput(System.Int32)">
            <summary>Discovers identifiers of every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.Ingredients"/> collection contains the given <paramref name="identifier"/>.</summary>
            <param name="identifier">The identifier of the input item.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by input identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of identifiers for every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.Ingredients"/> collection contains the given <paramref name="identifier"/>.</returns>
        </member>
        <member name="M:GW2NET.Recipes.IRecipeRepository.DiscoverByInputAsync(System.Int32)">
            <summary>Discovers identifiers of every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.Ingredients"/> collection contains the given <paramref name="identifier"/>.</summary>
            <param name="identifier">The identifier of the input item.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by input identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of identifiers for every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.Ingredients"/> collection contains the given <paramref name="identifier"/>.</returns>
        </member>
        <member name="M:GW2NET.Recipes.IRecipeRepository.DiscoverByInputAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>Discovers identifiers of every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.Ingredients"/> collection contains the given <paramref name="identifier"/>.</summary>
            <param name="identifier">The identifier of the input item.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by input identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>A collection of identifiers for every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.Ingredients"/> collection contains the given <paramref name="identifier"/>.</returns>
        </member>
        <member name="M:GW2NET.Recipes.IRecipeRepository.DiscoverByOutput(System.Int32)">
            <summary>Discovers identifiers of every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> is set to the given <paramref name="identifier"/>.</summary>
            <param name="identifier">The identifier of the output item.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by output identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of identifiers for every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> is set to the given <paramref name="identifier"/>.</returns>
        </member>
        <member name="M:GW2NET.Recipes.IRecipeRepository.DiscoverByOutputAsync(System.Int32)">
            <summary>Discovers identifiers of every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> is set to the given <paramref name="identifier"/>.</summary>
            <param name="identifier">The identifier of the output item.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by output identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <returns>A collection of identifiers for every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> is set to the given <paramref name="identifier"/>.</returns>
        </member>
        <member name="M:GW2NET.Recipes.IRecipeRepository.DiscoverByOutputAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>Discovers identifiers of every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> is set to the given <paramref name="identifier"/>.</summary>
            <param name="identifier">The identifier of the output item.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> that provides cancellation support.</param>
            <exception cref="T:System.NotSupportedException">The data source does not support searching by output identifiers.</exception>
            <exception cref="T:GW2NET.Common.ServiceException">An error occurred while retrieving data from the data source.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">A task was canceled.</exception>
            <returns>A collection of identifiers for every <see cref="T:GW2NET.Recipes.Recipe"/> whose <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> is set to the given <paramref name="identifier"/>.</returns>
        </member>
        <member name="T:GW2NET.Recipes.LegendaryComponent">
            <summary>Represents a crafting recipe for components used in crafting equipment with legendary stats.</summary>
        </member>
        <member name="T:GW2NET.Recipes.LeggingsRecipe">
            <summary>Represents a leg protection crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.LongBowRecipe">
            <summary>Represents a long bow crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.MaceRecipe">
            <summary>Represents a mace crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.MealRecipe">
            <summary>Represents a meal crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.PistolRecipe">
            <summary>Represents a pistol crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.PotionRecipe">
            <summary>Represents a potion crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.Recipe">
            <summary>Provides the base class for types that represent a crafting recipe.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.BuildId">
            <summary>Gets or sets the recipe's build number. Default: 0. Assign a build number for change tracking.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.CraftingDisciplines">
            <summary>Gets or sets the crafting disciplines that can learn the recipe.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.Flags">
            <summary>Gets or sets the recipe's flags.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.Ingredients">
            <summary>Gets or sets a collection of the required ingredients.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.MinimumRating">
            <summary>Gets or sets the recipe's minimum rating.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.OutputItem">
            <summary>Gets or sets the output item. This is a navigation property. Use the value of <see cref="P:GW2NET.Recipes.Recipe.OutputItemId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.OutputItemCount">
            <summary>Gets or sets the amount of items produced.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.OutputItemId">
            <summary>Gets or sets the output item identifier.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.RecipeId">
            <summary>Gets or sets the recipe identifier.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.TimeToCraft">
            <summary>Gets or sets the time it takes to craft the recipe.</summary>
        </member>
        <member name="P:GW2NET.Recipes.Recipe.ChatLink">
            <summary>Gets or sets a chat code that links to the current recipe in-game.</summary>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.op_Equality(GW2NET.Recipes.Recipe,GW2NET.Recipes.Recipe)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.op_Inequality(GW2NET.Recipes.Recipe,GW2NET.Recipes.Recipe)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.Equals(GW2NET.Recipes.Recipe)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.GetRecipeChatLink">
            <summary>Gets a recipe chat link for this item recipe.</summary>
            <returns>The <see cref="P:GW2NET.Recipes.Recipe.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.Recipes.Recipe.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Recipes.RecipeFlags">
            <summary>Enumerates known recipe flags.</summary>
        </member>
        <member name="F:GW2NET.Recipes.RecipeFlags.None">
            <summary>Indicates no recipe flags.</summary>
        </member>
        <member name="F:GW2NET.Recipes.RecipeFlags.AutoLearned">
            <summary>The 'Auto Learned' recipe flag.</summary>
        </member>
        <member name="F:GW2NET.Recipes.RecipeFlags.LearnedFromItem">
            <summary>The 'Learned From Item' recipe flag.</summary>
        </member>
        <member name="T:GW2NET.Recipes.RefinementEctoplasmRecipe">
            <summary>Represents an ectoplasm refinement crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.RefinementObsidianRecipe">
            <summary>Represents an obsidian refinement crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.RefinementRecipe">
            <summary>Represents a material refinement crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.RifleRecipe">
            <summary>Represents a rifle crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.RingRecipe">
            <summary>Represents a ring crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.ScepterRecipe">
            <summary>Represents a scepter crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.SeasoningRecipe">
            <summary>Represents a seasoning crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.ShieldRecipe">
            <summary>Represents a shield crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.ShortBowRecipe">
            <summary>Represents a short bow crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.ShouldersRecipe">
            <summary>Represents a shoulder protection crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.SnackRecipe">
            <summary>Represents a snack crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.SoupRecipe">
            <summary>Represents a soup crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.SpearGunRecipe">
            <summary>Represents a spear gun crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.StaffRecipe">
            <summary>Represents a staff crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.SwordRecipe">
            <summary>Represents a sword crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.TorchRecipe">
            <summary>Represents a torch crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.TridentRecipe">
            <summary>Represents a trident crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.UnknownRecipe">
            <summary>Represents an unknown crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.UpgradeComponentRecipe">
            <summary>Represents an upgrade component crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Recipes.WarHornRecipe">
            <summary>Represents a war horn crafting recipe.</summary>
        </member>
        <member name="T:GW2NET.Rendering.IRenderRequest">
            <summary>Provides the interface for render service requests.</summary>
        </member>
        <member name="P:GW2NET.Rendering.IRenderRequest.ImageFormat">
            <summary>Gets or sets the image format.</summary>
        </member>
        <member name="T:GW2NET.Rendering.RenderRequest">
            <summary>Represents a request for an in-game asset.</summary>
        </member>
        <member name="P:GW2NET.Rendering.RenderRequest.FileId">
            <summary>Gets or sets the file identifier.</summary>
        </member>
        <member name="P:GW2NET.Rendering.RenderRequest.FileSignature">
            <summary>Gets or sets the file signature.</summary>
        </member>
        <member name="P:GW2NET.Rendering.RenderRequest.ImageFormat">
            <summary>Gets or sets the image format.</summary>
        </member>
        <member name="P:GW2NET.Rendering.RenderRequest.Resource">
            <summary>Gets the resource path.</summary>
        </member>
        <member name="M:GW2NET.Rendering.RenderRequest.GetParameters">
            <summary>Gets the request parameters.</summary>
            <returns>A collection of parameters.</returns>
        </member>
        <member name="M:GW2NET.Rendering.RenderRequest.GetPathSegments">
            <summary>Gets additional path segments for the targeted resource.</summary>
            <returns>A collection of path segments.</returns>
        </member>
        <member name="T:GW2NET.Rendering.RenderService">
            <summary>Provides the default implementation of the render service.</summary>
        </member>
        <member name="F:GW2NET.Rendering.RenderService.serviceClient">
            <summary>Infrastructure. Holds a reference to the service client.</summary>
        </member>
        <member name="M:GW2NET.Rendering.RenderService.#ctor(GW2NET.Common.IServiceClient)">
            <summary>Initializes a new instance of the <see cref="T:GW2NET.Rendering.RenderService"/> class.</summary>
            <param name="serviceClient">The service client.</param>
            <exception cref="T:System.ArgumentNullException">The value of <paramref name="serviceClient"/> is a null reference.</exception>
        </member>
        <member name="M:GW2NET.Rendering.RenderService.GW2NET#Common#IRenderService#GetImage(GW2NET.Common.IRenderable,System.String)">
            <inheritdoc />
        </member>
        <member name="M:GW2NET.Rendering.RenderService.GetImageAsync(GW2NET.Common.IRenderable,System.String)">
            <inheritdoc />
        </member>
        <member name="M:GW2NET.Rendering.RenderService.GetImageAsync(GW2NET.Common.IRenderable,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:GW2NET.Skins.ArmorSkin">
            <summary>Represents an armor skin.</summary>
        </member>
        <member name="P:GW2NET.Skins.ArmorSkin.WeightClass">
            <summary>Gets or sets the armor skin's weight class.</summary>
        </member>
        <member name="T:GW2NET.Skins.AxeSkin">
            <summary>Represents an axe skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.BackpackSkin">
            <summary>Represents a backpack skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.BootsSkin">
            <summary>Represents a foot protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.CoatSkin">
            <summary>Represents a chest protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.DaggerSkin">
            <summary>Represents a dagger skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.FocusSkin">
            <summary>Represents a focus skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.GatheringToolSkin">
            <summary>Represents a gathering tool skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.GlovesSkin">
            <summary>Represents an arm protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.GreatSwordSkin">
            <summary>Represents a great sword skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.HammerSkin">
            <summary>Represents a hammer skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.HarpoonSkin">
            <summary>Represents a harpoon skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.HelmAquaticSkin">
            <summary>Represents an aquatic head protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.HelmSkin">
            <summary>Represents a head protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.ISkinRepository">
            <summary>Provides the interface for repositories that provide localized skin details.</summary>
        </member>
        <member name="T:GW2NET.Skins.LargeBundleSkin">
            <summary>Represents a large bundle skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.LeggingsSkin">
            <summary>Represents a leg protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.LongBowSkin">
            <summary>Represents a long bow skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.MaceSkin">
            <summary>Represents a mace skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.PistolSkin">
            <summary>Represents a pistol skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.RifleSkin">
            <summary>Represents a rifle skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.ScepterSkin">
            <summary>Represents a scepter skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.ShieldSkin">
            <summary>Represents a shield skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.ShortBowSkin">
            <summary>Represents a short bow skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.ShouldersSkin">
            <summary>Represents a shoulder protection skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.Skin">
            <summary>Represents an in-game item skin.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.Description">
            <summary>Gets or sets the skin's description.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.Flags">
            <summary>Gets or sets the skin's additional flags.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.IconFileId">
            <summary>Gets or sets the skin's icon identifier for use with the render service.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.IconFileSignature">
            <summary>Gets or sets the skin's icon signature for use with the render service.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.IconFileUrl">
            <summary>Gets or sets the icon file URL.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.Name">
            <summary>Gets or sets the name of the skin.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.Restrictions">
            <summary>Gets or sets the skin's restrictions.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.SkinId">
            <summary>Gets or sets the skin identifier.</summary>
        </member>
        <member name="P:GW2NET.Skins.Skin.GW2NET#Common#IRenderable#FileId">
            <inheritdoc />
        </member>
        <member name="P:GW2NET.Skins.Skin.GW2NET#Common#IRenderable#FileSignature">
            <inheritdoc />
        </member>
        <member name="M:GW2NET.Skins.Skin.op_Equality(GW2NET.Skins.Skin,GW2NET.Skins.Skin)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Skins.Skin.op_Inequality(GW2NET.Skins.Skin,GW2NET.Skins.Skin)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Skins.Skin.Equals(GW2NET.Skins.Skin)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Skins.Skin.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Skins.Skin.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Skins.Skin.GetSkinChatLink">
            <summary>Gets a skin chat link for this item skin.</summary>
            <returns>The <see cref="T:GW2NET.ChatLinks.ChatLink"/>.</returns>
        </member>
        <member name="M:GW2NET.Skins.Skin.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Skins.SkinFlags">
            <summary>Enumerates the known additional skin flags.</summary>
        </member>
        <member name="F:GW2NET.Skins.SkinFlags.None">
            <summary>Indicates no additional skin flags.</summary>
        </member>
        <member name="F:GW2NET.Skins.SkinFlags.ShowInWardrobe">
            <summary>The 'Show In Wardrobe' skin flag.</summary>
        </member>
        <member name="F:GW2NET.Skins.SkinFlags.NoCost">
            <summary>The 'No Cost' skin flag.</summary>
        </member>
        <member name="F:GW2NET.Skins.SkinFlags.HideIfLocked">
            <summary>The 'Hide If Locked' skin flag.</summary>
        </member>
        <member name="T:GW2NET.Skins.SmallBundleSkin">
            <summary>Represents a small bundle skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.SpearGunSkin">
            <summary>Represents a spear gun skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.StaffSkin">
            <summary>Represents a staff skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.SwordSkin">
            <summary>Represents a sword skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.TorchSkin">
            <summary>Represents a torch skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.ToySkin">
            <summary>Represents a toy skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.TridentSkin">
            <summary>Represents a trident skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.TwoHandedToySkin">
            <summary>Represents a two-handed toy skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.UnknownArmorSkin">
            <summary>Represents an unknown armor skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.UnknownSkin">
            <summary>Represents an unknown skin.</summary>
        </member>
        <member name="M:GW2NET.Skins.UnknownSkin.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.Skins.UnknownWeaponSkin">
            <summary>Represents an unknown weapon skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.WarHornSkin">
            <summary>Represents a war horn skin.</summary>
        </member>
        <member name="T:GW2NET.Skins.WeaponSkin">
            <summary>Represents a weapon skin.</summary>
        </member>
        <member name="P:GW2NET.Skins.WeaponSkin.DamageType">
            <summary>Gets or sets the weapon's damage type.</summary>
        </member>
        <member name="T:GW2NET.Worlds.IWorldRepository">
            <summary>Provides the interface for repositories that provide localized world details.</summary>
        </member>
        <member name="T:GW2NET.Worlds.World">
            <summary>Represents a world and its localized name and demographics.</summary>
        </member>
        <member name="P:GW2NET.Worlds.World.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.Worlds.World.Name">
            <summary>Gets or sets the name of the world.</summary>
        </member>
        <member name="P:GW2NET.Worlds.World.WorldId">
            <summary>Gets or sets the world identifier.</summary>
        </member>
        <member name="P:GW2NET.Worlds.World.Population">
            <summary>Gets or sets an indication of the world's population.</summary>
        </member>
        <member name="M:GW2NET.Worlds.World.op_Equality(GW2NET.Worlds.World,GW2NET.Worlds.World)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Worlds.World.op_Inequality(GW2NET.Worlds.World,GW2NET.Worlds.World)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.Worlds.World.Equals(GW2NET.Worlds.World)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.Worlds.World.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.Worlds.World.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.Worlds.World.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.Bloodlust">
            <summary>The Bloodlust bonus.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.BlueBorderlands">
            <summary>Represents the blue team's borderlands.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.CompetitiveMap">
            <summary>Provides the base class for World versus World maps.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.CompetitiveMap.Bonuses">
            <summary>Gets or sets the map's bonuses.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.CompetitiveMap.Objectives">
            <summary>Gets or sets the map's objectives.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.CompetitiveMap.Scores">
            <summary>Gets or sets the map's scoreboard.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.EternalBattlegrounds">
            <summary>Represents the Eternal Battlegrounds.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.GreenBorderlands">
            <summary>Represents the green team's borderlands.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.IMatchRepository">
            <summary>Provides the interface for repositories that provide match details.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.IObjectiveNameRepository">
            <summary>Provides the interface for repositories that provide localized objective names.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.MapBonus">
            <summary>Provides the base class for World versus World map bonuses.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.MapBonus.Owner">
            <summary>Gets or sets the team that holds the bonus.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.Match">
            <summary>Represents a World versus World match.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Match.Maps">
            <summary>Gets or sets the list of maps.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Match.MatchId">
            <summary>Gets or sets the match identifier.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Match.Scores">
            <summary>Gets or sets the total scores.</summary>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Match.op_Equality(GW2NET.WorldVersusWorld.Match,GW2NET.WorldVersusWorld.Match)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Match.op_Inequality(GW2NET.WorldVersusWorld.Match,GW2NET.WorldVersusWorld.Match)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Match.Equals(GW2NET.WorldVersusWorld.Match)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Match.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Match.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Match.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.Matchup">
            <summary>Represents a World versus World matchup.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.BlueWorld">
            <summary>Gets or sets the blue world. This is a navigation property. Use the value of <see cref="P:GW2NET.WorldVersusWorld.Matchup.BlueWorldId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.BlueWorldId">
            <summary>Gets or sets the blue world identifier.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.EndTime">
            <summary>Gets or sets the timestamp (UTC) of when the match ends.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.GreenWorld">
            <summary>Gets or sets the green world. This is a navigation property. Use the value of <see cref="P:GW2NET.WorldVersusWorld.Matchup.GreenWorldId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.GreenWorldId">
            <summary>Gets or sets the green world identifier.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.MatchId">
            <summary>Gets or sets the match identifier.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.RedWorld">
            <summary>Gets or sets the red world. This is a navigation property. Use the value of <see cref="P:GW2NET.WorldVersusWorld.Matchup.RedWorldId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.RedWorldId">
            <summary>Gets or sets the red world identifier.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Matchup.StartTime">
            <summary>Gets or sets the timestamp (UTC) of when the match started.</summary>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Matchup.op_Equality(GW2NET.WorldVersusWorld.Matchup,GW2NET.WorldVersusWorld.Matchup)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Matchup.op_Inequality(GW2NET.WorldVersusWorld.Matchup,GW2NET.WorldVersusWorld.Matchup)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Matchup.Equals(GW2NET.WorldVersusWorld.Matchup)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Matchup.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Matchup.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Matchup.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.Objective">
            <summary>Represents one of a World versus World map's objectives.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Objective.Name">
            <summary>Gets or sets the name of the objective. This is a navigation property. Use the value of <see cref="P:GW2NET.WorldVersusWorld.Objective.ObjectiveId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Objective.ObjectiveId">
            <summary>Gets or sets the objective identifier.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Objective.Owner">
            <summary>Gets or sets the current owner.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Objective.OwnerGuild">
            <summary>Gets or sets the guild currently claiming the objective. This is a navigation property. Use the value of <see cref="P:GW2NET.WorldVersusWorld.Objective.OwnerGuildId"/> to obtain a reference.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Objective.OwnerGuildId">
            <summary>Gets or sets the identifier of the guild currently claiming the objective.</summary>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Objective.op_Equality(GW2NET.WorldVersusWorld.Objective,GW2NET.WorldVersusWorld.Objective)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Objective.op_Inequality(GW2NET.WorldVersusWorld.Objective,GW2NET.WorldVersusWorld.Objective)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Objective.Equals(GW2NET.WorldVersusWorld.Objective)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Objective.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Objective.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Objective.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.ObjectiveName">
            <summary>Represents an objective and its localized name.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.ObjectiveName.Culture">
            <summary>Gets or sets the locale.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.ObjectiveName.Name">
            <summary>Gets or sets the name of the objective.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.ObjectiveName.ObjectiveId">
            <summary>Gets or sets the objective identifier.</summary>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.ObjectiveName.op_Equality(GW2NET.WorldVersusWorld.ObjectiveName,GW2NET.WorldVersusWorld.ObjectiveName)">
            <summary>Indicates whether an object is equal to another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter is equal to the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.ObjectiveName.op_Inequality(GW2NET.WorldVersusWorld.ObjectiveName,GW2NET.WorldVersusWorld.ObjectiveName)">
            <summary>Indicates whether an object differs from another object of the same type.</summary>
            <param name="left">The object on the left side.</param>
            <param name="right">The object on the right side.</param>
            <returns>true if the <paramref name="left" /> parameter differs from the <paramref name="right" /> parameter; otherwise, false.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.ObjectiveName.Equals(GW2NET.WorldVersusWorld.ObjectiveName)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.ObjectiveName.Equals(System.Object)">
            <summary>Determines whether the specified <see cref="T:System.Object"/> is equal to the current<see cref="T:System.Object"/>.</summary>
            <returns>true if the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>; otherwise, false.</returns>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>. </param>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.ObjectiveName.GetHashCode">
            <summary>Serves as a hash function for a particular type.</summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.ObjectiveName.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.RedBorderlands">
            <summary>Represents the red team's borderlands.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.Scoreboard">
            <summary>Represents a World versus World scoreboard.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Scoreboard.Blue">
            <summary>Gets or sets the blue team's score.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Scoreboard.Green">
            <summary>Gets or sets the green team's score.</summary>
        </member>
        <member name="P:GW2NET.WorldVersusWorld.Scoreboard.Red">
            <summary>Gets or sets the red team's score.</summary>
        </member>
        <member name="M:GW2NET.WorldVersusWorld.Scoreboard.ToString">
            <summary>Returns a string that represents the current object.</summary>
            <returns>A string that represents the current object.</returns>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.TeamColor">
            <summary>Enumerates known team colors.</summary>
        </member>
        <member name="F:GW2NET.WorldVersusWorld.TeamColor.Unknown">
            <summary>An unknown team color.</summary>
        </member>
        <member name="F:GW2NET.WorldVersusWorld.TeamColor.Blue">
            <summary>The blue team color.</summary>
        </member>
        <member name="F:GW2NET.WorldVersusWorld.TeamColor.Green">
            <summary>The green team color.</summary>
        </member>
        <member name="F:GW2NET.WorldVersusWorld.TeamColor.Red">
            <summary>The red team color.</summary>
        </member>
        <member name="F:GW2NET.WorldVersusWorld.TeamColor.Neutral">
            <summary>The neutral color.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.UnknownCompetitiveMap">
            <summary>Represents an unknown competitive map.</summary>
        </member>
        <member name="T:GW2NET.WorldVersusWorld.UnknownMapBonus">
            <summary>an unknown bonus.</summary>
        </member>
    </members>
</doc>

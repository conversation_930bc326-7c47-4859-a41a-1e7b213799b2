﻿using MihaZupan;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace ConsoleApp1
{
    class Program
    {
        static void Main(string[] args)
        {
            Test(true);
            Console.ReadLine();
        }

        static async Task Test(bool resolveHostnamesLocally)
        {
            var proxy = new HttpToSocks5Proxy("127.0.0.1", 2801);
            var handler = new HttpClientHandler { Proxy = proxy };
            HttpClient httpClient = new HttpClient(handler, true);

            var result = await httpClient.SendAsync(
                new HttpRequestMessage(HttpMethod.Get, "https://httpbin.org/ip"));

            Console.WriteLine("HTTPS GET: " + await result.Content.ReadAsStringAsync());
           
        }
    }
}

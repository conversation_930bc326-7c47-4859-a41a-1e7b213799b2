﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.Diagnostics;

using System.Management;
using System.Runtime.InteropServices;
using System.IO;
using System.Net;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Collections;
using MihaZupan;
using System.Net.Http;
using System.Security.Cryptography;
using System.Web.Script.Serialization;
using System.Web;
using JinYiHelp.EasyHTTPClient;
using Newtonsoft.Json;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;



namespace WindowsFormsApp1
{

    public static class ProcessExtensions
    {
        /// <summary>
        /// 获取一个正在运行的进程的命令行参数。
        /// 与 <see cref="Environment.GetCommandLineArgs"/> 一样，使用此方法获取的参数是包含应用程序路径的。
        /// 关于 <see cref="Environment.GetCommandLineArgs"/> 可参见：
        /// .NET 命令行参数包含应用程序路径吗？https://blog.walterlv.com/post/when-will-the-command-line-args-contain-the-executable-path.html
        /// </summary>
        /// <param name="process">一个正在运行的进程。</param>
        /// <returns>表示应用程序运行命令行参数的字符串。</returns>
        public static string GetCommandLineArgs(this Process process)
        {
            if (process is null) throw new ArgumentNullException(nameof(process));

            try
            {
                return GetCommandLineArgsCore();
            }
            catch (Win32Exception ex) when ((uint)ex.ErrorCode == 0x80004005)
            {
                // 没有对该进程的安全访问权限。
                return string.Empty;
            }
            catch (InvalidOperationException)
            {
                // 进程已退出。
                return string.Empty;
            }

            string GetCommandLineArgsCore()
            {
                using (var searcher = new ManagementObjectSearcher(
                    "SELECT CommandLine FROM Win32_Process WHERE ProcessId = " + process.Id))
                using (var objects = searcher.Get())
                {
                    var @object = objects.Cast<ManagementBaseObject>().SingleOrDefault();
                    return @object?["CommandLine"]?.ToString() ?? "";
                }
            }
        }
    }
    public class Util
    {

        public static string Jinengjiadian1;

        public static string Jinengjiadian2;

        public static string FuwenHeroID;

        public static string FuwenHeroIDImage;

        public static string SelectHeroID;

        public static string RiotId;

        public static Image SelectHeroIDImage;

        public static string accountId;

        public static string summonerId;

        public static string duank;
        public static string token;

        public class XXTEA2
        {
            public static string Encrypt(string source, string key)
            {
                System.Text.Encoding encoder = System.Text.Encoding.UTF8;
                //UTF8==>BASE64==>XXTEA==>BASE64 
                byte[] bytData = encoder.GetBytes(base64Encode(source));
                byte[] bytKey = encoder.GetBytes(key);
                if (bytData.Length == 0)
                {
                    return "";
                }
                return System.Convert.ToBase64String(ToByteArray(Encrypt(ToUInt32Array(bytData, true), ToUInt32Array(bytKey, false)), false));
            }
            public static string Decrypt(string source, string key)
            {
                if (source.Length == 0)
                {
                    return "";
                }
                // reverse 
                System.Text.Encoding encoder = System.Text.Encoding.UTF8;
                byte[] bytData = System.Convert.FromBase64String(source);
                byte[] bytKey = encoder.GetBytes(key);

                return base64Decode(encoder.GetString(ToByteArray(Decrypt(ToUInt32Array(bytData, false), ToUInt32Array(bytKey, false)), true)));
            }

            public static UInt32[] Encrypt(UInt32[] v, UInt32[] k)
            {
                Int32 n = v.Length - 1;
                if (n < 1)
                {
                    return v;
                }
                if (k.Length < 4)
                {
                    UInt32[] Key = new UInt32[4];
                    k.CopyTo(Key, 0);
                    k = Key;
                }
                UInt32 z = v[n], y = v[0], delta = 0x9F8749B6, sum = 0, e;
                Int32 p, q = 6 + 52 / (n + 1);
                while (q-- > 0)
                {
                    sum = unchecked(sum + delta);
                    e = sum >> 2 & 3;
                    for (p = 0; p < n; p++)
                    {
                        y = v[p + 1];
                        z = unchecked(v[p] += (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z));
                    }
                    y = v[0];
                    z = unchecked(v[n] += (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z));
                }
                return v;
            }

            public static UInt32[] Decrypt(UInt32[] v, UInt32[] k)
            {
                Int32 n = v.Length - 1;
                if (n < 1)
                {
                    return v;
                }
                if (k.Length < 4)
                {
                    UInt32[] Key = new UInt32[4];
                    k.CopyTo(Key, 0);
                    k = Key;
                }
                UInt32 z = v[n], y = v[0], delta = 0x9F8749B6, sum, e;
                Int32 p, q = 6 + 52 / (n + 1);
                sum = unchecked((UInt32)(q * delta));
                while (sum != 0)
                {
                    e = sum >> 2 & 3;
                    for (p = n; p > 0; p--)
                    {
                        z = v[p - 1];
                        y = unchecked(v[p] -= (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z));
                    }
                    z = v[n];
                    y = unchecked(v[0] -= (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z));
                    sum = unchecked(sum - delta);
                }
                return v;
            }

            private static UInt32[] ToUInt32Array(Byte[] Data, Boolean IncludeLength)
            {
                Int32 n = (((Data.Length & 3) == 0) ? (Data.Length >> 2) : ((Data.Length >> 2) + 1));
                UInt32[] Result;
                if (IncludeLength)
                {
                    Result = new UInt32[n + 1];
                    Result[n] = (UInt32)Data.Length;
                }
                else
                {
                    Result = new UInt32[n];
                }
                n = Data.Length;
                for (Int32 i = 0; i < n; i++)
                {
                    Result[i >> 2] |= (UInt32)Data[i] << ((i & 3) << 3);
                }
                return Result;
            }

            private static Byte[] ToByteArray(UInt32[] Data, Boolean IncludeLength)
            {
                Int32 n;
                if (IncludeLength)
                {
                    n = (Int32)Data[Data.Length - 1];
                }
                else
                {
                    n = Data.Length << 2;
                }
                Byte[] Result = new Byte[n];
                for (Int32 i = 0; i < n; i++)
                {
                    Result[i] = (Byte)(Data[i >> 2] >> ((i & 3) << 3));
                }
                return Result;
            }

            public static string base64Decode(string data)
            {
                try
                {
                    var encoder = System.Text.Encoding.UTF8;

                    byte[] todecode_byte = Convert.FromBase64String(data);
                    return encoder.GetString(todecode_byte);
                }
                catch (Exception e)
                {
                    throw new Exception("Error in base64Decode" + e.Message);
                }
            }

            public static string base64Encode(string data)
            {
                try
                {
                    byte[] encData_byte = new byte[data.Length];
                    encData_byte = System.Text.Encoding.UTF8.GetBytes(data);
                    string encodedData = Convert.ToBase64String(encData_byte);
                    return encodedData;
                }
                catch (Exception e)
                {
                    throw new Exception("Error in base64Encode" + e.Message);
                }
            }

        }

        //默认打开路径
        private static string InitialDirectory = "";
        //统一对话框
        public static bool InitialDialog(FileDialog fileDialog, string title)
        {

            fileDialog.InitialDirectory = InitialDirectory;//初始化路径
            fileDialog.Filter = "files (*.*)|*.*";//过滤选项设置，文本文件，所有文件。
            fileDialog.FilterIndex = 1;//当前使用第二个过滤字符串
            fileDialog.RestoreDirectory = true;//对话框关闭时恢复原目录
            fileDialog.Title = title;

            if (fileDialog.ShowDialog() == DialogResult.OK)
            {
                for (int i = 1; i <= fileDialog.FileName.Length; i++)
                {
                    if (fileDialog.FileName.Substring(fileDialog.FileName.Length - i, 1).Equals(@"\"))
                    {
                        //更改默认路径为最近打开路径
                        InitialDirectory = fileDialog.FileName.Substring(0, fileDialog.FileName.Length - i + 1);
                        return true;
                    }
                }

                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 创建目录时如果目录已存在，则不会重新创建目录，且不会报错。创建目录时会自动创建路径中各级不存在的目录。
        /// </summary>
        /// <param name="luji"></param>
        /// <param name="neir"></param>
        public static void Cjianmul(string activeDir)
        {
            //string newPath = System.IO.Path.Combine(activeDir, "mySubDirOne");
            System.IO.Directory.CreateDirectory(activeDir);
        }
        public static void Xieruwenj(string luji, string neir)
        {

            //MessageBox.Show(System.IO.Directory.GetCurrentDirectory());
            //File.AppendAllText(luji, neir);
            FileStream fs = new FileStream(luji, FileMode.Create, FileAccess.ReadWrite);
            StreamWriter sw = new StreamWriter(fs); // 创建写入流
            sw.WriteLine(neir); // 写入Hello World

            sw.Close(); //关闭文件
            fs.Close();//关闭文件
        }
        public static void Xieruwenj_0(string luji, string neir)
        {


            FileStream fs = new FileStream(luji, FileMode.Create, FileAccess.ReadWrite);
            StreamWriter sw = new StreamWriter(fs); // 创建写入流
            sw.WriteLine(neir); // 写入Hello World
            sw.Close(); //关闭文件
            fs.Close();//关闭文件
        }
        /// <summary>
        /// 取中间文本
        /// </summary>
        /// <param name="s1">全文本</param>
        /// <param name="s2">前面</param>
        /// <param name="s3">后面</param>
        /// <returns></returns>
        public static string GetMiddleText(string s1, string s2, string s3) // 全文本 前面 后面
        {
            var index1 = s1.IndexOf(s2);



            if (index1 != -1)
            {
                var index2 = s1.IndexOf(s3, index1);
                if (index2 != -1)
                {
                    return s1.Substring(index1 + s2.Count(), index2 - s2.Count() - index1);
                }
            }
            return "";
        }


        public class ProtocolHeader
        {
            public string name;
            public string value;

            public ProtocolHeader(string n, string v)
            {
                name = n;
                value = v;
            }

        }
        /// <summary>
        /// 发送网页访问
        /// </summary>
        /// <param name="url">网址</param>
        /// <param name="Mode">访问方式</param>
        /// <param name="data">提交数据</param>
        /// <param name="databm">提交编码</param>
        /// <param name="fwbianma">返回编码</param>
        /// <returns></returns>
        public static string zi_Sendshuju(string url, string Mode = "POST", string data = "", Encoding databm = null)
        {
            //return HttpRequest_1("https://127.0.0.1:" + duank + url, "Basic " + token, data, Mode, databm).retstring;
            return HttpRequest_Fssong("https://127.0.0.1:" + duank + url, "Basic " + token, data, Mode, databm);

            // return HttpRequest_1("https://127.0.0.1:" + duank + url, "Basic " + token, data, Mode, databm, fwbianma).retstring;
        }

        public static string zi_Getwenb_0(string url, string fangshi = "GET", Encoding bianma = null)
        {

            //return HttpRequest_0("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma).retstring;


            return HttpRequest_0Async("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma);

        }
        public static string zi_Getwenb_01(string url, string fangshi = "GET", Encoding bianma = null)
        {
            //return HttpRequest_0("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma).retstring;
            return HttpRequest_0Async1("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma);

        }

        public static string zi_Getwenb_9(string url, string fangshi = "GET", Encoding bianma = null)
        {

            //return HttpRequest_0("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma).retstring;

            return HttpRequest_0Async("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma);

        }

        public static string zi_Getwenb(string url, string fangshi = "GET", Encoding bianma = null)
        {




            return HttpRequest_0Async("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma);

            // return HttpRequest_1("https://127.0.0.1:" + duank + url, "Basic " + token, null, fangshi, bianma).retstring;

        }
        public static Image zi_GetwenbZ(string url)
        {


            return GetImage("https://127.0.0.1:" + duank + url, "Basic " + token);
        }



        public class SummonerInfo
        {
            public string summonerId { get; set; }
            public string displayName { get; set; }
            public string accountId { get; set; }
            public int profileIconId { get; set; }
            public long revisionDate { get; set; }
            public int summonerLevel { get; set; }
            public string gameName { get; set; }
            public string internalName { get; set; }
            public bool nameChangeFlag { get; set; }
            public int percentCompleteForNextLevel { get; set; }
            public string privacy { get; set; }
            public string puuid { get; set; }
            public RerollPoints rerollPoints { get; set; }
            public string tagLine { get; set; }
            public bool unnamed { get; set; }
            public int xpSinceLastLevel { get; set; }
            public int xpUntilNextLevel { get; set; }
        }

        public class RerollPoints
        {
            public int currentPoints { get; set; }
            public int maxRolls { get; set; }
            public int numberOfRolls { get; set; }
            public int pointsCostToRoll { get; set; }
            public int pointsToReroll { get; set; }
        }

        public static SummonerInfo GetSummonerInfo()
        {
            var responseContent = zi_Getwenb_01("/lol-summoner/v1/current-summoner");

            return JsonConvert.DeserializeObject<SummonerInfo>(responseContent);
        }



        /// <summary>
        /// get 以ID取英雄位置
        /// </summary>
        /// <param name="YXid"></param>
        /// <returns></returns>
        public static List<string> GetheronameWEI(string YXid)
        {

            foreach (var ed in Form1.HeroIDLibrary)
            {

                if (ed.HeroID == YXid)
                {
                    return ed.HeroNameWei;
                }
            }
            return new List<string>();

        }
        /// <summary>
        /// get ID取英雄名称
        /// </summary>
        /// <param name="YXid"></param>
        /// <returns></returns>
        public static string Getheroname(string YXid)
        {


            foreach (var ed in Form1.HeroIDLibrary)
            {

                if (ed.HeroID == YXid)
                {
                    return ed.HeroName;
                }
            }
            return "";
        }
        /// <summary>
        /// get ID取英雄名称1 比如黑暗之女
        /// </summary>
        /// <param name="YXid"></param>
        /// <returns></returns>
        public static string Getheroname1(string YXid)
        {


            foreach (var ed in Form1.HeroIDLibrary)
            {

                if (ed.HeroID == YXid)
                {
                    return ed.HeroZHONGName;
                }
            }
            return "无";
        }
        /// <summary>
        /// get 英雄名称取ID
        /// </summary>
        /// <param name="YXid"></param>
        /// <returns></returns>
        public static string GetheronameID(string YXid)
        {


            foreach (var ed in Form1.HeroIDLibrary)
            {

                if (ed.HeroName == YXid)
                {
                    return ed.HeroID;
                }
            }
            return "";
        }
        /// <summary>
        /// 解析获取英雄ID
        /// </summary>
        /// <param name="jsonGO"></param>
        /// <returns></returns>
        public static string GetID(string jsonGO)
        {
            if (jsonGO == "远程")
                return "-1";

            var jiexi = JSON.Jiexun(jsonGO);

            string VerifyID = jiexi["localPlayerCellId"].ToString();

            int Datavolume = jiexi["actions"].Count();

            for (var P = 0; P < Datavolume; P++)
            {
                int Datavolume1 = jiexi["actions"][P].Count();

                for (var I = 0; I < Datavolume1; I++)
                {
                    string duibi = jiexi["actions"][P][I]["actorCellId"].ToString();
                    string duibi1 = jiexi["actions"][P][I]["type"].ToString();
                    string SDING = jiexi["actions"][P][I]["completed"].ToString();
                    SDING = SDING.ToLower();
                    if (duibi == VerifyID && duibi1 == "pick" && SDING == "true")
                    {
                        var haha = jiexi["actions"][P][I]["championId"].ToString();
                        return haha;
                    }
                }
            }

            int Datavolume11 = jiexi["myTeam"].Count();

            for (var P = 0; P < Datavolume11; P++)
            {
                string duibi = jiexi["myTeam"][P]["cellId"].ToString();
                string duibi1 = jiexi["myTeam"][P]["summonerId"].ToString();

                if (VerifyID == duibi && duibi1 == Util.summonerId)
                {
                    var haha = jiexi["myTeam"][P]["championId"].ToString();
                    return haha;
                }
            }

            return "0";
        }
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]

        public static extern bool GetWindowRect(IntPtr hWnd, ref RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]

        public struct RECT
        {
            public int Left; //最左坐标

            public int Top; //最上坐标

            public int Right; //最右坐标

            public int Bottom; //最下坐标 }
        }


        [DllImport("user32.dll")]
        public static extern int GetWindow(int hwnd, int wCmd);
        /// <summary>
        /// 批量取字符串中间
        /// </summary>
        /// <param name="source">文本</param>
        /// <param name="startStr">前文本</param>
        /// <param name="endStr">后文本</param>
        /// <returns></returns>
        public static string[] SubstringMultiple(string source, string startStr, string endStr)
        {
            Regex rg = new Regex("(?<=(" + startStr + "))[.\\s\\S]*?(?=(" + endStr + "))", RegexOptions.Multiline | RegexOptions.Singleline);
            MatchCollection matches = rg.Matches(source);
            List<string> resList = new List<string>();
            foreach (Match item in matches)
                resList.Add(item.Value);
            return resList.ToArray();
        }

        /// <summary>
        /// 取文本左边内容
        /// </summary>
        /// <param name="str">文本</param>
        /// <param name="s">标识符</param>
        /// <returns>左边内容</returns>
        public static string GetLeft(string str, string s)
        {
            string temp = str.Substring(0, str.IndexOf(s));
            return temp;
        }


        /// <summary>
        /// 取文本右边内容
        /// </summary>
        /// <param name="str">文本</param>
        /// <param name="s">标识符</param>
        /// <returns>右边内容</returns>
        public static string GetRight(string str, string s)
        {
            string temp = str.Substring(str.IndexOf(s), str.Length - str.Substring(0, str.IndexOf(s)).Length);
            return temp;
        }

        /// <summary>
        /// 取文本中间内容
        /// </summary>
        /// <param name="str">原文本</param>
        /// <param name="leftstr">左边文本</param>
        /// <param name="rightstr">右边文本</param>
        /// <returns>返回中间文本内容</returns>
        public static string Between(string str, string leftstr, string rightstr)
        {
            int i = str.IndexOf(leftstr) + leftstr.Length;
            string temp = str.Substring(i, str.IndexOf(rightstr, i) - i);
            return temp;
        }


        /// <summary>
        /// 取文本中间到List集合
        /// </summary>
        /// <param name="str">文本字符串</param>
        /// <param name="leftstr">左边文本</param>
        /// <param name="rightstr">右边文本</param>
        /// <returns>List集合</returns>
        public List<string> BetweenArr(string str, string leftstr, string rightstr)
        {
            List<string> list = new List<string>();
            int leftIndex = str.IndexOf(leftstr);//左文本起始位置
            int leftlength = leftstr.Length;//左文本长度
            int rightIndex = 0;
            string temp = "";
            while (leftIndex != -1)
            {
                rightIndex = str.IndexOf(rightstr, leftIndex + leftlength);
                if (rightIndex == -1)
                {
                    break;
                }
                temp = str.Substring(leftIndex + leftlength, rightIndex - leftIndex - leftlength);
                list.Add(temp);
                leftIndex = str.IndexOf(leftstr, rightIndex + 1);
            }
            return list;
        }


        /// <summary>
        /// 指定文本倒序
        /// </summary>
        /// <param name="str">文本</param>
        /// <returns>倒序文本</returns>
        public static string StrReverse(string str)
        {
            char[] chars = str.ToCharArray();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < chars.Length; i++)
            {
                sb.Append(chars[chars.Length - 1 - i]);
            }
            return sb.ToString();
        }
        public static (string retstring, HttpStatusCode StatusCode) HttpRequest_0(string Url, string authorization, string data = null, string method = "GET", Encoding tijiaobianma = null, Encoding bianma = null, bool dailimoshi = false)
        {

            if (bianma == null)
            {
                bianma = Encoding.UTF8;
            }

            if (tijiaobianma == null)
            {
                tijiaobianma = Encoding.UTF8;
            }
            Uri uri1 = new Uri(Url);
            //Debug.WriteLine(Url);

            HttpWebRequest Myrq = WebRequest.Create(Url) as HttpWebRequest;


            Myrq.ServerCertificateValidationCallback = (sender, cert, chain, error) =>
            {
                return true;
            };
            //System.Net.ServicePointManager.DefaultConnectionLimit = 50;
            //Myrq.ContentType = "text/html";
            //Myrq.AllowAutoRedirect = true;
            //Myrq.Proxy = null;
            // Myrq.UnsafeAuthenticatedConnectionSharing = true;
            //Myrq.KeepAlive = true;//持续连接
            // Myrq.ProtocolVersion = HttpVersion.Version10;
            Myrq.UserAgent = Url;

            Myrq.Timeout = 15 * 1000;//30秒，*1000是因为基础单位为毫秒
            Myrq.Method = method;//请求方法
            Myrq.Referer = uri1.DnsSafeHost;
            // Myrq.Accept = "application/json";//自己去network里面找
            Myrq.Host = uri1.DnsSafeHost + ":" + Util.duank;
            //Myrq.UserAgent = "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36";

            Myrq.Headers.Add("Authorization", authorization);
            Myrq.CookieContainer = new CookieContainer();
            //Myrq.ServicePoint.Expect100Continue = false;

            //接受返回

            if (!string.IsNullOrWhiteSpace(data))
            {
                try
                {
                    using (Stream stream = Myrq.GetRequestStream())
                    {
                        var bytes = tijiaobianma.GetBytes(data);
                        stream.Write(bytes, 0, bytes.Length);
                        stream.Flush();
                    }
                }
                catch (Exception ex)
                {

                    return (ex.Message, HttpStatusCode.BadRequest);
                    // return ex.Message;
                }
            }

            HttpWebResponse res;
            try
            {



                res = (HttpWebResponse)Myrq.GetResponse();


                // 引起异常的语句
            }
            catch (WebException ee)
            {
                res = (HttpWebResponse)ee.Response;
                //return (res.StackTrace, HttpStatusCode.BadRequest);
                // return ex.Message;
            }

            try
            {

                if (res != null)
                {

                    Stream receiveStream = res.GetResponseStream();

                    StreamReader readStream = new StreamReader(receiveStream, bianma);


                    var vvvvv = readStream.ReadToEnd();

                    var ppppp = res.StatusCode;



                    res.Close();

                    return (vvvvv, ppppp);

                }
                else
                {
                    return ("错误提示无效地址", HttpStatusCode.BadRequest);
                }




            }
            catch (Exception ee)
            {
                return (ee.Message, HttpStatusCode.BadRequest);
            }









        }

        public static (string retstring, HttpStatusCode StatusCode) HttpRequest_1(string Url, string authorization, string data = null, string method = "GET", Encoding tijiaobianma = null, Encoding bianma = null, bool dailimoshi = false)
        {

            if (bianma == null)
            {
                bianma = Encoding.UTF8;
            }

            if (tijiaobianma == null)
            {
                tijiaobianma = Encoding.UTF8;
            }

            //Debug.WriteLine(Url);
            Uri uri1 = new Uri(Url);
            HttpWebRequest Myrq = WebRequest.Create(Url) as HttpWebRequest;


            Myrq.ServerCertificateValidationCallback = (sender, cert, chain, error) =>
            {
                return true;
            };
            Myrq.AllowAutoRedirect = true;
            Myrq.Proxy = null;
            Myrq.UnsafeAuthenticatedConnectionSharing = true;
            Myrq.KeepAlive = true;//持续连接
            Myrq.Timeout = 15 * 1000;//30秒，*1000是因为基础单位为毫秒
            Myrq.Method = method;//请求方法
            Myrq.Referer = uri1.DnsSafeHost;
            Myrq.Accept = "application/json";//自己去network里面找
            //Myrq.UserAgent = "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36";
            Myrq.Headers.Add("Authorization", authorization);
            Myrq.CookieContainer = new CookieContainer();
            //Myrq.ServicePoint.Expect100Continue = false;
            //接受返回

            if (!string.IsNullOrWhiteSpace(data))
            {
                try
                {
                    using (Stream stream = Myrq.GetRequestStream())
                    {
                        var bytes = tijiaobianma.GetBytes(data);
                        stream.Write(bytes, 0, bytes.Length);
                        stream.Flush();
                    }
                }
                catch (Exception ex)
                {

                    return (ex.Message, HttpStatusCode.BadRequest);
                    // return ex.Message;
                }
            }

            try
            {



                HttpWebResponse myWebResponse = (HttpWebResponse)Myrq.GetResponse();

                Stream receiveStream = myWebResponse.GetResponseStream();

                StreamReader readStream = new StreamReader(receiveStream, Encoding.UTF8);


                var vvvvv = readStream.ReadToEnd();

                var ppppp = myWebResponse.StatusCode;

                myWebResponse.Close();

                return (vvvvv, ppppp);
                // 引起异常的语句
            }
            catch (Exception ex)
            {

                return (ex.Message, HttpStatusCode.BadRequest);
                // return ex.Message;
            }
            //StreamReader sr = new StreamReader(res.GetResponseStream(), strEncode);
            //strHtml = sr.ReadToEnd();

            //using (HttpWebResponse myWebResponse = (HttpWebResponse)Myrq.GetResponse())
            //{

            //    using (Stream receiveStream = myWebResponse.GetResponseStream())
            //    {
            //        using (StreamReader readStream = new StreamReader(receiveStream, Encoding.UTF8))
            //        {
            //            return (readStream.ReadToEnd(), myWebResponse.StatusCode);
            //        }
            //    }



            //}



        }

        public static Image GetImage1(string Url, string ant)
        {
            string Url1 = "https://pic.netbian.com/uploads/allimg/190630/215651-1561903011838a.jpg";

            //Debug.WriteLine(Url);

            HttpWebRequest Myrq = WebRequest.Create(Url1) as HttpWebRequest;
            ServicePointManager.ServerCertificateValidationCallback += (s, cert, chain, sslPolicyErrors) => true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
            Myrq.ServerCertificateValidationCallback = (sender, cert, chain, error) =>
            {
                return true;
            };
            // ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.SystemDefault | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls13;
            Myrq.ContentType = "application/x-www-form-urlencoded";
            Myrq.Accept = "image/png,image/*;q=0.8,*/*;q=0.5";
            Myrq.KeepAlive = true;//持续连接
            Myrq.ProtocolVersion = HttpVersion.Version11;
            // Myrq.Timeout = 15 * 1000;//30秒，*1000是因为基础单位为毫秒
            Myrq.Method = "GET";//请求方法
            //Myrq.Accept = "application/json";//自己去network里面找
            //Myrq.UserAgent = "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36";
            //Myrq.Headers.Add("Authorization", ant);
            //接受返回

            HttpWebResponse Myrp = (HttpWebResponse)Myrq.GetResponse();

            if (Myrp.StatusCode != HttpStatusCode.OK)
            {
                return null;
            }
            return Image.FromStream(Myrp.GetResponseStream());
            //try
            //{
            //    HttpWebResponse Myrp = (HttpWebResponse)Myrq.GetResponse();

            //    if (Myrp.StatusCode != HttpStatusCode.OK)
            //    {
            //        return null;
            //    }
            //    return Image.FromStream(Myrp.GetResponseStream());
            //}
            //catch (Exception)
            //{
            //    return null;
            //}

        }
        public static Image GetImage(string Url, string ant)
        {
            string Url1 = "https://pic.netbian.com/uploads/allimg/190630/215651-1561903011838a.jpg";
            HttpItem item = new HttpItem()
            {
                URL = Url,
                ResultType = ResultType.Byte,

            };
            //item.ProtocolVersion = new Version("1.0");
            //item.MaximumAutomaticRedirections = 2048;
            //item.Timeout = 15000;
            //item.CookieContainer = new CookieContainer();
            item.Header.Add("Authorization", ant);

            var result = Task.Run(async () => await item.GetHtml()).Result;
            // var result = await item.GetHtml();

            if (result.IsSuccessStatusCode)
            {
                MemoryStream ms = new MemoryStream(result.ResultByte);

                //pic_img.Image = Image.FromStream(ms);

                var GGG = Image.FromStream(ms);
                ms.Close();
                return GGG;
                //


            }
            else
            {
                return null;

            }



        }

        public static string HttpRequest_Fssong(string Url, string authorization, string data = null, string method = "GET", Encoding tijiaobianma = null)
        {


            if (tijiaobianma == null)
            {
                tijiaobianma = Encoding.UTF8;
            }



            var DDD = System.Net.Http.HttpMethod.Post;
            if (method == "PUT")
                DDD = new HttpMethod("PUT");
            if (method == "DELETE")
                DDD = new HttpMethod("DELETE");
            if (method == "PATCH")
                DDD = new HttpMethod("PATCH");

            var bytes = tijiaobianma.GetBytes(data);

            HttpItem item = new HttpItem()
            {
                URL = Url,
                Method = DDD,
                Postdata = data,
                PostEncoding = Encoding.UTF8,
                Encoding = tijiaobianma,// Encoding.GetEncoding("GB2312")


            };
            item.MaximumAutomaticRedirections = 2048;
            item.ContentType = "application/json";
            //item.ProtocolVersion = new Version("1.1");
            //item.KeepAlive = false;
            item.Timeout = 15;

            item.Header.Add("Authorization", authorization);
            //item.UserAgent = URL11;
            //item.Header.CO. = new MediaTypeHeaderValue("application/octet-stream");
            //item.Header.Add("Content-Type", " application/json; charset=utf-8");

            //item.Referer = uri1.DnsSafeHost;
            item.CookieContainer = new CookieContainer();
            //Referer: https://[Host]/index.html



            var result = Task.Run(async () => await item.GetHtml()).Result;




            //Console.WriteLine(result.Cookie);
            if (result.IsSuccessStatusCode)
            {
                return result.Html;

            }
            else
            {
                return "远程";
            }









        }

        // 忽略证书验证的回调方法
        private static bool ValidateServerCertificate(
            object sender,
            X509Certificate certificate,
            X509Chain chain,
            SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }

        public  static async Task<string> SendRequestWithExternalParamAsync(string url)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
            // 这里示例添加了几种常见的协议版本，可根据服务器实际支持情况进行调整，比如如果服务器支持TLS 1.3，可尝试添加SecurityProtocolType.Tls13
            // 处理SSL证书验证，设置为忽略验证（不安全，仅示例）
            ServicePointManager.ServerCertificateValidationCallback += ValidateServerCertificate;

            try
            {
                using (WebResponse response = await request.GetResponseAsync())
                {
                    using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                    {
                        return await reader.ReadToEndAsync();
                    }
                }
            }
            catch (WebException ex)
            {
                // 可以更详细地处理不同类型的异常情况，这里简单返回异常信息
                return ex.Message;
            }
            finally
            {
                // 移除掉刚才添加的忽略证书验证的回调（避免影响后续其他请求验证逻辑）
                ServicePointManager.ServerCertificateValidationCallback -= ValidateServerCertificate;
            }
        }

       


        public static string HttpRequest_Get(string Url)
        {


            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
            try
            {
                // 设置ServicePointManager以启用TLS 1.2协议（可根据服务器要求修改为其他协议版本）
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                // 创建WebRequest对象，设置请求的URL
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(Url);
                request.Method = "GET";

                // 设置一些常见的请求头信息（可根据实际需求调整）
                request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
                request.Timeout = 15000; // 设置超时时间为15秒

                // 发送请求并获取响应
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    // 检查响应状态码是否成功（一般200表示成功）
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        // 读取响应流中的数据
                        using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                        {
                            return reader.ReadToEnd();
                        }
                    }
                    else
                    {
                        return $"请求失败，状态码: {response.StatusCode}";
                    }
                }
            }
            catch (WebException ex)
            {
                if (ex.Response != null)
                {
                    using (HttpWebResponse errorResponse = (HttpWebResponse)ex.Response)
                    {
                        using (StreamReader reader = new StreamReader(errorResponse.GetResponseStream(), Encoding.UTF8))
                        {
                            return $"请求出错，异常信息: {ex.Message}，状态码: {errorResponse.StatusCode}";
                        }
                    }
                }
                else
                {
                    return $"请求出错，异常信息: {ex.Message}";
                }
            }
            catch (Exception ex)
            {
                return $"请求出错，异常信息: {ex.Message}";
            }








        }

        public static string HttpRequest_0Async(string Url, string authorization, string data = null, string method = "GET", Encoding tijiaobianma = null, Encoding bianma = null, bool dailimoshi = false)
        {
            if (bianma == null)
            {
                bianma = Encoding.UTF8;
            }

            if (tijiaobianma == null)
            {
                tijiaobianma = Encoding.UTF8;
            }
            //Myrq.UserAgent = Url;


            //Myrq.Referer = uri1.DnsSafeHost;

            //Myrq.Host = uri1.DnsSafeHost + ":" + Util.duank;
            string URL11 = Url;
            Uri uri1 = new Uri(URL11);
            HttpItem item = new HttpItem()
            {
                URL = URL11,
                Host = uri1.DnsSafeHost + ":" + Util.duank,

                Encoding = tijiaobianma,// Encoding.GetEncoding("GB2312")
            };
            item.AutoHanderCookie = false;
            item.UserAgent = Url;
            //item.Referer = uri1.DnsSafeHost;
            item.MaximumAutomaticRedirections = 2048;
            item.ContentType = "application/json";
            //item.Timeout = 15000;
            //item.UserAgent = URL11;
            item.Header.Add("Authorization", authorization);
            //item.Referer = uri1.DnsSafeHost;
            //item.CookieContainer = new CookieContainer();
            //Referer: https://[Host]/index.html

            //var result = await item.GetHtml();


            var result = Task.Run(async () => await item.GetHtml()).Result;





            if (result.IsSuccessStatusCode)
            {

                return result.Html;
                //txt_Info.Text = result.Html;
            }
            else
            {
                return "远程";
            }









        }

        public static string HttpRequest_0Async1(string Url, string authorization, string data = null, string method = "GET", Encoding tijiaobianma = null, Encoding bianma = null, bool dailimoshi = false)
        {
            if (bianma == null)
            {
                bianma = Encoding.UTF8;
            }

            if (tijiaobianma == null)
            {
                tijiaobianma = Encoding.UTF8;
            }
            //Myrq.UserAgent = Url;


            //Myrq.Referer = uri1.DnsSafeHost;

            //Myrq.Host = uri1.DnsSafeHost + ":" + Util.duank;
            string URL11 = Url;
            Uri uri1 = new Uri(URL11);
            HttpItem item = new HttpItem()
            {
                URL = URL11,
                Host = uri1.DnsSafeHost + ":" + Util.duank,

                Encoding = tijiaobianma,// Encoding.GetEncoding("GB2312")
            };
            //item.AutoHanderCookie = false;
            //item.UserAgent = Url;
            //item.Referer = uri1.DnsSafeHost;
            //item.MaximumAutomaticRedirections = 2048;
            // item.ContentType = "application/json";
            //item.Timeout = 15000;
            //item.UserAgent = URL11;
            item.Header.Add("Authorization", authorization);
            //item.Referer = uri1.DnsSafeHost;
            item.CookieContainer = new CookieContainer();
            //Referer: https://[Host]/index.html

            //var result = await item.GetHtml();


            var result = Task.Run(async () => await item.GetHtmlGO()).Result;


            var WWWD = result.Html;




            if (WWWD != "" && WWWD.Contains("已取消一个任务") == false)
            {

                return WWWD;

            }
            else
            {
                return "远程";
            }









        }

        public static string fanhui = "";
        public static async Task HttpToSocks5Proxy(string uri, string IP, int duank)
        {
            fanhui = "";
            var proxy = new HttpToSocks5Proxy(IP, duank);
            var handler = new HttpClientHandler { Proxy = proxy };
            HttpClient httpClient = new HttpClient(handler, false);

            var result = await httpClient.SendAsync(
                new HttpRequestMessage(HttpMethod.Get, uri));
            // aresult.Content.Headers.Add("1", "1");
            //result.Headers.Add("Authorization", "Basic cmlvdDpWX2dKNmNWWWJncVZXNVlCWjhkNkd3");

            fanhui = await result.Content.ReadAsStringAsync();
            //Console.WriteLine("HTTPS GET: " + result.Content.Headers.ContentRange);
            //MessageBox.Show(await result.Content.ReadAsStringAsync());
        }
        /// <summary>
        /// 编码BASE64
        /// </summary>
        /// <param name="code_type"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        public static string EncodeBase64(string code_type, string code)
        {
            string encode = "";
            byte[] bytes = Encoding.GetEncoding(code_type).GetBytes(code);
            try
            {
                encode = Convert.ToBase64String(bytes);
            }
            catch
            {
                encode = code;
            }
            return encode;
        }

        /// <summary>
        /// 解码BASE64
        /// </summary>
        /// <param name="code_type"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        public static string DecodeBase64(string code_type, string code)
        {
            string decode = "";
            byte[] bytes = Convert.FromBase64String(code);
            try
            {
                decode = Encoding.GetEncoding(code_type).GetString(bytes);
            }
            catch
            {
                decode = code;
            }
            return decode;
        }


        [DllImport("kernel32")]
        private static extern long WritePrivateProfileString(string section, string key, string val, string filePath);
        [DllImport("kernel32")]
        private static extern long GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);
        /// <summary>
        /// 读取配置ini文件
        /// </summary>
        /// <param name="Section">节名称</param>
        /// <param name="Key">配置项名称</param>
        /// <param name="NoText">默认文本</param>
        /// <param name="iniFilePath">配置文件名</param>
        /// <returns></returns>

        public static string ReadIniData(string Section, string Key, string NoText, string iniFilePath)
        {//读INI文件
            if (File.Exists(iniFilePath))
            {
                StringBuilder temp = new StringBuilder(1024);
                GetPrivateProfileString(Section, Key, NoText, temp, 1024, iniFilePath);
                return temp.ToString();
            }
            else
            {
                return String.Empty;
            }
        }

        public static void XIErunin(string Section, string Key, string Value)
        {
            Util.WriteIniData(Section, Key, Value, @".\日志.ini");
        }

        /// <summary>
        /// 写入配置ini文件
        /// </summary>
        /// <param name="Section">节名称</param>
        /// <param name="Key">配置项名称</param>
        /// <param name="Value">欲写入值</param>
        /// <param name="iniFilePath">配置文件名</param>
        /// <returns></returns>
        public static bool WriteIniData(string Section, string Key, string Value, string iniFilePath)
        {//写入INI文件
            if (!File.Exists(iniFilePath))
            {
                Util.Cjianmul(Path.GetDirectoryName(iniFilePath));
                FileStream fs = new FileStream(iniFilePath, FileMode.Create, FileAccess.ReadWrite);
                fs.Close();
                long OpStation = WritePrivateProfileString(Section, Key, Value, iniFilePath);
                if (OpStation == 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {

                long OpStation = WritePrivateProfileString(Section, Key, Value, iniFilePath);
                if (OpStation == 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }

            }
        }


        [DllImport("user32.dll")]
        public static extern IntPtr FindWindow(string strClass, string strWindow); //查找窗口


        public static string execCMD(string command)
        {


            System.Diagnostics.Process pro = new System.Diagnostics.Process();
            pro.StartInfo.FileName = "cmd.exe";
            pro.StartInfo.UseShellExecute = false;
            pro.StartInfo.RedirectStandardError = true;
            pro.StartInfo.RedirectStandardInput = true;
            pro.StartInfo.RedirectStandardOutput = true;
            pro.StartInfo.CreateNoWindow = true;
            //pro.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden;
            pro.Start();
            pro.StandardInput.WriteLine(command);
            pro.StandardInput.WriteLine("exit");
            pro.StandardInput.AutoFlush = true;
            //获取cmd窗口的输出信息
            string output = pro.StandardOutput.ReadToEnd();
            pro.WaitForExit();//等待程序执行完退出进程
            pro.Close();
            return output;

        }
        public static string GetCommandLineArgs(Process process)
        {
            if (process is null) throw new ArgumentNullException(nameof(process));

            try
            {
                return GetCommandLineArgsCore();
            }
            catch (Win32Exception ex) when ((uint)ex.ErrorCode == 0x80004005)
            {
                // 没有对该进程的安全访问权限。
                return string.Empty;
            }
            catch (InvalidOperationException)
            {
                // 进程已退出。
                return string.Empty;
            }

            string GetCommandLineArgsCore()
            {
                using (var searcher = new ManagementObjectSearcher(
                    "SELECT CommandLine FROM Win32_Process WHERE ProcessId = " + process.Id))
                using (var objects = searcher.Get())
                {
                    var @object = objects.Cast<ManagementBaseObject>().SingleOrDefault();
                    return @object?["CommandLine"]?.ToString() ?? "";
                }
            }
        }

        /// <summary>
        /// 判断文件是否被占用! 返回fales 就是没有占用
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static bool IsFileInUse(string fileName)
        {

            if (!File.Exists(fileName))
            {
                return true;
            }
            bool inUse = true;

            FileStream fs = null;
            try
            {

                fs = new FileStream(fileName, FileMode.Open, FileAccess.Read,

                FileShare.None);

                inUse = false;
            }
            catch
            {
                return inUse;
            }
            finally
            {
                if (fs != null)
                    fs.Close();
            }
            return inUse;//true表示正在使用,false没有使用
        }

        #region 直接删除指定目录下的所有文件及文件夹(保留目录)
        /// <summary>
        /// 直接删除指定目录下的所有文件及文件夹(保留目录)
        /// </summary>
        /// <param name="strPath">文件夹路径</param>
        /// <returns>执行结果</returns>
        public static bool DeleteDir(string strPath)
        {
            try
            {

                // File.Delete(strPath);
                // 清除空格
                strPath = @strPath.Trim().ToString();
                // 判断文件夹是否存在
                if (System.IO.Directory.Exists(strPath))
                {
                    // 获得文件夹数组
                    string[] strDirs = System.IO.Directory.GetDirectories(strPath);
                    // 获得文件数组
                    string[] strFiles = System.IO.Directory.GetFiles(strPath);
                    // 遍历所有子文件夹

                    foreach (string strFile in strFiles)
                    {
                        if (!IsFileInUse(strFile))
                            File.Delete(strFile);
                        // 删除文件夹

                        //System.IO.File.Delete(strFile);
                    }
                    // 遍历所有文件
                    foreach (string strdir in strDirs)
                    {
                        if (!IsFileInUse(strdir))
                            // 删除文件
                            System.IO.Directory.Delete(strdir, true);
                    }
                }
                // 成功
                return true;
            }
            catch (Exception Exp) // 异常处理
            {
                // 异常信息
                System.Diagnostics.Debug.Write(Exp.Message.ToString());
                // 失败
                return false;
            }
        }
        #endregion

        static string FormBT = "";

        /// <summary>
        /// 批量保存控件内容 只支持选择框 编辑框 建议频繁少用!频繁使用会出问题!
        /// </summary>
        /// <param name="Form">窗口 比如Form1.ActiveForm</param>
        /// <param name="name">不保存控件名称-注意是控件名称 textbox1|textbox2</param>
        /// <param name="path">保存文件路径 默认程序当前目录\Data\Cofing.ini</param>
        public static void BulkSave(Control Form, string name = "", string path = @".\Data\Cofing.ini")
        {

            //return;
            if (Form != null)
            {
                allCtrls = new Queue<Control>();
                FormBT = Form.Name;
                CheckAllCtrls(Form);
            }
            foreach (Control devices in allCtrls)
            {

                if (devices is TextBox DVC)
                {

                    if (DVC.Name.ToLower().IndexOf(name.ToLower()) == -1)
                    {
                        // Console.WriteLine("我是编辑框:" + DVC.Name);

                        WriteIniData("GameCofing", FormBT + ":" + DVC.Name, DVC.Text, path);
                    }
                }

                if (devices is CheckBox DVC1)
                {
                    // name = "checkBox10";

                    if (DVC1.Name.ToLower().IndexOf(name.ToLower()) == -1)
                    {
                        WriteIniData("GameCofing", FormBT + ":" + DVC1.Name, DVC1.Checked.ToString(), path);
                    }


                }

                if (devices is ComboBox DVC2)
                {


                    if (DVC2.Name.ToLower().IndexOf(name.ToLower()) == -1)
                    {

                        if (DVC2.Items.Count > 0 && DVC2.SelectedIndex != -1)
                        {
                            WriteIniData("GameCofing", FormBT + ":" + DVC2.Name, DVC2.SelectedIndex.ToString(), path);
                        }

                    }



                }

            }



        }



        /// <summary>
        /// 读取批量控件内容 建议频繁少用!频繁使用会出问题!
        /// </summary>
        /// <param name="Form">窗口 比如Form1.ActiveForm</param>
        /// <param name="path">读取文件路径  默认根目录Data\Cofing.ini</param>
        public static void ReadBatch(Control Form, string path = @".\Data\Cofing.ini")
        {
            if (Form != null)
            {
                allCtrls = new Queue<Control>();
                FormBT = Form.Name;
                CheckAllCtrls(Form);
            }

            foreach (Control devices in allCtrls)
            {

                if (devices is TextBox DVC)
                {
                    var NEIR1 = ReadIniData("GameCofing", FormBT + ":" + DVC.Name, "", path);
                    // Console.WriteLine(ReadIniData("GameCofing", DVC.Name, "", path));
                    if (NEIR1 != "")
                        DVC.Text = NEIR1; ;


                }


                if (devices is CheckBox DVC1)
                {
                    // Console.WriteLine(ReadIniData("GameCofing", DVC1.Name, "1", path));

                    string hjie = ReadIniData("GameCofing", FormBT + ":" + DVC1.Name, "", path);

                    if (hjie == true.ToString())
                    {
                        DVC1.Checked = true;
                    }
                    else if (hjie != "")
                    {
                        DVC1.Checked = false;
                    }
                }

                if (devices is ComboBox DVC2)
                {
                    if (DVC2.Items.Count > 0)
                    {
                        var IND = -1;
                        if (ReadIniData("GameCofing", FormBT + ":" + DVC2.Name, "", path) == "")
                        {
                            IND = -1;
                        }
                        else
                        {
                            IND = int.Parse(ReadIniData("GameCofing", FormBT + ":" + DVC2.Name, "", path));
                        }



                        if (IND == -1)
                        {
                            DVC2.SelectedIndex = 0;
                        }
                        else
                        {
                            DVC2.SelectedIndex = int.Parse(ReadIniData("GameCofing", FormBT + ":" + DVC2.Name, "", path));
                        }
                    }



                }
            }



        }

        private static Queue<Control> allCtrls = new Queue<Control>();

        private static void CheckAllCtrls(Control item)
        {

            for (int i = 0; i < item.Controls.Count; i++)
            {
                if (item.Controls[i].HasChildren)
                {

                    CheckAllCtrls(item.Controls[i]);

                }
                //else{allCtrls.Enqueue (item.Controls[i]);}//如果只要子控件，那么这个语句在else里
                allCtrls.Enqueue(item.Controls[i]);
            }

        }

        /// <summary>
        /// 读取图片文件
        /// </summary>
        /// <param name="path">图片文件路径</param>
        /// <returns>图片文件</returns>
        public static Bitmap ReadImageFile(String path)
        {
            Bitmap bitmap = null;
            try
            {
                FileStream fileStream = File.OpenRead(path);
                Int32 filelength = 0;
                filelength = (int)fileStream.Length;
                Byte[] image = new Byte[filelength];
                fileStream.Read(image, 0, filelength);
                System.Drawing.Image result = System.Drawing.Image.FromStream(fileStream);
                fileStream.Close();
                bitmap = new Bitmap(result);
            }
            catch (Exception)
            {
                //  异常输出
            }
            return bitmap;
        }

        public class ListViewColumnSorter : IComparer
        {
            private int ColumnToSort;  //指定按照哪列排序
            private SortOrder OrderOfSort;  //指定排序的方式
            private CaseInsensitiveComparer ObjectCompare;  //声明CaaseInsensitiveComparer类对象
            public ListViewColumnSorter()  //构造函数
            {
                ColumnToSort = 0;  //默认按第一列排序
                OrderOfSort = SortOrder.None;  //排序
                ObjectCompare = new CaseInsensitiveComparer();  //初始化CaseInsensitiveComparer类对象
            }
            //重写IComparer接口
            //返回比较的结果:如果x=y返回0；如果x>y返回1；如果x<y返回-1
            public int Compare(object x, object y)
            {
                int compareResult;
                ListViewItem listViewX, listViewY;
                //将比较对象转换为ListViewItem对象
                listViewX = (ListViewItem)x;
                listViewY = (ListViewItem)y;
                //比较
                compareResult = ObjectCompare.Compare(listViewX.SubItems[ColumnToSort].Text, listViewY.SubItems[ColumnToSort].Text);
                // 返回比较的结果
                if (OrderOfSort == SortOrder.Ascending)
                {
                    // 因为是正序排序，所以直接返回结果
                    return compareResult;
                }
                else if (OrderOfSort == SortOrder.Descending)
                {
                    // 如果是反序排序，所以要取负值再返回
                    return (-compareResult);
                }
                else
                {
                    return 0;
                }
            }
            /// 获取并设置按照哪一列排序. 
            public int SortColumn
            {
                set
                {
                    ColumnToSort = value;
                }
                get
                {
                    return ColumnToSort;
                }
            }
            /// 获取并设置排序方式.
            public SortOrder Order
            {
                set
                {
                    OrderOfSort = value;
                }
                get
                {
                    return OrderOfSort;
                }
            }
        }

        public class Translation
        {
            public string Src { get; set; }
            public string Dst { get; set; }
        }

        public class TranslationResult
        {
            //错误码，翻译结果无法正常返回
            public string Error_code { get; set; }
            public string Error_msg { get; set; }
            public string From { get; set; }
            public string To { get; set; }
            public string Query { get; set; }
            //翻译正确，返回的结果
            //这里是数组的原因是百度翻译支持多个单词或多段文本的翻译，在发送的字段q中用换行符（\n）分隔
            public Translation[] Trans_result { get; set; }
        }

        public enum Language
        {
            //百度翻译API官网提供了多种语言，这里只列了几种
            auto = 0,
            zh = 1,
            en = 2,
            cht = 3,
            kor = 4,
        }

        //对字符串做md5加密
        private static string GetMD5WithString(string input)
        {
            if (input == null)
            {
                return null;
            }
            MD5 md5Hash = MD5.Create();
            //将输入字符串转换为字节数组并计算哈希数据  
            byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(input));
            //创建一个 Stringbuilder 来收集字节并创建字符串  
            StringBuilder sBuilder = new StringBuilder();
            //循环遍历哈希数据的每一个字节并格式化为十六进制字符串  
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }
            //返回十六进制字符串  
            return sBuilder.ToString();
        }

        /// <summary>
        /// 调用百度翻译API进行翻译
        /// 详情可参考http://api.fanyi.baidu.com/api/trans/product/apidoc
        /// </summary>
        /// <param name="q">待翻译字符</param>
        /// <param name="from">源语言</param>
        /// <param name="to">目标语言</param>
        /// <returns></returns>
        private static TranslationResult GetTranslationFromBaiduFanyi(string q, Language from, Language to)
        {
            //可以直接到百度翻译API的官网申请
            //一定要去申请，不然程序的翻译功能不能使用
            string appId = "20211125001009764";
            string password = "bK_caOMZsXbqhPQ4EYbb";

            string jsonResult = String.Empty;
            //源语言
            string languageFrom = from.ToString().ToLower();
            //目标语言
            string languageTo = to.ToString().ToLower();
            //随机数
            string randomNum = System.DateTime.Now.Millisecond.ToString();
            //md5加密
            string md5Sign = GetMD5WithString(appId + q + randomNum + password);
            //url
            string url = String.Format("http://api.fanyi.baidu.com/api/trans/vip/translate?q={0}&from={1}&to={2}&appid={3}&salt={4}&sign={5}",
                HttpUtility.UrlEncode(q, Encoding.UTF8),
                languageFrom,
                languageTo,
                appId,
                randomNum,
                md5Sign
                );
            WebClient wc = new WebClient();
            try
            {
                jsonResult = wc.DownloadString(url);
            }
            catch
            {
                jsonResult = string.Empty;
            }
            //解析json
            JavaScriptSerializer jss = new JavaScriptSerializer();
            TranslationResult result = jss.Deserialize<TranslationResult>(jsonResult);
            return result;
        }

        /// <summary>
        /// 将内容翻译为中文
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string translation(string source)
        {
            TranslationResult result = GetTranslationFromBaiduFanyi(source, Language.auto, Language.zh);
            //判断是否出错
            if (result.Error_code == null)
            {
                return result.Trans_result[0].Dst;
            }
            else
            {
                return "翻译太快!因为是免费";
                //检查appid和密钥是否正确
                //return "翻译出错，错误码：" + result.Error_code + "，错误信息：" + result.Error_msg;
            }
        }
        /// <summary>
        /// 将内容翻译为韩文
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static string translationHF(string source)
        {
            TranslationResult result = GetTranslationFromBaiduFanyi(source, Language.auto, Language.kor);
            //判断是否出错
            if (result.Error_code == null)
            {
                return result.Trans_result[0].Dst;
            }
            else
            {
                return "I'm too handsome so fail!";
                //检查appid和密钥是否正确
                //return "翻译出错，错误码：" + result.Error_code + "，错误信息：" + result.Error_msg;
            }
        }
        /// <summary>
        /// 判断是否选择英雄操作完成
        /// </summary>
        /// <param name="G_type"> </param>
        /// <param name="HeroID">当前操作英雄ID</param>
        /// <returns></returns>
        public static bool Getselectoperation(string G_type, string HeroID)
        {

            bool JG = false;


            var json = zi_Getwenb("/lol-champ-select/v1/session", "GET", Encoding.UTF8);

            if (json.IndexOf("httpStatus") == -1)
            {
                var jsonGO = JSON.Jiexun(json);

                var GO2 = jsonGO["actions"];

                var localPlayerCellId = jsonGO["localPlayerCellId"].ToString();

                for (int i1 = 0; i1 < GO2.Count(); i1++)
                {
                    var GO1 = GO2[i1];

                    for (int i = 0; i < GO1.Count(); i++)
                    {
                        var actorCellId = GO1[i]["actorCellId"].ToString();//判断是否自己

                        var championId = GO1[i]["championId"].ToString();//当前操作英雄ID

                        var type = GO1[i]["type"].ToString();//当前操作状态

                        var isInProgress = GO1[i]["isInProgress"].ToString();//是否正在进行中

                        var completed = GO1[i]["completed"].ToString();//是否操作完毕


                        if (actorCellId == localPlayerCellId)//判断是自己操作
                        {
                            if (G_type == type && type == "ban")
                            {
                                if (completed.ToLower() == "true" && championId == HeroID)
                                {
                                    JG = true;
                                    break;
                                }


                            }

                            if (G_type == type && type == "pick")
                            {
                                if (championId == HeroID)
                                {
                                    JG = true;
                                    break;
                                }


                            }

                            if (G_type == "sding" && type == "pick")
                            {

                                if (completed.ToLower() == "true")
                                {
                                    if (championId == HeroID)
                                    {
                                        JG = true;
                                        break;
                                    }



                                }


                            }

                        }

                    }


                }



            }
            return JG;
        }

        /// <summary>
        /// 取当前操作英雄ID
        /// </summary>
        /// <param name="G_type"></param>
        /// <returns></returns>
        public static string GetselectoperationID(string G_type)
        {

            string HeroID = "0";


            var json = zi_Getwenb("/lol-champ-select/v1/session", "GET", Encoding.UTF8);

            if (json.IndexOf("httpStatus") == -1)
            {
                var jsonGO = JSON.Jiexun(json);

                var GO2 = jsonGO["actions"];

                var localPlayerCellId = jsonGO["localPlayerCellId"].ToString();

                for (int i1 = 0; i1 < GO2.Count(); i1++)
                {
                    var GO1 = GO2[i1];

                    for (int i = 0; i < GO1.Count(); i++)
                    {
                        var actorCellId = GO1[i]["actorCellId"].ToString();//判断是否自己

                        var championId = GO1[i]["championId"].ToString();//当前操作英雄ID

                        var type = GO1[i]["type"].ToString();//当前操作状态

                        var isInProgress = GO1[i]["isInProgress"].ToString();//是否正在进行中

                        var completed = GO1[i]["completed"].ToString();//是否操作完毕


                        if (actorCellId == localPlayerCellId)//判断是自己操作
                        {

                            if (G_type == type)
                            {
                                HeroID = championId;
                                break;


                            }
                        }
                    }



                }



            }
            return HeroID;
        }



        public class Chatdata
        {
            public string SummonerName;
            public string Chatting;

            public Chatdata(string SummonerName1 = "", string Chatting1 = "")
            {
                this.SummonerName = SummonerName1;
                this.Chatting = Chatting1;


            }
        }
        /// <summary>
        /// ID取游戏名称
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        public static (string IDname, string ranked) GetIDname(string ID)
        {
            var J1 = Util.zi_Getwenb("/lol-summoner/v1/summoners/" + ID);

            var IDname1 = "";

            var accountpuuid11 = "";

            string duanwei = "(无)";

            if (J1.IndexOf("accountId") != -1)
            {
                var J2 = JSON.Jiexun(J1);

                IDname1 = J2["internalName"].ToString();
                accountpuuid11 = J2["puuid"].ToString();

                duanwei = string.Format("({0})", Getranked(accountpuuid11));

            }
            return (IDname1, IDname1 + duanwei);
        }


        public static string GetIDname1(string ID)
        {


            var IDname = "";

            foreach (var item in Form1.form1.AllFFF)
            {

                if (item.summonerId == ID)
                {
                    IDname = item.HeroNameGO;
                    break;
                }

            }

            return IDname;
        }

        public static string Getranked(string accountpuuid1)
        {
            var d1 = zi_Getwenb("/lol-ranked/v1/ranked-stats/" + accountpuuid1);

            string Rank = "";

            string RankLevel = "";

            string duanwei = "无";

            if (d1.IndexOf("远程服务器") == -1)
            {
                var d2 = JSON.Jiexun(d1);

                Rank = d2["queueMap"]["RANKED_SOLO_5x5"]["tier"].ToString().ToLower();//召唤师段位


                RankLevel = d2["queueMap"]["RANKED_SOLO_5x5"]["division"].ToString();//召唤师段位等级

                string wuwu = "无";

                switch (Rank)
                {
                    case "iron":
                        wuwu = "黑铁";
                        break;
                    case "grandmaster":
                        wuwu = "宗师";
                        break;
                    case "bronze":
                        wuwu = "黄铜";
                        break;
                    case "silver":
                        wuwu = "白银";
                        break;
                    case "gold":
                        wuwu = "黄金";
                        break;
                    case "platinum":
                        wuwu = "白金";
                        break;
                    case "diamond":
                        wuwu = "钻石";
                        break;
                    case "master":
                        wuwu = "宗师";
                        break;
                    case "challenger":
                        wuwu = "最强王者";
                        break;
                    default:
                        wuwu = "无";
                        break;
                }
                if (wuwu == "最强王者" || wuwu == "宗师")
                {
                    RankLevel = "";
                }
                if (wuwu == "无")
                {
                    Rank = "无";
                    RankLevel = "";
                };

                duanwei = wuwu + RankLevel;


            }

            return duanwei;

        }

        public static string GetIDname2(string ID)
        {
            var J1 = Util.zi_Getwenb("/lol-summoner/v1/summoners/" + ID);

            var IDname = "";
            var duanwei = "(无)";
            if (J1.IndexOf("accountId") != -1)
            {
                var J2 = JSON.Jiexun(J1);

                var name = J2["internalName"].ToString();

                var puuid = J2["puuid"].ToString().ToLower();

                var J3 = JSON.Jiexun(zi_Getwenb("/lol-gameflow/v1/session"));


                var J4 = J3["gameData"]["playerChampionSelections"];

                var gameId = J3["gameData"]["gameId"];

                var J33 = JSON.Jiexun(zi_Getwenb("/lol-match-history/v1/games/" + gameId));

                var participants = J33["participants"];//获取英雄数据

                var participantIdentities = J33["participantIdentities"];//获取人物数据

                string participantId = "";

                IDname = name;

                duanwei = string.Format("({0})", Getranked(puuid));

                for (int i = 0; i < participantIdentities.Count(); i++)
                {
                    var summonerIdG = participantIdentities[i]["player"]["summonerId"].ToString();

                    if (summonerIdG == ID)
                    {
                        participantId = participantIdentities[i]["participantId"].ToString();

                        break;
                    }

                }

                for (int i = 0; i < participants.Count(); i++)
                {
                    var participantIdG = participants[i]["participantId"].ToString();

                    if (participantIdG == participantId)
                    {
                        var ID1 = participants[i]["championId"].ToString();

                        IDname = Getheroname1(ID1);

                        break;
                    }


                }



                //for (int i = 0; i < J4.Count(); i++)
                //{
                //    var name1 = J4[i]["summonerInternalName"].ToString();

                //    var ID1 = J4[i]["championId"].ToString();

                //    if (name1 == name)
                //    {
                //        IDname = Getheroname1(ID1);

                //        break;
                //    }


                //}


            }
            return IDname + duanwei;
        }



        /// <summary>
        /// 取结束后所有聊天记录!
        /// </summary>
        /// <param name="S1"></param>
        /// <returns></returns>
        public static List<Chatdata> GetChatID(string S1)
        {
            string ID = "";

            List<Chatdata> SHUZU = new List<Chatdata>();

            //var S1 = zi_Getwenb("/lol-chat/v1/conversations");

            if (S1.IndexOf("id") != -1)
            {
                var S2 = JSON.Jiexun_1(S1);

                for (int i = 0; i < S2.Count; i++)
                {
                    var type = S2[i]["type"].ToString();

                    if (type == "postGame" || type == "championSelect")
                    {
                        ID = S2[i]["id"].ToString();
                    }
                }



            }

            var S3 = zi_Getwenb(@"/lol-chat/v1/conversations/" + ID + "/messages");

            if (S3.IndexOf("body") != -1)
            {

                SHUZU.Clear();

                var S4 = JSON.Jiexun_1(S3);

                for (int i = 0; i < S4.Count; i++)
                {
                    var type = S4[i]["type"].ToString();
                    var fromSummonerId = S4[i]["fromSummonerId"].ToString();
                    if (type == "groupchat" && fromSummonerId != summonerId)
                    {
                        Chatdata L_SHUZU = new Chatdata();


                        //string name1 = GetIDname1(fromSummonerId);

                        //if (name1 == "")
                        string name1 = GetIDname2(fromSummonerId);

                        L_SHUZU.SummonerName = name1;

                        L_SHUZU.Chatting = S4[i]["body"].ToString();

                        SHUZU.Add(L_SHUZU);
                    }

                }


            }



            return SHUZU;

        }



        public static string Getleague(string name)
        {
            string URI = "https://kr.api.riotgames.com/lol/summoner/v4/summoners/by-name/" + name.ToLower();
            string WWW = Util.HttpRequest_Get(URI);
            string FAN = "0";
            if (WWW.IndexOf("id") != -1)
            {
                var WWW1 = JSON.Jiexun(WWW);

                var encryptedSummonerId = WWW1["id"].ToString();

                string uri1 = "https://kr.api.riotgames.com/lol/league/v4/entries/by-summoner/" + encryptedSummonerId;

                string WWW2 = Util.HttpRequest_Get(uri1);

                if (WWW2.IndexOf("RANKED_SOLO_5x5") != -1)
                {
                    var WWW3 = JSON.Jiexun_1(WWW2);

                    for (int i = 0; i < WWW3.Count(); i++)
                    {

                        var WWW4 = WWW3[i];
                        var queueType = WWW4["queueType"].ToString();

                        if (queueType == "RANKED_SOLO_5x5")
                        {
                            var tier = WWW4["tier"].ToString();
                            var leaguePoints = WWW4["leaguePoints"].ToString();
                            var wins = float.Parse(WWW4["wins"].ToString());
                            var losses = float.Parse(WWW4["losses"].ToString());

                            var Zwin = ((int)(wins / (wins + losses) * 100));
                            FAN = Zwin.ToString();
                            break;
                            //Console.WriteLine("名称:{0} 段位:{1} 胜点:{2} 胜:{3} 败:{4}", queueType, tier, leaguePoints, wins, losses);
                        }

                    }
                }


            }
            return FAN;
            //Console.WriteLine("111:" + WWW);
        }



    }


}

{"format": 1, "restore": {"D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\HttpToSocks5Proxy.Tests.csproj": {}}, "projects": {"D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"version": "1.4.0", "restore": {"projectUniqueName": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj", "projectName": "HttpToSocks5Proxy", "projectPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net45": {"targetAlias": "net45", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}}, "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\HttpToSocks5Proxy.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\HttpToSocks5Proxy.Tests.csproj", "projectName": "HttpToSocks5Proxy.Tests", "projectPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\HttpToSocks5Proxy.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\test\\HttpToSocks5Proxy.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp2.2"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "projectReferences": {"D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"projectPath": "D:\\项目\\WindowsFormsApp1\\WindowsFormsApp1\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp2.2": {"targetAlias": "netcoreapp2.2", "dependencies": {"Microsoft.NETCore.App": {"suppressParent": "All", "target": "Package", "version": "[2.2.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}}}}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices; // 确保存在
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

// 添加 SharpAvi 相关的 using 声明 (虽然不再直接使用，但保留以防万一或兼容性考虑)
using SharpAvi;
using SharpAvi.Codecs;
using SharpAvi.Output;
using System.Drawing.Imaging; // 用于处理 Bitmap (如果需要其他图像处理)
// using System.Drawing; // 已在上面声明
using System.Reflection; // 用于反射获取属性 (如果需要)
using System.Xml.Serialization; // 添加 XML 序列化命名空间
using System.Management; // 确保 System.Management 引用已添加

namespace WindowsFormsApp1
{
    /// <summary>
    /// 支持的GPU类型
    /// </summary>
    public enum GpuType
    {
        None,
        NVIDIA,
        AMD,
        Intel
    }

    /// <summary>
    /// LOL自动录制工具主窗体
    /// </summary>
    public partial class LOL : Form
    {
        private GpuType _detectedGpuType = GpuType.None; // 检测到的GPU类型
        private bool _enableGpuAcceleration = true; // 是否启用GPU加速，从配置加载
        private string _screenCaptureMethod = "ddagrab"; // 默认ddagrab

        #region DPI Scaling Factors and Helpers

        private float dpiScaleFactorX = 0f; // 初始化为0，以便检测是否已获取
        private float dpiScaleFactorY = 0f;

        private void EnsureDpiFactorsInitialized()
        {
            if (this.dpiScaleFactorX == 0f || this.dpiScaleFactorY == 0f) // 仅在未初始化时获取
            {
                // 确保在窗体句柄创建后调用
                if (this.IsHandleCreated)
                {
                    using (Graphics g = this.CreateGraphics())
                    {
                        this.dpiScaleFactorX = g.DpiX / 96.0f;
                        this.dpiScaleFactorY = g.DpiY / 96.0f;
                    }
                }
                else
                {
                    // 尝试从主屏幕获取，作为备选方案（如果窗体句柄未创建）
                    // 这通常不应发生，因为 InitializeComponents 在 InitializeComponent() 之后
                    try
                    {
                        using (Graphics g = Graphics.FromHwnd(IntPtr.Zero))
                        {
                            this.dpiScaleFactorX = g.DpiX / 96.0f;
                            this.dpiScaleFactorY = g.DpiY / 96.0f;
                        }
                        LogMessage("Warning: DPI factors initialized using screen graphics as form handle was not ready.");
                    }
                    catch (Exception ex) // 如果连屏幕Graphics都获取失败
                    {
                        this.dpiScaleFactorX = 1.0f;
                        this.dpiScaleFactorY = 1.0f;
                        LogError($"Failed to get screen graphics for DPI: {ex.Message}. Using default 1.0.");
                    }
                }

                if (this.dpiScaleFactorX <= 0) this.dpiScaleFactorX = 1.0f;
                if (this.dpiScaleFactorY <= 0) this.dpiScaleFactorY = 1.0f;
                LogMessage($"DPI Scaling factors initialized. FactorX: {this.dpiScaleFactorX:F2}, FactorY: {this.dpiScaleFactorY:F2}");
            }
        }

        private int ScaleValueX(int value)
        {
            EnsureDpiFactorsInitialized();
            return (int)Math.Round(value * this.dpiScaleFactorX);
        }

        private int ScaleValueY(int value)
        {
            EnsureDpiFactorsInitialized();
            return (int)Math.Round(value * this.dpiScaleFactorY);
        }

        private Point ScalePoint(Point p)
        {
            return new Point(ScaleValueX(p.X), ScaleValueY(p.Y));
        }

        private Size ScaleSize(Size s)
        {
            return new Size(ScaleValueX(s.Width), ScaleValueY(s.Height));
        }

        private Font CreateScaledFont(FontFamily family, float baseSizeInPointsAt96Dpi, FontStyle style)
        {
            EnsureDpiFactorsInitialized();
            if (family == null) family = SystemFonts.DefaultFont.FontFamily;
            float scaledSize = baseSizeInPointsAt96Dpi * this.dpiScaleFactorY; // 通常字体大小更依赖垂直DPI
            if (scaledSize < 1.0f) scaledSize = 1.0f; // 避免字体过小
            return new Font(family, scaledSize, style);
        }

        #endregion

        #region 属性和字段

        // 配置参数
        private string _gameName = "League of Legends";
        // 游戏相关进程名，包括客户端和游戏对局进程
        private string[] _processNames = { "LeagueClientUx", "League of Legends" };
        // 客户端进程名和游戏对局进程名分开定义，便于单独处理
        private string _clientProcessName = "LeagueClientUx";
        private string _gameProcessName = "League of Legends";
        private string _savePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyVideos), "LOL录像");
        private Size _resolution = new Size(1920, 1080); // 目标录制分辨率
        private int _frameRate = 30;
        private int _quality = 70; // 录制质量 (0-100)，将映射到 FFmpeg 参数
        private bool _useHEVC = false; // 新增：是否使用HEVC编码
        private string _compressionPreset = "medium"; // 新增：压缩预设
        private string _fileNamePrefix = "LOL游戏录像";
        private Label lblCompression; // 新增：压缩设置标签
        private CheckBox cbUseHEVC; // 新增：是否使用HEVC编码的选项
        private ComboBox cbCompressionPreset; // 新增：压缩预设选项
        // 新增：说明标签
        private Label lblQualityInfo; // 画质说明标签
        private Label lblCompressionInfo; // 压缩预设说明标签
        private Label lblHEVCCompatibility; // HEVC兼容性提示标签
        private ToolTip toolTip; // 工具提示组件

        // 运行状态
        private bool _isRecording = false;
        private bool _isPaused = false;
        private bool _isGameRunning = false;
        private bool _autoStarted = false;
        private DateTime _recordStartTime;
        private int _recordingCount = 0;

        // 视频捕获和录制相关
        private VideoRecorder _recorder; // 使用新的 VideoRecorder 类
        // 替换WMI事件监视器为轻量级的Timer
        private System.Windows.Forms.Timer _processMonitorTimer;
        // 保留线程取消标记，用于清理资源
        private CancellationTokenSource _cancellationTokenSource;

        // 选区录制相关
        private Rectangle _selectedRegion = Rectangle.Empty; // 用于存储用户选择的录制区域

        // 内存管理
        private System.Windows.Forms.Timer _memoryMonitorTimer;
        private long _lastMemoryUsage = 0;
        private int _memoryStableCount = 0;

        // 视频文件管理
        private System.Windows.Forms.Timer _videoFileCleanupTimer;
        private const int VIDEO_RETENTION_DAYS = 3; // 视频保留天数

        // 旧的WMI监控资源（可能在其他地方仍被引用）
        private ManagementEventWatcher _managementEventWatcher = null;

        // 用于F8热键功能
        private const int WM_HOTKEY = 0x0312;
        private const int HOTKEY_ID_F8 = 1; // F8热键的唯一ID
        private Point _lastWindowLocation; // 用于存储窗口隐藏前的位置
        private bool _isWindowHiddenByHotkey = false; // 标记窗口是否由热键隐藏

        // 日志控制
        private bool _isLoggingEnabled = true; // 控制日志输出的开关，默认为true

        #endregion

        #region 初始化和窗体事件

        public LOL()
        {
            try
            {
                InitializeComponent();

                // 只在构造函数中执行基础的UI初始化
                InitializeComponents();

                // 添加日志记录，帮助诊断问题
                LogMessage("构造函数执行完成");
            }
            catch (Exception ex)
            {
                // 确保任何初始化异常都被捕获并记录
                MessageBox.Show($"初始化失败: {ex.Message}\n{ex.StackTrace}", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Win32 API 用于注册和注销热键
        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        // 定义按键修饰符 (如果需要，例如 Alt, Ctrl, Shift)
        private static class HotkeyModifiers
        {
            public const uint MOD_NONE = 0x0000;
            public const uint MOD_ALT = 0x0001;
            public const uint MOD_CONTROL = 0x0002;
            public const uint MOD_SHIFT = 0x0004;
            public const uint MOD_WIN = 0x0008;
        }

        /// <summary>
        /// 初始化界面组件
        /// </summary>
        private void InitializeComponents()
        {
            EnsureDpiFactorsInitialized(); // 确保DPI因子已初始化

            // 创建保存目录
            if (!Directory.Exists(_savePath))
            {
                Directory.CreateDirectory(_savePath);
            }

            // 初始化工具提示组件
            if (toolTip == null)
            {
                toolTip = new ToolTip();
                toolTip.AutoPopDelay = 10000; // 提示显示10秒
                toolTip.InitialDelay = 500; // 鼠标悬停0.5秒后显示
                toolTip.ReshowDelay = 200; // 从一个控件移动到另一个控件的延迟
            }

            // 初始化压缩相关控件（如果尚未创建）
            if (cbUseHEVC == null)
            {
                // 为了避免对Designer.cs文件的修改，动态创建这些控件
                Control parentContainer = this.groupBoxConfig;
                float baseFontSize = SystemFonts.DefaultFont.Size; // 获取系统默认字体大小作为基准 (96 DPI下的点数)

                // 计算Y坐标位置 - 从原有控件下方开始
                int baseY = ScaleValueY(150); // 开始位置
                int labelHeight = ScaleValueY(25); // 标签高度
                int spacing = ScaleValueY(5); // 控件间距

                // 创建压缩预设标签
                lblCompression = new Label();
                lblCompression.Text = "压缩预设:";
                lblCompression.AutoSize = true;
                lblCompression.Location = new Point(ScaleValueX(30), baseY); // 调整左对齐位置
                lblCompression.Font = CreateScaledFont(lblCompression.Font.FontFamily, baseFontSize, lblCompression.Font.Style);
                parentContainer.Controls.Add(lblCompression);

                // 创建压缩预设下拉框
                cbCompressionPreset = new ComboBox();
                // 更新编码级别选项以包含 "默认"
                cbCompressionPreset.Items.AddRange(new object[] { "默认", "极速", "超快", "很快", "较快", "快速", "中等", "慢速", "较慢" });
                cbCompressionPreset.Location = new Point(ScaleValueX(150), baseY - ScaleValueY(3)); // 微调垂直对齐
                cbCompressionPreset.DropDownStyle = ComboBoxStyle.DropDownList;
                cbCompressionPreset.Width = ScaleValueX(120);
                cbCompressionPreset.Font = CreateScaledFont(cbCompressionPreset.Font.FontFamily, baseFontSize, cbCompressionPreset.Font.Style);
                parentContainer.Controls.Add(cbCompressionPreset);

                // 创建HEVC复选框
                cbUseHEVC = new CheckBox();
                cbUseHEVC.Text = "使用HEVC编码（更小文件但编码更慢）";
                cbUseHEVC.AutoSize = true;
                cbUseHEVC.Location = new Point(ScaleValueX(330), baseY); // 调整位置与压缩预设保持适当间距
                cbUseHEVC.Checked = _useHEVC;
                cbUseHEVC.CheckedChanged += cbUseHEVC_CheckedChanged;
                cbUseHEVC.Font = CreateScaledFont(cbUseHEVC.Font.FontFamily, baseFontSize, cbUseHEVC.Font.Style);
                parentContainer.Controls.Add(cbUseHEVC);

                // 新增：创建画质说明标签
                lblQualityInfo = new Label();
                lblQualityInfo.Text = "* 画质说明：较高画质产生更大文件但清晰度更好";
                lblQualityInfo.AutoSize = true;
                lblQualityInfo.Font = CreateScaledFont(lblQualityInfo.Font.FontFamily, baseFontSize - 1f, lblQualityInfo.Font.Style); // 比默认小一点
                lblQualityInfo.ForeColor = Color.DarkBlue;
                lblQualityInfo.Location = new Point(ScaleValueX(400), ScaleValueY(100)); // 放在画质选择下方
                parentContainer.Controls.Add(lblQualityInfo);

                // 新增：创建压缩预设说明标签
                lblCompressionInfo = new Label();
                lblCompressionInfo.Text = "* 压缩预设说明：越慢CPU占用越高但文件更小";
                lblCompressionInfo.AutoSize = true;
                lblCompressionInfo.Font = CreateScaledFont(lblCompressionInfo.Font.FontFamily, baseFontSize - 1f, lblCompressionInfo.Font.Style);
                lblCompressionInfo.ForeColor = Color.DarkBlue;
                lblCompressionInfo.Location = new Point(ScaleValueX(30), baseY + labelHeight); // 与压缩预设标签左对齐
                parentContainer.Controls.Add(lblCompressionInfo);

                // 新增：HEVC兼容性提示标签
                lblHEVCCompatibility = new Label();
                lblHEVCCompatibility.Text = "* 注意：HEVC(H.265)编码需确保播放设备或软件兼容";
                lblHEVCCompatibility.AutoSize = true;
                lblHEVCCompatibility.Font = CreateScaledFont(lblHEVCCompatibility.Font.FontFamily, baseFontSize - 1f, lblHEVCCompatibility.Font.Style);
                lblHEVCCompatibility.ForeColor = Color.Crimson;
                lblHEVCCompatibility.Location = new Point(ScaleValueX(330), baseY + labelHeight); // 与HEVC复选框左对齐
                parentContainer.Controls.Add(lblHEVCCompatibility);

                // 新增：鼠标隐藏说明标签
                Label lblMouseHiddenInfo = new Label();
                lblMouseHiddenInfo.Text = "* 本工具已使用优化稳定录制配置，解决鼠标闪烁和屏幕闪烁问题";
                lblMouseHiddenInfo.AutoSize = true;
                lblMouseHiddenInfo.Font = CreateScaledFont(lblMouseHiddenInfo.Font.FontFamily, baseFontSize - 1f, lblMouseHiddenInfo.Font.Style);
                lblMouseHiddenInfo.ForeColor = Color.SteelBlue;
                lblMouseHiddenInfo.Location = new Point(ScaleValueX(30), baseY + labelHeight * 2); // 左对齐放置
                parentContainer.Controls.Add(lblMouseHiddenInfo);

                // 调整groupBoxConfig的高度以适应新添加的控件
                parentContainer.Height = baseY + labelHeight * 3 + spacing * 3; // 根据最后一个控件的位置增加高度

                // 相应调整其他组的位置
                if (groupBoxControls != null)
                {
                    // 假设groupBoxControls.Location.X 和 parentContainer.Location.Y 是由设计器处理或已正确缩放
                    // 我们只缩放额外的间距
                    groupBoxControls.Location = new Point(
                        groupBoxControls.Location.X,
                        parentContainer.Location.Y + parentContainer.Height + ScaleValueY(10)); // 控制组下移
                }

                // 添加工具提示 (工具提示内容不需要缩放，但其显示位置和大小由系统处理)
                toolTip.SetToolTip(cbQuality, "低：小文件，质量一般\n中：平衡文件大小和画质\n高：优质画面，文件较大");
                toolTip.SetToolTip(cbCompressionPreset, "ultrafast: 最低CPU使用，最大文件\nsuperfast/veryfast: 低CPU使用\nmedium: 平衡选项(推荐)\nslow/slower: 高CPU使用，最小文件");
                toolTip.SetToolTip(cbUseHEVC, "HEVC(H.265)编码可产生更小的文件，但需要较高的处理能力\n并非所有播放器都支持此格式");
                toolTip.SetToolTip(lblHEVCCompatibility, "Windows 10/11原生支持HEVC，但旧版Windows或某些播放器可能需要额外安装解码器\n建议使用VLC或Potplayer等现代播放器");

                // 添加测试建议和硬件加速提示到控制组
                Label lblTestSuggestion = new Label();
                lblTestSuggestion.Text = "* 建议：进行短时间录制测试，根据您的电脑性能调整画质和压缩设置";
                lblTestSuggestion.AutoSize = true;
                lblTestSuggestion.Font = CreateScaledFont(lblTestSuggestion.Font.FontFamily, baseFontSize - 1f, lblTestSuggestion.Font.Style);
                lblTestSuggestion.ForeColor = Color.Green;
                lblTestSuggestion.Location = new Point(ScaleValueX(30), ScaleValueY(110)); // 保持左对齐
                groupBoxControls.Controls.Add(lblTestSuggestion);

                Label lblHardwareAccel = new Label();
                lblHardwareAccel.Text = "* 提示：已支持GPU硬件加速，将自动检测并尝试使用兼容的显卡进行编码。";
                lblHardwareAccel.AutoSize = true;
                lblHardwareAccel.Font = CreateScaledFont(lblHardwareAccel.Font.FontFamily, baseFontSize - 1f, lblHardwareAccel.Font.Style);
                lblHardwareAccel.ForeColor = Color.DarkGreen; // 更改颜色以示强调
                lblHardwareAccel.Location = new Point(ScaleValueX(30), ScaleValueY(133)); // 放在测试建议下方，保持左对齐
                groupBoxControls.Controls.Add(lblHardwareAccel);
            }

            // 加载配置（如果有）
            LoadConfig();

            // 初始化UI控件显示
            txtSavePath.Text = _savePath;
            txtPrefix.Text = _fileNamePrefix;
            // 假设 cbFrameRate 和 cbQuality 在设计器中已填充选项
            // 根据配置设置默认选中项
            cbFrameRate.SelectedItem = _frameRate.ToString();
            // 根据 _quality 设置 cbQuality 的选中项
            // "1 原画", "2 高清", "3 标清", "4 流畅"
            if (_quality >= 95) cbQuality.SelectedIndex = 0; // 1 原画 (内部用 >=95 代表)
            else if (_quality >= 80) cbQuality.SelectedIndex = 1; // 2 高清
            else if (_quality >= 50) cbQuality.SelectedIndex = 2; // 3 标清
            else cbQuality.SelectedIndex = 3; // 4 流畅 (或默认选中原画)
            // 确保cbQuality有默认选项，如果配置文件未指定或指定的值无效
            if (cbQuality.SelectedIndex == -1) cbQuality.SelectedIndex = 0; // 默认选中 "1 原画"

            // 初始化 checkBox1 的状态，可以基于配置或默认值
            // 此处假设默认显示边框，如果 RecorderConfig 中有对应配置，LoadConfig会覆盖它
            //checkBox1.Checked = true;
            cbAutoStart.Checked = _autoStarted;

            // 初始化HEVC编码选项
            if (cbUseHEVC != null)
            {
                cbUseHEVC.Checked = _useHEVC;
            }

            // 初始化压缩预设选项
            if (cbCompressionPreset != null && !string.IsNullOrEmpty(_compressionPreset))
            {
                // 确保 _compressionPreset 的值在 cbCompressionPreset.Items 中存在
                if (cbCompressionPreset.Items.Contains(_compressionPreset))
            {
                cbCompressionPreset.SelectedItem = _compressionPreset;
                }
                else
                {
                    cbCompressionPreset.SelectedItem = "默认"; // 如果不存在，则选择 "默认"
                    _compressionPreset = "默认"; // 同时更新内部变量
                }
            }
            else if (cbCompressionPreset != null) // 如果 cbCompressionPreset 存在但 _compressionPreset 为空
            {
                cbCompressionPreset.SelectedItem = "默认"; // 默认选择 "默认"
                _compressionPreset = "默认";
            }

            // 在窗体加载后再更新按钮状态，避免句柄尚未创建的问题
            // UpdateButtonStatus(); // 移除这一行

            // 初始化窗口标题
            this.Text = $"LOL自动录制工具 (F8键隐藏/显示窗口)";

            // 初始化录屏方式选择
            if (comboBoxScreenCaptureMethod != null)
            {
                comboBoxScreenCaptureMethod.Items.Clear();
                comboBoxScreenCaptureMethod.Items.AddRange(new object[] { "gdigrab", "ddagrab" }); // 移除 dshow
                comboBoxScreenCaptureMethod.SelectedItem = _screenCaptureMethod;
                comboBoxScreenCaptureMethod.SelectedIndexChanged += ComboBoxScreenCaptureMethod_SelectedIndexChanged;
            }
        }
        /// <summary>
        /// 初始化进程监控 - 使用优化的自适应Timer
        /// </summary>
        private void InitializeProcessMonitor()
        {
            try
            {
                LogMessage("初始化进程监控定时器开始");

                // 释放旧资源
                if (_managementEventWatcher != null)
                {
                    try
                    {
                        _managementEventWatcher.Stop();
                        _managementEventWatcher.Dispose();
                        _managementEventWatcher = null;
                        LogMessage("已释放旧的WMI资源");
                    }
                    catch (Exception)
                    {
                        // 忽略WMI资源释放过程中的异常
                    }
                }

                if (_processMonitorTimer != null)
                {
                    _processMonitorTimer.Stop();
                    _processMonitorTimer.Dispose();
                }

                // 创建新的进程监控定时器，使用更高效的自适应检测策略
                _processMonitorTimer = new System.Windows.Forms.Timer();
                // 初始间隔设为2秒，平衡响应性和CPU使用
                _processMonitorTimer.Interval = 2000;
                _processMonitorTimer.Tick += ProcessMonitorTimer_Tick;
                _processMonitorTimer.Start();

                LogMessage("进程监控定时器初始化完成（高效优化版本）");
            }
            catch (Exception ex)
            {
                LogError($"初始化进程监控定时器失败: {ex.GetType().Name} - {ex.Message}");
                MessageBox.Show($"初始化进程监控定时器失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 存储上一次检测的进程状态，减少不必要的日志和事件触发
        private bool _lastClientRunningState = false;
        private bool _lastGameRunningState = false;
        private DateTime _lastIntervalChangeTime = DateTime.MinValue;
        private const int INTERVAL_CHANGE_THRESHOLD_MS = 10000; // 10秒内不重复调整定时器间隔

        /// <summary>
        /// 进程监控定时器触发事件 - 优化版，自适应检测频率
        /// </summary>
        private void ProcessMonitorTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // 使用缓存的进程状态减少不必要的检查
                bool isClientRunning = IsProcessRunning(_clientProcessName);
                bool isGameRunning = IsProcessRunning(_gameProcessName);

                // 只有当状态变化时才记录日志，减少I/O操作
                bool stateChanged = (isGameRunning != _lastGameRunningState) ||
                                  (isClientRunning != _lastClientRunningState);

                // 自适应检测频率 - 根据游戏状态调整检测间隔，但避免频繁调整
                DateTime now = DateTime.Now;
                bool canChangeInterval = (now - _lastIntervalChangeTime).TotalMilliseconds > INTERVAL_CHANGE_THRESHOLD_MS;

                if (canChangeInterval)
                {
                    int newInterval = _processMonitorTimer.Interval;
                if (isGameRunning)
                {
                        // 游戏运行中，设置检测频率为2秒（比原来的1秒略高，但仍保持良好响应性）
                        newInterval = 2000;
                }
                else if (isClientRunning)
                {
                        // 客户端运行但游戏未运行，设置检测频率为3秒
                        newInterval = 3000;
                }
                else
                {
                        // 游戏和客户端都未运行，降低检测频率到5秒，大幅减少资源占用
                        newInterval = 5000;
                    }

                    // 只有当需要调整且间隔有变化时才调整定时器
                    if (newInterval != _processMonitorTimer.Interval)
                    {
                        _processMonitorTimer.Interval = newInterval;
                        _lastIntervalChangeTime = now;

                        if (stateChanged) // 只在状态变化时记录日志
                        {
                            LogMessage($"游戏状态变化，调整监控频率为{newInterval/1000}秒", LogLevel.Debug);
                        }
                    }
                }

                // 检测游戏对局进程变化 - 这是触发录制的关键
                if (isGameRunning && !_isGameRunning)
                {
                    // 游戏对局进程启动，开始录制
                    LogMessage($"检测到游戏对局进程启动");
                    ProcessGameStarted();
                }
                else if (!isGameRunning && _isGameRunning)
                {
                    // 游戏对局进程结束，停止录制
                    LogMessage($"检测到游戏对局进程结束");
                    ProcessGameStopped();
                }

                // 更新游戏运行状态和缓存
                _isGameRunning = isGameRunning;
                _lastGameRunningState = isGameRunning;
                _lastClientRunningState = isClientRunning;
            }
            catch (Exception ex)
            {
                LogError($"进程监控定时器事件异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 游戏对局进程启动处理
        /// </summary>
        private void ProcessGameStarted()
        {
            LogMessage($"ProcessGameStarted: 检测到游戏对局进程启动。");

            // 如果开启了自动启动且当前未录制，则开始录制
            if (!_isRecording && _autoStarted)
            {
                LogMessage("ProcessGameStarted: 自动启动开启且未录制，开始录制游戏对局。");
                StartRecording();
                UpdateStatusInfo($"{_gameName}对局已开始，自动开始录制");
            }
            else
            {
                // 记录当前自动启动设置状态，帮助诊断问题
                LogMessage($"ProcessGameStarted: 自动启动设置当前为: {_autoStarted}，未能自动开始录制");

                // 由于用户明确期望游戏启动时自动录制，这里添加额外检查，确保录制开始
                if (!_isRecording)
                {
                    LogMessage("ProcessGameStarted: 用户可能期望自动录制，尝试强制启动录制");
                    StartRecording();
                    UpdateStatusInfo($"{_gameName}对局已开始，已启动录制");

                    // 如果用户确实希望每次都自动录制，可以考虑将设置更新为自动
                    // _autoStarted = true;
                    // cbAutoStart.Checked = true;
                }
                else
                {
                    UpdateStatusInfo($"{_gameName}对局已开始");
                }
            }

            // 更新按钮状态
            UpdateButtonStatus();
        }

        /// <summary>
        /// 游戏对局进程停止处理
        /// </summary>
        private void ProcessGameStopped()
        {
            LogMessage($"ProcessGameStopped: 检测到游戏对局进程停止。");

            // 如果当前正在录制，则停止录制（无论是否是自动启动的录制）
            if (_isRecording)
            {
                LogMessage("ProcessGameStopped: 检测到游戏进程结束，停止录制。");
                StopRecording(false);
                UpdateStatusInfo($"{_gameName}对局已结束，已停止录制");
            }
            else
            {
                UpdateStatusInfo($"{_gameName}对局已结束");
            }

            // 更新按钮状态
            UpdateButtonStatus();
        }

        /// <summary>
        /// 设置内存监控定时器 - 优化版本
        /// </summary>
        private void SetupMemoryMonitoring()
        {
            _memoryMonitorTimer = new System.Windows.Forms.Timer();
            _memoryMonitorTimer.Interval = 90000; // 每90秒检查一次，减少CPU唤醒频率
            _memoryMonitorTimer.Tick += MemoryMonitor_Tick;
            _memoryMonitorTimer.Start();

            LogMessage("内存监控已初始化，90秒间隔", LogLevel.Debug);
        }

        /// <summary>
        /// 设置视频文件清理定时器 - 优化版本
        /// </summary>
        private void SetupVideoFileCleanup()
        {
            _videoFileCleanupTimer = new System.Windows.Forms.Timer();
            _videoFileCleanupTimer.Interval = 7200000; // 每两小时检查一次，减少频率
            _videoFileCleanupTimer.Tick += VideoFileCleanup_Tick;
            _videoFileCleanupTimer.Start();

            // 启动后不立即执行清理，而是延迟执行，避免启动时的性能峰值
            // 使用 System.Windows.Forms.Timer，因为其Tick事件会在UI线程上触发，方便调用UI更新方法
            System.Windows.Forms.Timer startupDelayTimer = new System.Windows.Forms.Timer();
            startupDelayTimer.Interval = 180000; // 3分钟后执行首次清理
            startupDelayTimer.Tick += (s, e) => {
            CleanupOldVideoFiles();
                // 确保在UI线程中停止和释放Timer
                if (startupDelayTimer != null)
                {
                    startupDelayTimer.Stop();
                    startupDelayTimer.Dispose();
                }
            };
            startupDelayTimer.Start();

            LogMessage("视频文件清理已初始化，首次清理将在3分钟后执行", LogLevel.Debug);
        }

        /// <summary>
        /// 视频文件清理定时器事件
        /// </summary>
        private void VideoFileCleanup_Tick(object sender, EventArgs e)
        {
            try
            {
                CleanupOldVideoFiles();
            }
            catch (Exception ex)
            {
                LogError($"视频文件清理过程中发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理超过指定天数的视频文件 - 优化版本
        /// </summary>
        private void CleanupOldVideoFiles()
        {
            try
            {
                LogMessage($"开始清理超过 {VIDEO_RETENTION_DAYS} 天的视频文件...");

                // 检查保存目录是否存在
                if (!Directory.Exists(_savePath))
                {
                    LogMessage("视频保存目录不存在，跳过清理");
                    return;
                }

                // 获取当前日期
                DateTime currentDate = DateTime.Now;
                int filesDeleted = 0;
                long spaceFreed = 0;

                // 递归查找所有视频文件 - 使用更高效的非递归列举
                string[] videoExtensions = { "*.mp4", "*.avi", "*.mkv", "*.mov" };

                // 批量处理视频文件，减少循环复杂度
                Dictionary<string, List<FileInfo>> videoFilesByFolder = new Dictionary<string, List<FileInfo>>();
                videoFilesByFolder[_savePath] = new List<FileInfo>();

                // 使用EnumerateDirectories替代GetDirectories，避免一次性加载所有目录
                foreach (string dateFolder in Directory.EnumerateDirectories(_savePath))
                {
                    videoFilesByFolder[dateFolder] = new List<FileInfo>();
                }

                // 批量处理每个文件夹内的视频文件
                foreach (var folder in videoFilesByFolder.Keys)
                {
                    foreach (string extension in videoExtensions)
                    {
                        try
                        {
                            // 使用EnumerateFiles替代GetFiles，避免一次性加载所有文件
                            foreach (string filePath in Directory.EnumerateFiles(folder, extension))
                            {
                                videoFilesByFolder[folder].Add(new FileInfo(filePath));
                            }
                        }
                        catch (Exception ex)
                        {
                            LogError($"枚举文件夹 {folder} 中的 {extension} 文件时出错: {ex.Message}");
                        }
                    }
                }

                // 处理找到的文件
                List<string> emptyFolders = new List<string>();
                foreach (var folder in videoFilesByFolder.Keys)
                {
                    var files = videoFilesByFolder[folder];
                    bool anyFilesLeft = false;

                    foreach (FileInfo file in files)
                {
                    try
                    {
                        // 计算文件创建时间与当前时间的差值
                        TimeSpan fileAge = currentDate - file.CreationTime;

                        // 如果文件超过了指定的保留天数，则删除它
                        if (fileAge.TotalDays > VIDEO_RETENTION_DAYS)
                        {
                            long fileSize = file.Length;
                            string filePath = file.FullName;

                                LogMessage($"删除过期视频文件: {Path.GetFileName(filePath)} (已存在 {fileAge.TotalDays:F1} 天)", LogLevel.Debug);
                            file.Delete();

                            filesDeleted++;
                            spaceFreed += fileSize;
                        }
                            else
                            {
                                anyFilesLeft = true;
                            }
                    }
                    catch (Exception ex)
                    {
                        LogError($"处理文件 {file.FullName} 时出错: {ex.Message}");
                            anyFilesLeft = true; // 处理失败的文件视为存在，避免删除有问题的文件夹
                        }
                    }

                    // 记录空文件夹供后续处理
                    if (!anyFilesLeft && folder != _savePath)
                    {
                        emptyFolders.Add(folder);
                    }
                }

                // 批量删除空文件夹
                foreach (string folder in emptyFolders)
                {
                    try
                    {
                        // 再次检查文件夹是否为空，以防有新文件创建
                        if (Directory.Exists(folder) && !Directory.EnumerateFileSystemEntries(folder).Any())
                        {
                            Directory.Delete(folder);
                            LogMessage($"删除空的日期文件夹: {Path.GetFileName(folder)}", LogLevel.Debug);
                    }
                }
                catch (Exception ex)
                {
                        LogError($"删除文件夹 {folder} 时出错: {ex.Message}");
                    }
                }

                // 记录清理结果
                if (filesDeleted > 0)
                {
                    string freedSpace = FormatBytes(spaceFreed);
                    LogMessage($"清理完成，共删除 {filesDeleted} 个过期视频文件，释放空间 {freedSpace}");
                    UpdateStatusInfo($"已自动清理 {filesDeleted} 个过期视频文件，释放空间 {freedSpace}");
                }
                else
                {
                    LogMessage($"没有找到超过 {VIDEO_RETENTION_DAYS} 天的视频文件");
                }
            }
            catch (Exception ex)
            {
                LogError($"视频文件清理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体加载事件 - 优化初始化顺序，避免卡死
        /// </summary>
        private void LOL_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化日志系统
                InitializeLogger();

                // 初始化日志开关状态并订阅事件
                // 假设 checkBox2 在 Designer.cs 中已定义并添加到窗体
                if (this.checkBox2 != null) // 确保 checkBox2 存在
                {
                    _isLoggingEnabled = this.checkBox2.Checked;
                    this.checkBox2.CheckedChanged += CheckBox2_CheckedChanged;
                }
                else
                {
                    // 如果 checkBox2 不存在，可以考虑默认启用日志或记录一个警告
                    _isLoggingEnabled = true; // 默认启用
                    // LogMessage("警告: checkBox2 未找到，日志功能将默认启用。", LogLevel.Error); // 此时LogMessage可能还未完全准备好
                }

                LogMessage("窗体加载事件开始执行");

                // 检查游戏是否已经在运行
                _isGameRunning = IsProcessRunning(_gameProcessName);
                LogMessage($"游戏运行状态: {_isGameRunning}");

                // 检测显卡类型
                _detectedGpuType = DetectGpuType();
                LogMessage($"检测到的显卡类型: {_detectedGpuType}");
                // 根据检测结果更新UI提示（如果需要更详细的提示）
                // 例如: UpdateStatusInfo($"显卡类型: {_detectedGpuType}, GPU加速配置: {(_enableGpuAcceleration ? "开启" : "关闭")}");


                // 新增：如果游戏已经在运行，手动触发游戏启动事件
                if (_isGameRunning)
                {
                    LogMessage("检测到游戏已经在运行，将触发游戏启动事件");
                    ProcessGameStarted();
                }

                // 初始化HEVC兼容性标签显示状态
                InitializeHEVCCompatibilityLabel();

                // 在窗体加载事件中更新按钮状态
                UpdateButtonStatus();
                LogMessage("按钮状态更新完成");

                // 注册F8全局热键
                bool hotkeyRegistered = RegisterHotKey(this.Handle, HOTKEY_ID_F8, (uint)HotkeyModifiers.MOD_NONE, (uint)Keys.F8);
                if (hotkeyRegistered)
                {
                    LogMessage("F8全局热键注册成功。");
                }
                else
                {
                    LogError("F8全局热键注册失败。可能已被其他程序占用。");
                    // 可以考虑提示用户
                    // MessageBox.Show("无法注册F8热键，可能已被其他程序占用。", "热键注册失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 使用轻量级定时器监控取代WMI事件监控
                InitializeProcessMonitor();

                // 启动内存监控
                SetupMemoryMonitoring();
                LogMessage("内存监控初始化完成");

                // 启动视频文件清理
                SetupVideoFileCleanup();
                LogMessage("视频文件自动清理功能已启动");

                // 不再使用单独的监控线程，依靠Timer定时器处理
                // StartMonitoring(); - 移除此行

                LogMessage("窗体加载事件执行完成");

                // 订阅帧率选择变化事件
                if (cbFrameRate != null)
                {
                    cbFrameRate.SelectedIndexChanged += CbFrameRate_SelectedIndexChanged;
                    LogMessage("已订阅 cbFrameRate.SelectedIndexChanged 事件。");
                }
            }
            catch (Exception ex)
            {
                LogError($"窗体加载事件异常: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"初始化过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理帧率选择变化的事件，提示用户帧率限制
        /// </summary>
        private void CbFrameRate_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbFrameRate.SelectedItem != null)
            {
                if (int.TryParse(cbFrameRate.SelectedItem.ToString(), out int selectedFrameRate))
                {
                    if (selectedFrameRate > 60)
                    {
                        // 使用Invoke确保在UI线程执行MessageBox.Show
                        this.BeginInvoke(new Action(() =>
                        {
                            MessageBox.Show(this,
                                $"您选择了 {selectedFrameRate}fps。请注意，实际录制帧率取决于您的电脑性能以及FFmpeg的设置。\n如果选择的帧率过高，可能会导致录制不稳定或性能下降。\n建议进行短时间录制测试以验证所选帧率。",
                                "帧率提示",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                        }));
                    }
                }
            }
        }

        /// <summary>
        /// 窗体显示事件 - 只执行UI相关操作
        /// </summary>
        private void LOL_Shown(object sender, EventArgs e)
        {
            try
            {
                LogMessage("窗体显示事件开始执行");

                // 显示初始状态通知
                UpdateStatusInfo("监控已启动，等待游戏运行...");

                // 启动UI更新定时器
                // 设置UI更新计时器为低频率更新，降低CPU使用
                timerUI.Interval = 500; // 调整为500毫秒
                timerUI.Start();

                LogMessage("窗体显示事件执行完成");
            }
            catch (Exception ex)
            {
                LogError($"窗体显示事件异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void LOL_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 停止录制
            StopRecording(true);

            // 停止监控
            StopMonitoring();

            // 停止UI更新定时器
            timerUI.Stop();

            // 停止视频文件清理定时器
            if (_videoFileCleanupTimer != null)
            {
                _videoFileCleanupTimer.Stop();
                _videoFileCleanupTimer.Dispose();
                _videoFileCleanupTimer = null;
            }

            // 保存配置
            SaveConfig();

            // 注销F8全局热键
            UnregisterHotKey(this.Handle, HOTKEY_ID_F8);
            LogMessage("F8全局热键已注销。");

            // 取消订阅日志开关事件
            if (this.checkBox2 != null)
            {
                this.checkBox2.CheckedChanged -= CheckBox2_CheckedChanged;
            }

            // 取消订阅帧率选择变化事件
            if (cbFrameRate != null)
            {
                cbFrameRate.SelectedIndexChanged -= CbFrameRate_SelectedIndexChanged;
                LogMessage("已取消订阅 cbFrameRate.SelectedIndexChanged 事件。");
            }
        }

        /// <summary>
        /// 处理Windows消息，用于捕获热键事件
        /// </summary>
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);

            if (m.Msg == WM_HOTKEY)
            {
                if (m.WParam.ToInt32() == HOTKEY_ID_F8)
                {
                    ToggleWindowVisibility();
                }
            }
        }

        /// <summary>
        /// 切换窗口的显示/隐藏状态
        /// </summary>
        private void ToggleWindowVisibility()
        {
            try
            {
                if (this.Visible)
                {
                    _lastWindowLocation = this.Location; // 保存当前位置
                    this.Hide();
                    _isWindowHiddenByHotkey = true;
                    LogMessage("窗口已通过F8隐藏。");
                }
                else
                {
                    // 确保在显示之前设置位置，特别是当窗口从最小化状态恢复时
                    if (_isWindowHiddenByHotkey && _lastWindowLocation != Point.Empty)
                    {
                        this.Location = _lastWindowLocation; // 恢复上次位置
                    }
                    this.Show();
                    this.Activate(); // 激活窗口并置于前台
                    _isWindowHiddenByHotkey = false;
                    LogMessage("窗口已通过F8显示。");
                }

                // 强制处理挂起的消息，防止UI假死
                Application.DoEvents();
            }
            catch (Exception ex)
            {
                LogError($"切换窗口可见性时出错: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 兼容性方法 - 初始化进程监视器
        /// 已弃用WMI方式，改为使用轻量级Timer实现
        /// </summary>
        private void InitializeWatchers()
        {
            try
            {
                LogMessage("InitializeWatchers被调用：为保持兼容性，将转发到InitializeProcessMonitor方法");

                // 直接调用新方法，避免使用WMI
                InitializeProcessMonitor();
            }
            catch (Exception ex)
            {
                LogError($"初始化进程监控失败: {ex.GetType().Name} - {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"初始化进程监控失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region 进程监控

        /// <summary>
        /// 停止所有监控
        /// </summary>
        private void StopMonitoring()
        {
            try
            {
                LogMessage("停止所有监控开始");

                // 停止进程监控定时器
                if (_processMonitorTimer != null)
                {
                    _processMonitorTimer.Stop();
                    _processMonitorTimer.Dispose();
                    _processMonitorTimer = null;
                    LogMessage("进程监控定时器已停止");
                }

                // 停止内存监控
                if (_memoryMonitorTimer != null)
                {
                    _memoryMonitorTimer.Stop();
                    _memoryMonitorTimer.Dispose();
                    _memoryMonitorTimer = null;
                    LogMessage("内存监控定时器已停止");
                }

                // 停止视频文件清理定时器
                if (_videoFileCleanupTimer != null)
                {
                    _videoFileCleanupTimer.Stop();
                    _videoFileCleanupTimer.Dispose();
                    _videoFileCleanupTimer = null;
                    LogMessage("视频文件清理定时器已停止");
                }

                // 清理取消标记
                if (_cancellationTokenSource != null)
                {
                    _cancellationTokenSource.Cancel();
                    _cancellationTokenSource.Dispose();
                    _cancellationTokenSource = null;
                }

                LogMessage("所有监控已停止");
            }
            catch (Exception ex)
            {
                LogError($"停止监控时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// <summary>
        /// 检查指定进程是否正在运行
        /// </summary>
        private bool IsProcessRunning(string processName)
        {
            return Process.GetProcessesByName(processName).Length > 0;
        }

        /// <summary>
        /// 检查任一指定进程是否正在运行
        /// </summary>
        private bool IsAnyProcessRunning(string[] processNames)
        {
            foreach (string name in processNames)
            {
                if (IsProcessRunning(name))
                {
                    return true;
                }
            }
            return false;
        }

        // 不再需要WMI事件处理方法，改为使用定时器

        /// <summary>
        /// 原进程启动处理逻辑 - 为保持兼容性保留
        /// </summary>
        private void ProcessStartedManually()
        {
            LogMessage($"ProcessStartedManually: 检测到游戏相关进程启动（使用旧方法）。");

            // 检查是否是游戏对局进程启动
            if (IsProcessRunning(_gameProcessName))
            {
                // 如果是游戏对局进程，调用新方法处理
                ProcessGameStarted();
            }
            else
            {
                // 如果只是客户端进程启动，更新状态但不触发录制
                _isGameRunning = true;
                UpdateStatusInfo($"{_gameName}客户端已启动");
                UpdateButtonStatus();
            }
        }

        /// <summary>
        /// 原进程停止处理逻辑 - 为保持兼容性保留
        /// </summary>
        private void ProcessStoppedManually()
        {
            LogMessage($"ProcessStoppedManually: 检测到游戏相关进程停止（使用旧方法）。");

            // 检查是否是游戏对局进程结束
            if (!IsProcessRunning(_gameProcessName) && _isRecording)
            {
                // 如果游戏对局进程已结束且正在录制，调用新方法处理
                ProcessGameStopped();
            }
            else
            {
                // 更新游戏运行状态
                _isGameRunning = IsAnyProcessRunning(_processNames);
                UpdateStatusInfo($"{_gameName}客户端已关闭");
                UpdateButtonStatus();
            }
        }

        #endregion

        #region 视频录制

        /// <summary>
        /// 开始录制
        /// </summary>
        private void StartRecording()
        {
            LogMessage("StartRecording: 尝试开始录制...");
            try
            {
                if (_isRecording)
                {
                    LogMessage("StartRecording: 录制已在进行中，跳过。");
                    return;
                }

                ApplySettings();

                // 新增：尝试获取 League of Legends.exe 进程窗口区域
                Rectangle gameWindowRect = Rectangle.Empty;
                int retryCount = 0;
                const int maxRetries = 5; // 最多重试5次
                const int retryDelayMs = 1000; // 每次重试间隔1秒

                while (retryCount < maxRetries)
                {
                    gameWindowRect = GetGameWindowRectImproved(_gameProcessName);
                    if (gameWindowRect != Rectangle.Empty && gameWindowRect.Width > 1 && gameWindowRect.Height > 1)
                    {
                        LogMessage($"第 {retryCount + 1} 次尝试获取游戏窗口成功。");
                        break;
                    }
                    retryCount++;
                    LogMessage($"第 {retryCount} 次尝试获取游戏窗口失败。将在 {retryDelayMs}ms 后重试...");
                    System.Threading.Thread.Sleep(retryDelayMs);
                }

                if (gameWindowRect != Rectangle.Empty && gameWindowRect.Width > 1 && gameWindowRect.Height > 1)
                {
                    LogMessage($"StartRecording: 成功获取到 '{_gameProcessName}' 窗口区域 (来自 GetGameWindowRectImproved): X={gameWindowRect.X}, Y={gameWindowRect.Y}, W={gameWindowRect.Width}, H={gameWindowRect.Height}。将优先使用此区域进行录制。");
                    _selectedRegion = gameWindowRect; // 将游戏窗口区域设置为当前选区
                    UpdateStatusInfo($"录制区域更新为游戏窗口: {_selectedRegion.Width}x{_selectedRegion.Height}"); // 通用状态更新
                    // 特别更新选区标签
                    if (lblSelectedRegion != null && IsHandleCreated && !IsDisposed)
                    {
                        BeginInvoke(new Action(() =>
                        {
                            if (lblSelectedRegion != null && !lblSelectedRegion.IsDisposed) // 再次检查，因为 BeginInvoke 是异步的
                            {
                                lblSelectedRegion.Text = $"选区 (游戏窗口): X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}";
                            }
                        }));
                    }
                }
                else
                {
                    LogMessage($"StartRecording: 未能获取到 '{_gameProcessName}' 的有效窗口区域。当前 _selectedRegion (可能来自用户选择或配置加载): X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}");
                    // 如果 _selectedRegion 已经是用户选择的，则使用它。
                    // 如果 _selectedRegion 是 Empty，则后续会使用默认设置。
                    // 可以在这里根据 _selectedRegion 的状态更新一下提示信息。
                    if (_selectedRegion != Rectangle.Empty && _selectedRegion.Width > 0 && _selectedRegion.Height > 0)
                    {
                        UpdateStatusInfo($"游戏窗口获取失败，将使用已选区域: {_selectedRegion.Width}x{_selectedRegion.Height}");
                        if (lblSelectedRegion != null && IsHandleCreated && !IsDisposed)
                        {
                            BeginInvoke(new Action(() =>
                            {
                                if (lblSelectedRegion != null && !lblSelectedRegion.IsDisposed)
                                {
                                    lblSelectedRegion.Text = $"选区 (用户选择/配置): X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}";
                                }
                            }));
                        }
                    }
                    else // _selectedRegion 为 Empty，即无用户选区，也无游戏窗口
                    {
                        UpdateStatusInfo("游戏窗口获取失败，将使用默认录制区域。");
                        if (lblSelectedRegion != null && IsHandleCreated && !IsDisposed)
                        {
                            BeginInvoke(new Action(() =>
                            {
                                if (lblSelectedRegion != null && !lblSelectedRegion.IsDisposed)
                                {
                                    lblSelectedRegion.Text = "选区: 未选择 (将录制默认区域)";
                                }
                            }));
                        }
                    }
                }

                string dateFolder = Path.Combine(_savePath, DateTime.Now.ToString("yyyyMMdd"));
                if (!Directory.Exists(dateFolder))
                {
                    Directory.CreateDirectory(dateFolder);
                }

                // 获取当前选择的英雄名称 (需要确保 Form1.form1 和 Util 类可用且已初始化)
                string championName = "UnknownHero"; // 默认值
                try
                {
                    // 尝试从 Form1 的 Util 类获取英雄ID和名称
                    // 注意：这里假设 Util.FuwenHeroID 存储了当前选择的英雄ID
                    // 并且 Util.Getheroname 能根据ID返回英文名称
                    if (!string.IsNullOrEmpty(WindowsFormsApp1.Util.FuwenHeroID) && WindowsFormsApp1.Util.FuwenHeroID != "0")
                    {
                        string heroId = WindowsFormsApp1.Util.FuwenHeroID;
                        // 调用 Form1.cs 中的方法获取英雄英文名
                        // 需要确认 Getheroname 是否是静态方法或需要 Form1 实例
                        // 假设 Getheroname 是 Util 类的静态方法
                        string englishName = WindowsFormsApp1.Util.Getheroname(heroId);
                        if (!string.IsNullOrEmpty(englishName) && englishName != "失败")
                        {
                            // 清理文件名，移除或替换非法字符，例如空格替换为下划线
                            championName = System.Text.RegularExpressions.Regex.Replace(englishName, @"[^\w\.-]", "_");
                            LogMessage($"成功获取到英雄名称: {englishName}, 清理后文件名后缀: {championName}");
                        }
                        else
                        {
                            LogMessage($"未能通过ID {heroId} 获取到有效的英雄名称，将使用默认后缀。Getheroname 返回: {englishName}");
                        }
                    }
                    else
                    {
                        LogMessage($"未能获取到有效的英雄ID (Util.FuwenHeroID: {WindowsFormsApp1.Util.FuwenHeroID})，将使用默认后缀。");
                    }
                }
                catch (Exception ex)
                {
                    LogError($"获取英雄名称时发生异常: {ex.Message}");
                    // 异常情况下使用默认值
                }

                // 使用获取到的英雄名称作为文件后缀的一部分
                string fileName = $"{_fileNamePrefix}_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}_{championName}.mp4"; // 修改后的代码
                string filePath = Path.Combine(dateFolder, fileName);

                int captureWidth; // 将用于FFmpeg的逻辑宽度
                int captureHeight; // 将用于FFmpeg的逻辑高度
                int offsetX; // 将用于FFmpeg的逻辑X偏移
                int offsetY; // 将用于FFmpeg的逻辑Y偏移

                // 获取主屏幕DPI缩放因子 (统一获取)
                float dpiScaleX = 1.0f;
                float dpiScaleY = 1.0f;
                try
                {
                    using (Graphics g = this.CreateGraphics()) // 或者使用特定屏幕的Graphics对象
                    {
                        dpiScaleX = g.DpiX / 96.0f;
                        dpiScaleY = g.DpiY / 96.0f;
                        LogMessage($"StartRecording: 获取到屏幕DPI缩放 (基于this.CreateGraphics): X={dpiScaleX:F2}, Y={dpiScaleY:F2}, DpiX={g.DpiX}, DpiY={g.DpiY}");
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"获取DPI缩放信息失败: {ex.Message}，将使用默认值1.0");
                    // 保留 dpiScaleX 和 dpiScaleY 为 1.0f
                }
                // 确保缩放因子有效，防止除以零
                if (dpiScaleX <= 0) dpiScaleX = 1.0f;
                if (dpiScaleY <= 0) dpiScaleY = 1.0f;


                if (_selectedRegion != Rectangle.Empty && _selectedRegion.Width > 0 && _selectedRegion.Height > 0)
                {
                    // _selectedRegion 现在是由 RegionSelectorForm 返回的屏幕逻辑坐标矩形
                    // 获取所有屏幕的有效边界
                    Rectangle allScreensBounds = GetAllScreensBounds();
                    LogMessage($"StartRecording: 所有显示器的总边界: X={allScreensBounds.X}, Y={allScreensBounds.Y}, Width={allScreensBounds.Width}, Height={allScreensBounds.Height}");

                    // 记录原始选择的区域
                    Rectangle originalRegion = _selectedRegion;
                    LogMessage($"StartRecording: 接收到选定区域 (屏幕逻辑像素): X={_selectedRegion.X}, Y={_selectedRegion.Y}, Width={_selectedRegion.Width}, Height={_selectedRegion.Height}");

                    // 检查并调整区域，确保在屏幕范围内
                    _selectedRegion = EnsureRectangleWithinBounds(_selectedRegion, allScreensBounds);

                    // 如果区域被调整，记录日志并通知用户
                    if (!originalRegion.Equals(_selectedRegion))
                    {
                        LogMessage($"StartRecording: 选定区域超出屏幕范围，已自动调整为: X={_selectedRegion.X}, Y={_selectedRegion.Y}, Width={_selectedRegion.Width}, Height={_selectedRegion.Height}");
                        UpdateStatusInfo($"录制区域已自动调整以确保在屏幕范围内");
                    }

                    offsetX = _selectedRegion.X;
                    offsetY = _selectedRegion.Y;
                    captureWidth = _selectedRegion.Width;
                    captureHeight = _selectedRegion.Height;

                    // 不再需要从物理像素转换回逻辑像素
                    LogMessage($"StartRecording: 最终使用的屏幕逻辑像素: X={offsetX}, Y={offsetY}, Width={captureWidth}, Height={captureHeight}");
                }
                else
                {
                    // 默认录制逻辑：目标是录制一个固定物理尺寸的区域，居中显示，需要转换为逻辑像素给FFmpeg
                    int targetPhysicalWidth = 2560;  // 目标录制物理宽度 (例如2K)
                    int targetPhysicalHeight = 1440; // 目标录制物理高度 (例如2K)
                    LogMessage($"StartRecording: 目标录制物理分辨率: {targetPhysicalWidth}x{targetPhysicalHeight}");

                    Rectangle screenBounds = Screen.PrimaryScreen.Bounds; // 这是主屏幕的逻辑尺寸
                    int screenLogicalWidth = screenBounds.Width;
                    int screenLogicalHeight = screenBounds.Height;
                    LogMessage($"StartRecording: 主屏幕逻辑分辨率: {screenLogicalWidth}x{screenLogicalHeight}");

                    // 计算目标录制区域的逻辑尺寸
                    int targetLogicalWidth = (int)Math.Round(targetPhysicalWidth / dpiScaleX);
                    int targetLogicalHeight = (int)Math.Round(targetPhysicalHeight / dpiScaleY);
                    LogMessage($"StartRecording: 目标录制逻辑分辨率 (用于FFmpeg video_size): {targetLogicalWidth}x{targetLogicalHeight}");

                    // 计算捕获区域的逻辑偏移量，使其在屏幕中央 (如果目标逻辑尺寸小于屏幕逻辑尺寸)
                    if (screenLogicalWidth >= targetLogicalWidth)
                    {
                        offsetX = (screenLogicalWidth - targetLogicalWidth) / 2;
                        captureWidth = targetLogicalWidth;
                    }
                    else
                    {
                        offsetX = 0; // 从屏幕边缘开始捕获
                        captureWidth = screenLogicalWidth; // 捕获整个屏幕的逻辑宽度
                        LogMessage($"警告: 屏幕逻辑宽度 ({screenLogicalWidth}px) 小于目标逻辑录制宽度 ({targetLogicalWidth}px)。实际录制逻辑宽度将调整为 {captureWidth}px。");
                    }

                    if (screenLogicalHeight >= targetLogicalHeight)
                    {
                        offsetY = (screenLogicalHeight - targetLogicalHeight) / 2;
                        captureHeight = targetLogicalHeight;
                    }
                    else
                    {
                        offsetY = 0; // 从屏幕边缘开始捕获
                        captureHeight = screenLogicalHeight; // 捕获整个屏幕的逻辑高度
                        LogMessage($"警告: 屏幕逻辑高度 ({screenLogicalHeight}px) 小于目标逻辑录制高度 ({targetLogicalHeight}px)。实际录制逻辑高度将调整为 {captureHeight}px。");
                    }
                    LogMessage($"StartRecording: 最终计算出的录制区域参数(逻辑像素, 用于gdigrab) - X={offsetX}, Y={offsetY}, Width={captureWidth}, Height={captureHeight}");
                }

                // 确保传递给FFmpeg的宽高为偶数 (在逻辑像素上调整)
                // VideoRecorder 内部也会做此处理，但在此处预先调整并记录更清晰
                if (captureWidth % 2 != 0) captureWidth = Math.Max(0, captureWidth - 1); // 调整为偶数，优先减小
                if (captureHeight % 2 != 0) captureHeight = Math.Max(0, captureHeight - 1); // 调整为偶数，优先减小

                // 确保调整后的宽高不小于FFmpeg通常要求的最小尺寸 (例如 2x2)
                captureWidth = Math.Max(2, captureWidth);
                captureHeight = Math.Max(2, captureHeight);

                if (captureWidth <= 1 || captureHeight <= 1) // 严格来说应为 < 2
                {
                    UpdateStatusInfo($"计算得到的录制区域无效 ({captureWidth}x{captureHeight})，录制启动失败。");
                    LogError($"StartRecording: 计算得到的录制区域无效 ({captureWidth}x{captureHeight})，录制启动失败。");
                    return;
                }

                LogMessage($"StartRecording: 调整后最终用于FFmpeg的逻辑坐标和尺寸: X={offsetX}, Y={offsetY}, Width={captureWidth}, Height={captureHeight}");

                string captureTitle = "DesktopScreenRecord";
                LogMessage($"StartRecording: 最终计算并准备传递给 VideoRecorder 的参数(逻辑像素): X={offsetX}, Y={offsetY}, Width={captureWidth}, Height={captureHeight}");
                LogMessage($"StartRecording: 即将创建 VideoRecorder 实例，参数 - filePath: {filePath}, frameRate: {_frameRate}, quality: {_quality}, captureWidth: {captureWidth}, captureHeight: {captureHeight}, offsetX: {offsetX}, offsetY: {offsetY}");
                _recorder = new VideoRecorder(filePath, _frameRate, _quality,
                    captureWidth, captureHeight, // 这些现在都是逻辑像素
                    offsetX, offsetY,           // 这些现在都是逻辑像素
                                                // 根据 checkBox1 的状态决定是否显示边框
                    captureTitle, checkBox1.Checked,
                    _useHEVC, _compressionPreset,
                    _detectedGpuType, _enableGpuAcceleration, _screenCaptureMethod); // 传递GPU相关参数

                _recorder.RecordingError += Recorder_RecordingError;
                _recorder.RecordingOutput += Recorder_RecordingOutput;
                _recorder.LogMessageEvent += Recorder_LogMessageEvent;

                _recorder.Start();

                _isRecording = true;
                _isPaused = false;
                _recordStartTime = DateTime.Now;
                _recordingCount++;

                UpdateStatusInfo($"录制开始: {fileName}");
                LogMessage($"StartRecording: 录制已成功启动到文件: {filePath}");

                // 开始录制后隐藏窗口
                if (this.Visible)
                {
                    _lastWindowLocation = this.Location; // 保存位置，以便F8可以恢复
                    this.Hide();
                    _isWindowHiddenByHotkey = true; // 标记为由程序逻辑隐藏，F8可以正常切换
                    LogMessage("录制开始，窗口已自动隐藏。");
                }
            }
            catch (Exception ex)
            {
                LogError($"开始录制失败: {ex.Message}\n{ex.StackTrace}"); // 添加堆栈跟踪
                MessageBox.Show($"开始录制失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                UpdateButtonStatus();
            }
        }

        /// <summary>
        /// 暂停录制
        /// </summary>
        private void PauseRecording()
        {
            if (!_isRecording || _isPaused) return;

            try
            {
                _recorder?.Pause();
                _isPaused = true;
                UpdateStatusInfo("录制已暂停");
            }
            catch (Exception ex)
            {
                LogError($"暂停录制失败: {ex.Message}");
            }
            finally
            {
                UpdateButtonStatus();
            }
        }

        /// <summary>
        /// 恢复录制
        /// </summary>
        private void ResumeRecording()
        {
            if (!_isRecording || !_isPaused) return;

            try
            {
                _recorder?.Resume();
                _isPaused = false;
                UpdateStatusInfo("录制已恢复");
            }
            catch (Exception ex)
            {
                LogError($"恢复录制失败: {ex.Message}");
            }
            finally
            {
                UpdateButtonStatus();
            }
        }

        /// <summary>
        /// 停止录制
        /// </summary>
        /// <param name="forceStop">是否强制停止</param>
        private void StopRecording(bool forceStop)
        {
            LogMessage($"StopRecording: 尝试停止录制 (强制停止: {forceStop})...");
            if (!_isRecording)
            {
                LogMessage("StopRecording: 当前未在录制，跳过停止操作。");
                return;
            }

            bool stopSuccess = false;
            try
            {
                TimeSpan duration = DateTime.Now - _recordStartTime;
                if (_recorder != null)
                {
                    try
                    {
                        _recorder.RecordingError -= Recorder_RecordingError;
                        _recorder.RecordingOutput -= Recorder_RecordingOutput;
                        _recorder.Stop();
                        _recorder.Dispose();
                        stopSuccess = true;
                    }
                    catch (Exception ex)
                    {
                        LogError($"StopRecording: VideoRecorder 停止/释放异常: {ex.Message}");
                    }
                    finally
                    {
                        _recorder = null;
                    }
                }
                else
                {
                    LogMessage("StopRecording: _recorder为null，无需释放。");
                }

                string stopReason = forceStop ? "手动停止" : "游戏结束";
                UpdateStatusInfo($"录制已停止 ({stopReason})，时长: {duration.ToString(@"hh\:mm\:ss")}");
                LogMessage($"StopRecording: 录制已停止 ({stopReason})，时长: {duration.ToString(@"hh\:mm\:ss")}");
            }
            catch (Exception ex)
            {
                LogError($"停止录制失败: {ex.Message}");
            }
            finally
            {
                _isRecording = false;
                _isPaused = false;
                try
                {
                    UpdateButtonStatus();
                }
                catch (Exception ex)
                {
                    LogError($"StopRecording: UpdateButtonStatus异常: {ex.Message}");
                }
            }
        }

        #endregion

        #region 内存管理

        // 增加内存监控相关字段
        private Queue<long> _memoryHistory = new Queue<long>(10); // 存储最近10次内存采样
        private DateTime _lastMemoryOptimization = DateTime.MinValue; // 上次内存优化时间
        private int _criticalMemoryCount = 0; // 记录连续高内存状态的次数
        private const long MEMORY_THRESHOLD_MEDIUM = 350 * 1024 * 1024; // 350MB
        private const long MEMORY_THRESHOLD_HIGH = 500 * 1024 * 1024;   // 500MB
        private const long MEMORY_THRESHOLD_CRITICAL = 700 * 1024 * 1024; // 700MB

        /// <summary>
        /// 内存监控定时事件 - 高效优化版
        /// </summary>
        private void MemoryMonitor_Tick(object sender, EventArgs e)
        {
            // 录制未开始时完全跳过监控
            if (!_isRecording) return;

            try
            {
                // 使用更轻量级的方式获取内存信息，不强制GC
                // 注意：这种方法获取的内存信息可能不如GC.GetTotalMemory准确
                // 但足够用于监控趋势，且不会触发额外的GC
                long currentMemoryUsage = Process.GetCurrentProcess().PrivateMemorySize64;

                // 将当前内存使用量添加到历史记录，使用固定长度数组避免不必要的内存分配
                if (_memoryHistory.Count >= 10)
                    _memoryHistory.Dequeue();
                _memoryHistory.Enqueue(currentMemoryUsage);

                // 只有当内存使用较高时才执行详细分析，减少CPU开销
                MemoryUsageLevel currentLevel = GetMemoryUsageLevel(currentMemoryUsage);
                if (currentLevel >= MemoryUsageLevel.Medium)
                {
                    // 检查内存使用趋势，仅在内存级别中等或更高时执行
                bool isMemoryTrendIncreasing = IsMemoryTrendIncreasing();

                // 日志记录当前内存状态(仅在较高级别或明显变化时)
                if (currentLevel >= MemoryUsageLevel.High ||
                        (_lastMemoryUsage > 0 && Math.Abs(currentMemoryUsage - _lastMemoryUsage) > 100 * 1024 * 1024))
                {
                    LogMessage($"内存监控: 当前使用 {FormatBytes(currentMemoryUsage)}, " +
                              $"趋势 {(isMemoryTrendIncreasing ? "上升" : "稳定或下降")}, " +
                              $"级别 {currentLevel}");
                }

                // 根据内存使用情况和趋势决定是否需要优化
                bool shouldOptimize = false;
                bool shouldForceRestart = false;

                    // 简化决策逻辑，减少条件分支
                if (currentLevel == MemoryUsageLevel.Critical)
                {
                    _criticalMemoryCount++;
                    LogError($"检测到内存使用量处于危险级别: {FormatBytes(currentMemoryUsage)}");

                    if (_criticalMemoryCount >= 2) // 连续两次危险级别，强制重启录制
                    {
                        shouldForceRestart = true;
                        _criticalMemoryCount = 0;
                    }
                    else
                    {
                        shouldOptimize = true;
                    }
                }
                    else if ((currentLevel == MemoryUsageLevel.High && isMemoryTrendIncreasing) ||
                             (currentLevel == MemoryUsageLevel.Medium &&
                              DateTime.Now - _lastMemoryOptimization > TimeSpan.FromMinutes(3))) // 扩大优化间隔
                    {
                    shouldOptimize = true;
                    _criticalMemoryCount = 0;
                }
                else
                {
                    // 内存使用正常或已经最近优化过
                    _criticalMemoryCount = 0;
                }

                // 执行相应的内存优化策略
                if (shouldForceRestart)
                {
                    LogError($"内存使用量持续处于危险级别，执行录制重启");
                    RestartRecording();
                    _lastMemoryOptimization = DateTime.Now;
                }
                else if (shouldOptimize)
                {
                    OptimizeMemory(currentLevel);
                    _lastMemoryOptimization = DateTime.Now;
                    }
                }

                // 更新上次内存使用量
                _lastMemoryUsage = currentMemoryUsage;
            }
            catch (Exception ex)
            {
                LogError($"内存监控异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据内存使用量确定内存使用级别
        /// </summary>
        private enum MemoryUsageLevel
        {
            Normal,  // 正常
            Medium,  // 中等
            High,    // 高
            Critical // 危险
        }

        /// <summary>
        /// 获取当前内存使用级别
        /// </summary>
        private MemoryUsageLevel GetMemoryUsageLevel(long memoryUsage)
        {
            if (memoryUsage >= MEMORY_THRESHOLD_CRITICAL)
                return MemoryUsageLevel.Critical;
            if (memoryUsage >= MEMORY_THRESHOLD_HIGH)
                return MemoryUsageLevel.High;
            if (memoryUsage >= MEMORY_THRESHOLD_MEDIUM)
                return MemoryUsageLevel.Medium;
            return MemoryUsageLevel.Normal;
        }

        /// <summary>
        /// 分析内存使用趋势是否上升
        /// </summary>
        private bool IsMemoryTrendIncreasing()
        {
            if (_memoryHistory.Count < 3) return false;

            // 获取最新的几次内存采样
            long[] recent = _memoryHistory.ToArray();
            int count = recent.Length;

            // 计算线性回归的斜率来判断趋势
            double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
            for (int i = 0; i < count; i++)
            {
                sumX += i;
                sumY += recent[i];
                sumXY += i * recent[i];
                sumX2 += i * i;
            }

            double slope = (count * sumXY - sumX * sumY) / (count * sumX2 - sumX * sumX);

            // 斜率大于某个阈值，判定为上升趋势
            return slope > 5 * 1024 * 1024; // 每个时间单位增加5MB以上视为明显上升
        }

        /// <summary>
        /// 内存优化 - 针对不同内存级别采用不同策略
        /// </summary>
        private void OptimizeMemory(MemoryUsageLevel level)
        {
            try
            {
                if (!_isRecording) return;

                LogMessage($"执行内存优化，当前级别: {level}");

                // 基本优化 - 鼓励垃圾回收
                // GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced, true, true); // 强制GC代价较高，暂时注释以观察性能影响

                // 根据内存级别采取不同措施
                switch (level)
                {
                    case MemoryUsageLevel.Critical:
                        // 危险级别 - 考虑重启录制
                        _memoryStableCount++;
                        if (_memoryStableCount >= 2)
                        {
                            _memoryStableCount = 0;
                            LogError("内存使用达到危险级别，准备重启录制");
                            RestartRecording();
                        }
                        break;

                    case MemoryUsageLevel.High:
                        // 高级别 - 执行更积极的优化
                        LogMessage("内存使用较高，执行增强型优化");
                        // 可以在这里添加对大型缓存的清理
                        // 例如清理日志缓存、临时缓冲区等
                        break;

                    case MemoryUsageLevel.Medium:
                        // 中等级别 - 执行一般优化
                        LogMessage("内存使用中等，执行标准优化");
                        break;

                    default:
                        // 正常级别 - 无需额外操作
                        break;
                }

                // 记录优化后的内存使用情况
                long afterOptimizeMemory = GC.GetTotalMemory(true);
                LogMessage($"内存优化完成，优化前: {FormatBytes(_lastMemoryUsage)}, 优化后: {FormatBytes(afterOptimizeMemory)}");
            }
            catch (Exception ex)
            {
                LogError($"内存优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内存优化
        /// </summary>
        private void OptimizeMemory()
        {
            try
            {
                if (!_isRecording) return;

                // 如果内存问题严重且持续，考虑重启录制
                // 移除手动GC调用，依赖.NET自动垃圾回收
                _memoryStableCount++;

                if (_memoryStableCount >= 3) // 连续3次内存问题
                {
                    _memoryStableCount = 0;

                    // 重启录制
                    RestartRecording();
                }
            }
            catch (Exception ex)
            {
                LogError($"内存优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重启录制（在内存问题时）
        /// </summary>
        private void RestartRecording()
        {
            try
            {
                if (!_isRecording) return;

                UpdateStatusInfo("检测到内存问题，正在重启录制...");
                LogError("检测到内存问题，正在重启录制...");

                // 先停止当前录制
                StopRecording(false);

                // 短暂延迟后重新开始录制
                Thread.Sleep(1000);

                // 重新开始录制
                StartRecording();
            }
            catch (Exception ex)
            {
                LogError($"重启录制失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化字节显示
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffix = { "B", "KB", "MB", "GB", "TB" };
            int i;
            double dblSByte = bytes;

            for (i = 0; i < suffix.Length && bytes >= 1024; i++, bytes /= 1024)
            {
                dblSByte = bytes / 1024.0;
            }

            return String.Format("{0:0.##} {1}", dblSByte, suffix[i]);
        }

        #endregion



        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo(string message)
        {
            // 记录到日志
            LogMessage(message);

            // 更新状态标签和界面
            this.BeginInvoke(new Action(() =>
            {
                lblStatus.Text = message;
                statusLabel.Text = message;

                // 更新窗体标题
                this.Text = $"LOL自动录制 - {message} (F8键隐藏/显示窗口)";

                // 添加到日志列表
                AddToLogList(message);
            }));
        }

        /// <summary>
        /// 添加消息到日志列表
        /// </summary>
        private void AddToLogList(string message)
        {
            // 如果日志未启用，则不添加到UI列表
            if (!_isLoggingEnabled && !message.StartsWith("[Recorder] FFmpeg Error:")) // 允许FFmpeg错误消息总是显示在UI
            {
                 // 如果需要，可以针对特定类型的消息（如错误）即使日志关闭也显示在UI
                if (message.ToUpper().Contains("ERROR") || message.ToUpper().Contains("失败"))
                {
                    // 对于错误信息，即使日志关闭也显示在UI列表
                }
                else
                {
                    return;
                }
            }

            try
            {
                // 确保在UI线程中操作
                // 确保在UI线程中操作，并且控件句柄已创建且未被释放
                if (logListView.InvokeRequired && logListView.IsHandleCreated && !logListView.IsDisposed)
                {
                    logListView.BeginInvoke(new Action(() => AddToLogList(message)));
                    return;
                }

                ListViewItem item = new ListViewItem(DateTime.Now.ToString("HH:mm:ss"));
                item.SubItems.Add(message);
                logListView.Items.Insert(0, item);

                // 限制日志数量
                const int maxLogEntries = 100;
                while (logListView.Items.Count > maxLogEntries)
                {
                    logListView.Items.RemoveAt(logListView.Items.Count - 1);
                }
            }
            catch (Exception ex)
            {
                // 此处的LogError也会被_isLoggingEnabled控制，但错误日志本身比较重要
                // 可以考虑直接写入文件或使用一个不受开关影响的紧急日志通道
                Console.WriteLine($"紧急日志 - 添加日志到UI列表失败: {ex.Message}"); // 作为备选输出
                // LogError($"添加日志失败: {ex.Message}"); // 这行会受开关影响
            }
        }

        // 日志缓冲区和日志级别枚举
        private enum LogLevel { Debug, Info, Error }
        private static readonly Dictionary<string, List<string>> _logBuffers = new Dictionary<string, List<string>>();
        private static readonly object _logLock = new object();
        // 明确指定为 System.Threading.Timer，用于非UI线程的日志刷新
        private static System.Threading.Timer _logFlushTimer;
        private static LogLevel _currentLogLevel = LogLevel.Info; // 默认日志级别
        private const int LOG_BUFFER_SIZE = 20; // 缓冲区大小，超过此数量将写入文件
        private const int LOG_FLUSH_INTERVAL = 5000; // 日志刷新时间间隔(毫秒)

        /// <summary>
        /// 初始化日志系统
        /// </summary>
        private void InitializeLogger()
        {
            // 创建日志刷新计时器，定期将日志写入文件
            if (_logFlushTimer == null)
            {
                // 使用 System.Threading.Timer，它在线程池线程上执行回调
                _logFlushTimer = new System.Threading.Timer(FlushAllLogs, null, LOG_FLUSH_INTERVAL, LOG_FLUSH_INTERVAL);
            }
        }

        /// <summary>
        /// 记录日志消息
        /// </summary>
        private void LogMessage(string message, LogLevel level = LogLevel.Info)
        {
            // 检查日志总开关
            if (!_isLoggingEnabled)
            {
                // 对于错误级别的日志，即使总开关关闭，也考虑记录到文件，但不在UI显示（除非AddToLogList有特殊处理）
                if (level == LogLevel.Error)
                {
                    // 这里可以决定是否要绕过 _isLoggingEnabled 直接写入错误日志文件
                    // 为了保持一致性，当前实现是完全关闭，但这是一个可调整点
                }
                else
                {
                    return; // 非错误日志，在开关关闭时直接返回
                }
                // 如果决定错误日志总是要记录到文件，则移除下面的 return，并调整后续逻辑
                if (level != LogLevel.Error) return; // 如果不是错误，且开关关闭，则不记录
            }

            // 如果日志级别低于当前设置级别，则不记录
            if (level < _currentLogLevel)
                return;

            try
            {
                string fileName = level == LogLevel.Error ?
                    $"Error_{DateTime.Now.ToString("yyyy-MM-dd")}.txt" :
                    $"Log_{DateTime.Now.ToString("yyyy-MM-dd")}.txt";
                string logEntry = $"[{DateTime.Now.ToString("HH:mm:ss")}]{(level == LogLevel.Error ? " ERROR: " : " ")}{message}";

                // 添加到缓冲区
                lock (_logLock)
                {
                    if (!_logBuffers.ContainsKey(fileName))
                        _logBuffers[fileName] = new List<string>();

                    _logBuffers[fileName].Add(logEntry);

                    // 如果缓冲区大小超过阈值，立即刷新该日志文件
                    if (_logBuffers[fileName].Count >= LOG_BUFFER_SIZE)
                        FlushLogFile(fileName);
                }
            }
            catch
            {
                // 日志记录失败时静默处理
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string error)
        {
            LogMessage(error, LogLevel.Error);
        }

        /// <summary>
        /// 刷新所有日志文件
        /// </summary>
        private void FlushAllLogs(object state)
        {
            lock (_logLock)
            {
                foreach (var fileName in _logBuffers.Keys.ToList())
                {
                    FlushLogFile(fileName);
                }
            }
        }

        /// <summary>
        /// 刷新单个日志文件
        /// </summary>
        private void FlushLogFile(string fileName)
        {
            if (!_logBuffers.ContainsKey(fileName) || _logBuffers[fileName].Count == 0)
                return;

            try
            {
                string logFolder = Path.Combine(_savePath, "logs");
                if (!Directory.Exists(logFolder))
                {
                    Directory.CreateDirectory(logFolder);
                }

                string logFile = Path.Combine(logFolder, fileName);
                string content = string.Join(Environment.NewLine, _logBuffers[fileName]) + Environment.NewLine;

                // 批量写入文件
                File.AppendAllText(logFile, content);

                // 清空缓冲区
                _logBuffers[fileName].Clear();
            }
            catch
            {
                // 日志写入失败时静默处理
            }
        }

        #region UI事件处理

        /// <summary>
        /// 浏览按钮点击事件
        /// </summary>
        private void btnBrowse_Click(object sender, EventArgs e)
        {
            try
            {
                // 设置初始目录为当前保存路径
                folderBrowserDialog.SelectedPath = _savePath;

                // 显示文件夹选择对话框
                if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
                {
                    _savePath = folderBrowserDialog.SelectedPath;
                    txtSavePath.Text = _savePath;

                    // 确保目录存在
                    if (!Directory.Exists(_savePath))
                    {
                        Directory.CreateDirectory(_savePath);
                    }

                    UpdateStatusInfo($"保存路径已更改为: {_savePath}");
                }
            }
            catch (Exception ex)
            {
                LogError($"选择保存路径失败: {ex.Message}");
                MessageBox.Show($"选择保存路径失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 开始录制按钮点击事件
        /// </summary>
        private void btnStart_Click(object sender, EventArgs e)
        {
            try
            {
                if (_isRecording)
                {
                    MessageBox.Show("录制已在进行中", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 应用设置
                ApplySettings();

                // 开始录制
                StartRecording();

                // 更新按钮状态 (StartRecording 内部已调用)
                // UpdateButtonStatus();
            }
            catch (Exception ex)
            {
                LogError($"手动开始录制失败: {ex.Message}");
                MessageBox.Show($"开始录制失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 暂停/恢复按钮点击事件
        /// </summary>
        private void btnPauseResume_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_isRecording)
                {
                    return;
                }

                if (_isPaused)
                {
                    // 恢复录制
                    ResumeRecording();
                    // btnPauseResume.Text = "暂停录制"; // ResumeRecording 内部已调用 UpdateStatusInfo，其中会更新按钮状态
                }
                else
                {
                    // 暂停录制
                    PauseRecording();
                    // btnPauseResume.Text = "恢复录制"; // PauseRecording 内部已调用 UpdateStatusInfo，其中会更新按钮状态
                }

                // 更新按钮状态 (PauseRecording/ResumeRecording 内部已调用)
                // UpdateButtonStatus();
            }
            catch (Exception ex)
            {
                LogError($"暂停/恢复录制失败: {ex.Message}");
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private void btnStop_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_isRecording)
                {
                    return;
                }

                // 停止录制
                StopRecording(true);

                // 更新按钮状态 (StopRecording 内部已调用)
                // UpdateButtonStatus();
            }
            catch (Exception ex)
            {
                LogError($"停止录制失败: {ex.Message}");
            }
            finally
            {
                UpdateButtonStatus();
            }
        }

        // 添加上次UI更新时间，用于减少不必要的更新
        private DateTime _lastUiUpdateTime = DateTime.MinValue;
        private string _lastDurationText = string.Empty;
        private string _lastMemoryText = string.Empty;
        private int _lastMemoryBarValue = 0;
        private bool _lastGameRunningStatus = false;

        /// <summary>
        /// UI更新定时器事件 - 优化版本
        /// </summary>
        private void timerUI_Tick(object sender, EventArgs e)
        {
            try
            {
                DateTime now = DateTime.Now;
                bool forceUpdate = (now - _lastUiUpdateTime).TotalSeconds >= 1.0; // 最少1秒才强制更新
                bool needUpdate = false;

                // 更新录制时长
                if (_isRecording)
                {
                    TimeSpan duration = now - _recordStartTime;
                    string newDurationText = $"时长: {duration.ToString(@"hh\:mm\:ss")}";

                    if (newDurationText != _lastDurationText || forceUpdate)
                    {
                        lblDuration.Text = newDurationText;
                        _lastDurationText = newDurationText;
                        needUpdate = true;
                    }

                    // 更新内存使用情况 - 使用Process.PrivateMemorySize64而非GC.GetTotalMemory
                    // 这样可以避免触发额外的垃圾回收
                    long currentMemoryUsage = Process.GetCurrentProcess().PrivateMemorySize64;
                    string newMemoryText = $"内存: {FormatBytes(currentMemoryUsage)}";

                    if (newMemoryText != _lastMemoryText || forceUpdate)
                    {
                        lblMemoryUsage.Text = newMemoryText;
                        _lastMemoryText = newMemoryText;
                        needUpdate = true;
                    }

                    // 更新内存进度条 - 只在值变化时更新
                    const long memoryThresholdMB = 1000; // 1GB
                    int newValue = (int)Math.Min(currentMemoryUsage / (1024 * 1024), memoryThresholdMB);

                    if (newValue != _lastMemoryBarValue || forceUpdate)
                    {
                    memoryProgressBar.Maximum = (int)memoryThresholdMB;
                        memoryProgressBar.Value = newValue;
                        _lastMemoryBarValue = newValue;
                        needUpdate = true;
                }
                }
                else if (_lastDurationText != "时长: 00:00:00" ||
                        _lastMemoryText != "内存: 0 MB" ||
                        _lastMemoryBarValue != 0)
                {
                    // 非录制状态，仅在需要重置时更新UI
                    lblDuration.Text = "时长: 00:00:00";
                    lblMemoryUsage.Text = "内存: 0 MB";
                    memoryProgressBar.Value = 0;

                    _lastDurationText = "时长: 00:00:00";
                    _lastMemoryText = "内存: 0 MB";
                    _lastMemoryBarValue = 0;
                    needUpdate = true;
                }

                // 更新游戏状态文本 - 只在状态变化时更新
                if (_isGameRunning != _lastGameRunningStatus || forceUpdate)
                {
                    statusLabel.Text = _isGameRunning ?
                        $"{_gameName}正在运行" :
                        $"{_gameName}未运行";
                    _lastGameRunningStatus = _isGameRunning;
                    needUpdate = true;
                }

                // 记录更新时间
                if (needUpdate)
                {
                    _lastUiUpdateTime = now;
                }
            }
            catch (Exception ex)
            {
                LogError($"UI更新异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStatus()
        {
            try
            {
                if (!this.IsHandleCreated || this.IsDisposed)
                {
                    LogMessage("控件句柄尚未创建或已被释放，跳过按钮状态更新。");
                    return;
                }

                if (this.InvokeRequired && !this.IsDisposed)
                {
                    this.BeginInvoke(new Action(UpdateButtonStatus));
                    return;
                }

                if (btnStart == null || btnPauseResume == null || btnStop == null || btnSelectRegion == null || lblSelectedRegion == null)
                {
                    LogMessage("部分按钮或标签控件实例无效，跳过更新。");
                    return;
                }

                btnStart.Enabled = !_isRecording;
                btnPauseResume.Enabled = _isRecording;
                btnStop.Enabled = _isRecording;
                btnSelectRegion.Enabled = !_isRecording;

                btnPauseResume.Text = _isPaused ? "恢复录制" : "暂停录制";

                if (_selectedRegion != Rectangle.Empty && _selectedRegion.Width > 0 && _selectedRegion.Height > 0)
                {
                    lblSelectedRegion.Text = $"选区: X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}";
                }
                else
                {
                    lblSelectedRegion.Text = "选区: 未选择 (将录制全屏或默认区域)";
                }
            }
            catch (Exception ex)
            {
                LogError($"更新按钮状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用设置
        /// </summary>
        private void ApplySettings()
        {
            // 保存路径
            if (!string.IsNullOrEmpty(txtSavePath.Text))
            {
                _savePath = txtSavePath.Text;
                // 确保目录存在
                if (!Directory.Exists(_savePath))
                {
                    Directory.CreateDirectory(_savePath);
                }
            }

            // 文件名前缀
            if (!string.IsNullOrEmpty(txtPrefix.Text))
            {
                _fileNamePrefix = txtPrefix.Text;
            }

            // 帧率
            if (cbFrameRate.SelectedItem != null)
            {
                if (int.TryParse(cbFrameRate.SelectedItem.ToString(), out int selectedFrameRate))
                {
                    // 移除了对 selectedFrameRate > 30 的判断和 _frameRate = 30 的强制设置
                    // 直接使用用户选择的帧率
                    _frameRate = selectedFrameRate;
                    LogMessage($"用户尝试设置帧率为 {_frameRate}fps。实际录制帧率将取决于 screen-capture-recorder 和 FFmpeg 的能力。如果过高可能导致问题。");
                }
            }

            // 画质
            if (cbQuality.SelectedIndex >= 0)
            {
                switch (cbQuality.SelectedItem.ToString()) // 使用SelectedItem.ToString()更安全
                {
                    case "1 原画":
                        _quality = 100; // 代表原画质量
                        break;
                    case "2 高清":
                        _quality = 85; // 调整高清的内部值
                        break;
                    case "3 标清":
                        _quality = 60; // 调整标清的内部值
                        break;
                    case "4 流畅":
                        _quality = 40; // 调整流畅的内部值
                        break;
                    default:
                        _quality = 100; // 默认原画
                        break;
                }
            }
            else // 如果没有选中项，默认设置为原画
            {
                _quality = 100;
            }

            // 自动开始录制设置
            _autoStarted = cbAutoStart.Checked;

            // 应用HEVC编码选项
            if (cbUseHEVC != null)
            {
                _useHEVC = cbUseHEVC.Checked;
            }

            // 应用压缩预设选项
            if (cbCompressionPreset != null && cbCompressionPreset.SelectedItem != null)
            {
                _compressionPreset = cbCompressionPreset.SelectedItem.ToString();
            }

            // 分辨率设置 (假设有控件用于设置分辨率，这里使用默认值)
            // 如果有分辨率设置控件，需要在这里读取并更新 _resolution
            // 例如：_resolution = new Size(int.Parse(txtWidth.Text), int.Parse(txtHeight.Text));
        }

        /// <summary>
        /// 处理 VideoRecorder 报告的错误
        /// </summary>
        private void Recorder_RecordingError(object sender, string errorMessage)
        {
            LogError($"录制器错误: {errorMessage}");
            // 可以在这里更新UI，例如显示错误消息框
            this.BeginInvoke(new Action(() =>
            {
                MessageBox.Show($"录制过程中发生错误: {errorMessage}", "录制错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }));
        }

        /// <summary>
        /// 处理 VideoRecorder 报告的输出信息 (例如 FFmpeg 进度)
        /// </summary>
        private void Recorder_RecordingOutput(object sender, string outputMessage)
        {
            // 可以选择将 FFmpeg 的输出信息添加到日志列表或状态栏
            // AddToLogList($"FFmpeg Output: {outputMessage}");
            // 或者更新一个专门的进度标签
            // this.BeginInvoke(new Action(() => { lblFFmpegOutput.Text = outputMessage; }));
        }

        /// <summary>
        /// 选择录制区域按钮点击事件
        /// </summary>
        private void btnSelectRegion_Click(object sender, EventArgs e)
        {
            if (_isRecording)
            {
                MessageBox.Show("正在录制中，无法选择区域。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 隐藏主窗体，避免干扰选区
            this.Hide();
            // Application.DoEvents(); // 移除：通常不推荐，可能导致重入或意外行为。窗体的隐藏应在ShowDialog前自然完成。

            // 暂停主窗体的定时器，避免在选区时干扰
            bool processMonitorTimerWasEnabled = _processMonitorTimer != null && _processMonitorTimer.Enabled;
            bool memoryMonitorTimerWasEnabled = _memoryMonitorTimer != null && _memoryMonitorTimer.Enabled;
            bool uiTimerWasEnabled = timerUI != null && timerUI.Enabled;

            if (processMonitorTimerWasEnabled) _processMonitorTimer.Stop();
            if (memoryMonitorTimerWasEnabled) _memoryMonitorTimer.Stop();
            if (uiTimerWasEnabled) timerUI.Stop();
            LogMessage("主窗体定时器已暂停，准备显示选区窗体。");

            try
            {
                using (RegionSelectorForm selectorForm = new RegionSelectorForm())
                {
                    LogMessage("RegionSelectorForm 实例已创建。");
                    // 将主窗体作为父窗体传入 ShowDialog，有助于焦点管理和模态行为的正确性。
                    // selectorForm.Owner = this; // 或者直接在ShowDialog中传递
                    DialogResult selectorResult = selectorForm.ShowDialog(this);
                    LogMessage($"RegionSelectorForm.ShowDialog 返回: {selectorResult}");

                    if (selectorResult == DialogResult.OK)
                    {
                        _selectedRegion = selectorForm.SelectedRegion;
                        if (_selectedRegion.Width > 0 && _selectedRegion.Height > 0)
                        {
                            LogMessage($"用户选择了录制区域: X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}");
                            UpdateStatusInfo($"已选择录制区域: {_selectedRegion.Width}x{_selectedRegion.Height} at ({_selectedRegion.X},{_selectedRegion.Y})");
                            if (lblSelectedRegion != null)
                            {
                                lblSelectedRegion.Text = $"选区: X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}";
                            }
                        }
                        else
                        {
                            _selectedRegion = Rectangle.Empty; // 无效选择则清空
                            LogMessage("用户未能选择有效区域或取消选择 (选区宽高为0)。");
                            UpdateStatusInfo("未选择有效录制区域。");
                            if (lblSelectedRegion != null)
                            {
                                lblSelectedRegion.Text = "选区: 未选择";
                            }
                        }
                    }
                    else
                    {
                        _selectedRegion = Rectangle.Empty; // 用户取消选择
                        LogMessage("用户取消了区域选择。");
                        UpdateStatusInfo("区域选择已取消。");
                        if (lblSelectedRegion != null)
                        {
                            lblSelectedRegion.Text = "选区: 未选择";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 异常处理，记录日志并提示用户
                LogMessage($"选择区域时发生严重错误: {ex.ToString()}"); // 记录详细异常信息
                MessageBox.Show($"选择区域过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _selectedRegion = Rectangle.Empty;
                if (lblSelectedRegion != null)
                {
                    lblSelectedRegion.Text = "选区: 错误";
                }
            }
            finally
            {
                // 确保主窗体总是能重新显示和激活，即使发生异常
                this.Show();
                this.Activate(); // 尝试将焦点设置回主窗体
                LogMessage("主窗体已重新显示和激活。");

                // 恢复主窗体的定时器
                if (processMonitorTimerWasEnabled && _processMonitorTimer != null) _processMonitorTimer.Start();
                if (memoryMonitorTimerWasEnabled && _memoryMonitorTimer != null) _memoryMonitorTimer.Start();
                if (uiTimerWasEnabled && timerUI != null) timerUI.Start();
                LogMessage("主窗体定时器已恢复。");
                // Application.DoEvents(); // 谨慎使用，如果Show和Activate后UI仍未及时响应，可考虑，但优先排查阻塞问题
            }
            UpdateButtonStatus(); // 更新按钮状态
        }

        /// <summary>
        /// 预览录制区域按钮点击事件
        /// </summary>
        private void btnPreviewRegion_Click(object sender, EventArgs e)
        {
            PreviewSelectedRegion();
        }

        /// <summary>
        /// 预览选定的录制区域
        /// </summary>
        private void PreviewSelectedRegion()
        {
            if (_selectedRegion == Rectangle.Empty || _selectedRegion.Width <= 0 || _selectedRegion.Height <= 0)
            {
                MessageBox.Show("请先选择一个有效的录制区域。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 创建一个简单的窗体来显示选定区域的边框
                using (Form previewForm = new Form())
                {
                    previewForm.FormBorderStyle = FormBorderStyle.None;
                    // 使用一个不太可能被用作透明键的颜色，并设置透明度
                    previewForm.BackColor = Color.Magenta;
                    previewForm.TransparencyKey = Color.Magenta;
                    previewForm.ShowInTaskbar = false;
                    previewForm.TopMost = true;
                    previewForm.StartPosition = FormStartPosition.Manual; // 必须手动设置位置

                    // 设置窗体大小和位置与选定区域一致
                    previewForm.Bounds = _selectedRegion;

                    // 添加绘制边框的事件处理程序
                    previewForm.Paint += (s, e) =>
                    {
                        // 绘制红色边框，宽度为3像素
                        using (Pen pen = new Pen(Color.Red, 3))
                        {
                            // 在窗体边缘绘制矩形
                            e.Graphics.DrawRectangle(pen, 0, 0, previewForm.Width - 1, previewForm.Height - 1);
                        }
                    };

                    // 显示预览窗体
                    previewForm.Show();

                    // 3秒后自动关闭预览
                    // 明确指定使用 System.Windows.Forms.Timer，因为它与UI交互
                    System.Windows.Forms.Timer closeTimer = new System.Windows.Forms.Timer();
                    closeTimer.Interval = 3000;
                    closeTimer.Tick += (s, e) =>
                    {
                        // 确保在UI线程中停止和释放Timer
                        if (closeTimer != null)
                    {
                        closeTimer.Stop();
                            closeTimer.Dispose();
                        }
                        // 检查窗体是否已被释放
                        if (!previewForm.IsDisposed)
                        {
                            previewForm.Close();
                        }
                    };
                    closeTimer.Start();

                    // 显示提示信息
                    UpdateStatusInfo($"正在预览录制区域 ({_selectedRegion.Width}x{_selectedRegion.Height} at {_selectedRegion.X},{_selectedRegion.Y})，3秒后自动关闭");
                }
            }
            catch (Exception ex)
            {
                LogError($"预览录制区域失败: {ex.Message}");
                MessageBox.Show($"预览录制区域失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                string configFile = Path.Combine(Application.StartupPath, "LolRecorderConfig.xml");
                if (File.Exists(configFile))
                {
                    // 使用简单的XML序列化加载配置
                    XmlSerializer serializer = new XmlSerializer(typeof(RecorderConfig));
                    using (FileStream fs = new FileStream(configFile, FileMode.Open))
                    {
                        RecorderConfig config = (RecorderConfig)serializer.Deserialize(fs);
                        _savePath = config.SavePath;
                        _fileNamePrefix = config.FileNamePrefix;
                        _frameRate = config.FrameRate;
                        _quality = config.Quality;
                        _autoStarted = config.AutoStart;
                        _resolution = config.Resolution; // 保留原有的分辨率加载
                        checkBox1.Checked = config.ShowRecordingBorder; // 加载显示边框的设置
                        _useHEVC = config.UseHEVC; // 加载HEVC编码设置
                        _compressionPreset = config.CompressionPreset; // 加载压缩预设设置
                        _enableGpuAcceleration = config.EnableGpuAcceleration; // 加载GPU加速设置
                        _screenCaptureMethod = config.ScreenCaptureMethod; // 加载录屏方式

                        // 加载选定区域
                        if (config.SelectedRegionWidth > 0 && config.SelectedRegionHeight > 0)
                        {
                            _selectedRegion = new Rectangle(config.SelectedRegionX, config.SelectedRegionY, config.SelectedRegionWidth, config.SelectedRegionHeight);
                            LogMessage($"从配置加载选定区域: X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}");
                            // 更新UI显示已加载的选区
                            if (lblSelectedRegion != null && IsHandleCreated) // 确保控件有效
                            {
                                BeginInvoke(new Action(() =>
                                {
                                    lblSelectedRegion.Text = $"选区: X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}";
                                }));
                            }
                        }
                        else
                        {
                            _selectedRegion = Rectangle.Empty;
                            LogMessage("配置中无有效选定区域或未保存选区。");
                        }
                    }
                    LogMessage("配置加载成功。");
                }
                else
                {
                    LogMessage("配置文件不存在，使用默认配置。");
                }
            }
            catch (Exception ex)
            {
                LogError($"加载配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfig()
        {
            // 在保存配置前，从UI控件应用最新的设置到内部字段
            ApplySettings();

            try
            {
                string configFile = Path.Combine(Application.StartupPath, "LolRecorderConfig.xml");

                // 使用简单的XML序列化保存配置
                RecorderConfig config = new RecorderConfig
                {
                    SavePath = _savePath,
                    FileNamePrefix = _fileNamePrefix,
                    FrameRate = _frameRate,
                    Quality = _quality,
                    AutoStart = _autoStarted,
                    Resolution = _resolution, // 保留原有的分辨率保存
                    ShowRecordingBorder = checkBox1.Checked, // 保存显示边框的设置
                    UseHEVC = _useHEVC, // 保存HEVC编码设置
                    CompressionPreset = _compressionPreset, // 保存压缩预设设置
                    EnableGpuAcceleration = _enableGpuAcceleration, // 保存GPU加速设置
                    ScreenCaptureMethod = _screenCaptureMethod, // 保存录屏方式

                    // 保存选定区域
                    SelectedRegionX = _selectedRegion.IsEmpty ? -1 : _selectedRegion.X,
                    SelectedRegionY = _selectedRegion.IsEmpty ? -1 : _selectedRegion.Y,
                    SelectedRegionWidth = _selectedRegion.IsEmpty ? -1 : _selectedRegion.Width,
                    SelectedRegionHeight = _selectedRegion.IsEmpty ? -1 : _selectedRegion.Height
                };
                if (!_selectedRegion.IsEmpty && _selectedRegion.Width > 0 && _selectedRegion.Height > 0)
                {
                    LogMessage($"保存选定区域到配置: X={_selectedRegion.X}, Y={_selectedRegion.Y}, W={_selectedRegion.Width}, H={_selectedRegion.Height}");
                }
                else
                {
                    LogMessage("未选择有效区域，配置中选区将保存为无效值。");
                }
                XmlSerializer serializer = new XmlSerializer(typeof(RecorderConfig));
                using (FileStream fs = new FileStream(configFile, FileMode.Create))
                {
                    serializer.Serialize(fs, config);
                }
                LogMessage("配置保存成功。");
            }
            catch (Exception ex)
            {
                LogError($"保存配置失败: {ex.Message}");
            }
        }

        #region Win32 API and Window Helper
        // Win32 API
        [DllImport("kernel32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetShortPathName(
            [MarshalAs(UnmanagedType.LPTStr)]
            string lpszLongPath,
            [MarshalAs(UnmanagedType.LPTStr)]
            StringBuilder lpszShortPath,
            int cchBuffer);

        // Win32 API 用于获取窗口句柄和位置大小
        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct POINT
        {
            public int X;
            public int Y;
        }

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, ref RECT rect);

        [DllImport("user32.dll")]
        private static extern bool GetClientRect(IntPtr hWnd, ref RECT rect);

        [DllImport("user32.dll")]
        private static extern bool ClientToScreen(IntPtr hWnd, ref POINT point);

        [DllImport("user32.dll")]
        private static extern bool IsIconic(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        // 查找窗口句柄的辅助方法 (通过窗口标题)
        [DllImport("user32.dll", SetLastError = true)]
        static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll", SetLastError = true)]
        static extern IntPtr FindWindowEx(IntPtr hwndParent, IntPtr hwndChildAfter, string lpszClass, string lpszWindow);

        /// <summary>
        /// 改进的获取游戏窗口矩形方法 - 获取真实游戏显示区域
        /// </summary>
        private Rectangle GetGameWindowRectImproved(string processName)
        {
            LogMessage($"尝试获取游戏窗口客户区 - 进程名: {processName}");
            Process[] processes = Process.GetProcessesByName(processName);
            LogMessage($"找到 {processes.Length} 个名为 '{processName}' 的进程实例。");

            foreach (Process p in processes)
            {
                LogMessage($"正在检查进程 ID: {p.Id}, 进程名: {p.ProcessName}");
                IntPtr handle = p.MainWindowHandle;
                if (handle != IntPtr.Zero)
                {
                    string windowTitle = string.IsNullOrEmpty(p.MainWindowTitle) ? "N/A" : p.MainWindowTitle;
                    LogMessage($"进程 ID: {p.Id} - 获取到有效句柄: {handle.ToInt64()}, 窗口标题: '{windowTitle}'");

                    // 检查窗口是否最小化或不可见
                    if (IsIconic(handle) || !IsWindowVisible(handle))
                    {
                        LogMessage("找到窗口句柄但窗口最小化或不可见，跳过");
                        continue; // 跳过不可用窗口
                    }

                    // 获取客户区大小
                    RECT clientRect = new RECT();
                    if (GetClientRect(handle, ref clientRect))
                    {
                        LogMessage($"进程 ID: {p.Id} - GetClientRect 成功: Left={clientRect.Left}, Top={clientRect.Top}, Right={clientRect.Right}, Bottom={clientRect.Bottom}");
                        // 客户区域宽度和高度
                        int clientWidth = clientRect.Right - clientRect.Left;
                        int clientHeight = clientRect.Bottom - clientRect.Top;
                        LogMessage($"进程 ID: {p.Id} - 计算得到的客户区尺寸: Width={clientWidth}, Height={clientHeight}");

                        // 确认客户区大小有效
                        if (clientWidth <= 1 || clientHeight <= 1) // 严格来说应为 < 2
                        {
                            LogMessage($"获取到的客户区大小无效: {clientWidth}x{clientHeight}");
                            continue;
                        }

                        LogMessage($"进程 ID: {p.Id} - 客户区尺寸有效。");
                        LogMessage("注意：已确保录制时彻底禁用鼠标指针，避免任何鼠标闪烁问题");

                        // 获取客户区左上角在屏幕上的位置
                        POINT clientPoint = new POINT();
                        clientPoint.X = clientRect.Left;
                        clientPoint.Y = clientRect.Top;
                        if (ClientToScreen(handle, ref clientPoint))
                        {
                            LogMessage($"成功获取游戏客户区: 位置({clientPoint.X},{clientPoint.Y}), 大小({clientWidth}x{clientHeight})");
                            return new Rectangle(clientPoint.X, clientPoint.Y, clientWidth, clientHeight);
                        }
                        else
                        {
                            LogMessage($"进程 ID: {p.Id} - ClientToScreen 失败。");
                        }
                    }
                    else
                    {
                        LogMessage($"进程 ID: {p.Id} - GetClientRect 失败。");
                    }

                    // 如果获取客户区失败，回退到获取整个窗口区域
                    LogMessage("获取客户区失败，回退到获取整个窗口区域");
                    RECT rect = new RECT();
                    if (GetWindowRect(handle, ref rect))
                    {
                        LogMessage($"进程 ID: {p.Id} - GetWindowRect 成功: Left={rect.Left}, Top={rect.Top}, Right={rect.Right}, Bottom={rect.Bottom}");
                        int width = rect.Right - rect.Left;
                        int height = rect.Bottom - rect.Top;
                        if (width > 1 && height > 1) // 严格来说应为 >= 2
                        {
                            LogMessage($"回退方案：获取到整个窗口区域: 位置({rect.Left},{rect.Top}), 大小({width}x{height})");
                            return new Rectangle(rect.Left, rect.Top, width, height);
                        }
                    }
                    else
                    {
                        LogMessage($"进程 ID: {p.Id} - GetWindowRect 失败。");
                    }
                }
                else
                {
                    LogMessage($"进程 ID: {p.Id} - MainWindowHandle 为 IntPtr.Zero。");
                }
            }
            LogMessage("未找到有效的游戏窗口");
            return Rectangle.Empty; // 返回空矩形表示未找到或获取失败
        }

        /// <summary>
        /// 获取指定进程的主窗口标题
        /// </summary>
        private string GetGameWindowTitle(string processName)
        {
            try
            {
                LogMessage($"尝试获取游戏窗口标题 - 进程名: {processName}");
                Process[] processes = Process.GetProcessesByName(processName);
                foreach (Process p in processes)
                {
                    if (p.MainWindowHandle != IntPtr.Zero && !string.IsNullOrEmpty(p.MainWindowTitle))
                    {
                        LogMessage($"找到游戏窗口标题: {p.MainWindowTitle}");
                        return p.MainWindowTitle;
                    }
                }

                // 如果找不到窗口标题，使用默认值
                LogMessage("无法获取游戏窗口标题，使用默认标题");
                return "League of Legends"; // 默认标题
            }
            catch (Exception ex)
            {
                LogError($"获取游戏窗口标题时发生错误: {ex.Message}");
                return "League of Legends"; // 出错时使用默认标题
            }
        }
        #endregion

        /// <summary>
        /// 获取所有显示器的组合边界
        /// </summary>
        private Rectangle GetAllScreensBounds()
        {
            Rectangle bounds = Rectangle.Empty;

            foreach (Screen screen in Screen.AllScreens)
            {
                if (bounds == Rectangle.Empty)
                {
                    bounds = screen.Bounds;
                }
                else
                {
                    bounds = Rectangle.Union(bounds, screen.Bounds);
                }
            }

            return bounds;
        }

        /// <summary>
        /// 确保矩形在指定边界内
        /// </summary>
        private Rectangle EnsureRectangleWithinBounds(Rectangle rect, Rectangle bounds)
        {
            Rectangle adjustedRect = rect;

            // 调整左边缘和上边缘
            if (adjustedRect.X < bounds.X)
            {
                adjustedRect.Width -= (bounds.X - adjustedRect.X);
                adjustedRect.X = bounds.X;
            }

            if (adjustedRect.Y < bounds.Y)
            {
                adjustedRect.Height -= (bounds.Y - adjustedRect.Y);
                adjustedRect.Y = bounds.Y;
            }

            // 调整宽度和高度以确保右边缘和下边缘在边界内
            if (adjustedRect.Right > bounds.Right)
            {
                adjustedRect.Width = bounds.Right - adjustedRect.X;
            }

            if (adjustedRect.Bottom > bounds.Bottom)
            {
                adjustedRect.Height = bounds.Bottom - adjustedRect.Y;
            }

            // 确保至少有最小尺寸
            if (adjustedRect.Width < 2) adjustedRect.Width = 2;
            if (adjustedRect.Height < 2) adjustedRect.Height = 2;

            return adjustedRect;
        }

        #region VideoRecorder Class
        /// <summary>
        /// 视频录制器类 (使用 FFmpeg 实现)
        /// </summary>
        private class VideoRecorder : IDisposable
        {
            private string _filePath;
            private int _frameRate;
            private int _quality; // FFmpeg 质量参数通常不同，需要映射
            private int _captureWidth; // 存储调整后的捕获宽度
            private int _captureHeight; // 存储调整后的捕获高度
            private int _offsetX; // 捕获区域 X 偏移
            private int _offsetY; // 捕获区域 Y 偏移
            private string _windowTitle; // 存储窗口标题，用于窗口捕获模式
            private Process _ffmpegProcess;
            private volatile bool _stopRequested;
            private bool _useHEVCConfig; // 新增：存储HEVC配置
            private string _compressionPresetConfig; // 新增：存储压缩预设配置
            private GpuType _detectedGpuTypeConfig; // 新增：存储检测到的GPU类型
            private bool _enableGpuAccelerationConfig; // 新增：存储是否启用GPU加速的配置
            private string _screenCaptureMethod; // 新增：存储录屏方式

            // FFmpeg 命令行录制不直接支持暂停，这里保留标志但实际暂停需要更复杂的实现
            private ManualResetEvent _pauseEvent = new ManualResetEvent(true);

            // 定义一个事件用于报告错误和输出信息
            public event EventHandler<string> RecordingError;
            public event EventHandler<string> RecordingOutput; // 保留此事件用于传递原始输出

            // 新增一个事件用于传递处理后的日志信息
            public event EventHandler<string> LogMessageEvent;

            // 添加录制区域边框显示相关变量
            private Form _borderForm = null;
            private bool _showBorder = true;


            public VideoRecorder(string filePath, int frameRate, int quality, int width, int height, int offsetX, int offsetY, string windowTitle, bool showBorder = true, bool useHEVC = false, string compressionPreset = "medium", GpuType detectedGpu = GpuType.None, bool enableGpuAcceleration = true, string screenCaptureMethod = "gdigrab")
            {
                _filePath = filePath;
                _frameRate = frameRate;
                _quality = quality;
                // 在构造函数中进行尺寸调整并保存
                _captureWidth = width % 2 == 0 ? width : width - 1;
                _captureHeight = height % 2 == 0 ? height : height - 1;
                _offsetX = offsetX;
                _offsetY = offsetY;
                _windowTitle = windowTitle; // 保存窗口标题，用于窗口捕获模式
                _showBorder = showBorder; // 是否显示录制区域边框
                _useHEVCConfig = useHEVC; // 存储传入的HEVC配置
                _compressionPresetConfig = compressionPreset; // 存储传入的压缩预设配置
                _detectedGpuTypeConfig = detectedGpu; // 存储检测到的GPU类型
                _enableGpuAccelerationConfig = enableGpuAcceleration; // 存储GPU加速配置
                _screenCaptureMethod = screenCaptureMethod; // 存储录屏方式

                // 如果调整后尺寸为0或负数，可以抛出异常或记录错误
                if (_captureWidth <= 0 || _captureHeight <= 0)
                {
                    // 抛出异常或记录错误
                    throw new ArgumentException($"计算捕获区域尺寸无效: {width}x{height} -> {_captureWidth}x{_captureHeight}");
                }

                _stopRequested = false;
                _pauseEvent = new ManualResetEvent(true);

                OnLogMessage($"VideoRecorder Ctor: Received - FilePath: {_filePath}, FrameRate: {_frameRate}, Quality: {_quality}, Original Width: {width}, Original Height: {height}, OffsetX: {_offsetX}, OffsetY: {_offsetY}, WindowTitle: {_windowTitle}, ShowBorder: {_showBorder}");
                OnLogMessage($"VideoRecorder Ctor: Adjusted to even - CaptureWidth: {_captureWidth}, CaptureHeight: {_captureHeight}");
            }

            // 触发日志消息事件的方法
            protected virtual void OnLogMessage(string message)
            {
                LogMessageEvent?.Invoke(this, message);
            }

            // 修改 RecordingOutput 事件处理，将输出信息传递给日志事件
            protected virtual void OnRecordingOutput(string outputMessage)
            {
                // 可以选择过滤掉一些不重要的FFmpeg输出，或者直接全部记录
                // 例如，只记录包含 "frame=", "fps=", "size=", "time=", "bitrate=" 的进度信息
                // 或者记录所有非空的输出行
                if (!string.IsNullOrEmpty(outputMessage))
                {
                    OnLogMessage($"FFmpeg Output: {outputMessage}"); // 将FFmpeg输出记录到日志
                }
                RecordingOutput?.Invoke(this, outputMessage); // 仍然触发原始事件，如果外部有订阅者
            }

            // 修改 RecordingError 事件处理，将错误信息传递给日志事件并记录为错误日志
            protected virtual void OnRecordingError(string errorMessage)
            {
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    OnLogMessage($"FFmpeg Error: {errorMessage}"); // 将FFmpeg错误记录到日志
                    // 可以在这里进一步判断错误级别，决定是否触发 RecordingError 事件
                }
                RecordingError?.Invoke(this, errorMessage); // 触发原始错误事件
            }

            public void Start()
            {
                OnLogMessage("VideoRecorder.Start: 尝试启动 FFmpeg 录制...");
                if (_ffmpegProcess != null && !_ffmpegProcess.HasExited)
                {
                    OnLogMessage("FFmpeg 进程已在运行中。");
                    return;
                }

                try
                {
                    // 确保保存目录存在
                    string directory = Path.GetDirectoryName(_filePath);
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    string selectedEncoder;
                    string hardwareAccelerationInfo = "CPU";
                    string qualityParam;
                    string presetToUse = GetEnglishPresetName(_compressionPresetConfig);

                    if (_enableGpuAccelerationConfig)
                    {
                        switch (_detectedGpuTypeConfig)
                        {
                            case GpuType.NVIDIA:
                                selectedEncoder = _useHEVCConfig ? "hevc_nvenc" : "h264_nvenc";
                                hardwareAccelerationInfo = "NVIDIA NVENC";
                                break;
                            case GpuType.AMD:
                                selectedEncoder = _useHEVCConfig ? "hevc_amf" : "h264_amf";
                                hardwareAccelerationInfo = "AMD AMF";
                                break;
                            case GpuType.Intel:
                                selectedEncoder = _useHEVCConfig ? "hevc_qsv" : "h264_qsv";
                                hardwareAccelerationInfo = "Intel QSV";
                                break;
                            default: // GpuType.None or unknown
                                string cpuEncoderReason = "未检测到兼容GPU或GPU检测失败";
                                if (_useHEVCConfig) {
                                    selectedEncoder = "libopenh264"; // Fallback to H.264 as no software HEVC encoder like libx265
                                    hardwareAccelerationInfo = $"CPU (libopenh264) - {cpuEncoderReason}. HEVC软件编码器不可用, 已回退到H.264.";
                                    OnLogMessage($"警告: 选择HEVC但在此FFmpeg构建中无可用HEVC软件编码器 (如 libx265). {cpuEncoderReason}. 将使用libopenh264进行H.264编码.");
                                } else {
                                    selectedEncoder = "libopenh264"; // libx264 is not available in this build
                                    hardwareAccelerationInfo = $"CPU (libopenh264) - {cpuEncoderReason}.";
                                }
                                break;
                        }
                    }
                    else // GPU acceleration disabled
                    {
                        string cpuEncoderReason = "GPU加速已禁用";
                        if (_useHEVCConfig) {
                            selectedEncoder = "libopenh264"; // Fallback to H.264
                            hardwareAccelerationInfo = $"CPU (libopenh264) - {cpuEncoderReason}. HEVC软件编码器不可用, 已回退到H.264.";
                            OnLogMessage($"警告: 选择HEVC但GPU加速已禁用且在此FFmpeg构建中无可用HEVC软件编码器 (如 libx265). 将使用libopenh264进行H.264编码.");
                        } else {
                            selectedEncoder = "libopenh264";
                            hardwareAccelerationInfo = $"CPU (libopenh264) - {cpuEncoderReason}.";
                        }
                    }
                    OnLogMessage($"选用的编码器: {selectedEncoder} ({hardwareAccelerationInfo})");

                    // 根据编码器类型调整质量参数
                    if (selectedEncoder.Contains("nvenc"))
                    {
                        int cq_value;
                        if (_quality >= 95) cq_value = 21;       // 原画: 调整自 18 (Excellent, smaller file)
                        else if (_quality >= 80) cq_value = 25;  // 高清: 调整自 23 (High, smaller file)
                        else if (_quality >= 50) cq_value = 28;  // Medium (保持不变)
                        else cq_value = 32;                      // Fair (保持不变)
                        qualityParam = $"-cq {cq_value}";
                        OnLogMessage($"使用 NVIDIA NVENC，质量参数映射为 -cq {cq_value} (基于原始质量 {_quality})");
                    }
                    else if (selectedEncoder.Contains("amf"))
                    {
                        // 保留原有的AMF映射或使用AMF特定的质量参数
                        int cq_value = 51 - (_quality * 51 / 100);
                        cq_value = Math.Max(0, Math.Min(51, cq_value));
                        qualityParam = $"-cq {cq_value}"; // AMF 可能使用例如 -qp_i, -qp_p
                        OnLogMessage($"使用 AMD AMF，质量参数映射为 -cq {cq_value} (基于原始质量 {_quality}). 可能需要AMF特定的调整。");
                    }
                    else if (selectedEncoder.Contains("qsv"))
                    {
                        // QSV global_quality: 1-51, lower is better.
                        // For QSV, -q or -global_quality can be used. -global_quality is often preferred for CQP mode.
                        int qsv_quality_val = 51 - (_quality * 51 / 100);
                        qsv_quality_val = Math.Max(1, Math.Min(51, qsv_quality_val));
                        qualityParam = $"-global_quality {qsv_quality_val}";
                        // QSV might also benefit from -look_ahead 1 for CQP.
                        // presetToUse for QSV might be different, e.g., "faster", "fast", "medium", "slow"
                        // FFmpeg maps generic presets to QSV specific ones.
                        OnLogMessage($"使用硬件编码器 {selectedEncoder}，质量参数映射为 -global_quality {qsv_quality_val} (基于原始质量 {_quality})");
                    }
                    else // CPU encoders (libx264, libx265)
                    {
                    int crf;
                        if (_useHEVCConfig) // libx265
                        {
                            if (_quality >= 95) crf = 25;       // 原画 (HEVC): 调整为更平衡的值
                            else if (_quality >= 80) crf = 28;  // 高清 (HEVC): 调整为更平衡的值
                            else if (_quality >= 50) crf = 30;  // Medium: 优化中等质量
                            else crf = 33;                      // Fair: 略微调整压缩率
                        }
                        else // libx264
                        {
                            if (_quality >= 95) crf = 21;       // 原画 (H.264): 调整自 18
                            else if (_quality >= 80) crf = 24;  // 高清 (H.264): 调整自 22
                            else if (_quality >= 50) crf = 26;  // Medium (保持不变)
                            else crf = 30;                      // Fair (保持不变)
                        }
                        qualityParam = $"-crf {crf}";
                        OnLogMessage($"使用CPU编码器 {selectedEncoder}，质量 {_quality} 映射到 FFmpeg CRF 参数: {crf}");
                    }

                    // 根据选定的编码器修正预设值
                    string finalPreset = presetToUse; // 使用一个新变量存储最终使用的预设
                    if (selectedEncoder.Contains("nvenc")) // NVIDIA NVENC
                    {
                        switch (presetToUse)
                        {
                            case "ultrafast":
                            case "superfast":
                            case "veryfast":
                                finalPreset = "fast"; // NVENC 支持 'fast'
                                break;
                            case "slower":
                                finalPreset = "slow"; // NVENC 支持 'slow', 不支持 'slower'
                                break;
                            // 'fast', 'medium', 'slow' 是 NVENC 支持的预设
                            case "faster":
                                finalPreset = "fast"; // NVENC 不支持 'faster'，映射到 'fast'
                                break;
                            default:
                                // 如果 GetEnglishPresetName 返回了这些之外的值，且不是 NVENC 直接支持的，
                                // FFmpeg 可能会报错或选择默认。为保险起见，映射到 'medium'。
                                // 但基于 GetEnglishPresetName 的当前实现，此 default 不太可能被命中。
                                if (presetToUse != "fast" && presetToUse != "medium" && presetToUse != "slow")
                                {
                                    OnLogMessage($"NVENC: 原始预设 '{presetToUse}' 不是标准支持的快速预设，将尝试使用。如果出错，考虑映射到 'medium' 或 'fast'。");
                                    // finalPreset = "medium"; // 或者保持 presetToUse，让 FFmpeg 尝试处理
                                }
                                break;
                        }
                        if (finalPreset != presetToUse)
                        {
                            OnLogMessage($"NVENC 预设从 '{presetToUse}' 修正为 '{finalPreset}'");
                        }
                        presetToUse = finalPreset;
                    }
                    else if (selectedEncoder.Contains("amf")) // AMD AMF
                    {
                        // 优先保证帧率，其次画质，再次性能
                        // -rc cqp: 恒定质量，-qp_i/-qp_p: 画质，-quality speed: 最高性能，-bf 0: 禁用B帧，-g 2倍帧率: 关键帧间隔
                        int qpValue = _quality >= 95 ? 16 : (_quality >= 80 ? 20 : (_quality >= 50 ? 24 : 28));
                        // 使用当前帧率设置关键帧间隔
                        int gopValue = _frameRate * 2;
                    }
                    else if (selectedEncoder.Contains("qsv")) // Intel QSV
                    {
                        // QSV 支持: veryfast, faster, fast, medium, slow, slower, veryslow
                        if (presetToUse == "ultrafast")
                        {
                            finalPreset = "veryfast"; // 将 'ultrafast' 映射到 QSV 的 'veryfast'
                        }
                        // 其他来自 GetEnglishPresetName 的预设 (superfast, veryfast, faster, fast, medium, slow, slower)
                        // 通常会被 FFmpeg 正确处理或映射到 QSV 支持的预设。
                        // 例如，FFmpeg 可能会将 'superfast' 映射到 'veryfast'。
                        // 如果需要更严格的控制，可以添加更多 case。
                        if (finalPreset != presetToUse)
                        {
                            OnLogMessage($"Intel QSV 预设从 '{presetToUse}' 修正为 '{finalPreset}'");
                        }
                        presetToUse = finalPreset;
                    }
                    // 对于 CPU 编码器 (libx264, libx265)，来自 GetEnglishPresetName 的预设通常是正确的。

                    OnLogMessage($"最终使用的预设 (presetToUse): {presetToUse}");


                    // 获取安全文件路径 (短路径或原路径)
                    string safeFilePath = GetShortPath(_filePath);
                    if (string.IsNullOrEmpty(safeFilePath))
                    {
                        OnRecordingError($"无法处理文件路径: {_filePath}");
                        return;
                    }

                    OnLogMessage($"VideoRecorder.Start: 准备构建 FFmpeg 参数。内部参数 - OffsetX: {_offsetX}, OffsetY: {_offsetY}, CaptureWidth: {_captureWidth}, CaptureHeight: {_captureHeight}, FrameRate: {_frameRate}");
                    OnLogMessage($"尝试使用 {_screenCaptureMethod} 按桌面区域捕获，区域 X:{_offsetX}, Y:{_offsetY}, W:{_captureWidth}, H:{_captureHeight}"); // 修正日志记录
                    OnLogMessage($"编码器: {selectedEncoder}, 质量参数: {qualityParam}, 预设: {presetToUse}");

                    // 优化编码器参数配置
                    string encoderOptions = "";
                    int gop = 100; // 默认关键帧间隔
                    if (selectedEncoder.Contains("nvenc")) // NVIDIA NVENC
                    {
                        // 由于驱动API版本不匹配，将encoderOptions留空，
                        // 依赖外部的 -c:v, qualityParam, 和 presetToUse 来使用最基础的设置。
                        encoderOptions = ""; // 使用最少的参数，尝试兼容旧API
                        OnLogMessage($"NVIDIA encoderOptions left empty for maximum compatibility due to API version mismatch.");
                        gop = 100; // 保持原有逻辑
                    }
                    else if (selectedEncoder.Contains("amf")) // AMD AMF
                    {
                        // 优先保证帧率，其次画质，再次性能
                        // -rc cqp: 恒定质量，-qp_i/-qp_p: 画质，-quality speed: 最高性能，-bf 0: 禁用B帧，-g 2倍帧率: 关键帧间隔
                        int qpValue = _quality >= 95 ? 16 : (_quality >= 80 ? 20 : (_quality >= 50 ? 24 : 28));
                        gop = _frameRate * 2;
                        encoderOptions = $"-rc cqp -qp_i {qpValue} -qp_p {qpValue + 2} -quality speed -bf 0 -g {gop} -threads 1";
                    }
                    else if (selectedEncoder.Contains("qsv")) // Intel QSV
                    {
                        int qValue = _quality >= 80 ? 18 : (_quality >= 50 ? 22 : 28);
                        encoderOptions = $"-preset faster -global_quality {qValue} -look_ahead 1";
                        gop = 100; // 保持原有逻辑
                    }
                    else if (selectedEncoder == "libx265") // CPU HEVC
                    {
                        int crf = _quality >= 95 ? 18 : (_quality >= 80 ? 22 : (_quality >= 50 ? 26 : 28));
                        encoderOptions = $"-crf {crf} -preset {presetToUse} -tune zerolatency -x265-params \"profile=main:tier=main:me=dia:subme=2:aq-mode=2\"";
                        gop = 100; // 保持原有逻辑
                    }
                    else // libx264 CPU H.264
                    {
                        int crf = _quality >= 95 ? 16 : (_quality >= 80 ? 20 : (_quality >= 50 ? 23 : 26));
                        encoderOptions = $"-crf {crf} -preset {presetToUse} -tune zerolatency -profile:v high -level 4.1 -aq-mode 2 -x264opts no-dct-decimate";
                        gop = 100; // 保持原有逻辑
                    }

                    string cropFilter = "";
                    // screen-capture-recorder dshow filter handles cropping via its own options,
                    // but if we were using gdigrab, this is how crop would be applied.
                    // For dshow, we rely on it capturing the full desktop and then cropping if needed,
                    // or if it supports region capture directly.
                    // The current FFmpeg command uses screen-capture-recorder which captures the whole screen.
                    // The crop filter is applied *after* capture.
                    if (_captureWidth > 0 && _captureHeight > 0 && (_offsetX > 0 || _offsetY > 0 || _captureWidth < Screen.PrimaryScreen.Bounds.Width || _captureHeight < Screen.PrimaryScreen.Bounds.Height))
                    {
                        cropFilter = $",crop={_captureWidth}:{_captureHeight}:{_offsetX}:{_offsetY}";
                    }

                    // 重点优化参数如下
                    // 替换原有的ffmpeg参数设置，优化后的完整参数字符串
                    // 更换为 gdigrab 捕获方式
                    string ffmpegArgs = "";
                    string globalLowLatency = "-vsync cfr -fflags nobuffer -flush_packets 1 "; // Changed from passthrough to cfr to work with -r parameter
                    if (_screenCaptureMethod == "gdigrab")
                    {
                        // 优化gdigrab录制参数，解决高频率刷屏和鼠标闪烁问题
                        ffmpegArgs = $"-hide_banner " +
                            $"-loglevel error " +
                            $"-f gdigrab " +
                            // Removed: $"-pixel_format bgra " + // 明确指定输入像素格式 - Caused "Option not found"
                            $"-framerate {_frameRate} " +
                            $"-offset_x {_offsetX} " +
                            $"-offset_y {_offsetY} " +
                            $"-video_size {_captureWidth}x{_captureHeight} " +
                            $"-draw_mouse 0 " +
                            $"-probesize 32M " + // 增加探测缓冲区大小
                            $"-thread_queue_size 1024 " + // 增加线程队列大小
                            $"-hwaccel auto " + // 硬件加速选项必须放在输入文件之前
                            $"-i desktop " +
                            globalLowLatency +
                            // 使用简单通用的过滤器设置，避免CUDA兼容性问题
                            $"-vf \"format=nv12\" " + // 简化过滤器链
                            $"-c:v {selectedEncoder} " +
                            (string.IsNullOrEmpty(encoderOptions) ? "" : $"{encoderOptions} ") +
                            $"{qualityParam} " +
                            $"-preset {presetToUse} " +
                            (selectedEncoder.Contains("nvenc") ? "-profile high -tune ull -zerolatency 1 -delay 0 " : "") + // 为NVENC添加profile high, zerolatency, ull tune 和 delay 0
                            $"-b:v {GetTargetBitrate(_captureWidth, _captureHeight, _frameRate)} " +
                            $"-maxrate {GetMaxBitrate(_captureWidth, _captureHeight, _frameRate)} " +
                            $"-bufsize {GetBufferSize(_captureWidth, _captureHeight, _frameRate)} " +
                            $"-r {_frameRate} " +
                            $"-pix_fmt nv12 " +
                            $"-movflags +faststart " +
                            $"-g {gop} " +
                            $"-keyint_min {_frameRate} " +
                            $"-sc_threshold 0 " +
                            $"-threads 1 " +
                            $"-y \"{safeFilePath}\"";
                    }
                    else if (_screenCaptureMethod == "ddagrab") // 直接转到ddagrab
                    {
                        //https://ayosec.github.io/ffmpeg-filters-docs/7.1/Sources/Video/ddagrab.html
                        ffmpegArgs = $"-hide_banner -loglevel error -f lavfi " +
                                    $"-hwaccel cuda -hwaccel_output_format cuda " + // 添加CUDA硬件加速和输出格式
                                    $"-probesize 32M -analyzeduration 16M " + // 增加探测缓冲区和分析时长
                                    $"-i \"ddagrab=framerate={_frameRate}:output_fmt=auto:video_size={_captureWidth}x{_captureHeight}:offset_x={_offsetX}:offset_y={_offsetY}\" " + // 使用DirectX捕获方式，配置帧率、输出格式、尺寸和偏移
                                    $"-vsync cfr " + // 使用CFR模式，与-r参数兼容
                                    $"-c:v {selectedEncoder} {qualityParam} " + // 使用传入的质量参数
                                    (selectedEncoder.Contains("nvenc") ? $"-preset p7 -tune ll " : $"-preset {presetToUse} ") + // 为NVENC添加特定预设
                                    $"-g {_frameRate * 10} -keyint_min {_frameRate * 3} -sc_threshold 0 " + // 配置关键帧参数
                                    $"-b:v {(int)(int.Parse(GetMaxBitrate(_captureWidth, _captureHeight, _frameRate).TrimEnd('k')) * 0.7)}k " + // 增加比特率
                                    $"-maxrate {(int)(int.Parse(GetMaxBitrate(_captureWidth, _captureHeight, _frameRate).TrimEnd('k')) * 0.8)}k " + // 增加最大比特率
                                    $"-bufsize {(int)(int.Parse(GetBufferSize(_captureWidth, _captureHeight, _frameRate).TrimEnd('k')) * 2)}k " + // 增加缓冲区大小提高稳定性
                                    $"-profile:v high -movflags +faststart -r {_frameRate} -y \"{safeFilePath}\""; // 指定输出文件路径，覆盖已有文件

                        OnLogMessage($"使用优化后的ddagrab DirectX捕获方式。已添加CUDA硬件加速和CFR模式修复参数冲突。");

                        OnLogMessage($"[调试 ddagrab with optimized output] FFmpeg 参数: {ffmpegArgs}");
                    }
                    else
                    {
                        OnRecordingError($"未知的录屏方式: {_screenCaptureMethod}");
                        return;
                    }

                    // QSV specific init if needed (often not required if ffmpeg is built correctly)
                    // if (selectedEncoder.Contains("qsv")) {
                    //    ffmpegArgs = $"-init_hw_device qsv=hw -hwaccel qsv -hwaccel_output_format qsv " + ffmpegArgs;
                    // }

                    OnLogMessage($"VideoRecorder.Start: 使用的FFmpeg参数: {ffmpegArgs}");
                    OnLogMessage($"录制设置已采用稳定配置，并尝试使用 {hardwareAccelerationInfo} 进行硬件加速。");

                    // 查找 FFmpeg
                    string ffmpegPath = FindFFmpegPath();
                    if (string.IsNullOrEmpty(ffmpegPath))
                    {
                        OnRecordingError("找不到ffmpeg.exe。请确保已安装FFmpeg并添加到系统PATH环境变量中，或将ffmpeg.exe放置在应用程序目录下。");
                        return;
                    }

                    // 创建并配置 FFmpeg 进程
                    _ffmpegProcess = new Process();
                    _ffmpegProcess.StartInfo.FileName = ffmpegPath;
                    _ffmpegProcess.StartInfo.Arguments = ffmpegArgs;
                    _ffmpegProcess.StartInfo.UseShellExecute = false;
                    _ffmpegProcess.StartInfo.RedirectStandardInput = true;
                    _ffmpegProcess.StartInfo.RedirectStandardOutput = true;
                    _ffmpegProcess.StartInfo.RedirectStandardError = true;
                    _ffmpegProcess.StartInfo.CreateNoWindow = true;
                    _ffmpegProcess.StartInfo.StandardOutputEncoding = System.Text.Encoding.UTF8;
                    _ffmpegProcess.StartInfo.StandardErrorEncoding = System.Text.Encoding.UTF8;

                    // **使用具名方法订阅事件**
                    _ffmpegProcess.OutputDataReceived += FfmpegProcess_OutputDataReceived;
                    _ffmpegProcess.ErrorDataReceived += FfmpegProcess_ErrorDataReceived; // FFmpeg 通常将进度信息输出到 stderr
                    _ffmpegProcess.EnableRaisingEvents = true;
                    _ffmpegProcess.Exited += FfmpegProcess_Exited; // 订阅具名 Exited 处理程序

                    // 启动进程
                    _ffmpegProcess.Start();

                    // 开始异步读取流
                    _ffmpegProcess.BeginOutputReadLine();
                    _ffmpegProcess.BeginErrorReadLine();

                    _stopRequested = false;
                    _pauseEvent.Set(); // 初始状态为非暂停

                    // 显示录制边框
                    ShowRecordingBorder();

                    OnLogMessage($"FFmpeg 录制进程已启动，PID: {_ffmpegProcess.Id}, 参数: {_ffmpegProcess.StartInfo.Arguments}");
                }
                catch (Exception ex)
                {
                    OnLogMessage($"启动 FFmpeg 录制失败: {ex.Message}\n{ex.StackTrace}"); // 添加堆栈跟踪
                    // 可以在这里触发 RecordingError 事件
                    OnRecordingError($"启动 FFmpeg 失败: {ex.Message}");
                }
            }

            /// <summary>
            /// 创建并显示录制区域边框
            /// </summary>
            private void ShowRecordingBorder()
            {
                if (!_showBorder) return;

                try
                {
                    // 确保先关闭之前的边框窗体
                    CloseRecordingBorder();

                    // 使用工厂方法创建窗体，确保任何异常都不会导致资源泄漏
                    _borderForm = CreateBorderForm();

                    if (_borderForm != null)
                    {
                        // 注册窗体关闭事件，确保资源被正确释放
                        _borderForm.FormClosed += (s, e) =>
                        {
                            OnLogMessage("边框窗体已关闭");
                            if (s is Form form && s == _borderForm)
                            {
                                _borderForm = null;
                            }
                        };

                        // 显示窗体
                        _borderForm.Show();
                        OnLogMessage("录制边框窗体已显示");
                    }
                }
                catch (Exception ex)
                {
                    OnLogMessage($"显示录制边框窗体失败: {ex.Message}");
                    // 确保出错时也清理资源
                    CloseRecordingBorder();
                }
            }

            /// <summary>
            /// 创建边框窗体的工厂方法
            /// </summary>
            private Form CreateBorderForm()
            {
                try
                {
                    Form form = new Form
                    {
                        FormBorderStyle = FormBorderStyle.None,
                        BackColor = Color.Magenta,
                        TransparencyKey = Color.Magenta,
                        ShowInTaskbar = false,
                        TopMost = true,
                        StartPosition = FormStartPosition.Manual,
                        Bounds = new Rectangle(_offsetX, _offsetY, _captureWidth, _captureHeight)
                    };

                    // 添加绘制边框的事件处理程序
                    form.Paint += (s, e) =>
                    {
                        try
                        {
                            // 绘制红色边框，宽度为3像素
                            using (Pen pen = new Pen(Color.Red, 3))
                            {
                                // 在窗体边缘绘制矩形
                                e.Graphics.DrawRectangle(pen, 0, 0, form.Width - 1, form.Height - 1);
                            }
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage($"绘制边框失败: {ex.Message}");
                        }
                    };

                    // 使边框窗体点击穿透，不拦截鼠标操作
                    try
                    {
                        int initialStyle = Win32.GetWindowLong(form.Handle, Win32.GWL_EXSTYLE);
                        Win32.SetWindowLong(form.Handle, Win32.GWL_EXSTYLE, initialStyle | Win32.WS_EX_TRANSPARENT);
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"设置窗体透明点击失败: {ex.Message}");
                    }

                    return form;
                }
                catch (Exception ex)
                {
                    OnLogMessage($"创建边框窗体失败: {ex.Message}");
                    return null;
                }
            }

            /// <summary>
            /// 关闭录制区域边框
            /// </summary>
            private void CloseRecordingBorder()
            {
                if (_borderForm != null)
                {
                    try
                    {
                        Form formToClose = _borderForm;
                        _borderForm = null; // 先将引用置空，避免重入问题

                        if (!formToClose.IsDisposed)
                        {
                            try
                            {
                                formToClose.Hide();
                            }
                            catch
                            {
                                // 忽略隐藏过程中的错误
                            }

                            try
                            {
                                formToClose.Close();
                            }
                            catch (Exception ex)
                            {
                                OnLogMessage($"关闭边框窗体失败: {ex.Message}");
                            }

                            try
                            {
                                formToClose.Dispose();
                            }
                            catch
                            {
                                // 忽略Dispose过程中的错误
                            }
                        }

                        OnLogMessage("录制边框窗体已关闭和释放");
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"关闭录制边框窗体过程中发生错误: {ex.Message}");
                        _borderForm = null; // 确保引用被置空
                    }
                }
            }


            public void Stop()
            {
                OnLogMessage("VideoRecorder.Stop: 尝试停止FFmpeg录制...");

                if (_ffmpegProcess == null)
                {
                    OnLogMessage("VideoRecorder.Stop: FFmpeg进程为null，无需停止");
                    CloseRecordingBorder();
                    return;
                }

                try
                {
                    _stopRequested = true;
                    if (_pauseEvent != null)
                    {
                        _pauseEvent.Set();
                    }
                    else
                    {
                        OnLogMessage("VideoRecorder.Stop: _pauseEvent为null，跳过Set操作");
                    }

                    CloseRecordingBorder();

                    // 移除不必要的CancellationTokenSource，它没有实际用处
                    OnLogMessage("VideoRecorder.Stop: 尝试优雅地结束FFmpeg进程");
                    Process currentProcess = _ffmpegProcess; // 保存本地引用，防止异步操作中的空引用

                    // 首先检查进程是否已退出
                    if (currentProcess != null && !currentProcess.HasExited)
                    {
                        try
                        {
                            OnLogMessage("VideoRecorder.Stop: 步骤1 - 发送 'q' 到 FFmpeg 标准输入以请求优雅退出。");
                            if (currentProcess.StandardInput != null && currentProcess.StandardInput.BaseStream.CanWrite)
                            {
                                try
                                {
                                    currentProcess.StandardInput.WriteLine("q");
                                    currentProcess.StandardInput.Flush(); // 确保命令已发送
                                    OnLogMessage("VideoRecorder.Stop: 'q' 命令已发送。");
                                    // 关闭输入流，表示不再发送命令
                                    currentProcess.StandardInput.Close();
                                    OnLogMessage("VideoRecorder.Stop: 标准输入流已关闭。");
                                }
                                catch (ObjectDisposedException) { OnLogMessage("VideoRecorder.Stop: 发送 'q' 时标准输入流已被释放。"); }
                                catch (IOException ex) { OnLogMessage($"VideoRecorder.Stop: 发送 'q' 或关闭标准输入时发生IO异常: {ex.Message}");}
                            }
                            else
                            {
                                OnLogMessage("VideoRecorder.Stop: 标准输入流已关闭或不可用，无法发送 'q'。");
                            }

                            OnLogMessage("VideoRecorder.Stop: 等待 FFmpeg 进程退出 (最多15秒)...");
                            bool exitedGracefully = currentProcess.WaitForExit(15000); // 增加超时到15秒
                            if (exitedGracefully)
                            {
                                OnLogMessage($"VideoRecorder.Stop: FFmpeg 进程在发送 'q' 后成功退出。退出代码: {currentProcess.ExitCode}");
                            }
                            else
                            {
                                OnLogMessage("VideoRecorder.Stop: FFmpeg 进程在发送 'q' 后未能及时退出。");
                            }
                        }
                        catch (InvalidOperationException ex) // 例如，进程已退出时尝试访问 StandardInput
                        {
                             OnLogMessage($"VideoRecorder.Stop: 尝试发送 'q' 时发生 InvalidOperationException (可能进程已自行退出): {ex.Message}");
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage($"VideoRecorder.Stop: 发送 'q' 命令过程中发生未知错误: {ex.Message}");
                        }
                    }
                    else
                    {
                        OnLogMessage("VideoRecorder.Stop: 步骤1 - FFmpeg 进程已退出或为null，跳过发送 'q' 命令。");
                    }

                    // 步骤2: 如果进程仍然存在，尝试 CloseMainWindow
                    if (currentProcess != null && !currentProcess.HasExited)
                    {
                        OnLogMessage("VideoRecorder.Stop: 步骤2 - FFmpeg 进程仍在运行，尝试 CloseMainWindow()");
                        try
                        {
                            bool closed = currentProcess.CloseMainWindow();
                            OnLogMessage($"VideoRecorder.Stop: CloseMainWindow() 返回 {closed}. 等待进程退出 (最多5秒)...");
                            if (currentProcess.WaitForExit(5000))
                            {
                                OnLogMessage($"VideoRecorder.Stop: FFmpeg 进程在 CloseMainWindow() 后成功退出。退出代码: {currentProcess.ExitCode}");
                            }
                            else
                            {
                                OnLogMessage("VideoRecorder.Stop: FFmpeg 进程在 CloseMainWindow() 后未能及时退出。");
                            }
                        }
                        catch (InvalidOperationException ex) // 进程可能在此期间已退出
                        {
                            OnLogMessage($"VideoRecorder.Stop: CloseMainWindow() 时发生 InvalidOperationException (可能进程已自行退出): {ex.Message}");
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage($"VideoRecorder.Stop: CloseMainWindow() 过程中发生错误: {ex.Message}");
                        }
                    }
                    else
                    {
                         OnLogMessage("VideoRecorder.Stop: 步骤2 - FFmpeg 进程已退出或为null，跳过 CloseMainWindow()。");
                    }

                    // 步骤3: 如果进程仍然存在，强制终止
                    if (currentProcess != null && !currentProcess.HasExited)
                    {
                        OnLogMessage("VideoRecorder.Stop: 步骤3 - FFmpeg 进程仍在运行，执行 Kill()。");
                        try
                        {
                            currentProcess.Kill();
                            OnLogMessage("VideoRecorder.Stop: Kill() 命令已发送。等待进程退出 (最多3秒)...");
                            if (currentProcess.WaitForExit(3000))
                            {
                                OnLogMessage($"VideoRecorder.Stop: FFmpeg 进程被 Kill() 后成功退出。退出代码: {currentProcess.ExitCode}");
                            }
                            else
                            {
                                OnLogMessage("VideoRecorder.Stop: FFmpeg 进程被 Kill() 后未能及时退出 (这不常见)。");
                            }
                            // 强制终止后，文件很可能损坏，尝试修复
                            if (!string.IsNullOrEmpty(_filePath))
                            {
                                OnLogMessage("VideoRecorder.Stop: 由于进程被强制终止，尝试修复视频文件。");
                                RepairVideoFile(_filePath);
                            }
                        }
                        catch (InvalidOperationException ex) // 进程可能在此期间已退出
                        {
                             OnLogMessage($"VideoRecorder.Stop: Kill() 时发生 InvalidOperationException (可能进程已自行退出): {ex.Message}");
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage($"VideoRecorder.Stop: Kill() 过程中发生错误: {ex.Message}");
                        }
                    }
                    else
                    {
                        OnLogMessage("VideoRecorder.Stop: 步骤3 - FFmpeg 进程已退出或为null，跳过 Kill()。");
                    }
                }
                catch (Exception ex)
                {
                    OnLogMessage($"VideoRecorder.Stop: 停止过程中发生异常: {ex.Message}");
                }
                finally
                {
                    SafeCleanupProcessResources();
                }
            }
            /// <summary>
            /// 安全清理进程资源 - 即使异常也会继续完成所有清理步骤
            /// </summary>
            private void SafeCleanupProcessResources()
            {
                OnLogMessage("SafeCleanupProcessResources: 开始安全清理进程资源");

                if (_ffmpegProcess != null)
                {
                    try
                    {
                        // 取消事件订阅
                        try
                        {
                            _ffmpegProcess.OutputDataReceived -= FfmpegProcess_OutputDataReceived;
                            _ffmpegProcess.ErrorDataReceived -= FfmpegProcess_ErrorDataReceived;
                            _ffmpegProcess.Exited -= FfmpegProcess_Exited;
                            OnLogMessage("SafeCleanupProcessResources: 事件订阅已取消");
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage($"SafeCleanupProcessResources: 取消事件订阅时出错: {ex.Message}");
                        }

                        // 再次检查进程是否仍在运行
                        // 添加检查进程是否已退出
                        if (_ffmpegProcess != null && !_ffmpegProcess.HasExited)
                        {
                            try
                            {
                                OnLogMessage("SafeCleanupProcessResources: 进程仍在运行，尝试终止");
                                _ffmpegProcess.Kill();
                                OnLogMessage("SafeCleanupProcessResources: 已发送终止命令");
                            }
                            catch (Exception ex)
                            {
                                OnLogMessage($"SafeCleanupProcessResources: 检查/终止进程时出错: {ex.Message}");
                            }
                        } else {
                            OnLogMessage("SafeCleanupProcessResources: 进程已退出或为null，跳过检查/终止");
                        }


                        // 释放进程资源
                        try
                        {
                            _ffmpegProcess.Dispose();
                            OnLogMessage("SafeCleanupProcessResources: 进程资源已Dispose");
                        }
                        catch (Exception ex)
                        {
                            OnLogMessage($"SafeCleanupProcessResources: Dispose进程时出错: {ex.Message}");
                        }
                    }
                    finally
                    {
                        _ffmpegProcess = null;
                        OnLogMessage("SafeCleanupProcessResources: 进程引用已置空");
                    }
                }
            }

            public void Dispose()
            {
                OnLogMessage("VideoRecorder.Dispose: 开始释放资源");

                try
                {
                    // 1. 尝试停止录制 (Stop方法内部会处理FFmpeg进程)
                    try
                    {
                        Stop();
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"VideoRecorder.Dispose: 停止录制时出错: {ex.Message}");
                    }

                    // 2. 释放暂停事件对象
                    try
                    {
                        if (_pauseEvent != null)
                        {
                            _pauseEvent.Dispose();
                            _pauseEvent = null;
                            OnLogMessage("VideoRecorder.Dispose: 暂停事件对象已释放");
                        }
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"VideoRecorder.Dispose: 释放暂停事件对象时出错: {ex.Message}");
                    }

                    // 3. 关闭边框窗体 (Stop已调用，此处为保险)
                    try
                    {
                        CloseRecordingBorder();
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"VideoRecorder.Dispose: 关闭边框窗体时出错: {ex.Message}");
                    }

                    // 4. 清除所有事件订阅
                    try
                    {
                        RecordingError = null;
                        RecordingOutput = null;
                        LogMessageEvent = null;
                        OnLogMessage("VideoRecorder.Dispose: 所有事件订阅已清除");
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"VideoRecorder.Dispose: 清除事件订阅时出错: {ex.Message}");
                    }

                    // 5. 再次确保进程资源被清理 (保险措施)
                    try
                    {
                        SafeCleanupProcessResources();
                    }
                    catch (Exception ex)
                    {
                        OnLogMessage($"VideoRecorder.Dispose: 最终清理进程资源时出错: {ex.Message}");
                    }
                }
                catch (Exception ex)
                {
                    OnLogMessage($"VideoRecorder.Dispose: 资源释放过程中发生未处理异常: {ex.Message}");
                }
                finally
                {
                    OnLogMessage("VideoRecorder.Dispose: 资源释放完成");
                }
            }

            /// <summary>
            /// 尝试修复可能损坏的视频文件
            /// </summary>
            private void RepairVideoFile(string filePath)
            {
                try
                {
                    if (!File.Exists(filePath))
                    {
                        OnLogMessage($"找不到需要修复的文件: {filePath}"); // 使用新的日志事件
                        return;
                    }

                    // 检查文件大小，如果太小则没有修复的必要
                    FileInfo fileInfo = new FileInfo(filePath);
                    if (fileInfo.Length < 10 * 1024) // 小于10KB
                    {
                        OnLogMessage($"文件过小无法修复: {FormatBytes(fileInfo.Length)}"); // 使用新的日志事件
                        return;
                    }

                    // 创建修复后的文件路径
                    string directory = Path.GetDirectoryName(filePath);
                    string fileName = Path.GetFileNameWithoutExtension(filePath);
                    string extension = Path.GetExtension(filePath);
                    string repairedFilePath = Path.Combine(directory, $"{fileName}_repaired{extension}");

                    // 查找FFmpeg路径
                    string ffmpegPath = FindFFmpegPath();
                    if (string.IsNullOrEmpty(ffmpegPath))
                    {
                        OnLogMessage("找不到FFmpeg，无法修复视频文件"); // 使用新的日志事件
                        return;
                    }

                    // 使用FFmpeg尝试修复视频
                    OnLogMessage($"正在尝试修复视频文件: {filePath}"); // 使用新的日志事件

                    // 使用-c copy参数尝试重新封装视频而不重新编码
                    Process repairProcess = new Process();
                    repairProcess.StartInfo.FileName = ffmpegPath;
                    // 增加详细日志输出参数 -v debug，移除 -err_detect ignore_err 以便看到所有错误
                    repairProcess.StartInfo.Arguments = $"-v debug -i \"{filePath}\" -c copy -movflags +faststart \"{repairedFilePath}\"";
                    repairProcess.StartInfo.UseShellExecute = false;
                    repairProcess.StartInfo.CreateNoWindow = true;
                    repairProcess.StartInfo.RedirectStandardOutput = true;
                    repairProcess.StartInfo.RedirectStandardError = true;
                    repairProcess.StartInfo.StandardOutputEncoding = System.Text.Encoding.UTF8;
                    repairProcess.StartInfo.StandardErrorEncoding = System.Text.Encoding.UTF8;

                    StringBuilder repairOutput = new StringBuilder();
                    // 订阅修复进程的输出和错误，记录到日志
                    repairProcess.OutputDataReceived += (sender, args) => { if(args.Data != null) repairOutput.AppendLine($"[Repair STDOUT] {args.Data}"); };
                    repairProcess.ErrorDataReceived += (sender, args) => { if(args.Data != null) repairOutput.AppendLine($"[Repair STDERR] {args.Data}"); };

                    repairProcess.Start();
                    OnLogMessage($"FFmpeg repair process started. Command: {repairProcess.StartInfo.Arguments}");

                    // 开始异步读取输出和错误流
                    repairProcess.BeginOutputReadLine();
                    repairProcess.BeginErrorReadLine();

                    // 等待修复完成 (设置超时)
                    bool repairExited = repairProcess.WaitForExit(60000); // 等待1分钟

                    OnLogMessage($"FFmpeg repair process output:\n{repairOutput.ToString()}");

                    if (!repairExited)
                    {
                        OnLogMessage("FFmpeg repair process timed out. Killing process.");
                        try { repairProcess.Kill(); } catch (Exception killEx) { OnLogMessage($"Error killing repair process: {killEx.Message}");}
                        OnLogMessage("视频修复超时。");
                        return;
                    }

                    OnLogMessage($"FFmpeg repair process exited with code: {repairProcess.ExitCode}");

                    // 检查修复结果
                    if (File.Exists(repairedFilePath))
                    {
                        long repairedFileSize = new FileInfo(repairedFilePath).Length;
                        OnLogMessage($"修复后的文件存在，大小: {FormatBytes(repairedFileSize)}");
                        if (repairedFileSize > fileInfo.Length * 0.5 || repairedFileSize > 10240) // 修复后文件大小至少是原文件的50% 或 大于10KB
                        {
                            OnLogMessage($"视频修复成功，修复后的文件保存为: {repairedFilePath}");

                        // 创建.txt说明文件
                        string noteFilePath = Path.Combine(directory, $"{fileName}_修复说明.txt");
                        File.WriteAllText(noteFilePath,
                            $"原始视频文件 {fileName}{extension} 可能因为录制意外中断而损坏。\r\n" +
                            $"已尝试自动修复，修复后的文件为 {fileName}_repaired{extension}。\r\n" +
                            $"如果修复后的文件仍无法播放，请尝试使用VLC、Potplayer等专业播放器打开。\r\n" +
                            $"修复时间: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}"
                        );
                    }
                    else
                    {
                             OnLogMessage($"视频修复后文件过小 ({FormatBytes(repairedFileSize)})，可能修复未成功或原始文件损坏严重。");
                             try { File.Delete(repairedFilePath); OnLogMessage($"已删除过小的修复文件: {repairedFilePath}"); } catch (Exception delEx) { OnLogMessage($"删除过小的修复文件失败: {delEx.Message}");}
                        }
                    }
                    else
                    {
                        OnLogMessage($"视频修复失败，未生成修复文件: {repairedFilePath}");
                    }
                }
                catch (Exception ex)
                {
                    OnLogMessage($"修复视频文件时发生错误: {ex.Message}"); // 使用新的日志事件
                }
            }

            /// <summary>
            /// 将中文压缩预设名称转换为FFmpeg可识别的英文名称
            /// </summary>
            private string GetEnglishPresetName(string chinesePreset)
            {
                if (string.IsNullOrEmpty(chinesePreset))
                    return "medium"; // 默认值

                switch (chinesePreset)
                {
                    case "默认": return "medium"; // "默认" 映射到 "superfast"
                    case "极速": return "ultrafast";
                    case "超快": return "superfast";
                    case "很快": return "veryfast";
                    case "较快": return "faster";
                    case "快速": return "fast";
                    case "中等": return "medium";
                    case "慢速": return "slow";
                    case "较慢": return "slower";
                    default:
                        OnLogMessage($"未知的预设名称: {chinesePreset}，使用默认值'medium'");
                        return "medium"; // 对于未知值，使用默认值
                }
            }
/// &lt;summary&gt;
            /// 根据分辨率和帧率计算合适的目标比特率
            /// &lt;/summary&gt;
            private string GetTargetBitrate(int width, int height, int frameRate)
            {
                // 基于分辨率计算基础比特率（单位：kbps）
                int basePixels = width * height;
                int baseBitrate;

                if (basePixels <= 1280 * 720) // 720p或更低
                    baseBitrate = 4000;
                else if (basePixels <= 1920 * 1080) // 1080p
                    baseBitrate = 8000;
                else if (basePixels <= 2560 * 1440) // 1440p
                    baseBitrate = 12000;
                else // 4K或更高
                    baseBitrate = 16000;

                // 根据帧率调整（30fps作为基础）
                float frameRateFactor = frameRate / 30.0f;
                int adjustedBitrate = (int)(baseBitrate * frameRateFactor);

                // 根据编码器类型调整
                if (this._useHEVCConfig) // HEVC通常可以使用更低的比特率
                    adjustedBitrate = (int)(adjustedBitrate * 0.75f);

                return adjustedBitrate + "k";
            }

            /// &lt;summary&gt;
            /// 计算最大比特率（通常为目标比特率的1.5倍）
            /// &lt;/summary&gt;
            private string GetMaxBitrate(int width, int height, int frameRate)
            {
                string targetBitrate = GetTargetBitrate(width, height, frameRate);
                int numericValue = int.Parse(targetBitrate.TrimEnd('k'));
                return (int)(numericValue * 1.5f) + "k";
            }

            /// &lt;summary&gt;
            /// 计算编码器缓冲区大小（通常为目标比特率的2倍）
            /// &lt;/summary&gt;
            private string GetBufferSize(int width, int height, int frameRate)
            {
                string targetBitrate = GetTargetBitrate(width, height, frameRate);
                int numericValue = int.Parse(targetBitrate.TrimEnd('k'));
                return (numericValue * 2) + "k";
            }

            /// <summary>
            /// 暂停录制
            /// </summary>
            public void Pause()
            {
                OnLogMessage("VideoRecorder.Pause: 尝试暂停录制");
                if (_ffmpegProcess == null || _ffmpegProcess.HasExited)
                {
                    OnLogMessage("VideoRecorder.Pause: FFmpeg进程不存在或已退出，无法暂停");
                    return;
                }

                try
                {
                    // 设置暂停事件为非信号状态，阻止其他操作
                    _pauseEvent.Reset();

                    // FFmpeg不直接支持暂停，这里仅记录状态
                    // 在实际应用中，可以考虑使用SIGUSR1信号或发送'p'到标准输入实现暂停（某些FFmpeg版本支持）
                    // 或者停止当前进程并在恢复时用新参数重新启动
                    OnLogMessage("VideoRecorder.Pause: 录制已暂停（状态标记）");
                }
                catch (Exception ex)
                {
                    OnLogMessage($"VideoRecorder.Pause: 暂停录制时出错: {ex.Message}");
                }
            }

            /// <summary>
            /// 恢复录制
            /// </summary>
            public void Resume()
            {
                OnLogMessage("VideoRecorder.Resume: 尝试恢复录制");
                if (_ffmpegProcess == null || _ffmpegProcess.HasExited)
                {
                    OnLogMessage("VideoRecorder.Resume: FFmpeg进程不存在或已退出，无法恢复");
                    return;
                }

                try
                {
                    // 设置暂停事件为有信号状态，允许继续操作
                    _pauseEvent.Set();

                    // FFmpeg不直接支持暂停/恢复，这里仅记录状态
                    OnLogMessage("VideoRecorder.Resume: 录制已恢复（状态标记）");
                }
                catch (Exception ex)
                {
                    OnLogMessage($"VideoRecorder.Resume: 恢复录制时出错: {ex.Message}");
                }
            }

            /// <summary>
            /// 获取适合命令行的短路径形式
            /// </summary>
            private string GetShortPath(string longPath)
            {
                try
                {
                    OnLogMessage($"GetShortPath: 尝试获取短路径，原路径: {longPath}");

                    // 检查路径是否为空
                    if (string.IsNullOrEmpty(longPath))
                    {
                        OnLogMessage("GetShortPath: 输入路径为空");
                        return string.Empty;
                    }

                    // 确保目录存在
                    string directory = Path.GetDirectoryName(longPath);
                    if (!Directory.Exists(directory))
                    {
                        OnLogMessage($"GetShortPath: 目录不存在，尝试创建: {directory}");
                        Directory.CreateDirectory(directory);
                    }

                    // 使用Win32 API获取短路径名
                    StringBuilder shortPath = new StringBuilder(260); // MAX_PATH
                    int result = GetShortPathName(longPath, shortPath, shortPath.Capacity);

                    if (result != 0)
                    {
                        OnLogMessage($"GetShortPath: 成功获取短路径: {shortPath}");
                        return shortPath.ToString();
                    }
                    else
                    {
                        // 如果失败，返回原路径
                        OnLogMessage($"GetShortPath: 无法获取短路径，使用原路径: {longPath}");
                        return longPath;
                    }
                }
                catch (Exception ex)
                {
                    OnLogMessage($"GetShortPath: 获取短路径时出错: {ex.Message}");
                    return longPath;
                }
            }

            /// <summary>
            /// 查找FFmpeg可执行文件路径
            /// </summary>
            private string FindFFmpegPath()
            {
                try
                {
                    OnLogMessage("FindFFmpegPath: 开始搜索FFmpeg可执行文件");

                    // 首先检查应用程序目录
                    string appDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                    OnLogMessage($"FindFFmpegPath: 应用程序目录: {appDir}");

                    // 在应用程序目录中查找
                    string ffmpegPath = Path.Combine(appDir, "ffmpeg.exe");
                    if (File.Exists(ffmpegPath))
                    {
                        OnLogMessage($"FindFFmpegPath: 在应用程序目录中找到FFmpeg: {ffmpegPath}");
                        return ffmpegPath;
                    }

                    // 在子目录"bin"中查找
                    ffmpegPath = Path.Combine(appDir, "bin", "ffmpeg.exe");
                    if (File.Exists(ffmpegPath))
                    {
                        OnLogMessage($"FindFFmpegPath: 在bin子目录中找到FFmpeg: {ffmpegPath}");
                        return ffmpegPath;
                    }

                    // 在子目录"tools"中查找
                    ffmpegPath = Path.Combine(appDir, "tools", "ffmpeg.exe");
                    if (File.Exists(ffmpegPath))
                    {
                        OnLogMessage($"FindFFmpegPath: 在tools子目录中找到FFmpeg: {ffmpegPath}");
                        return ffmpegPath;
                    }

                    // 从PATH环境变量中查找
                    OnLogMessage("FindFFmpegPath: 在本地目录未找到，尝试从PATH环境变量中查找");
                    string pathEnv = Environment.GetEnvironmentVariable("PATH");
                    if (!string.IsNullOrEmpty(pathEnv))
                    {
                        string[] paths = pathEnv.Split(';');
                        foreach (string path in paths)
                        {
                            if (string.IsNullOrEmpty(path)) continue;

                            try
                            {
                                string testPath = Path.Combine(path, "ffmpeg.exe");
                                if (File.Exists(testPath))
                                {
                                    OnLogMessage($"FindFFmpegPath: 在PATH中找到FFmpeg: {testPath}");
                                    return testPath;
                                }
                            }
                            catch
                            {
                                // 忽略单个路径中可能出现的错误
                                continue;
                            }
                        }
                    }

                    // 如果以上方法都找不到，返回空字符串
                    OnLogMessage("FindFFmpegPath: 未能找到ffmpeg.exe");
                    return string.Empty;
                }
                catch (Exception ex)
                {
                    OnLogMessage($"FindFFmpegPath: 搜索FFmpeg时出错: {ex.Message}");
                    return string.Empty;
                }
            }

            /// <summary>
            /// FFmpeg进程退出事件处理
            /// </summary>
            private void FfmpegProcess_Exited(object sender, EventArgs e)
            {
                try
                {
                    // 获取进程退出代码
                    int exitCode = -1;
                    if (sender is Process process)
                    {
                        exitCode = process.ExitCode;
                    }

                    OnLogMessage($"FfmpegProcess_Exited: FFmpeg进程已退出。StopRequested: {_stopRequested}, ExitCode: {exitCode}");

                    // 根据退出代码判断是否正常结束
                    if (exitCode == 0)
                    {
                        OnLogMessage("FfmpegProcess_Exited: FFmpeg 进程正常退出 (ExitCode 0)。");
                    }
                    // 常见的一些FFmpeg因 'q' 命令或SIGINT而正常退出的代码 (不同平台和版本可能略有差异)
                    // 255 or -1 often mean killed by signal (like SIGINT from 'q') on some systems.
                    // 0xC000013A (STATUS_CONTROL_C_EXIT) is Windows specific for Ctrl+C.
                    else if (_stopRequested && (exitCode == 255 || exitCode == -1 || exitCode == 1 || exitCode == -1073741510))
                    {
                        OnLogMessage($"FfmpegProcess_Exited: FFmpeg 进程因接收到停止信号而退出 (ExitCode {exitCode})。这通常是正常的。");
                    }
                    else
                    {
                        OnLogMessage($"FfmpegProcess_Exited: FFmpeg 进程异常退出 (ExitCode {exitCode})。");
                        // 如果不是由用户主动请求停止的（例如，_stopRequested为false），并且退出码非0，则尝试修复
                        if (!_stopRequested)
                        {
                            OnLogMessage("FfmpegProcess_Exited: 检测到 FFmpeg 意外退出，尝试修复视频文件。");
                            RepairVideoFile(_filePath);
                        }
                        else
                        {
                            OnLogMessage("FfmpegProcess_Exited: FFmpeg 进程退出，但 StopRequested 为 true，可能是Kill()导致的，已在Stop()中尝试修复。");
                        }
                    }
                }
                catch (Exception ex)
                {
                    OnLogMessage($"FfmpegProcess_Exited: 处理FFmpeg退出事件时出错: {ex.Message}");
                }
                finally
                {
                    // 确保清理资源
                    SafeCleanupProcessResources();
                }
            }

            /// <summary>
            /// 处理FFmpeg的标准输出数据
            /// </summary>
            private void FfmpegProcess_OutputDataReceived(object sender, DataReceivedEventArgs e)
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    OnRecordingOutput(e.Data);
                }
            }

            /// <summary>
            /// 处理FFmpeg的标准错误输出数据（FFmpeg通常将进度信息输出到标准错误）
            /// </summary>
            private void FfmpegProcess_ErrorDataReceived(object sender, DataReceivedEventArgs e)
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    // 分析输出行，判断是普通进度信息还是错误信息
                    string data = e.Data.Trim();

                    // FFmpeg的进度信息通常包含这些关键词
                    bool isProgressInfo = data.Contains("frame=") || data.Contains("fps=") ||
                                          data.Contains("size=") || data.Contains("time=") ||
                                          data.Contains("bitrate=") || data.Contains("speed=");

                    if (isProgressInfo)
                    {
                        // 这是进度信息，使用输出处理方法
                        OnRecordingOutput(data);
                    }
                    else if (data.Contains("error") || data.Contains("Error") ||
                             data.Contains("failed") || data.Contains("Failed") ||
                             data.Contains("cannot") || data.Contains("Cannot") ||
                             data.Contains("unable") || data.Contains("Unable"))
                    {
                        // 这可能是错误信息
                        OnRecordingError(data);
                    }
                    else
                    {
                        // 其他FFmpeg信息，仍然记录为输出
                        OnRecordingOutput(data);
                    }
                }
            }
        }

        /// <summary>
        /// Win32 API 支持窗体点击穿透功能
        /// </summary>
        private static class Win32
        {
            public const int GWL_EXSTYLE = -20;
            public const int WS_EX_TRANSPARENT = 0x00000020;

            [DllImport("user32.dll")]
            public static extern int GetWindowLong(IntPtr hwnd, int index);

            [DllImport("user32.dll")]
            public static extern int SetWindowLong(IntPtr hwnd, int index, int newStyle);
        }
        #endregion

        #region RecorderConfig Class
        /// <summary>
        /// 录制器配置类
        /// </summary>
        public class RecorderConfig
        {
            public string SavePath { get; set; }
            public string FileNamePrefix { get; set; }
            public int FrameRate { get; set; }
            public int Quality { get; set; }
            public bool AutoStart { get; set; }
            public Size Resolution { get; set; } // 保留原有的分辨率属性
            public bool ShowRecordingBorder { get; set; } // 新增：是否显示录制区域边框
            public bool UseHEVC { get; set; } = false; // 新增：是否使用HEVC (H.265)编码
            public string CompressionPreset { get; set; } = "medium"; // 新增：压缩预设选项
            public bool EnableGpuAcceleration { get; set; } = true; // 新增：是否启用GPU加速，默认为true
            public string ScreenCaptureMethod { get; set; } = "gdigrab"; // 新增：录屏方式

            // 新增用于存储选定区域的属性
            public int SelectedRegionX { get; set; }
            public int SelectedRegionY { get; set; }
            public int SelectedRegionWidth { get; set; }
            public int SelectedRegionHeight { get; set; }

            // 为XML序列化器提供一个无参数的构造函数（如果尚不存在）
            public RecorderConfig()
            {
                // 设置默认值，防止加载空配置时出错
                SavePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyVideos), "LOL录像");
                FileNamePrefix = "LOL游戏录像";
                FrameRate = 30;
                Quality = 70;
                AutoStart = false;
                Resolution = new Size(1920, 1080);
                ShowRecordingBorder = true; // 默认显示边框
                UseHEVC = false; // 默认不使用HEVC
                CompressionPreset = "medium"; // 默认使用medium预设
                EnableGpuAcceleration = true; // 默认启用GPU加速
                SelectedRegionX = -1; // 使用-1表示无效或未设置
                SelectedRegionY = -1;
                SelectedRegionWidth = -1;
                SelectedRegionHeight = -1;
            }
        }
        #endregion

        #region GPU Detection
        /// <summary>
        /// 检测系统中主要的GPU类型
        /// </summary>
        /// <returns>GpuType枚举，指示显卡类型</returns>
        private GpuType DetectGpuType()
        {
            try
            {
                LogMessage("开始检测显卡类型...");
                // 优先检查独立显卡
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController WHERE AdapterCompatibility IS NOT NULL AND PNPDeviceID LIKE 'PCI\\\\VEN_%'"))
                {
                    foreach (ManagementObject mo in searcher.Get())
                    {
                        string description = mo["Description"]?.ToString()?.ToUpperInvariant();
                        string caption = mo["Caption"]?.ToString()?.ToUpperInvariant(); // Caption有时更准确
                        string adapterCompatibility = mo["AdapterCompatibility"]?.ToString()?.ToUpperInvariant();

                        LogMessage($"检测到显卡: Description='{description}', Caption='{caption}', AdapterCompatibility='{adapterCompatibility}'");

                        // 优先使用Caption或Description字段进行判断
                        string primaryIdentifier = !string.IsNullOrEmpty(caption) ? caption : description;

                        if (!string.IsNullOrEmpty(primaryIdentifier))
                        {
                            if (primaryIdentifier.Contains("NVIDIA")) { LogMessage("显卡类型判定为: NVIDIA"); return GpuType.NVIDIA; }
                            if (primaryIdentifier.Contains("AMD") || primaryIdentifier.Contains("RADEON") || primaryIdentifier.Contains("ATI")) { LogMessage("显卡类型判定为: AMD"); return GpuType.AMD; }
                            if (primaryIdentifier.Contains("INTEL")) { LogMessage("显卡类型判定为: Intel"); return GpuType.Intel; }
                        }
                        // 如果上述字段未明确，再尝试AdapterCompatibility
                        if (!string.IsNullOrEmpty(adapterCompatibility))
                        {
                             if (adapterCompatibility.Contains("NVIDIA")) { LogMessage("显卡类型判定为: NVIDIA (来自AdapterCompatibility)"); return GpuType.NVIDIA; }
                             if (adapterCompatibility.Contains("ADVANCED MICRO DEVICES") || adapterCompatibility.Contains("AMD")) { LogMessage("显卡类型判定为: AMD (来自AdapterCompatibility)"); return GpuType.AMD; }
                             if (adapterCompatibility.Contains("INTEL")) { LogMessage("显卡类型判定为: Intel (来自AdapterCompatibility)"); return GpuType.Intel; }
                        }
                    }
                }
                // 如果没有通过PCI VEN检测到，尝试通用的查询（可能包含集成显卡）
                 using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    foreach (ManagementObject mo in searcher.Get())
                    {
                        string description = mo["Description"]?.ToString()?.ToUpperInvariant();
                        string caption = mo["Caption"]?.ToString()?.ToUpperInvariant();
                        LogMessage($"再次检测到显卡 (通用查询): Description='{description}', Caption='{caption}'");
                        string primaryIdentifier = !string.IsNullOrEmpty(caption) ? caption : description;
                        if (!string.IsNullOrEmpty(primaryIdentifier))
                        {
                            if (primaryIdentifier.Contains("NVIDIA")) { LogMessage("显卡类型判定为: NVIDIA (通用查询)"); return GpuType.NVIDIA; }
                            if (primaryIdentifier.Contains("AMD") || primaryIdentifier.Contains("RADEON") || primaryIdentifier.Contains("ATI")) { LogMessage("显卡类型判定为: AMD (通用查询)"); return GpuType.AMD; }
                            if (primaryIdentifier.Contains("INTEL")) { LogMessage("显卡类型判定为: Intel (通用查询)"); return GpuType.Intel; }
                        }
                    }
                }
                LogMessage("未能明确识别显卡类型，将使用CPU编码。");
            }
            catch (Exception ex)
            {
                LogError($"检测显卡类型时发生错误: {ex.Message}");
            }
            return GpuType.None;
        }
        #endregion

        // 新增 VideoRecorder 的日志事件处理方法
        private void Recorder_LogMessageEvent(object sender, string message)
        {
            // 将来自 VideoRecorder 的日志消息添加到主窗体的日志列表和文件日志
            AddToLogList($"[Recorder] {message}");
            LogMessage($"[Recorder] {message}");
        }

        // HEVC选项变化响应处理
        private void cbUseHEVC_CheckedChanged(object sender, EventArgs e)
        {
            // 当用户选择HEVC时，显示警告提示对话框
            if (cbUseHEVC.Checked)
            {
                MessageBox.Show(
                    "您已选择HEVC(H.265)编码格式，请注意：\n\n" +
                    "1. HEVC产生更小的文件，但编码更慢且需要更多CPU资源\n" +
                    "2. 请确保您的播放设备或软件支持H.265格式\n" +
                    "3. 若遇播放问题，建议使用VLC或Potplayer播放器\n\n" +
                    "如CPU占用过高，请考虑改用普通H.264编码或选择更快的压缩预设",
                    "HEVC兼容性提示",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }

            // 更新选项状态
            _useHEVC = cbUseHEVC.Checked;

            // 更新相关控件状态
            lblHEVCCompatibility.Visible = cbUseHEVC.Checked;
        }

        // 初始隐藏HEVC兼容性提示（如果未选择HEVC）
        private void InitializeHEVCCompatibilityLabel()
        {
            if (lblHEVCCompatibility != null)
            {
                // 根据HEVC选项状态设置兼容性标签可见性
                lblHEVCCompatibility.Visible = cbUseHEVC != null && cbUseHEVC.Checked;
            }
        }

        // checkBox2 的 CheckedChanged 事件处理程序
        private void CheckBox2_CheckedChanged(object sender, EventArgs e)
        {
            if (this.checkBox2 != null)
            {
                _isLoggingEnabled = this.checkBox2.Checked;
                // 可以选择在这里记录一条特殊的日志，表明日志状态已更改
                // 但要注意这条日志本身也会受 _isLoggingEnabled 的影响，可能需要特殊处理或在UI上提示
                if (_isLoggingEnabled)
                {
                    LogMessage("日志记录已启用。", LogLevel.Info); // 这条会被记录
                    UpdateStatusInfo("日志记录已启用。");
                }
                else
                {
                    // 当日志关闭时，这条LogMessage不会被记录到文件
                    // LogMessage("日志记录已禁用。", LogLevel.Info);
                    // 可以直接更新UI状态
                    UpdateStatusInfo("日志记录已禁用。");
                    // 如果希望禁用时也记录一条到文件，需要临时打开开关或特殊处理
                }
            }
        }

        // 录屏方式选择变化事件处理
        private void ComboBoxScreenCaptureMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (comboBoxScreenCaptureMethod.SelectedItem != null)
            {
                _screenCaptureMethod = comboBoxScreenCaptureMethod.SelectedItem.ToString();
                LogMessage($"已切换录屏方式为: {_screenCaptureMethod}");
            }
        }
    }
}
#endregion


﻿using EasyHook;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WSockConnectHook;

namespace WindowsFormsApp1
{
    public partial class RemoteManagementFrom : Form
    {
        public static RemoteManagementFrom RemoteManagementfrom;
        public RemoteManagementFrom()
        {
            InitializeComponent();

        }


        class MyRemoteClass
        {
            public string IP { get; set; }
            public string User { get; set; }
            public string PassWord { get; set; }
            public string Remark { get; set; }

            public string Port { get; set; }
        }

        Dictionary<string, MyRemoteClass> MyRemoteValues = new Dictionary<string, MyRemoteClass>();


        public void ReadRemote()
        {
            try
            {
                if (!File.Exists("./RemoteSavedValues.json"))
                {
                    return;
                }
                // listView1.BeginUpdate();
                Util.ReadBatch(this, @".\Data\RDP.ini");
                var sr = File.ReadAllText("./RemoteSavedValues.json");
                MyRemoteValues = JsonConvert.DeserializeObject<Dictionary<string, MyRemoteClass>>(sr);

                if (MyRemoteValues != null)
                {
                    foreach (var i in MyRemoteValues)
                    {
                        var r = new ListViewItem();

                        r.SubItems[0].Text = i.Value.IP;
                        r.SubItems.Add(i.Value.User);
                        r.SubItems.Add(i.Value.PassWord);
                        r.SubItems.Add(i.Value.Remark);
                        r.SubItems.Add(i.Value.Port);
                        listView1.Items.Add(r);
                    }
                }
                else
                {
                    MyRemoteValues = new Dictionary<string, MyRemoteClass>();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
            }
            // listView1.EndUpdate();
        }

        public class Socks5ProxyHelp
        {
            private Socks5ProxyHelp() { }

            public static string[] errorMsgs = {
     "Operation completed successfully.",//操作成功完成
     "General SOCKS server failure.",//常规服务器失败
     "Connection not allowed by ruleset.",//连接不被允许
     "Network unreachable.",//网络不能到达
     "Host unreachable.",//主机不能到达
     "Connection refused.",//连接被拒绝
     "TTL expired.",//TTL期满
     "Command not supported.",//不支持的命令
     "Address type not supported.",//不被支持的地址类型
     "Unknown error."//未名的错误
    };



            // <summary>
            // 连接到socks5代理
            // </summary>
            // <param name="proxyAdress">代理服务期地址</param>
            // <param name="proxyPort">代理服务器端口</param>
            // <param name="destAddress">目标地址  Destination: 目的地,UDP命令时是本机的地址</param>
            // <param name="destPort">目标端口,UDP命令时是本机的UDP端口</param>
            // <param name="userName">用户名</param>
            // <param name="password">密码</param>
            // <returns>用于TCP连接的SOCKET</returns>
            public static void ConnectToSocks5Proxy(string proxyAdress, int proxyPort, string userName, string password, out string ErrorMsg)
            {
                ErrorMsg = "";
                IPAddress proxyIP = null;
                byte[] request = new byte[257]; //请求
                byte[] response = new byte[257];//应答
                ushort nIndex;
                try
                {
                    proxyIP = IPAddress.Parse(proxyAdress);
                }
                catch (FormatException)
                {
                    proxyIP = Dns.GetHostEntry(proxyAdress).AddressList[0];
                }
                // 解析 destAddress (要求是类似 "**************" 的string),否则是类似 "www.microsoft.com"的地址

                IPEndPoint proxyEndPoint = new IPEndPoint(proxyIP, proxyPort);
                Socket s = null;
                try
                {
                    s = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                    s.Connect(proxyEndPoint);//客户端连到服务器后，然后就发送请求来协商版本和认证方法： 

                    nIndex = 0;
                    request[nIndex++] = 0x05; // V 5.              [版本]
                    request[nIndex++] = 0x02; // 2种验证方式    [方法的数目]
                    request[nIndex++] = 0x00; // X'00'  不需要认证 [方法1]
                    request[nIndex++] = 0x02; // X'02'  用户名/密码[方法2]
                    s.Send(request, nIndex, SocketFlags.None);
                    // 收到2个字节的应答,填充到response中,如果不是两个字节,则抛出异常
                    int nGot = s.Receive(response, 2, SocketFlags.None);
                    if (nGot != 2) throw new Exception("从 proxy server 返回错误的应答.");

                    // 当前定义的方法有：
                    // X'00'  不需要认证
                    // X'01'     GSSAPI
                    // X'02'     用户名/密码
                    // X'03' -- X'7F'   由IANA分配
                    // X'80' -- X'FE'  为私人方法所保留的
                    // X'FF'      没有可以接受的方法
                    switch (response[1])
                    {
                        case 0xFF:
                            没有可以接受的方法(s);
                            break;
                        case 0x02:
                            用户名密码验证(s, userName, password);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    ErrorMsg = ex.Message;
                }
                finally
                {
                    if (s != null)
                    {
                        s.Close();
                        s.Dispose();
                        s = null;
                    }
                }
                //SendUDP.UDP命令(s);
            }
            static void 没有可以接受的方法(Socket s)
            {
                // 客户端没有一中验证方式能被服务器接受,则关闭该socket.
                s.Close();
                throw new Exception("客户端没有一中验证方式能被代理服务器接受.");
            }
            static bool 用户名密码验证(Socket s, string userName, string password)
            {
                byte[] request = new byte[257]; //请求
                byte[] response = new byte[257];//应答
                ushort nIndex;
                byte[] rawBytes;
                nIndex = 0;

                request[nIndex++] = 0x05; // Version 5.   不清楚为什么报文的首字节是0x01(按照惯例应当是0x05)

                // 加入 user name
                request[nIndex++] = (byte)userName.Length;  //一个字节,放UserName的长度
                rawBytes = Encoding.Default.GetBytes(userName);
                rawBytes.CopyTo(request, nIndex);          //将userName 加入
                nIndex += (ushort)rawBytes.Length;

                // 加入 password
                request[nIndex++] = (byte)password.Length;  //一个字节,放PassWord的长度
                rawBytes = Encoding.Default.GetBytes(password);
                rawBytes.CopyTo(request, nIndex);
                nIndex += (ushort)rawBytes.Length;

                // 发送 Username/Password 请求
                s.Send(request, nIndex, SocketFlags.None);
                int nGot = s.Receive(response, 2, SocketFlags.None);
                if (nGot != 2)
                {
                    throw new Exception("从 proxy server 返回错误的应答.");
                }
                if (response[1] != 0x00) //返回如下的报文字节序列映像为：0x01 | 验证结果标志-->0x00 验证通过，其余均表示有故障
                {
                    throw new Exception("账号或密码错误！");
                }
                Console.WriteLine("okpass");
                return true;
            }
        }
        private void Form2_Load(object sender, EventArgs e)
        {
            ReadRemote();
        }
        private string key = "nichoubi";
        private void button1_Click(object sender, EventArgs e)
        {
            if (!MyRemoteValues.ContainsKey(IPtextBox.Text))
            {
                var password = Util.XXTEA2.Encrypt(PassWordtextBox.Text, key);

                var r = new ListViewItem();

                r.SubItems[0].Text = IPtextBox.Text;
                r.SubItems.Add(UsertextBox.Text);
                r.SubItems.Add(password);
                r.SubItems.Add(textBox4.Text);
                r.SubItems.Add(textBox3.Text);
                listView1.Items.Add(r);


                var myRemote1 = new MyRemoteClass();
                myRemote1.IP = IPtextBox.Text;
                myRemote1.User = UsertextBox.Text;
                myRemote1.PassWord = password;
                myRemote1.Remark = textBox4.Text;
                myRemote1.Port = textBox3.Text;


                MyRemoteValues.Add(IPtextBox.Text, myRemote1);

                File.WriteAllText("./RemoteSavedValues.json", "");
                File.WriteAllText("./RemoteSavedValues.json", JsonConvert.SerializeObject(MyRemoteValues));
            }

        }
        /// <summary>
        /// 判断VK是否已经连接!
        /// </summary>
        /// <returns></returns>
        private bool Getvk()
        {
            if (!checkBox2.Checked)
                return true;

            NetworkInterface[] adapters = NetworkInterface.GetAllNetworkInterfaces();//获取本地计算机上网络接口的对象
            Console.WriteLine("适配器个数：" + adapters.Length);

            string CUOW = "111";

            Socks5ProxyHelp.ConnectToSocks5Proxy(textBox_IP.Text, int.Parse(textBox_端口.Text), "zhanghao", "mima", out CUOW);

            Console.WriteLine(CUOW);

            if (CUOW != "")
            {
                foreach (NetworkInterface adapter in adapters)
                {
                    if (adapter.Description == "WireGuard Tunnel" && adapter.OperationalStatus.ToString() == "Up")
                    {
                        //Console.WriteLine(adapter.OperationalStatus.ToString());
                        return true;
                    }
                }
            }
            else
            {
                return true;
            }


            return false;
            // Console.ReadKey();
        }
        private bool checkPortEnable(string _ip, int _port)
        {
            //将IP和端口替换成为你要检测的
            string ipAddress = _ip;
            int portNum = _port;
            IPAddress ip = IPAddress.Parse(ipAddress);
            IPEndPoint point = new IPEndPoint(ip, portNum);

            bool _portEnable = false;
            try
            {
                using (Socket sock = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp))
                {
                    sock.Connect(point);
                    //Console.WriteLine("连接{0}成功!", point);
                    sock.Close();

                    _portEnable = true;
                }
            }
            catch (SocketException e)
            {
                //Console.WriteLine("连接{0}失败", point);
                _portEnable = false;
            }
            return _portEnable;
        }
        string SocKS_IP = "", SocCK_端口 = "";
        private void listView1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                var itim1 = listView1.SelectedItems[0];

                string IP = itim1.SubItems[0].Text;

                int Myport = int.Parse(itim1.SubItems[4].Text);

               var aaa =Util.XXTEA2.Decrypt(itim1.SubItems[2].Text, key);
                Debug.WriteLine(aaa);
                if (listView1.SelectedIndices.Count > 0)
                {
                    if (Getvk())
                    {

                        int lianj = 1;
                        if (checkBox2.Checked)
                        {
                            if (SocKS_IP != textBox_IP.Text || SocCK_端口 != textBox_端口.Text)
                            {
                                Console.WriteLine("连接SOCks5!");

                                SocKS_IP = textBox_IP.Text;

                                SocCK_端口 = textBox_端口.Text;

                                HookManager.Instance.InitHooks(SocKS_IP, SocCK_端口);

                                RemoteHooking.WakeUpProcess();
                            }

                        }
                        else
                        {
                            if (SocKS_IP == "")
                                lianj = (int)MessageBox.Show("目前是直连服务器存在危险,确定连接?", "连接提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk);
                        }

                        if (lianj == 1)
                        {
                            if (!checkPortEnable(IP, Myport))
                            {
                                Form1.form1.T_msg("无法连接此服务器!", 2, 3);
                                return;
                            }


                            
                               
                            var remoteFrom = new RemoteFrom(itim1.SubItems[0].Text, itim1.SubItems[1].Text, itim1.SubItems[2].Text, int.Parse(itim1.SubItems[4].Text), int.Parse(textBox1.Text), int.Parse(textBox2.Text), checkBox1.Checked);

                            remoteFrom.Show();
                        }


                    }
                    else
                    {
                        Form1.form1.T_msg("未连接到SOCKS5代理", 2, 5);
                    }
                }


            }
        }

        ListViewItem ListSelected = null;
        private void listView1_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (listView1.SelectedIndices.Count > 0)
                {
                    ListSelected = listView1.SelectedItems[0];
                    var r = listView1.SelectedItems[0];
                    IPtextBox.Text = r.SubItems[0].Text;
                    UsertextBox.Text = r.SubItems[1].Text;
                    PassWordtextBox.Text = r.SubItems[2].Text;
                    textBox4.Text = r.SubItems[3].Text;
                    textBox3.Text = r.SubItems[4].Text;
                }
            }

            else if (e.Button == MouseButtons.Right)
            {
                if (listView1.SelectedIndices.Count > 0)
                {
                    ListSelected = listView1.SelectedItems[0];
                    var r = listView1.SelectedItems[0];
                    IPtextBox.Text = r.SubItems[0].Text;
                    UsertextBox.Text = r.SubItems[1].Text;
                    PassWordtextBox.Text = r.SubItems[2].Text;
                    textBox4.Text = r.SubItems[3].Text;
                    textBox3.Text = r.SubItems[4].Text;

                    contextMenuStrip1.Show(listView1, new Point(e.X, e.Y));
                }


            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (listView1.SelectedIndices.Count > 0)
            {
                if (ListSelected == listView1.SelectedItems[0])
                {
                    var r = listView1.SelectedItems[0];
                    r.SubItems[0].Text = IPtextBox.Text;
                    r.SubItems[1].Text = UsertextBox.Text;
                    if (r.SubItems[2].Text != PassWordtextBox.Text)
                    {
                        var password = Util.XXTEA2.Encrypt(PassWordtextBox.Text, key);

                        r.SubItems[2].Text = password;
                    }
                    r.SubItems[3].Text = textBox4.Text;
                    r.SubItems[4].Text = textBox3.Text;



                    var myRemote1 = new MyRemoteClass();
                    myRemote1.IP = r.SubItems[0].Text;
                    myRemote1.User = r.SubItems[1].Text;
                    myRemote1.PassWord = r.SubItems[2].Text;
                    myRemote1.Remark = r.SubItems[3].Text;
                    myRemote1.Port = r.SubItems[4].Text;
                    MyRemoteValues[myRemote1.IP] = myRemote1;
                    // MyRemoteValues.Add(myRemote1.IP, myRemote1);

                    File.WriteAllText("./RemoteSavedValues.json", "");
                    File.WriteAllText("./RemoteSavedValues.json", JsonConvert.SerializeObject(MyRemoteValues));
                }

            }
        }

        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            Util.BulkSave(this, "", @".\Data\RDP.ini");

            if (!checkBox2.Checked)
            {

                if (SocKS_IP != "")
                {
                    MessageBox.Show("你之前使用过代理连接,这次取消代理连接-需要重启软件!");
                    //开启新的实例
                    Process.Start(Application.ExecutablePath);

                    //关闭当前实例
                    Process.GetCurrentProcess().Kill();
                }

            }
        }

        private void RemoteManagementFrom_FormClosing(object sender, FormClosingEventArgs e)
        {
            Util.BulkSave(RemoteManagementfrom, "", @".\Data\RDP.ini");
        }

        private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listView1.SelectedIndices.Count > 0)
            {
                if (ListSelected == listView1.SelectedItems[0])
                {

                    MyRemoteValues.Remove(ListSelected.SubItems[0].Text);
                    listView1.Items.Remove(ListSelected);

                    File.WriteAllText("./RemoteSavedValues.json", "");
                    File.WriteAllText("./RemoteSavedValues.json", JsonConvert.SerializeObject(MyRemoteValues));
                }
            }
        }


    }
}

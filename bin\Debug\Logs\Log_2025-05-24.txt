2025-05-24 00:14:21: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:14:21: WebDAV服务器: www1.movemama.cn
2025-05-24 00:14:21: 目标文件夹: 便捷
2025-05-24 00:14:21: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:14:21: 已创建本地文件夹
2025-05-24 00:14:21: 已设置WebClient认证信息
2025-05-24 00:14:21: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:14:22: PROPFIND请求响应状态: 207
2025-05-24 00:14:22: 成功获取文件夹内容列表
2025-05-24 00:14:22: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-14-22.txt
2025-05-24 00:14:22: 开始解析WebDAV XML响应...
2025-05-24 00:14:22: XML分割为 115 行
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/
2025-05-24 00:14:22: 跳过文件夹路径: /%e4%be%bf%e6%8d%b7/
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/%e5%85%8d%e9%87%8d%e5%90%af-%e5%90%af%e5%8a%a8VanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-24 00:14:22: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/%e9%bb%91%e5%8f%b7.txt
2025-05-24 00:14:22: 添加文件: 黑号.txt
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/%e7%bd%91%e5%90%a7%e5%85%8d%e9%87%8d%e5%90%af%e5%8a%a0%e8%bd%bdVanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-24 00:14:22: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/%e6%94%b9%e7%9a%84%e5%86%85%e5%ae%b9.txt
2025-05-24 00:14:22: 添加文件: 改的内容.txt
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-24 00:14:22: 跳过文件夹路径: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-24 00:14:22: 找到href: /%e4%be%bf%e6%8d%b7/setup.exe
2025-05-24 00:14:22: 添加文件: setup.exe
2025-05-24 00:14:22: 最终解析到 5 个文件
2025-05-24 00:14:22: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-24 00:14:22: 文件列表: 黑号.txt
2025-05-24 00:14:22: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:14:22: 文件列表: 改的内容.txt
2025-05-24 00:14:22: 文件列表: setup.exe
2025-05-24 00:14:22: 解析到 5 个项目
2025-05-24 00:14:24: 文件夹中包含: 5 个文件, 0 个子文件夹
2025-05-24 00:14:24: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-24 00:14:24:   源URL: http://www1.movemama.cn/便捷/免重启-启动VanGuard驱动.bat
2025-05-24 00:14:24:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-24 00:14:24: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-24 00:14:24:   文件大小: 1,193 字节 (1.17 KB)
2025-05-24 00:14:24:   下载耗时: 0.36 秒
2025-05-24 00:14:24: 正在下载: 黑号.txt
2025-05-24 00:14:24:   源URL: http://www1.movemama.cn/便捷/黑号.txt
2025-05-24 00:14:24:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-24 00:14:24: 下载完成: 黑号.txt
2025-05-24 00:14:24:   文件大小: 125 字节 (0.12 KB)
2025-05-24 00:14:24:   下载耗时: 0.36 秒
2025-05-24 00:14:24: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:14:24:   源URL: http://www1.movemama.cn/便捷/网吧免重启加载VanGuard驱动.bat
2025-05-24 00:14:24:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-24 00:14:25: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:14:25:   文件大小: 1,884 字节 (1.84 KB)
2025-05-24 00:14:25:   下载耗时: 0.36 秒
2025-05-24 00:14:25: 正在下载: 改的内容.txt
2025-05-24 00:14:25:   源URL: http://www1.movemama.cn/便捷/改的内容.txt
2025-05-24 00:14:25:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-24 00:14:25: 下载完成: 改的内容.txt
2025-05-24 00:14:25:   文件大小: 1,750 字节 (1.71 KB)
2025-05-24 00:14:25:   下载耗时: 0.36 秒
2025-05-24 00:14:25: 正在下载: setup.exe
2025-05-24 00:14:25:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-24 00:14:25:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-24 00:14:33: 下载完成: setup.exe
2025-05-24 00:14:33:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-24 00:14:33:   下载耗时: 7.69 秒
2025-05-24 00:14:33: === 下载完成统计 ===
2025-05-24 00:14:33: 成功下载: 5 个文件
2025-05-24 00:14:33: 下载失败: 0 个文件
2025-05-24 00:14:33: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:14:33: WebDAV下载操作完成
2025-05-24 00:24:26: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:24:26: WebDAV服务器: www1.movemama.cn
2025-05-24 00:24:26: 目标文件夹: 便捷
2025-05-24 00:24:26: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:24:26: 已创建本地文件夹
2025-05-24 00:24:26: 已设置WebClient认证信息
2025-05-24 00:24:26: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:24:27: PROPFIND请求响应状态: 207
2025-05-24 00:24:27: 成功获取文件夹内容列表
2025-05-24 00:24:27: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-24-27.txt
2025-05-24 00:24:27: 开始解析WebDAV XML响应...
2025-05-24 00:24:27: XML分割为 115 行
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/
2025-05-24 00:24:27: 发现文件夹路径: /%e4%be%bf%e6%8d%b7/
2025-05-24 00:24:27: 添加文件夹: 便捷
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/%e5%85%8d%e9%87%8d%e5%90%af-%e5%90%af%e5%8a%a8VanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-24 00:24:27: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/%e9%bb%91%e5%8f%b7.txt
2025-05-24 00:24:27: 添加文件: 黑号.txt
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/%e7%bd%91%e5%90%a7%e5%85%8d%e9%87%8d%e5%90%af%e5%8a%a0%e8%bd%bdVanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-24 00:24:27: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/%e6%94%b9%e7%9a%84%e5%86%85%e5%ae%b9.txt
2025-05-24 00:24:27: 添加文件: 改的内容.txt
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-24 00:24:27: 发现文件夹路径: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-24 00:24:27: 添加文件夹: 脚本
2025-05-24 00:24:27: 找到href: /%e4%be%bf%e6%8d%b7/setup.exe
2025-05-24 00:24:27: 添加文件: setup.exe
2025-05-24 00:24:27: 最终解析到 7 个文件
2025-05-24 00:24:27: 文件列表: 便捷
2025-05-24 00:24:27: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-24 00:24:27: 文件列表: 黑号.txt
2025-05-24 00:24:27: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:24:27: 文件列表: 改的内容.txt
2025-05-24 00:24:27: 文件列表: 脚本
2025-05-24 00:24:27: 文件列表: setup.exe
2025-05-24 00:24:27: 解析到 7 个项目
2025-05-24 00:24:34: 文件夹中包含: 7 个文件, 0 个子文件夹
2025-05-24 00:24:34: 正在下载: 便捷
2025-05-24 00:24:34:   源URL: http://www1.movemama.cn/便捷/便捷
2025-05-24 00:24:34:   目标路径: C:\Users\<USER>\Desktop\便捷\便捷
2025-05-24 00:24:34: 下载文件 便捷 失败: 远程服务器返回错误: (404) 未找到。
2025-05-24 00:24:34: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-24 00:24:34:   源URL: http://www1.movemama.cn/便捷/免重启-启动VanGuard驱动.bat
2025-05-24 00:24:34:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-24 00:24:35: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-24 00:24:35:   文件大小: 1,193 字节 (1.17 KB)
2025-05-24 00:24:35:   下载耗时: 0.68 秒
2025-05-24 00:24:35: 正在下载: 黑号.txt
2025-05-24 00:24:35:   源URL: http://www1.movemama.cn/便捷/黑号.txt
2025-05-24 00:24:35:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-24 00:24:36: 下载完成: 黑号.txt
2025-05-24 00:24:36:   文件大小: 125 字节 (0.12 KB)
2025-05-24 00:24:36:   下载耗时: 0.69 秒
2025-05-24 00:24:36: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:24:36:   源URL: http://www1.movemama.cn/便捷/网吧免重启加载VanGuard驱动.bat
2025-05-24 00:24:36:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-24 00:24:36: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:24:36:   文件大小: 1,884 字节 (1.84 KB)
2025-05-24 00:24:36:   下载耗时: 0.68 秒
2025-05-24 00:24:36: 正在下载: 改的内容.txt
2025-05-24 00:24:36:   源URL: http://www1.movemama.cn/便捷/改的内容.txt
2025-05-24 00:24:36:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-24 00:24:37: 下载完成: 改的内容.txt
2025-05-24 00:24:37:   文件大小: 1,750 字节 (1.71 KB)
2025-05-24 00:24:37:   下载耗时: 0.72 秒
2025-05-24 00:24:37: 正在下载: 脚本
2025-05-24 00:24:37:   源URL: http://www1.movemama.cn/便捷/脚本
2025-05-24 00:24:37:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本
2025-05-24 00:24:38: 下载文件 脚本 失败: 远程服务器返回错误: (401) 未经授权。
2025-05-24 00:24:38: 正在下载: setup.exe
2025-05-24 00:24:38:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-24 00:24:38:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-24 00:24:46: 下载完成: setup.exe
2025-05-24 00:24:46:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-24 00:24:46:   下载耗时: 7.56 秒
2025-05-24 00:24:46: === 下载完成统计 ===
2025-05-24 00:24:46: 成功下载: 5 个文件
2025-05-24 00:24:46: 下载失败: 2 个文件
2025-05-24 00:24:46: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:24:46: WebDAV下载操作完成
2025-05-24 00:29:58: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:29:58: WebDAV服务器: www1.movemama.cn
2025-05-24 00:29:58: 目标文件夹: 便捷
2025-05-24 00:29:58: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:29:58: 本地文件夹已存在
2025-05-24 00:29:58: 已设置WebClient认证信息
2025-05-24 00:29:58: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:29:59: 访问文件夹 http://www1.movemama.cn/便捷/ 失败 (WebException): 基础连接已经关闭: 服务器关闭了本应保持活动状态的连接。
2025-05-24 00:29:59: === 下载完成统计 ===
2025-05-24 00:29:59: 成功下载: 0 个文件
2025-05-24 00:29:59: 下载失败: 0 个文件
2025-05-24 00:29:59: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:29:59: WebDAV下载操作完成
2025-05-24 00:31:43: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:31:43: WebDAV服务器: www1.movemama.cn
2025-05-24 00:31:43: 目标文件夹: 便捷
2025-05-24 00:31:43: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:31:43: 已创建本地文件夹
2025-05-24 00:31:43: 已设置WebClient认证信息
2025-05-24 00:31:43: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:31:43: 访问文件夹 http://www1.movemama.cn/便捷/ 失败 (WebException): 基础连接已经关闭: 服务器关闭了本应保持活动状态的连接。
2025-05-24 00:31:43: === 下载完成统计 ===
2025-05-24 00:31:43: 成功下载: 0 个文件
2025-05-24 00:31:43: 下载失败: 0 个文件
2025-05-24 00:31:43: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:31:43: WebDAV下载操作完成
2025-05-24 00:32:04: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:32:04: WebDAV服务器: www1.movemama.cn
2025-05-24 00:32:04: 目标文件夹: 便捷
2025-05-24 00:32:04: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:32:04: 本地文件夹已存在
2025-05-24 00:32:04: 已设置WebClient认证信息
2025-05-24 00:32:04: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:32:06: PROPFIND请求响应状态: 207
2025-05-24 00:32:06: 成功获取文件夹内容列表
2025-05-24 00:32:06: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-32-06.txt
2025-05-24 00:32:06: 开始解析WebDAV XML响应...
2025-05-24 00:32:06: 解析到 7 个WebDAV项目
2025-05-24 00:32:06: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06: 添加文件: 黑号.txt
2025-05-24 00:32:06: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:06: 添加文件: 改的内容.txt
2025-05-24 00:32:06: 添加文件夹: 脚本
2025-05-24 00:32:06: 添加文件: setup.exe
2025-05-24 00:32:06: 最终解析到 6 个文件
2025-05-24 00:32:06: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06: 文件列表: 黑号.txt
2025-05-24 00:32:06: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:06: 文件列表: 改的内容.txt
2025-05-24 00:32:06: 文件列表: 脚本
2025-05-24 00:32:06: 文件列表: setup.exe
2025-05-24 00:32:06: 解析到 6 个项目
2025-05-24 00:32:06: 识别为文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06: 识别为文件: 黑号.txt
2025-05-24 00:32:06: 识别为文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:06: 识别为文件: 改的内容.txt
2025-05-24 00:32:06: 识别为文件夹: 脚本
2025-05-24 00:32:06: 识别为文件: setup.exe
2025-05-24 00:32:06: 文件夹中包含: 5 个文件, 1 个子文件夹
2025-05-24 00:32:06: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06:   源URL: http://www1.movemama.cn/便捷/免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-24 00:32:06:   文件大小: 1,193 字节 (1.17 KB)
2025-05-24 00:32:06:   下载耗时: 0.35 秒
2025-05-24 00:32:06: 正在下载: 黑号.txt
2025-05-24 00:32:06:   源URL: http://www1.movemama.cn/便捷/黑号.txt
2025-05-24 00:32:06:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-24 00:32:06: 下载完成: 黑号.txt
2025-05-24 00:32:06:   文件大小: 125 字节 (0.12 KB)
2025-05-24 00:32:06:   下载耗时: 0.35 秒
2025-05-24 00:32:06: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:06:   源URL: http://www1.movemama.cn/便捷/网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:06:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:07: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:32:07:   文件大小: 1,884 字节 (1.84 KB)
2025-05-24 00:32:07:   下载耗时: 0.35 秒
2025-05-24 00:32:07: 正在下载: 改的内容.txt
2025-05-24 00:32:07:   源URL: http://www1.movemama.cn/便捷/改的内容.txt
2025-05-24 00:32:07:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-24 00:32:07: 下载完成: 改的内容.txt
2025-05-24 00:32:07:   文件大小: 1,750 字节 (1.71 KB)
2025-05-24 00:32:07:   下载耗时: 0.36 秒
2025-05-24 00:32:07: 正在下载: setup.exe
2025-05-24 00:32:07:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-24 00:32:07:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-24 00:32:15: 下载完成: setup.exe
2025-05-24 00:32:15:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-24 00:32:15:   下载耗时: 7.54 秒
2025-05-24 00:32:15: 处理子文件夹: 脚本
2025-05-24 00:32:15:   源URL: http://www1.movemama.cn/便捷/脚本/
2025-05-24 00:32:15:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本
2025-05-24 00:32:15:   已创建本地子文件夹: C:\Users\<USER>\Desktop\便捷\脚本
2025-05-24 00:32:15: 正在获取文件夹内容: http://www1.movemama.cn/便捷/脚本/
2025-05-24 00:32:15: PROPFIND请求响应状态: 207
2025-05-24 00:32:15: 成功获取文件夹内容列表
2025-05-24 00:32:15: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-32-15.txt
2025-05-24 00:32:15: 开始解析WebDAV XML响应...
2025-05-24 00:32:15: 解析到 4 个WebDAV项目
2025-05-24 00:32:15: 添加文件夹: EngineSoul
2025-05-24 00:32:15: 添加文件: League of Legends.exe
2025-05-24 00:32:15: 添加文件: ES.dll
2025-05-24 00:32:15: 最终解析到 3 个文件
2025-05-24 00:32:15: 文件列表: EngineSoul
2025-05-24 00:32:15: 文件列表: League of Legends.exe
2025-05-24 00:32:15: 文件列表: ES.dll
2025-05-24 00:32:15: 解析到 3 个项目
2025-05-24 00:32:15: 识别为文件夹: EngineSoul
2025-05-24 00:32:15: 识别为文件: League of Legends.exe
2025-05-24 00:32:15: 识别为文件: ES.dll
2025-05-24 00:32:15: 文件夹中包含: 2 个文件, 1 个子文件夹
2025-05-24 00:32:15: 正在下载: League of Legends.exe
2025-05-24 00:32:15:   源URL: http://www1.movemama.cn/便捷/脚本/League of Legends.exe
2025-05-24 00:32:15:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\League of Legends.exe
2025-05-24 00:32:20: 下载完成: League of Legends.exe
2025-05-24 00:32:20:   文件大小: 31,058,304 字节 (30330.38 KB)
2025-05-24 00:32:20:   下载耗时: 4.79 秒
2025-05-24 00:32:20: 正在下载: ES.dll
2025-05-24 00:32:20:   源URL: http://www1.movemama.cn/便捷/脚本/ES.dll
2025-05-24 00:32:20:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\ES.dll
2025-05-24 00:32:30: 下载完成: ES.dll
2025-05-24 00:32:30:   文件大小: 92,838,400 字节 (90662.50 KB)
2025-05-24 00:32:30:   下载耗时: 10.00 秒
2025-05-24 00:32:30: 处理子文件夹: EngineSoul
2025-05-24 00:32:30:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/
2025-05-24 00:32:30:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul
2025-05-24 00:32:30:   已创建本地子文件夹: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul
2025-05-24 00:32:30: 正在获取文件夹内容: http://www1.movemama.cn/便捷/脚本/EngineSoul/
2025-05-24 00:32:31: PROPFIND请求响应状态: 207
2025-05-24 00:32:31: 成功获取文件夹内容列表
2025-05-24 00:32:31: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-32-31.txt
2025-05-24 00:32:31: 开始解析WebDAV XML响应...
2025-05-24 00:32:31: 解析到 7 个WebDAV项目
2025-05-24 00:32:31: 添加文件: Img.zip
2025-05-24 00:32:31: 添加文件: T.data
2025-05-24 00:32:31: 添加文件: Temp.data
2025-05-24 00:32:31: 添加文件: DATA1
2025-05-24 00:32:31: 添加文件: DATA2
2025-05-24 00:32:31: 添加文件: DATA4
2025-05-24 00:32:31: 最终解析到 6 个文件
2025-05-24 00:32:31: 文件列表: Img.zip
2025-05-24 00:32:31: 文件列表: T.data
2025-05-24 00:32:31: 文件列表: Temp.data
2025-05-24 00:32:31: 文件列表: DATA1
2025-05-24 00:32:31: 文件列表: DATA2
2025-05-24 00:32:31: 文件列表: DATA4
2025-05-24 00:32:31: 解析到 6 个项目
2025-05-24 00:32:31: 识别为文件: Img.zip
2025-05-24 00:32:31: 识别为文件: T.data
2025-05-24 00:32:31: 识别为文件: Temp.data
2025-05-24 00:32:31: 识别为文件: DATA1
2025-05-24 00:32:31: 识别为文件: DATA2
2025-05-24 00:32:31: 识别为文件: DATA4
2025-05-24 00:32:31: 文件夹中包含: 6 个文件, 0 个子文件夹
2025-05-24 00:32:31: 正在下载: Img.zip
2025-05-24 00:32:31:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/Img.zip
2025-05-24 00:32:31:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\Img.zip
2025-05-24 00:32:33: 下载完成: Img.zip
2025-05-24 00:32:33:   文件大小: 2,998,994 字节 (2928.71 KB)
2025-05-24 00:32:33:   下载耗时: 1.85 秒
2025-05-24 00:32:33: 正在下载: T.data
2025-05-24 00:32:33:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/T.data
2025-05-24 00:32:33:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\T.data
2025-05-24 00:32:33: 下载完成: T.data
2025-05-24 00:32:33:   文件大小: 198 字节 (0.19 KB)
2025-05-24 00:32:33:   下载耗时: 0.36 秒
2025-05-24 00:32:33: 正在下载: Temp.data
2025-05-24 00:32:33:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/Temp.data
2025-05-24 00:32:33:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\Temp.data
2025-05-24 00:32:34: 下载完成: Temp.data
2025-05-24 00:32:34:   文件大小: 662,212 字节 (646.69 KB)
2025-05-24 00:32:34:   下载耗时: 0.42 秒
2025-05-24 00:32:34: 正在下载: DATA1
2025-05-24 00:32:34:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA1
2025-05-24 00:32:34:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA1
2025-05-24 00:32:38: 下载完成: DATA1
2025-05-24 00:32:38:   文件大小: 34,647,072 字节 (33835.03 KB)
2025-05-24 00:32:38:   下载耗时: 3.98 秒
2025-05-24 00:32:38: 正在下载: DATA2
2025-05-24 00:32:38:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA2
2025-05-24 00:32:38:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA2
2025-05-24 00:32:39: 下载完成: DATA2
2025-05-24 00:32:39:   文件大小: 5,956,096 字节 (5816.50 KB)
2025-05-24 00:32:39:   下载耗时: 0.97 秒
2025-05-24 00:32:39: 正在下载: DATA4
2025-05-24 00:32:39:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA4
2025-05-24 00:32:39:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA4
2025-05-24 00:32:39: 下载完成: DATA4
2025-05-24 00:32:39:   文件大小: 3,383,808 字节 (3304.50 KB)
2025-05-24 00:32:39:   下载耗时: 0.71 秒
2025-05-24 00:32:39: === 下载完成统计 ===
2025-05-24 00:32:39: 成功下载: 13 个文件
2025-05-24 00:32:39: 下载失败: 0 个文件
2025-05-24 00:32:39: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:32:39: WebDAV下载操作完成
2025-05-24 00:39:20: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:39:20: WebDAV服务器: www1.movemama.cn
2025-05-24 00:39:20: 目标文件夹: 便捷
2025-05-24 00:39:20: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:39:20: 本地文件夹已存在
2025-05-24 00:39:20: 已设置WebClient认证信息
2025-05-24 00:39:20: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:39:21: PROPFIND请求响应状态: 207
2025-05-24 00:39:21: 成功获取文件夹内容列表
2025-05-24 00:39:21: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-39-21.txt
2025-05-24 00:39:21: 开始解析WebDAV XML响应...
2025-05-24 00:39:21: 解析到 7 个WebDAV项目
2025-05-24 00:39:21: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:39:21: 添加文件: 黑号.txt
2025-05-24 00:39:21: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:21: 添加文件: 改的内容.txt
2025-05-24 00:39:21: 添加文件夹: 脚本
2025-05-24 00:39:21: 添加文件: setup.exe
2025-05-24 00:39:21: 最终解析到 6 个文件
2025-05-24 00:39:21: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-24 00:39:21: 文件列表: 黑号.txt
2025-05-24 00:39:21: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:21: 文件列表: 改的内容.txt
2025-05-24 00:39:21: 文件列表: 脚本
2025-05-24 00:39:21: 文件列表: setup.exe
2025-05-24 00:39:21: 解析到 6 个项目
2025-05-24 00:39:21: 识别为文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:39:21: 识别为文件: 黑号.txt
2025-05-24 00:39:21: 识别为文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:21: 识别为文件: 改的内容.txt
2025-05-24 00:39:21: 识别为文件夹: 脚本
2025-05-24 00:39:21: 识别为文件: setup.exe
2025-05-24 00:39:21: 文件夹中包含: 5 个文件, 1 个子文件夹
2025-05-24 00:39:21: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-24 00:39:21:   源URL: http://www1.movemama.cn/便捷/免重启-启动VanGuard驱动.bat
2025-05-24 00:39:21:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-24 00:39:22: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-24 00:39:22:   文件大小: 1,193 字节 (1.17 KB)
2025-05-24 00:39:22:   下载耗时: 0.70 秒
2025-05-24 00:39:22: 正在下载: 黑号.txt
2025-05-24 00:39:22:   源URL: http://www1.movemama.cn/便捷/黑号.txt
2025-05-24 00:39:22:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-24 00:39:23: 下载完成: 黑号.txt
2025-05-24 00:39:23:   文件大小: 125 字节 (0.12 KB)
2025-05-24 00:39:23:   下载耗时: 0.74 秒
2025-05-24 00:39:23: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:23:   源URL: http://www1.movemama.cn/便捷/网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:23:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:24: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:39:24:   文件大小: 1,884 字节 (1.84 KB)
2025-05-24 00:39:24:   下载耗时: 0.70 秒
2025-05-24 00:39:24: 正在下载: 改的内容.txt
2025-05-24 00:39:24:   源URL: http://www1.movemama.cn/便捷/改的内容.txt
2025-05-24 00:39:24:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-24 00:39:24: 下载完成: 改的内容.txt
2025-05-24 00:39:24:   文件大小: 1,750 字节 (1.71 KB)
2025-05-24 00:39:24:   下载耗时: 0.67 秒
2025-05-24 00:39:24: 正在下载: setup.exe
2025-05-24 00:39:24:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-24 00:39:24:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-24 00:39:32: 下载完成: setup.exe
2025-05-24 00:39:32:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-24 00:39:32:   下载耗时: 7.79 秒
2025-05-24 00:39:32: 处理子文件夹: 脚本
2025-05-24 00:39:32:   源URL: http://www1.movemama.cn/便捷/脚本/
2025-05-24 00:39:32:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本
2025-05-24 00:39:32: 正在获取文件夹内容: http://www1.movemama.cn/便捷/脚本/
2025-05-24 00:39:33: PROPFIND请求响应状态: 207
2025-05-24 00:39:33: 成功获取文件夹内容列表
2025-05-24 00:39:33: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-39-33.txt
2025-05-24 00:39:33: 开始解析WebDAV XML响应...
2025-05-24 00:39:33: 解析到 4 个WebDAV项目
2025-05-24 00:39:33: 添加文件夹: EngineSoul
2025-05-24 00:39:33: 添加文件: League of Legends.exe
2025-05-24 00:39:33: 添加文件: ES.dll
2025-05-24 00:39:33: 最终解析到 3 个文件
2025-05-24 00:39:33: 文件列表: EngineSoul
2025-05-24 00:39:33: 文件列表: League of Legends.exe
2025-05-24 00:39:33: 文件列表: ES.dll
2025-05-24 00:39:33: 解析到 3 个项目
2025-05-24 00:39:33: 识别为文件夹: EngineSoul
2025-05-24 00:39:33: 识别为文件: League of Legends.exe
2025-05-24 00:39:33: 识别为文件: ES.dll
2025-05-24 00:39:33: 文件夹中包含: 2 个文件, 1 个子文件夹
2025-05-24 00:39:33: 正在下载: League of Legends.exe
2025-05-24 00:39:33:   源URL: http://www1.movemama.cn/便捷/脚本/League of Legends.exe
2025-05-24 00:39:33:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\League of Legends.exe
2025-05-24 00:39:38: 下载完成: League of Legends.exe
2025-05-24 00:39:38:   文件大小: 31,058,304 字节 (30330.38 KB)
2025-05-24 00:39:38:   下载耗时: 4.98 秒
2025-05-24 00:39:38: 正在下载: ES.dll
2025-05-24 00:39:38:   源URL: http://www1.movemama.cn/便捷/脚本/ES.dll
2025-05-24 00:39:38:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\ES.dll
2025-05-24 00:39:50: 下载完成: ES.dll
2025-05-24 00:39:50:   文件大小: 92,838,400 字节 (90662.50 KB)
2025-05-24 00:39:50:   下载耗时: 11.57 秒
2025-05-24 00:39:50: 处理子文件夹: EngineSoul
2025-05-24 00:39:50:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/
2025-05-24 00:39:50:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul
2025-05-24 00:39:50: 正在获取文件夹内容: http://www1.movemama.cn/便捷/脚本/EngineSoul/
2025-05-24 00:39:50: PROPFIND请求响应状态: 207
2025-05-24 00:39:50: 成功获取文件夹内容列表
2025-05-24 00:39:50: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-39-50.txt
2025-05-24 00:39:50: 开始解析WebDAV XML响应...
2025-05-24 00:39:50: 解析到 7 个WebDAV项目
2025-05-24 00:39:50: 添加文件: Img.zip
2025-05-24 00:39:50: 添加文件: T.data
2025-05-24 00:39:50: 添加文件: Temp.data
2025-05-24 00:39:50: 添加文件: DATA1
2025-05-24 00:39:50: 添加文件: DATA2
2025-05-24 00:39:50: 添加文件: DATA4
2025-05-24 00:39:50: 最终解析到 6 个文件
2025-05-24 00:39:50: 文件列表: Img.zip
2025-05-24 00:39:50: 文件列表: T.data
2025-05-24 00:39:50: 文件列表: Temp.data
2025-05-24 00:39:50: 文件列表: DATA1
2025-05-24 00:39:50: 文件列表: DATA2
2025-05-24 00:39:50: 文件列表: DATA4
2025-05-24 00:39:50: 解析到 6 个项目
2025-05-24 00:39:50: 识别为文件: Img.zip
2025-05-24 00:39:50: 识别为文件: T.data
2025-05-24 00:39:50: 识别为文件: Temp.data
2025-05-24 00:39:50: 识别为文件: DATA1
2025-05-24 00:39:50: 识别为文件: DATA2
2025-05-24 00:39:50: 识别为文件: DATA4
2025-05-24 00:39:50: 文件夹中包含: 6 个文件, 0 个子文件夹
2025-05-24 00:39:50: 正在下载: Img.zip
2025-05-24 00:39:50:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/Img.zip
2025-05-24 00:39:50:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\Img.zip
2025-05-24 00:39:53: 下载完成: Img.zip
2025-05-24 00:39:53:   文件大小: 2,998,994 字节 (2928.71 KB)
2025-05-24 00:39:53:   下载耗时: 2.27 秒
2025-05-24 00:39:53: 正在下载: T.data
2025-05-24 00:39:53:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/T.data
2025-05-24 00:39:53:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\T.data
2025-05-24 00:39:53: 下载完成: T.data
2025-05-24 00:39:53:   文件大小: 198 字节 (0.19 KB)
2025-05-24 00:39:53:   下载耗时: 0.68 秒
2025-05-24 00:39:53: 正在下载: Temp.data
2025-05-24 00:39:53:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/Temp.data
2025-05-24 00:39:53:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\Temp.data
2025-05-24 00:39:55: 下载完成: Temp.data
2025-05-24 00:39:55:   文件大小: 662,212 字节 (646.69 KB)
2025-05-24 00:39:55:   下载耗时: 1.75 秒
2025-05-24 00:39:55: 正在下载: DATA1
2025-05-24 00:39:55:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA1
2025-05-24 00:39:55:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA1
2025-05-24 00:40:01: 下载完成: DATA1
2025-05-24 00:40:01:   文件大小: 34,647,072 字节 (33835.03 KB)
2025-05-24 00:40:01:   下载耗时: 5.56 秒
2025-05-24 00:40:01: 正在下载: DATA2
2025-05-24 00:40:01:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA2
2025-05-24 00:40:01:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA2
2025-05-24 00:40:03: 下载完成: DATA2
2025-05-24 00:40:03:   文件大小: 5,956,096 字节 (5816.50 KB)
2025-05-24 00:40:03:   下载耗时: 2.32 秒
2025-05-24 00:40:03: 正在下载: DATA4
2025-05-24 00:40:03:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA4
2025-05-24 00:40:03:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA4
2025-05-24 00:40:06: 下载完成: DATA4
2025-05-24 00:40:06:   文件大小: 3,383,808 字节 (3304.50 KB)
2025-05-24 00:40:06:   下载耗时: 2.54 秒
2025-05-24 00:40:06: === 下载完成统计 ===
2025-05-24 00:40:06: 成功下载: 13 个文件
2025-05-24 00:40:06: 下载失败: 0 个文件
2025-05-24 00:40:06: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:40:06: WebDAV下载操作完成
2025-05-24 00:40:06: 正在搜索英雄联盟客户端和Riot客户端...
2025-05-24 00:40:06: 正在获取Riot Games最新版本信息...
2025-05-24 00:40:07: 成功获取版本列表，共467个版本，最新版本为: 15.10.1
2025-05-24 00:40:07: 已获取Riot最新版本数据，当前最新版本: 15.10.1, 最低要求版本: 15.8.1
2025-05-24 00:40:07: 搜索驱动器 C:\ (深度 6)...
2025-05-24 00:40:08: 搜索驱动器 D:\ (深度 6)...
2025-05-24 00:40:08: 检测到LeagueClient.exe版本: 15.9.678.1165
2025-05-24 00:40:08: 比较客户端版本(15.9.678.1165)与最低要求版本(15.8.1)
2025-05-24 00:40:08: 客户端版本(15.9.678.1165)满足最低要求版本(15.8.1)
2025-05-24 00:40:08: 尝试查找配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-24 00:40:08: 配置文件是否存在: True
2025-05-24 00:40:08: 配置文件大小: 1260 字节
2025-05-24 00:40:08: 成功读取配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1260 字节
2025-05-24 00:40:08: 检测到游戏区域: TW2
2025-05-24 00:40:08: 检测到非PBE区域，将作为候选客户端
2025-05-24 00:40:08: 添加英雄联盟客户端 (非PBE区域) 到候选列表: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165
2025-05-24 00:40:08: 找到Riot客户端: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-24 00:40:09: 搜索驱动器 F:\ (深度 6)...
2025-05-24 00:40:10: 搜索驱动器 G:\ (深度 6)...
2025-05-24 00:40:10: 检测到LeagueClient.exe版本: 15.10.683.1368
2025-05-24 00:40:10: 比较客户端版本(15.10.683.1368)与最低要求版本(15.8.1)
2025-05-24 00:40:10: 客户端版本(15.10.683.1368)满足最低要求版本(15.8.1)
2025-05-24 00:40:10: 尝试查找配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-24 00:40:10: 配置文件是否存在: True
2025-05-24 00:40:10: 配置文件大小: 1251 字节
2025-05-24 00:40:10: 成功读取配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1251 字节
2025-05-24 00:40:10: 检测到游戏区域: TW2
2025-05-24 00:40:10: 检测到非PBE区域，将作为候选客户端
2025-05-24 00:40:10: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368
2025-05-24 00:40:10: 检测到LeagueClient.exe版本: 15.10.677.6592
2025-05-24 00:40:10: 比较客户端版本(15.10.677.6592)与最低要求版本(15.8.1)
2025-05-24 00:40:10: 客户端版本(15.10.677.6592)满足最低要求版本(15.8.1)
2025-05-24 00:40:10: 尝试查找配置文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-24 00:40:10: 配置文件是否存在: False
2025-05-24 00:40:10: 未找到LeagueClientSettings.yaml文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-24 00:40:10: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592
2025-05-24 00:40:11: 找到 3 个候选客户端，按优先级排序如下:
2025-05-24 00:40:11: 1. 路径: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-24 00:40:11: 2. 路径: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592, 区域类型: 普通
2025-05-24 00:40:11: 3. 路径: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165, 区域类型: 普通
2025-05-24 00:40:11: 选择最佳客户端: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-24 00:40:11: 搜索完成，耗时: 5.12秒
2025-05-24 00:40:11: 英雄联盟客户端路径: G:\Riot Games\League of Legends\LeagueClient.exe
2025-05-24 00:40:11: Riot客户端路径: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-24 00:40:16: Riot客户端快捷方式已创建
2025-05-24 00:40:16: 启动脚本已写入桌面
2025-05-24 00:40:16: 正在获取推荐网站列表...
2025-05-24 00:40:16: 获取到 3 个链接
2025-05-24 00:40:18: 已打开 3 个网站
2025-05-24 00:49:27: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:49:27: WebDAV服务器: www1.movemama.cn
2025-05-24 00:49:27: 目标文件夹: 便捷
2025-05-24 00:49:27: 正在搜索英雄联盟客户端和Riot客户端...
2025-05-24 00:49:27: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:49:27: 已创建本地文件夹
2025-05-24 00:49:27: 已设置WebClient认证信息
2025-05-24 00:49:27: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:49:27: 正在获取Riot Games最新版本信息...
2025-05-24 00:49:27: 访问文件夹 http://www1.movemama.cn/便捷/ 失败 (WebException): 基础连接已经关闭: 服务器关闭了本应保持活动状态的连接。
2025-05-24 00:49:27: === 下载完成统计 ===
2025-05-24 00:49:27: 成功下载: 0 个文件
2025-05-24 00:49:27: 下载失败: 0 个文件
2025-05-24 00:49:27: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:49:27: WebDAV下载操作完成
2025-05-24 00:49:28: 成功获取版本列表，共467个版本，最新版本为: 15.10.1
2025-05-24 00:49:28: 已获取Riot最新版本数据，当前最新版本: 15.10.1, 最低要求版本: 15.8.1
2025-05-24 00:49:28: 搜索驱动器 C:\ (深度 6)...
2025-05-24 00:49:28: 搜索驱动器 D:\ (深度 6)...
2025-05-24 00:49:28: 检测到LeagueClient.exe版本: 15.9.678.1165
2025-05-24 00:49:28: 比较客户端版本(15.9.678.1165)与最低要求版本(15.8.1)
2025-05-24 00:49:28: 客户端版本(15.9.678.1165)满足最低要求版本(15.8.1)
2025-05-24 00:49:28: 尝试查找配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-24 00:49:28: 配置文件是否存在: True
2025-05-24 00:49:28: 配置文件大小: 1260 字节
2025-05-24 00:49:28: 成功读取配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1260 字节
2025-05-24 00:49:28: 检测到游戏区域: TW2
2025-05-24 00:49:28: 检测到非PBE区域，将作为候选客户端
2025-05-24 00:49:28: 添加英雄联盟客户端 (非PBE区域) 到候选列表: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165
2025-05-24 00:49:28: 找到Riot客户端: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-24 00:49:28: 搜索驱动器 F:\ (深度 6)...
2025-05-24 00:49:28: 搜索驱动器 G:\ (深度 6)...
2025-05-24 00:49:28: 检测到LeagueClient.exe版本: 15.10.683.1368
2025-05-24 00:49:28: 比较客户端版本(15.10.683.1368)与最低要求版本(15.8.1)
2025-05-24 00:49:28: 客户端版本(15.10.683.1368)满足最低要求版本(15.8.1)
2025-05-24 00:49:28: 尝试查找配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-24 00:49:28: 配置文件是否存在: True
2025-05-24 00:49:28: 配置文件大小: 1251 字节
2025-05-24 00:49:28: 成功读取配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1251 字节
2025-05-24 00:49:28: 检测到游戏区域: TW2
2025-05-24 00:49:28: 检测到非PBE区域，将作为候选客户端
2025-05-24 00:49:28: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368
2025-05-24 00:49:28: 检测到LeagueClient.exe版本: 15.10.677.6592
2025-05-24 00:49:28: 比较客户端版本(15.10.677.6592)与最低要求版本(15.8.1)
2025-05-24 00:49:28: 客户端版本(15.10.677.6592)满足最低要求版本(15.8.1)
2025-05-24 00:49:28: 尝试查找配置文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-24 00:49:28: 配置文件是否存在: False
2025-05-24 00:49:28: 未找到LeagueClientSettings.yaml文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-24 00:49:28: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592
2025-05-24 00:49:29: 找到 3 个候选客户端，按优先级排序如下:
2025-05-24 00:49:29: 1. 路径: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-24 00:49:29: 2. 路径: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592, 区域类型: 普通
2025-05-24 00:49:29: 3. 路径: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165, 区域类型: 普通
2025-05-24 00:49:29: 选择最佳客户端: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-24 00:49:29: 搜索完成，耗时: 2.03秒
2025-05-24 00:49:29: 英雄联盟客户端路径: G:\Riot Games\League of Legends\LeagueClient.exe
2025-05-24 00:49:29: Riot客户端路径: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-24 00:49:38: Riot客户端快捷方式已创建
2025-05-24 00:49:38: 启动脚本已写入桌面
2025-05-24 00:49:38: 正在获取推荐网站列表...
2025-05-24 00:49:38: 获取到 3 个链接
2025-05-24 00:49:40: 已打开 3 个网站
2025-05-24 00:50:04: 正在搜索英雄联盟客户端和Riot客户端...
2025-05-24 00:50:04: 正在获取Riot Games最新版本信息...
2025-05-24 00:50:04: 开始从WebDAV服务器下载便捷文件夹...
2025-05-24 00:50:04: WebDAV服务器: www1.movemama.cn
2025-05-24 00:50:04: 目标文件夹: 便捷
2025-05-24 00:50:04: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:50:04: 已创建本地文件夹
2025-05-24 00:50:04: 已设置WebClient认证信息
2025-05-24 00:50:04: 正在获取文件夹内容: http://www1.movemama.cn/便捷/
2025-05-24 00:50:04: PROPFIND请求响应状态: 207
2025-05-24 00:50:04: 成功获取文件夹内容列表
2025-05-24 00:50:04: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-50-04.txt
2025-05-24 00:50:04: 开始解析WebDAV XML响应...
2025-05-24 00:50:04: 解析到 7 个WebDAV项目
2025-05-24 00:50:04: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:50:04: 添加文件: 黑号.txt
2025-05-24 00:50:04: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:04: 添加文件: 改的内容.txt
2025-05-24 00:50:04: 添加文件夹: 脚本
2025-05-24 00:50:04: 添加文件: setup.exe
2025-05-24 00:50:04: 最终解析到 6 个文件
2025-05-24 00:50:04: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-24 00:50:04: 文件列表: 黑号.txt
2025-05-24 00:50:04: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:04: 文件列表: 改的内容.txt
2025-05-24 00:50:04: 文件列表: 脚本
2025-05-24 00:50:04: 文件列表: setup.exe
2025-05-24 00:50:04: 解析到 6 个项目
2025-05-24 00:50:04: 识别为文件: 免重启-启动VanGuard驱动.bat
2025-05-24 00:50:04: 识别为文件: 黑号.txt
2025-05-24 00:50:04: 识别为文件: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:04: 识别为文件: 改的内容.txt
2025-05-24 00:50:04: 识别为文件夹: 脚本
2025-05-24 00:50:04: 识别为文件: setup.exe
2025-05-24 00:50:04: 文件夹中包含: 5 个文件, 1 个子文件夹
2025-05-24 00:50:04: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-24 00:50:04:   源URL: http://www1.movemama.cn/便捷/免重启-启动VanGuard驱动.bat
2025-05-24 00:50:04:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-24 00:50:05: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-24 00:50:05:   文件大小: 1,193 字节 (1.17 KB)
2025-05-24 00:50:05:   下载耗时: 0.36 秒
2025-05-24 00:50:05: 正在下载: 黑号.txt
2025-05-24 00:50:05:   源URL: http://www1.movemama.cn/便捷/黑号.txt
2025-05-24 00:50:05:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-24 00:50:05: 下载完成: 黑号.txt
2025-05-24 00:50:05:   文件大小: 125 字节 (0.12 KB)
2025-05-24 00:50:05:   下载耗时: 0.36 秒
2025-05-24 00:50:05: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:05:   源URL: http://www1.movemama.cn/便捷/网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:05:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:06: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-24 00:50:06:   文件大小: 1,884 字节 (1.84 KB)
2025-05-24 00:50:06:   下载耗时: 0.38 秒
2025-05-24 00:50:06: 正在下载: 改的内容.txt
2025-05-24 00:50:06:   源URL: http://www1.movemama.cn/便捷/改的内容.txt
2025-05-24 00:50:06:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-24 00:50:06: 下载完成: 改的内容.txt
2025-05-24 00:50:06:   文件大小: 1,750 字节 (1.71 KB)
2025-05-24 00:50:06:   下载耗时: 0.36 秒
2025-05-24 00:50:06: 正在下载: setup.exe
2025-05-24 00:50:06:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-24 00:50:06:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-24 00:50:13: 下载完成: setup.exe
2025-05-24 00:50:13:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-24 00:50:13:   下载耗时: 7.41 秒
2025-05-24 00:50:13: 处理子文件夹: 脚本
2025-05-24 00:50:13:   源URL: http://www1.movemama.cn/便捷/脚本/
2025-05-24 00:50:13:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本
2025-05-24 00:50:13:   已创建本地子文件夹: C:\Users\<USER>\Desktop\便捷\脚本
2025-05-24 00:50:13: 正在获取文件夹内容: http://www1.movemama.cn/便捷/脚本/
2025-05-24 00:50:14: 网络请求异常: 操作超时
2025-05-24 00:50:14: 无法获取Riot最新版本数据，将使用默认版本检查逻辑
2025-05-24 00:50:14: 搜索驱动器 C:\ (深度 6)...
2025-05-24 00:50:14: 搜索驱动器 D:\ (深度 6)...
2025-05-24 00:50:14: 检测到LeagueClient.exe版本: 15.9.678.1165
2025-05-24 00:50:14: 客户端版本(15.9.678.1165)满足默认最低要求版本(15.0.0.0)
2025-05-24 00:50:14: 尝试查找配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-24 00:50:14: 配置文件是否存在: True
2025-05-24 00:50:14: 配置文件大小: 1260 字节
2025-05-24 00:50:14: 成功读取配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1260 字节
2025-05-24 00:50:14: 检测到游戏区域: TW2
2025-05-24 00:50:14: 检测到非PBE区域，将作为候选客户端
2025-05-24 00:50:14: 添加英雄联盟客户端 (非PBE区域) 到候选列表: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165
2025-05-24 00:50:14: 找到Riot客户端: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-24 00:50:14: 搜索驱动器 F:\ (深度 6)...
2025-05-24 00:50:14: 搜索驱动器 G:\ (深度 6)...
2025-05-24 00:50:14: 检测到LeagueClient.exe版本: 15.10.683.1368
2025-05-24 00:50:14: 客户端版本(15.10.683.1368)满足默认最低要求版本(15.0.0.0)
2025-05-24 00:50:14: 尝试查找配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-24 00:50:14: 配置文件是否存在: True
2025-05-24 00:50:14: 配置文件大小: 1251 字节
2025-05-24 00:50:14: 成功读取配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1251 字节
2025-05-24 00:50:14: 检测到游戏区域: TW2
2025-05-24 00:50:14: 检测到非PBE区域，将作为候选客户端
2025-05-24 00:50:14: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368
2025-05-24 00:50:14: 检测到LeagueClient.exe版本: 15.10.677.6592
2025-05-24 00:50:14: 客户端版本(15.10.677.6592)满足默认最低要求版本(15.0.0.0)
2025-05-24 00:50:14: 尝试查找配置文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-24 00:50:14: 配置文件是否存在: False
2025-05-24 00:50:14: 未找到LeagueClientSettings.yaml文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-24 00:50:14: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592
2025-05-24 00:50:14: PROPFIND请求响应状态: 207
2025-05-24 00:50:14: 成功获取文件夹内容列表
2025-05-24 00:50:14: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-50-14.txt
2025-05-24 00:50:14: 开始解析WebDAV XML响应...
2025-05-24 00:50:14: 解析到 4 个WebDAV项目
2025-05-24 00:50:14: 添加文件夹: EngineSoul
2025-05-24 00:50:14: 添加文件: League of Legends.exe
2025-05-24 00:50:14: 添加文件: ES.dll
2025-05-24 00:50:14: 最终解析到 3 个文件
2025-05-24 00:50:14: 文件列表: EngineSoul
2025-05-24 00:50:14: 文件列表: League of Legends.exe
2025-05-24 00:50:14: 文件列表: ES.dll
2025-05-24 00:50:14: 解析到 3 个项目
2025-05-24 00:50:14: 识别为文件夹: EngineSoul
2025-05-24 00:50:14: 识别为文件: League of Legends.exe
2025-05-24 00:50:14: 识别为文件: ES.dll
2025-05-24 00:50:14: 文件夹中包含: 2 个文件, 1 个子文件夹
2025-05-24 00:50:14: 正在下载: League of Legends.exe
2025-05-24 00:50:14:   源URL: http://www1.movemama.cn/便捷/脚本/League of Legends.exe
2025-05-24 00:50:14:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\League of Legends.exe
2025-05-24 00:50:14: 找到 3 个候选客户端，按优先级排序如下:
2025-05-24 00:50:14: 1. 路径: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-24 00:50:14: 2. 路径: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592, 区域类型: 普通
2025-05-24 00:50:14: 3. 路径: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165, 区域类型: 普通
2025-05-24 00:50:14: 选择最佳客户端: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-24 00:50:14: 搜索完成，耗时: 10.65秒
2025-05-24 00:50:14: 英雄联盟客户端路径: G:\Riot Games\League of Legends\LeagueClient.exe
2025-05-24 00:50:14: Riot客户端路径: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-24 00:50:19: 下载完成: League of Legends.exe
2025-05-24 00:50:19:   文件大小: 31,058,304 字节 (30330.38 KB)
2025-05-24 00:50:19:   下载耗时: 4.73 秒
2025-05-24 00:50:19: 正在下载: ES.dll
2025-05-24 00:50:19:   源URL: http://www1.movemama.cn/便捷/脚本/ES.dll
2025-05-24 00:50:19:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\ES.dll
2025-05-24 00:50:29: 下载完成: ES.dll
2025-05-24 00:50:29:   文件大小: 92,838,400 字节 (90662.50 KB)
2025-05-24 00:50:29:   下载耗时: 9.99 秒
2025-05-24 00:50:29: 处理子文件夹: EngineSoul
2025-05-24 00:50:29:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/
2025-05-24 00:50:29:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul
2025-05-24 00:50:29:   已创建本地子文件夹: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul
2025-05-24 00:50:29: 正在获取文件夹内容: http://www1.movemama.cn/便捷/脚本/EngineSoul/
2025-05-24 00:50:30: PROPFIND请求响应状态: 207
2025-05-24 00:50:30: 成功获取文件夹内容列表
2025-05-24 00:50:30: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-24_00-50-30.txt
2025-05-24 00:50:30: 开始解析WebDAV XML响应...
2025-05-24 00:50:30: 解析到 7 个WebDAV项目
2025-05-24 00:50:30: 添加文件: Img.zip
2025-05-24 00:50:30: 添加文件: T.data
2025-05-24 00:50:30: 添加文件: Temp.data
2025-05-24 00:50:30: 添加文件: DATA1
2025-05-24 00:50:30: 添加文件: DATA2
2025-05-24 00:50:30: 添加文件: DATA4
2025-05-24 00:50:30: 最终解析到 6 个文件
2025-05-24 00:50:30: 文件列表: Img.zip
2025-05-24 00:50:30: 文件列表: T.data
2025-05-24 00:50:30: 文件列表: Temp.data
2025-05-24 00:50:30: 文件列表: DATA1
2025-05-24 00:50:30: 文件列表: DATA2
2025-05-24 00:50:30: 文件列表: DATA4
2025-05-24 00:50:30: 解析到 6 个项目
2025-05-24 00:50:30: 识别为文件: Img.zip
2025-05-24 00:50:30: 识别为文件: T.data
2025-05-24 00:50:30: 识别为文件: Temp.data
2025-05-24 00:50:30: 识别为文件: DATA1
2025-05-24 00:50:30: 识别为文件: DATA2
2025-05-24 00:50:30: 识别为文件: DATA4
2025-05-24 00:50:30: 文件夹中包含: 6 个文件, 0 个子文件夹
2025-05-24 00:50:30: 正在下载: Img.zip
2025-05-24 00:50:30:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/Img.zip
2025-05-24 00:50:30:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\Img.zip
2025-05-24 00:50:31: 下载完成: Img.zip
2025-05-24 00:50:31:   文件大小: 2,998,994 字节 (2928.71 KB)
2025-05-24 00:50:31:   下载耗时: 1.80 秒
2025-05-24 00:50:31: 正在下载: T.data
2025-05-24 00:50:31:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/T.data
2025-05-24 00:50:31:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\T.data
2025-05-24 00:50:32: 下载完成: T.data
2025-05-24 00:50:32:   文件大小: 198 字节 (0.19 KB)
2025-05-24 00:50:32:   下载耗时: 0.35 秒
2025-05-24 00:50:32: 正在下载: Temp.data
2025-05-24 00:50:32:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/Temp.data
2025-05-24 00:50:32:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\Temp.data
2025-05-24 00:50:32: 下载完成: Temp.data
2025-05-24 00:50:32:   文件大小: 662,212 字节 (646.69 KB)
2025-05-24 00:50:32:   下载耗时: 0.42 秒
2025-05-24 00:50:32: 正在下载: DATA1
2025-05-24 00:50:32:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA1
2025-05-24 00:50:32:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA1
2025-05-24 00:50:36: 下载完成: DATA1
2025-05-24 00:50:36:   文件大小: 34,647,072 字节 (33835.03 KB)
2025-05-24 00:50:36:   下载耗时: 3.95 秒
2025-05-24 00:50:36: 正在下载: DATA2
2025-05-24 00:50:36:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA2
2025-05-24 00:50:36:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA2
2025-05-24 00:50:37: 下载完成: DATA2
2025-05-24 00:50:37:   文件大小: 5,956,096 字节 (5816.50 KB)
2025-05-24 00:50:37:   下载耗时: 0.98 秒
2025-05-24 00:50:37: 正在下载: DATA4
2025-05-24 00:50:37:   源URL: http://www1.movemama.cn/便捷/脚本/EngineSoul/DATA4
2025-05-24 00:50:37:   目标路径: C:\Users\<USER>\Desktop\便捷\脚本\EngineSoul\DATA4
2025-05-24 00:50:37: Riot客户端快捷方式已创建
2025-05-24 00:50:37: 启动脚本已写入桌面
2025-05-24 00:50:37: 正在获取推荐网站列表...
2025-05-24 00:50:38: 下载完成: DATA4
2025-05-24 00:50:38:   文件大小: 3,383,808 字节 (3304.50 KB)
2025-05-24 00:50:38:   下载耗时: 0.70 秒
2025-05-24 00:50:38: === 下载完成统计 ===
2025-05-24 00:50:38: 成功下载: 13 个文件
2025-05-24 00:50:38: 下载失败: 0 个文件
2025-05-24 00:50:38: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-24 00:50:38: WebDAV下载操作完成
2025-05-24 00:50:38: 获取到 3 个链接
2025-05-24 00:50:40: 已打开 3 个网站

保留英文菜单标题和绘图 只添加中文注释
优化代码 改进代码 请优先用代码库里的SDK接口!
优化代码 改进代码 请优先用代码库里的SDK接口 遵守以下规则 请遵守SDK的开发规范

## 1. 基础 C++ 标准库
- <stddef.h>, <stdint.h> - 基本类型定义
- <string> - 字符串处理
- <vector> - 动态数组容器
- <map> - 键值对容器
- <functional> - 函数对象
- <algorithm> - 标准算法库
- <math.h>, <float.h> - 数学函数
- <iostream>, <fstream> - 输入输出流处理

## 2. Windows API
- windows.h - Windows 核心 API
- winternl.h - Windows 内部 API
- shlobj.h - Shell 对象接口
- io.h - 文件 IO 操作
- Ntdll.h - NT 系统调用

## 3. 图形渲染技术
- **DirectX**
  - d3d9.h - DirectX 9 API
  - d3d11.h - DirectX 11 API
  - d3dcompiler.h - DirectX 着色器编译器
- **ImGui** (图形用户界面库)
  - imgui.h, imgui_internal.h
  - imgui_impl_win32.h - ImGui Windows 平台实现
  - imgui_impl_dx11.h - ImGui DirectX 11 实现
  - imgui_impl_dx9.h - ImGui DirectX 9 实现

## 4. 数学与几何库
- mathc.h - 数学计算库
- Vector2.h, Vector3.h - 向量操作
- Rect.h - 矩形区域
- Circle_3D.h - 3D圆形
- RoundedRectangle_3D.h - 3D圆角矩形
- MathUtils.h - 数学工具函数
- Polygons.h - 多边形处理

## 5. 底层与内存操作
- x86.h - x86架构相关
- zcrt.h - 自定义C运行时
- intrin.h - 内联汇编指令

## 6. 数据处理
- json.hpp (nlohmann::json) - JSON解析器
- Enum.h - 枚举定义
- MyStruct.h - 自定义结构体

## 7. 游戏开发核心
- GameObject.h - 游戏对象系统
- Game.h - 游戏核心
- ObjectManager.h - 对象管理
- EventManager.h - 事件管理
- Prediction.h, NewPrediction.h - 预测系统
- Damage.h - 伤害计算
- Spell.h - 技能系统
- Orbwalking.h - 走A系统

## 8. 游戏功能模块
- **躲避系统** (Evade/)
  - SpellData.h, SpellDB.h, SpellDetector.h等
- **目标选择与辅助**
  - TargetSelector.h - 目标选择
  - HealthPrediction.h - 生命值预测
  - Gapcloser.h - 突进检测
  - Interrupter.h - 打断系统

## 9. 用户界面系统
- **菜单系统** (Menu/)
  - MenuComponent.h, Menu.h
  - CheckBox.h, KeyBind.h, List.h
  - Slider.h, ColorPicker.h等
- **渲染与绘制**
  - Renderer.h - 渲染器
  - Drawings.h - 绘制功能

## 10. 游戏增强功能
- Activator.h - 主动物品
- ZoomHack.h - 视距调整
- RecallTracker.h - 回城追踪
- GankAlerter.h - 偷袭警报
- JungleTracker.h - 野怪追踪
- BaseUlt.h - 基地大招
- AutoSkills.h - 自动技能

## 11. 本地化与扩展
- PermaShow.h - 永久显示
- Extensions.h - 扩展功能



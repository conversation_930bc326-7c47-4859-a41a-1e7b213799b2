{"version": 3, "targets": {".NETCoreApp,Version=v3.1": {"HttpToSocks5Proxy/1.4.0": {"type": "project", "framework": ".NETStandard,Version=v2.0", "compile": {"bin/placeholder/HttpToSocks5Proxy.dll": {}}, "runtime": {"bin/placeholder/HttpToSocks5Proxy.dll": {}}}}}, "libraries": {"HttpToSocks5Proxy/1.4.0": {"type": "project", "path": "../src/HttpToSocks5Proxy/HttpToSocks5Proxy.csproj", "msbuildProject": "../src/HttpToSocks5Proxy/HttpToSocks5Proxy.csproj"}}, "projectFileDependencyGroups": {".NETCoreApp,Version=v3.1": ["HttpToSocks5Proxy >= 1.4.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\ConsoleApp1.csproj", "projectName": "ConsoleApp1", "projectPath": "D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\ConsoleApp1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\项目\\HttpToSocks5Proxy-master\\ConsoleApp1\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj": {"projectPath": "D:\\项目\\HttpToSocks5Proxy-master\\src\\HttpToSocks5Proxy\\HttpToSocks5Proxy.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.402\\RuntimeIdentifierGraph.json"}}}}
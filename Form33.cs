﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    public partial class Form33 : Form
    {
        public Form33()
        {
            InitializeComponent();
        }

        private void Form33_Load(object sender, EventArgs e)
        {
            listView1.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView2.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView3.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView4.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView5.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView6.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);

            TIAOTIAO();
        }
        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {



            if (e.Button == MouseButtons.Left)
                InitializationHero(((ListView)sender));





        }

        public void TIAOTIAO()
        {
            string[] nameGO = { "TOP", "JG", "MID", "ADC", "SUP" };

            string item = "";
            foreach (var itemG in nameGO)
            {
                if (Form1.paiwei.XINHAO_ban.IndexOf(itemG) != -1)
                {
                    item = itemG;
                    break;
                }



            }
            switch (item)
            {

                case "TOP":

                    tabControl1.SelectedIndex = 0;
                    break;

                case "JG":

                    tabControl1.SelectedIndex = 1;

                    break;

                case "MID":

                    tabControl1.SelectedIndex = 2;

                    break;
                case "ADC":

                    tabControl1.SelectedIndex = 3;

                    break;
                case "SUP":

                    tabControl1.SelectedIndex = 4;

                    break;
                default:
                    break;
            }
        }
        private void InitializationHero(ListView listViewYY /*ImageList GO*/)
        {

            if (listViewYY.SelectedItems[0].Index != -1)
            {

                var index1 = listViewYY.SelectedItems[0].Index;

                Image QQQWW = null;

                switch (listViewYY.Name)
                {

                    case "listView1":
                        QQQWW = imageList1.Images[index1];
                        break;
                    case "listView2":
                        QQQWW = imageList2.Images[index1];
                        break;
                    case "listView3":
                        QQQWW = imageList3.Images[index1];
                        break;
                    case "listView4":
                        QQQWW = imageList4.Images[index1];
                        break;
                    case "listView5":
                        QQQWW = imageList5.Images[index1];
                        break;
                    case "listView6":
                        QQQWW = imageList6.Images[index1];
                        break;
                    default:
                        break;
                }
                if (QQQWW != null)
                {

                    var HAHAID = listViewYY.Items[index1].ImageKey;

                    if (Form1.paiwei.XINHAOINT_ban != -1)
                    {
                        var XQ = Form1.paiwei.XINHAOINT_ban;

                        Handlingarraycontrols(Form1.paiwei.XINHAO_ban, XQ, HAHAID, WhiteAndBlack((System.Drawing.Bitmap)QQQWW));

                    }



                    if (Form1.BanID != -1)
                    {

                        var XQ = Form1.BanID;


                        if (XQ == 1 && Form1.BanyxSHUZU[1] == HAHAID)//第一个和第二个
                        {
                            var Banid = Form1.BanyxSHUZU[0];

                            var BanTP = Form1.form1.Banyx1.Image;


                            Form1.BanyxSHUZU[1] = Banid;

                            Form1.form1.Banyx2.Image = BanTP;

                            Util.WriteIniData("AutomaticHero", "BanHeroID2", Banid, @".\Data\Cofing.ini");

                        }
                        else if (XQ == 1 && Form1.BanyxSHUZU[2] == HAHAID)//第一个和第三个
                        {
                            var Banid = Form1.BanyxSHUZU[0];

                            var BanTP = Form1.form1.Banyx1.Image;


                            Form1.BanyxSHUZU[2] = Banid;

                            Form1.form1.Banyx3.Image = BanTP;
                            Util.WriteIniData("AutomaticHero", "BanHeroID3", Banid, @".\Data\Cofing.ini");
                        }
                        else if (XQ == 2 && Form1.BanyxSHUZU[0] == HAHAID)//第二个和第一个
                        {
                            var Banid = Form1.BanyxSHUZU[1];

                            var BanTP = Form1.form1.Banyx2.Image;


                            Form1.BanyxSHUZU[0] = Banid;

                            Form1.form1.Banyx1.Image = BanTP;
                            Util.WriteIniData("AutomaticHero", "BanHeroID1", Banid, @".\Data\Cofing.ini");
                        }
                        else if (XQ == 2 && Form1.BanyxSHUZU[2] == HAHAID)//第二个和第三个
                        {
                            var Banid = Form1.BanyxSHUZU[1];

                            var BanTP = Form1.form1.Banyx2.Image;


                            Form1.BanyxSHUZU[2] = Banid;

                            Form1.form1.Banyx3.Image = BanTP;

                            Util.WriteIniData("AutomaticHero", "BanHeroID3", Banid, @".\Data\Cofing.ini");
                        }
                        else if (XQ == 3 && Form1.BanyxSHUZU[0] == HAHAID)//第三个和第一个
                        {
                            var Banid = Form1.BanyxSHUZU[2];

                            var BanTP = Form1.form1.Banyx3.Image;


                            Form1.BanyxSHUZU[0] = Banid;

                            Form1.form1.Banyx1.Image = BanTP;

                            Util.WriteIniData("AutomaticHero", "BanHeroID1", Banid, @".\Data\Cofing.ini");
                        }
                        else if (XQ == 3 && Form1.BanyxSHUZU[1] == HAHAID)//第三个和第二个
                        {
                            var Banid = Form1.BanyxSHUZU[2];

                            var BanTP = Form1.form1.Banyx3.Image;


                            Form1.BanyxSHUZU[1] = Banid;

                            Form1.form1.Banyx2.Image = BanTP;

                            Util.WriteIniData("AutomaticHero", "BanHeroID2", Banid, @".\Data\Cofing.ini");
                        }




                        //if (Form1.BanyxSHUZU.Contains(HAHAID))
                        //{
                        //    //MessageBox.Show("不能重复Ban一个英雄!");
                        //    return;
                        //}

                        if (XQ == 1)
                        {
                            Form1.BanyxSHUZU[0] = HAHAID;
                            Form1.form1.Banyx1.Image = WhiteAndBlack((System.Drawing.Bitmap)QQQWW);
                        }
                        else if (XQ == 2)
                        {

                            Form1.BanyxSHUZU[1] = HAHAID;
                            Form1.form1.Banyx2.Image = WhiteAndBlack((System.Drawing.Bitmap)QQQWW);
                        }
                        else if (XQ == 3)
                        {

                            Form1.BanyxSHUZU[2] = HAHAID;
                            Form1.form1.Banyx3.Image = WhiteAndBlack((System.Drawing.Bitmap)QQQWW);
                        }


                        Console.WriteLine("你选择Ban英雄:{0}", HAHAID);

                        Util.WriteIniData("AutomaticHero", "BanHeroID" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");
                    }



                    this.Close();
                }


            }


        }
        void Handlingarraycontrols(string name, int bianhao, string YXid, Image ziyuan)
        {
            var baoc = "";

            if (name.IndexOf("TOP") != -1)
            {
                baoc = "TOP";

                for (int i = 0; i < Form1.paiwei.TOP.Count(); i++)
                {
                    if (bianhao != i)
                    {
                        if (Form1.WeiTOPban[i] == YXid)
                        {
                            Form1.WeiTOPban[i] = Form1.WeiTOPban[bianhao];

                            Form1.paiwei.TOP[i].Image = Form1.paiwei.TOP[bianhao].Image;

                            Form1.WeiTOPban[bianhao] = YXid;

                            Form1.paiwei.TOP[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + i.ToString(), Form1.WeiTOPban[i], @".\Data\Cofing.ini");
                            break;
                        }
                    }


                }
                Form1.WeiTOPban[bianhao] = YXid;

                Form1.paiwei.TOP[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");
            }

            if (name.IndexOf("JG") != -1)
            {
                baoc = "JG";

                for (int i = 0; i < Form1.paiwei.JG.Count(); i++)
                {
                    if (bianhao != i)
                    {
                        if (Form1.WeiJGban[i] == YXid)
                        {
                            Form1.WeiJGban[i] = Form1.WeiJGban[bianhao];

                            Form1.paiwei.JG[i].Image = Form1.paiwei.JG[bianhao].Image;

                            Form1.WeiJGban[bianhao] = YXid;

                            Form1.paiwei.JG[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + i.ToString(), Form1.WeiJGban[i], @".\Data\Cofing.ini");
                            break;
                        }
                    }


                }
                Form1.WeiJGban[bianhao] = YXid;

                Form1.paiwei.JG[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");
            }

            if (name.IndexOf("MID") != -1)
            {
                baoc = "MID";

                for (int i = 0; i < Form1.paiwei.MID.Count(); i++)
                {
                    if (bianhao != i)
                    {
                        if (Form1.WeiMIDban[i] == YXid)
                        {
                            Form1.WeiMIDban[i] = Form1.WeiMIDban[bianhao];

                            Form1.paiwei.MID[i].Image = Form1.paiwei.MID[bianhao].Image;

                            Form1.WeiMIDban[bianhao] = YXid;

                            Form1.paiwei.MID[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + i.ToString(), Form1.WeiMIDban[i], @".\Data\Cofing.ini");
                            break;
                        }
                    }


                }
                Form1.WeiMIDban[bianhao] = YXid;

                Form1.paiwei.MID[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");
            }

            if (name.IndexOf("ADC") != -1)
            {
                baoc = "ADC";

                for (int i = 0; i < Form1.paiwei.ADC.Count(); i++)
                {
                    if (bianhao != i)
                    {
                        if (Form1.WeiADCban[i] == YXid)
                        {
                            Form1.WeiADCban[i] = Form1.WeiADCban[bianhao];

                            Form1.paiwei.ADC[i].Image = Form1.paiwei.ADC[bianhao].Image;

                            Form1.WeiADCban[bianhao] = YXid;

                            Form1.paiwei.ADC[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + i.ToString(), Form1.WeiADCban[i], @".\Data\Cofing.ini");
                            break;
                        }
                    }


                }
                Form1.WeiADCban[bianhao] = YXid;

                Form1.paiwei.ADC[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");
            }

            if (name.IndexOf("SUP") != -1)
            {
                baoc = "SUP";

                for (int i = 0; i < Form1.paiwei.SUP.Count(); i++)
                {
                    if (bianhao != i)
                    {
                        if (Form1.WeiSUPban[i] == YXid)
                        {
                            Form1.WeiSUPban[i] = Form1.WeiSUPban[bianhao];

                            Form1.paiwei.SUP[i].Image = Form1.paiwei.SUP[bianhao].Image;

                            Form1.WeiSUPban[bianhao] = YXid;

                            Form1.paiwei.SUP[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");

                            Util.WriteIniData("AutomaticHero", baoc + "BanID" + i.ToString(), Form1.WeiSUPban[i], @".\Data\Cofing.ini");
                            break;
                        }
                    }


                }
                Form1.WeiSUPban[bianhao] = YXid;

                Form1.paiwei.SUP[bianhao].Image = WhiteAndBlack((System.Drawing.Bitmap)ziyuan);

                Util.WriteIniData("AutomaticHero", baoc + "BanID" + bianhao.ToString(), YXid, @".\Data\Cofing.ini");
            }




        }



        public bool dddddddd()
        {



            if (Form1.BanDataKun.Count > 0)
            {

                imageList1.Images.Clear();
                listView1.Items.Clear();

                imageList2.Images.Clear();
                listView2.Items.Clear();

                imageList3.Images.Clear();
                listView3.Items.Clear();

                imageList4.Images.Clear();
                listView4.Items.Clear();

                imageList5.Images.Clear();
                listView5.Items.Clear();

                imageList6.Images.Clear();
                listView6.Items.Clear();



                for (var i = 0; i < Form1.BanDataKun.Count; i++)
                {

                    var HeroID = Form1.BanDataKun[i].HeroID;

                    var HeroLEIXING = Form1.BanDataKun[i].Weizi;

                    var Heroname = Form1.BanDataKun[i].Heroname;

                    var icon = Form1.BanDataKun[i].icon;

                    if (HeroLEIXING == "TOP")
                    {
                        this.listView1.BeginUpdate();
                        this.imageList1.Images.Add(HeroID, icon);

                        this.listView1.Items.Add(Heroname, HeroID);
                        this.listView1.EndUpdate();
                    }

                    if (HeroLEIXING == "JUNGLE")
                    {

                        this.listView2.BeginUpdate();
                        this.imageList2.Images.Add(HeroID, icon);

                        this.listView2.Items.Add(Heroname, HeroID);
                        this.listView2.EndUpdate();
                    }

                    if (HeroLEIXING == "MID")
                    {

                        this.listView3.BeginUpdate();
                        this.imageList3.Images.Add(HeroID, icon);

                        this.listView3.Items.Add(Heroname, HeroID);
                        this.listView3.EndUpdate();
                    }

                    if (HeroLEIXING == "ADC")
                    {

                        this.listView4.BeginUpdate();
                        this.imageList4.Images.Add(HeroID, icon);

                        this.listView4.Items.Add(Heroname, HeroID);
                        this.listView4.EndUpdate();
                    }

                    if (HeroLEIXING == "SUPPORT")
                    {

                        this.listView5.BeginUpdate();
                        this.imageList5.Images.Add(HeroID, icon);

                        this.listView5.Items.Add(Heroname, HeroID);
                        this.listView5.EndUpdate();
                    }
                    if (HeroLEIXING == "QITA")
                    {
                        this.listView6.BeginUpdate();
                        this.imageList6.Images.Add(HeroID, icon);

                        this.listView6.Items.Add(Heroname, HeroID);
                        this.listView6.EndUpdate();
                    }



                }



                Form1.form1.Banyx1.Enabled = true;

                Form1.form1.Banyx2.Enabled = true;

                Form1.form1.Banyx3.Enabled = true;


                if (Form1.BanDataKun1.Count > 0)
                {
                    foreach (var Getban in Form1.BanDataKun1)
                    {

                        int i = Getban.ID;
                        var woowo = Getban.woowo;
                        var TUP = Getban.TUP;
                        var itemG = Getban.Banweiz;

                        if (woowo != "0")
                        {
                            switch (itemG)
                            {

                                case "TOP":

                                    Form1.WeiTOPban[i] = woowo;
                                    Form1.paiwei.TOP[i].Image = WhiteAndBlack((System.Drawing.Bitmap)TUP);

                                    break;

                                case "JG":

                                    Form1.WeiJGban[i] = woowo;
                                    Form1.paiwei.JG[i].Image = WhiteAndBlack((System.Drawing.Bitmap)TUP);

                                    break;

                                case "MID":

                                    Form1.WeiMIDban[i] = woowo;
                                    Form1.paiwei.MID[i].Image = WhiteAndBlack((System.Drawing.Bitmap)TUP);

                                    break;
                                case "ADC":


                                    Form1.WeiADCban[i] = woowo;
                                    Form1.paiwei.ADC[i].Image = WhiteAndBlack((System.Drawing.Bitmap)TUP);

                                    break;
                                case "SUP":

                                    Form1.WeiSUPban[i] = woowo;
                                    Form1.paiwei.SUP[i].Image = WhiteAndBlack((System.Drawing.Bitmap)TUP);

                                    break;
                                default:
                                    break;
                            }
                        }

                    }
                }

                Form1.form1.InitializationG = 3;
                return true;

            }
            else
            {
                return false;
            }

        }
        public Bitmap WhiteAndBlack(System.Drawing.Bitmap image)
        {

            if (image == null)
                return image;
            //原来图片的长度

            int width = image.Width;

            //原来图片的高度

            int height = image.Height;

            //改变色素

            //横坐标

            for (int x = 0; x < width; x++)

            {
                //纵坐标

                for (int y = 0; y < height; y++)

                {
                    //获得坐标(x,y)颜色

                    Color color = image.GetPixel(x, y);

                    //获得该颜色下的黑白色

                    int value = (color.R + color.G + color.B) / 3;

                    //设置颜色

                    image.SetPixel(x, y, Color.FromArgb(value, value, value));

                }

            }

            return image;

        }
        public void Getrunninginitialization()
        {
            bool wanbi = false;
            while (true)
            {


                var HeroDataJX = Form1.HeroIDLibrary;

                try
                {
                    if (Form1.form1.QQQQQQQQQ || Form1.form1.EEEEEEEEEEEE)
                        break;

                    if (Form1.form1.Banyx1.InvokeRequired)
                    {

                        Form1.form1.Banyx1.Invoke(new Action<int>(n =>
                        {
                            Form1.form1.Banyx1.Enabled = true;
                            Form1.form1.Banyx2.Enabled = true;
                            var woowo1 = Util.ReadIniData("AutomaticHero", "BanHeroID1", "123", @".\Data\Cofing.ini");
                            var woowo2 = Util.ReadIniData("AutomaticHero", "BanHeroID2", "123", @".\Data\Cofing.ini");
                            var woowo3 = Util.ReadIniData("AutomaticHero", "BanHeroID3", "123", @".\Data\Cofing.ini");
                            //InitializationHero(1);
                            if (HeroDataJX.Count > 0)
                            {
                                for (var i = 0; i < HeroDataJX.Count; i++)
                                {
                                    if (HeroDataJX[i].HeroID == woowo1)
                                    {

                                        var QQQWW = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + HeroDataJX[i].HeroID + ".png");
                                        Form1.form1.Banyx1.Image = WhiteAndBlack((System.Drawing.Bitmap)QQQWW);
                                        Form1.BanyxSHUZU[0] = HeroDataJX[i].HeroID;


                                    }
                                    if (HeroDataJX[i].HeroID == woowo2)
                                    {
                                        var QQQWW = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + HeroDataJX[i].HeroID + ".png");
                                        Form1.form1.Banyx2.Image = WhiteAndBlack((System.Drawing.Bitmap)QQQWW);
                                        Form1.BanyxSHUZU[1] = HeroDataJX[i].HeroID;
                                    }
                                    if (HeroDataJX[i].HeroID == woowo3)
                                    {
                                        var QQQWW = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + HeroDataJX[i].HeroID + ".png");
                                        Form1.form1.Banyx3.Image = WhiteAndBlack((System.Drawing.Bitmap)QQQWW);
                                        Form1.BanyxSHUZU[2] = HeroDataJX[i].HeroID;
                                    }
                                }



                                Form1.form1.Banchushi = true;
                            }


                        }), 1);


                    }




                    Form1.BanDataKun.Clear();
                    Form1.BanDataKun1.Clear();
                    if (HeroDataJX.Count > 0)
                    {
                        for (var i = 0; i < HeroDataJX.Count; i++)
                        {

                            var HeroID = HeroDataJX[i].HeroID;

                            var HeroLEIXING = HeroDataJX[i].HeroNameWei;

                            var Heroname = Util.Getheroname1(HeroID);


                            var icon = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + HeroID + ".png");

                            if (HeroLEIXING.Contains("TOP"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "TOP";
                                JUCS.icon = icon;
                                JUCS.Heroname = Heroname;
                                JUCS.HeroID = HeroID;
                                Form1.BanDataKun.Add(JUCS);
                            }

                            if (HeroLEIXING.Contains("JUNGLE"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "JUNGLE";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.BanDataKun.Add(JUCS);

                            }

                            if (HeroLEIXING.Contains("MID"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "MID";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.BanDataKun.Add(JUCS);
                            }

                            if (HeroLEIXING.Contains("ADC"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "ADC";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.BanDataKun.Add(JUCS);
                            }

                            if (HeroLEIXING.Contains("SUPPORT"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "SUPPORT";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.BanDataKun.Add(JUCS);
                            }
                            if (HeroLEIXING.Count() == 0)
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "QITA";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.BanDataKun.Add(JUCS);
                            }



                        }

                        string[] nameGO = { "TOP", "JG", "MID", "ADC", "SUP" };


                        foreach (var item in nameGO)
                        {

                            for (int i = 0; i < 3; i++)
                            {

                                var woowo = Util.ReadIniData("AutomaticHero", item + "BanID" + i.ToString(), "0", @".\Data\Cofing.ini");
                                var TUP = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + woowo + ".png");
                                if (woowo != "0")
                                {
                                    switch (item)
                                    {

                                        case "TOP":

                                            var JE = new Form1.JInData();
                                            JE.TUP = TUP;
                                            JE.Banweiz = item;
                                            JE.woowo = woowo;
                                            JE.ID = i;
                                            Form1.BanDataKun1.Add(JE);
                                            break;

                                        case "JG":

                                            var JE1 = new Form1.JInData();
                                            JE1.TUP = TUP;
                                            JE1.Banweiz = item;
                                            JE1.woowo = woowo;
                                            JE1.ID = i;
                                            Form1.BanDataKun1.Add(JE1);
                                            break;

                                        case "MID":

                                            var JE2 = new Form1.JInData();
                                            JE2.TUP = TUP;
                                            JE2.Banweiz = item;
                                            JE2.woowo = woowo;
                                            JE2.ID = i;
                                            Form1.BanDataKun1.Add(JE2);
                                            break;
                                        case "ADC":

                                            var JE3 = new Form1.JInData();
                                            JE3.TUP = TUP;
                                            JE3.Banweiz = item;
                                            JE3.woowo = woowo;
                                            JE3.ID = i;
                                            Form1.BanDataKun1.Add(JE3);
                                            break;
                                        case "SUP":

                                            var JE4 = new Form1.JInData();
                                            JE4.TUP = TUP;
                                            JE4.Banweiz = item;
                                            JE4.woowo = woowo;
                                            JE4.ID = i;
                                            Form1.BanDataKun1.Add(JE4);
                                            break;
                                        default:
                                            break;
                                    }
                                }



                            }

                        }


                    }



                    if (Form1.form1.Banyx1.InvokeRequired)
                    {

                        Form1.form1.Banyx1.Invoke(new Action<int>(n =>
                        {

                            if (dddddddd())
                                wanbi = true;






                        }), 1);

                    }
                    else
                    {

                        Form1.form1.Banyx1.Enabled = false;
                        if (dddddddd())
                            wanbi = true;


                    }
                    if (wanbi)
                        break;



                }
                catch (Exception e)
                {

                    Console.WriteLine(e.Message);
                }


                Thread.Sleep(100);


            }




        }
    }
}

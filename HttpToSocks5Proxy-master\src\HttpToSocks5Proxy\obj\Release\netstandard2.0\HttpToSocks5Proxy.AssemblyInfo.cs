//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("MihaZupan")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Release")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © Miha Zupan 2019")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("This is a class that implements the IWebProxy interface to act as an HTTP(S) prox" +
    "y while connecting to a SOCKS5 server behind the scenes.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.4.0")]
[assembly: System.Reflection.AssemblyProductAttribute("HttpToSocks5Proxy")]
[assembly: System.Reflection.AssemblyTitleAttribute("MihaZupan.HttpToSocks5Proxy")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/MihaZupan/HttpToSocks5Proxy.git")]

// 由 MSBuild WriteCodeFragment 类生成。


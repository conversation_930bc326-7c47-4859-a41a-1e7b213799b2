﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    public partial class QuickStartForm : Form
    {

        Dictionary<string, string> PathSavedValues = new Dictionary<string, string>();


        public QuickStartForm()
        {
            InitializeComponent();
            listView1.ListViewItemSorter = new ListViewIndexComparer();
        }

        private void QuickStartForm_Load(object sender, EventArgs e)
        {
            this.AllowDrop = true;

            //listView1.DragEnter += (sender1, e1) =>
            //{
            //    e1.Effect = DragDropEffects.Link;//拖动时的图标
            //};

            //listView1.DragDrop += listView1_DragDrop;

            ReadPath();
        }


        public void ReadPath()
        {
            try
            {


                if (!File.Exists("./PathSavedValues.json"))
                {
                    return;
                }
                // listView1.BeginUpdate();

                var sr = File.ReadAllText("./PathSavedValues.json");
                PathSavedValues = JsonConvert.DeserializeObject<Dictionary<string, string>>(sr);

                foreach (var i in PathSavedValues)
                {
                    var pathname = i.Key;
                    if (System.IO.File.Exists(pathname))
                    {
                        var icon = Icon.ExtractAssociatedIcon(pathname);
                        var key = pathname;
                        this.imageList1.Images.Add(key, icon.ToBitmap());
                        this.listView1.Items.Add(i.Value, i.Key);
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
            }
            // listView1.EndUpdate();
        }
        //private void listView1_DragDrop(object sender, DragEventArgs e)
        //{
        //    var pathname = ((System.Array)e.Data.GetData(DataFormats.FileDrop)).GetValue(0).ToString();

        //    listView1.BeginUpdate();

        //    if (System.IO.File.Exists(pathname))
        //    {
        //        var icon = Icon.ExtractAssociatedIcon(pathname);

        //        var key = pathname;
        //        this.imageList1.Images.Add(key, icon.ToBitmap());
        //        string filename = System.IO.Path.GetFileName(pathname);

        //        this.listView1.Items.Add(filename, key);


        //        PathSavedValues.Add(key, filename);


        //        File.WriteAllText("./PathSavedValues.json", Newtonsoft.Json.JsonConvert.SerializeObject(PathSavedValues));

        //        /* Settings1.Default.Name.Add(filename);
        //         Settings1.Default.PathName.Add(key);
        //         Settings1.Default.Save();*/

        //    }

        //    listView1.EndUpdate();
        //}

        private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < listView1.SelectedIndices.Count; i++)
            {

                var index1 = listView1.SelectedIndices[i];
                PathSavedValues.Remove(listView1.Items[index1].ImageKey);
                this.imageList1.Images.RemoveAt(index1);
                listView1.Items.RemoveAt(index1);
                File.WriteAllText("./PathSavedValues.json", Newtonsoft.Json.JsonConvert.SerializeObject(PathSavedValues));

                /* Settings1.Default.PathName.RemoveAt(index1);
                 Settings1.Default.Name.RemoveAt(index1);
                 Settings1.Default.Save();*/
            }
        }
        int GOindex1 = -1;
        private void 重命名ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < listView1.SelectedIndices.Count; i++)
            {
                var index1 = listView1.SelectedIndices[i];
                listView1.Items[index1].BeginEdit();
                GOindex1 = index1;
            }
        }

        private void 打开ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < listView1.SelectedIndices.Count; i++)
            {
                var index1 = listView1.SelectedIndices[i];
               
                Process.Start("explorer.exe", listView1.Items[index1].ImageKey);
            }
        }

        private void listView1_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                listView1.ContextMenuStrip = null;
                if (listView1.SelectedItems.Count > 0)
                {
                    contextMenuStrip1.Show(listView1, new Point(e.X, e.Y));
                }
                else
                {
                    contextMenuStrip4.Show(listView1, new Point(e.X, e.Y));
                }
            }


        }

        private void 添加ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Multiselect = true;
            if (Util.InitialDialog(openFileDialog, "Open"))
            {

                for (int i = 0; i < openFileDialog.FileNames.Length; i++)
                {

                    var pathname = openFileDialog.FileNames[i];

                    if (System.IO.File.Exists(pathname))
                    {
                        var icon = Icon.ExtractAssociatedIcon(pathname);

                        var key = pathname;
                        this.imageList1.Images.Add(key, icon.ToBitmap());
                        string filename = System.IO.Path.GetFileName(pathname);
                        this.listView1.Items.Add(filename, key);


                        PathSavedValues.Add(key, filename);
                        File.WriteAllText("./PathSavedValues.json", Newtonsoft.Json.JsonConvert.SerializeObject(PathSavedValues));

                        /* Settings1.Default.Name.Add(filename);
                         Settings1.Default.PathName.Add(key);
                         Settings1.Default.Save();*/

                    }
                }


            }
        }

        private void listView1_MouseDoubleClick(object sender, MouseEventArgs e)
        {

            for (int i = 0; i < listView1.SelectedIndices.Count; i++)
            {
                var index1 = listView1.SelectedIndices[i];
                Process.Start("explorer.exe", listView1.Items[index1].ImageKey);
            }
        }

        private void 打开文件目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < listView1.SelectedIndices.Count; i++)
            {
                var index1 = listView1.SelectedIndices[i];

                string filename = System.IO.Path.GetFileName(listView1.Items[index1].ImageKey);

                var lujing = listView1.Items[index1].ImageKey.Substring(0, listView1.Items[index1].ImageKey.Length - filename.Length);
                if (File.Exists(lujing+ filename))
                {
                    Process.Start("explorer", "/select,\"" + lujing+ filename + "\"");
                }
                // Process.Start(lujing);
                //System.Diagnostics.Process.Start("Explorer", "/select," + lujing + "\\" + filename);

            }
        }





        private void listView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyData == Keys.F2)
            {
                if (listView1.SelectedIndices.Count > 0)
                {
                    重命名ToolStripMenuItem_Click(null, null);
                }

            }

            if (e.KeyData == Keys.Enter)
            {
                listView1_AfterLabelEdit(null, null);
            }
        }




        Dictionary<string, string> LSPathSavedValues = new Dictionary<string, string>();
        private void listView1_AfterLabelEdit(object sender, LabelEditEventArgs e)
        {
            try
            {
                if (listView1.Items.Count > 0)
                {
                    LSPathSavedValues.Clear();

                    for (int i = 0; i < listView1.Items.Count; i++)
                    {
                        string WBText = listView1.Items[i].Text; ;

                        string WKey = listView1.Items[i].ImageKey; ;


                        LSPathSavedValues.Add(WKey, WBText);


                    }
                    File.WriteAllText("./PathSavedValues.json", Newtonsoft.Json.JsonConvert.SerializeObject(LSPathSavedValues));


                }
            }
            catch (Exception)
            {


            }
        }

        private class ListViewIndexComparer : System.Collections.IComparer
        {
            public int Compare(object x, object y)
            {
                return ((ListViewItem)x).Index - ((ListViewItem)y).Index;
            }
        }
        //启动拖拽，设置拖拽的数据和效果。
        private void listView1_ItemDrag(object sender, ItemDragEventArgs e)
        {
            //sssss = e.Item;
            listView1.DoDragDrop(e.Item, DragDropEffects.Move);
        }
        //拖拽进入ListView，判断拖拽的数据格式，并设置拖拽的效果。
        private void listView1_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = e.AllowedEffect;
            //e.Effect = DragDropEffects.Move;
        }
        //拖动经过ListView时，设置拖动的效果，显示拖放位置线
        private void listView1_DragOver(object sender, DragEventArgs e)
        {
            Point ptScreen = new Point(e.X, e.Y);
            Point pt = listView1.PointToClient(ptScreen);
            ListViewItem item = listView1.GetItemAt(pt.X, pt.Y);

            int targetIndex = listView1.InsertionMark.NearestIndex(pt);
            if (targetIndex > -1)
            {
                Rectangle itemBounds = listView1.GetItemRect(targetIndex);
                if (pt.X > itemBounds.Left + (itemBounds.Width / 2))
                {
                    listView1.InsertionMark.AppearsAfterItem = true;
                }
                else
                {
                    listView1.InsertionMark.AppearsAfterItem = false;
                }
            }
            listView1.InsertionMark.Index = targetIndex;
            //if (item != null)
            //    item.Checked = true;
        }

        private void listView1_DragLeave(object sender, EventArgs e)
        {
            //MessageBox.Show(listView1.InsertionMark.Index.ToString()+"|"+sssss.ToString());

            listView1.InsertionMark.Index = -1;

        }



        private void listView1_DragDrop(object sender, DragEventArgs e)
        {







            ListViewItem draggedItem = (ListViewItem)e.Data.GetData(typeof(ListViewItem));
            //Point ptScreen = new Point(e.X, e.Y);
            //Point pt = listView1.PointToClient(ptScreen);
            //ListViewItem targetItem = listView1.GetItemAt(pt.X, pt.Y);//拖动的项将放置于该项之前

            //if (null == targetItem || targetItem == draggedItem)
            //    return;

            // NeedForFix: 项实际已经交换，但是显示没有交换
            if (draggedItem != null)
            {
                int targetIndex = listView1.InsertionMark.Index;
                if (targetIndex == -1)
                {
                    return;
                }
                if (listView1.InsertionMark.AppearsAfterItem)
                {
                    targetIndex++;
                }

                listView1.BeginUpdate();

                listView1.Items.Insert(targetIndex, (ListViewItem)draggedItem.Clone());
                listView1.Items.Remove(draggedItem);
                listView1.EndUpdate();

                listView1_AfterLabelEdit(null, null);
            }
            else
            {
                var pathname = ((System.Array)e.Data.GetData(DataFormats.FileDrop)).GetValue(0).ToString();

                listView1.BeginUpdate();

                if (System.IO.File.Exists(pathname))
                {
                    try
                    {
                       
                        listView1.BeginUpdate();

                        if (System.IO.File.Exists(pathname))
                        {
                            var icon = Icon.ExtractAssociatedIcon(pathname);

                            var key = pathname;
                            this.imageList1.Images.Add(key, icon.ToBitmap());
                            string filename = System.IO.Path.GetFileName(pathname);

                            this.listView1.Items.Add(filename, key);


                            PathSavedValues.Add(key, filename);


                            File.WriteAllText("./PathSavedValues.json", Newtonsoft.Json.JsonConvert.SerializeObject(PathSavedValues));

                            /* Settings1.Default.Name.Add(filename);
                             Settings1.Default.PathName.Add(key);
                             Settings1.Default.Save();*/

                        }

                        listView1.EndUpdate();
                    }
                    catch (Exception EE)
                    {
                        MessageBox.Show("异常错误" + EE.Message);
                        throw;
                    }







                }
            }

            listView1.EndUpdate();
        }


       
    }
}

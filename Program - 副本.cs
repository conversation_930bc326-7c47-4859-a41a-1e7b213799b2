﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;
using MythCapture.Properties;
using System.Net.NetworkInformation;
using Microsoft.Win32;

namespace WindowsFormsApp1
{
    static class Program
    {
        //开启控制台
        [DllImport("kernel32.dll")]
        public static extern Boolean AllocConsole();

        [STAThread]

        /// 该函数设置由不同线程产生的窗口的显示状态  
        /// </summary>  
        /// <param name="hWnd">窗口句柄</param>  
        /// <param name="cmdShow">指定窗口如何显示。查看允许值列表，请查阅ShowWlndow函数的说明部分</param>  
        /// <returns>如果函数原来可见，返回值为非零；如果函数原来被隐藏，返回值为零</returns>  
        [DllImport("User32.dll")]
        private static extern bool ShowWindowAsync(IntPtr hWnd, int cmdShow);


        [DllImport("user32.dll", EntryPoint = "ShowWindow", CharSet = CharSet.Auto)]
        public static extern int ShowWindow(IntPtr hwnd, int nCmdShow);

        /// <summary>  
        ///  该函数将创建指定窗口的线程设置到前台，并且激活该窗口。键盘输入转向该窗口，并为用户改各种可视的记号。  
        ///  系统给创建前台窗口的线程分配的权限稍高于其他线程。   
        /// </summary>  
        /// <param name="hWnd">将被激活并被调入前台的窗口句柄</param>  
        /// <returns>如果窗口设入了前台，返回值为非零；如果窗口未被设入前台，返回值为零</returns>  
        [DllImport("User32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);
        private const int SW_SHOWNOMAL = 1;

        public static MythCapture.res.MythCapture JIEJIEJIE =null;

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {



            //Process process = RunningInstance();

            //if (process != null)
            //{
            //    HandleRunningInstance(process);
            //    System.Environment.Exit(1);
            //}

            //{

            //    Application.EnableVisualStyles();
            //    Application.SetCompatibleTextRenderingDefault(false);
            //    Application.Run(new Form1());	//这里修改成主界面的Form名
            //}



            // 注册系统重启前事件监听器
            SystemEvents.SessionEnding += new SessionEndingEventHandler(SystemEvents_SessionEnding);
            // 注册系统关机前事件监听器
            SystemEvents.SessionEnded += new SessionEndedEventHandler(SystemEvents_SessionEnded);

            Console.WriteLine("正在监听系统重启和关机前事件，按任意键退出程序。");
            
            bool isRuned;
            System.Threading.Mutex mutex = new System.Threading.Mutex(true, "@asd2l123ig235..1235i2893751235", out isRuned);
            if (isRuned)
            {
                //AllocConsole();
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                //Application.Run(new Form1());
                

                /**
             * 当前用户是管理员的时候，直接启动应用程序
             * 如果不是管理员，则使用启动对象启动程序，以确保使用管理员身份运行
             */
                //获得当前登录的Windows用户标示
                System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
                //判断当前登录用户是否为管理员
                if (principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator))
                {

                    //new Thread(MythCapture.p).Start();
                    //new System.Threading.Thread((System.Threading.ThreadStart)delegate
                    //{
                    //    JIEJIEJIE = new MythCapture.res.MythCapture();
                    //    Application.Run(JIEJIEJIE);


                    //}).Start();
                    //如果是管理员，则直接运行
                    Application.Run(new Form1());
                    mutex.ReleaseMutex();
                }
                else
                {
                    //创建启动对象
                    System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                    startInfo.UseShellExecute = true;
                    startInfo.WorkingDirectory = Environment.CurrentDirectory;
                    startInfo.FileName = Application.ExecutablePath;
                    //设置启动动作,确保以管理员身份运行
                    startInfo.Verb = "runas";
                    try
                    {
                        System.Diagnostics.Process.Start(startInfo);
                        mutex.ReleaseMutex();
                    }
                    catch
                    {
                        return;
                    }
                    //退出
                    Application.Exit();


                }
                //Application.EnableVisualStyles();
                //Application.SetCompatibleTextRenderingDefault(false);
                //Application.Run(new Form1());
            }
            else
            {
                Util.Xieruwenj(@".\Data\HUID.dll", "1");
                //MessageBox.Show("程序已启动!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            //AllocConsole();



        }


        static void SystemEvents_SessionEnding(object sender, SessionEndingEventArgs e)
        {
            if (e.Reason == SessionEndReasons.SystemShutdown || e.Reason == SessionEndReasons.Logoff)
            {
                // 记录系统即将重启的日志
                LogMessage("系统即将重启-还原DNS");
                // 设置网络接口为自动获取DNS
                SetAutoDNS();
            }
        }

        static void SystemEvents_SessionEnded(object sender, SessionEndedEventArgs e)
        {
            // 记录系统已关机的日志
            LogMessage("系统已关机-还原DNS");
            // 这里也可以根据需求添加其他针对关机情况想执行的逻辑
            SetAutoDNS();
        }

        /// <summary>
        /// 设置网络接口为自动获取DNS
        /// </summary>
        public static void SetAutoDNS()
        {
            NetworkInterface[] nics = NetworkInterface.GetAllNetworkInterfaces();
            foreach (NetworkInterface adapter in nics)
            {
                if (adapter.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                {
                    string interfaceGuid = adapter.Id;
                    RegistryKey rk = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces\" + interfaceGuid, true);
                    if (rk != null)
                    {
                        // 设置DNS服务器为空（自动获取）
                        rk.SetValue("NameServer", "", RegistryValueKind.String);
                        // 启用DHCP（自动获取IP相关设置）
                        rk.SetValue("EnableDHCP", 1, RegistryValueKind.DWord);
                        rk.Close();
                    }
                }
            }
        }

        /// <summary>
        /// 记录日志信息到文件
        /// </summary>
        /// <param name="message">要记录的日志消息</param>
        public static void LogMessage(string message)
        {
            string logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "system_events.log");
            using (StreamWriter writer = File.AppendText(logFilePath))
            {
                writer.WriteLine($"{DateTime.Now}: {message}");
            }
        }
        private static void HandleRunningInstance(Process instance)
        {
           
           if (instance.MainWindowHandle.ToInt32()==0)
            {
                MessageBox.Show("程序已启动!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowWindowAsync(instance.MainWindowHandle, SW_SHOWNOMAL);   //显示  
                SetForegroundWindow(instance.MainWindowHandle); //当到最前端  
            }

            

        }
        //public static Process RunningInstance()
        //{
        //    Process currentProcess = Process.GetCurrentProcess();
        //    Process[] Processes = Process.GetProcessesByName(currentProcess.ProcessName);
        //    foreach (Process process in Processes)
        //    {
        //        if (process.Id != currentProcess.Id)
        //        {
        //            if (Assembly.GetExecutingAssembly().Location.Replace("/", "\\") == currentProcess.MainModule.FileName)
        //            {
        //                return process;
        //            }
        //        }
        //    }
        //    return null;
        //    throw new NotImplementedException();


        //}
       　public static Process RunningInstance()

        {

            Process current = Process.GetCurrentProcess();

            Process[] processes = Process.GetProcessesByName(current.ProcessName);

            //Loop through the running processes in with the same name

            foreach (Process process in processes)

            {

                //Ignore the current process

                if (process.Id != current.Id)

                {

                    //Make sure that the process is running from the exe file.

                    if (Assembly.GetExecutingAssembly().Location.Replace("/", @"\") == current.MainModule.FileName)
                  

{

                        //Return the other process instance.

                        return process;

                    }

                }

            }

            //No other instance was found, return null.

            return null;

        }
    }

 }

﻿using MihaZupan;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;

namespace WindowsFormsApp1
{
    public partial class Form4 : Form
    {
        public static Form4 form4;
        public Form4()
        {
            InitializeComponent();
            form4 = this;
            //Initialising();
        }

        public static bool C4_YIJI = false;
        public static Color QQQQ = Color.FromArgb(17, 27, 34);
        public static Color EEEE = Color.FromArgb(27, 47, 34);
        public static Color PPPP = Color.FromArgb(16, 43, 46);
        public static ToolTip ttpSettings = new ToolTip();
        private void Form4_Load(object sender, EventArgs e)
        {
            //this.WindowState = FormWindowState.Normal;

            Util.ReadBatch(form4);

            ttpSettings.InitialDelay = 50;
            ttpSettings.AutoPopDelay = 10 * 1000;
            ttpSettings.ReshowDelay = 50;
            ttpSettings.ShowAlways = true;
            ttpSettings.IsBalloon = true;
            ttpSettings.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(17)))), ((int)(((byte)(27)))), ((int)(((byte)(34)))));
            ttpSettings.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(163)))), ((int)(((byte)(143)))), ((int)(((byte)(120)))));


        }
        static Thread thread = null;
        public static void ceshi123(string Signal, int jubing)
        {
            switch (Signal)
            {
                case "窗口隐藏":
                    Form4.form4.Visible = false;
                    Console.WriteLine("窗口隐藏" + "-可视:" + Form4.form4.Visible);
                    break;
                case "窗口移动":
                    Form4.form4.Visible = true;
                    Form4.Adjustwindow(jubing); ;
                    Console.WriteLine("窗口移动"+ "--可视:"+Form4.form4.Visible);
                    break;

                case "一键符文":
                    Fenzuname = "";
                    Util.ReadBatch(form4);
                    chongxin(0);
                    Console.WriteLine("一键符文" + "---可视:" + Form4.form4.Visible);
                    break;
                default:
                    //Console.WriteLine("无效的成绩");
                    break;
            }
        }


        class Runeimages
        {
            public string ImageID;
            public string Yey;
            public string Runicmarkers;
            public Runeimages(string ImageID1 = "", string Yey1 = "", string Runicmarkers1 = "")
            {
                ImageID = ImageID1;
                Yey = Yey1;
                Runicmarkers = Runicmarkers1;
            }
        }



        static void Adjustwindow(int CKjubing)
        {
            if (CKjubing != 0)
            {
                IntPtr WWWW = new IntPtr(CKjubing);

                Util.RECT rect = new Util.RECT();

                Util.GetWindowRect(WWWW, ref rect);

                int width = rect.Right - rect.Left; //窗口的宽度

                int height = rect.Bottom - rect.Top; //窗口的高度

                int x = rect.Left;

                int y = rect.Top;

                Form4.form4.Left = x + width;

                Form4.form4.Top = y + height - Form4.form4.Height;
            }


        }


        static void fuweishuchu(string shuchu, int moshi = 0)
        {




            //r=control.BackColor.R;//红色分量

            //g = control.BackColor.G;//绿色分量

            // b = control.BackColor.B;//蓝色分量
            if (form4.shuchuru.InvokeRequired)
            {
                form4.shuchuru.Invoke(new Action<int>(n =>
                {
                    form4.shuchuru.Text = shuchu;

                    switch (moshi)
                    {
                        case 0://获取
                            form4.shuchuru.ForeColor = Color.FromArgb(128, 128, 128, 0);
                            break;
                        case 1://失败
                            form4.shuchuru.ForeColor = Color.FromArgb(178, 34, 34, 0);
                            break;
                        case 2://成功
                            form4.shuchuru.ForeColor = Color.FromArgb(60, 179, 113, 0);
                            break;
                        case 3://使用
                            form4.shuchuru.ForeColor = Color.FromArgb(255, 255, 0, 0);
                            break;

                        default://未知
                            form4.shuchuru.ForeColor = Color.FromArgb(255, 255, 0, 0);
                            break;
                    }


                }), 1);
            }
        }


        class CECECE
        {
            public List<string> Rank;

            public CECECE(List<string> Rank1 = null)
            {
                Rank = Rank1;
            }

        }



        static List<CECECE> Runeimages_1 = new List<CECECE>();//符文

        static List<string> SkillAddition = new List<string>();//主要加点
        static List<string> SkillAddition_0 = new List<string>();//顺序加点

        static List<string> Boots = new List<string>();//靴子
        static List<string> Initializing = new List<string>();//初始化装备
        static List<string> Coreequipment = new List<string>();//核心装备




        static List<string> TJEquipment = new List<string>();

        static string Fenzuname = "";

        static string[] Z_zhaohua = { "0", "0" };
        // static string FFFWWWW = "";

        static async Task asdasd(string utimode, string MOshi, string YXname, string moSHI, string xuanzhong)
        {
            var YXIDID = Util.GetheronameID(YXname);

            var YXfenzhu = Util.GetheronameWEI(YXIDID);

            var FFFWWWW = Util.Getheroname1(YXIDID);


            utimode = utimode + YXIDID;

            if (MOshi == "正常模式")
            {

                if (Fenzuname == "")
                {
                    if (YXfenzhu.Count() > 0)
                    {
                        TIaoFUW = YXfenzhu[0];
                        utimode = utimode + @"/" + YXfenzhu[0];
                        FFFWWWW = FFFWWWW + " [" + YXfenzhu[0] + "] ";
                    }
                }
                else
                {
                    utimode = utimode + @"/" + Fenzuname;

                    FFFWWWW = FFFWWWW + " [" + Fenzuname + "] ";

                    TIaoFUW = Fenzuname;
                }




            }
            else
            {
                TIaoFUW = moSHI;
                FFFWWWW = FFFWWWW + " [" + MOshi + "] ";
                utimode += "/None";
            }

          
            string wTXT = "";
            fuweishuchu("正在为" + YXname + "匹配符文");
            //wTXT = Util.HttpRequest_Get(utimode);
            wTXT = Util.HttpRequest_Get(utimode);


            if (wTXT.IndexOf("data") != -1)
            {
                if (form4.comboBox1.InvokeRequired)
                {

                    form4.comboBox1.Invoke(new Action<int>(n =>
                    {

                        form4.comboBox1.Items.Clear();



                        if (MOshi == "正常模式")
                        {

                            for (int i = 0; i < YXfenzhu.Count(); i++)
                            {
                                form4.comboBox1.Items.Add(YXfenzhu[i]);
                            }
                        }
                        else
                        {
                            form4.comboBox1.Items.Add(MOshi);
                        }
                        if (form4.comboBox1.Items.Count > 0)
                        {

                            form4.comboBox1.SelectedIndex = int.Parse(xuanzhong);

                        }

                    }), 1);


                }
                else
                {
                    form4.comboBox1.Items.Clear();
                }


                Runeimages_1 = new List<CECECE>();//符文

                SkillAddition = new List<string>();//主要加点

                SkillAddition_0 = new List<string>();//顺序加点

                Boots = new List<string>();//靴子

                Initializing = new List<string>();//初始化装备

                Coreequipment = new List<string>();//核心装备




                var S1 = JSON.Jiexun(wTXT);

                var S2 = S1["data"];

                Console.WriteLine(S2["rune_pages"].Count());

                var S3 = S2["rune_pages"];//符文

                var S4 = S2["skill_masteries"];//加点

                //----------------------------------------------------------
                var S5 = S2["boots"];//推荐靴子

                var S6 = S2["starter_items"];//推荐初始化

                var S7 = S2["core_items"];//推荐核心装备

                var S8 = S2["summoner_spells"];//推荐召唤师技能


                for (int i = 0; i < S8.Count(); i++)
                {
                    var S8_1 = S8[i]["ids"];//取召唤师技能数组

                    Z_zhaohua[0] = S8_1[0].ToString();
                    Z_zhaohua[1] = S8_1[1].ToString();

                    break;
                }

                for (int i = 0; i < S7.Count(); i++)//加入推荐核心装备
                {
                    if (i == 3)
                        break;
                    var S7_1 = S7[i]["ids"];//取初推荐核心装备

                    for (int i1 = 0; i1 < S7_1.Count(); i1++)
                    {
                        Console.WriteLine(S7_1[i1].ToString());//加入推荐核心装备
                        Coreequipment.Add(S7_1[i1].ToString());
                    }
                }

                for (int i = 0; i < S6.Count(); i++)//加入初始化装备
                {
                    if (i == 2)
                        break;
                    var S6_1 = S6[i]["ids"];//取初始化装备数组

                    for (int i1 = 0; i1 < S6_1.Count(); i1++)
                    {
                        Console.WriteLine(S6_1[i1].ToString());//加入初始化装备
                        Initializing.Add(S6_1[i1].ToString());
                    }
                }


                for (int i = 0; i < S5.Count(); i++)//加入靴子
                {
                    if (i == 3)
                        break;
                    var S5_1 = S5[i]["ids"];//取靴子数组

                    for (int i1 = 0; i1 < S5_1.Count(); i1++)
                    {
                        Console.WriteLine(S5_1[i1].ToString());//加入靴子

                        Boots.Add(S5_1[i1].ToString());
                    }
                }




                for (int i = 0; i < S4.Count(); i++)
                {
                    if (i == 1)
                        break;
                    var S4_1 = S4[i]["ids"];//取主加点数组
                    for (int i1 = 0; i1 < S4_1.Count(); i1++)
                    {
                        Console.WriteLine(S4_1[i1].ToString());//加入主加点1-3

                        SkillAddition.Add((S4_1[i1].ToString()));
                    }

                    var S4_2 = S4[i]["builds"];//取顺序加点数组1

                    for (int i2 = 0; i2 < S4_2.Count(); i2++)
                    {
                        if (i2 == 1)
                            break;
                        var S4_3 = S4_2[i2]["order"];//取顺序加点数组2

                        for (int i3 = 0; i3 < S4_3.Count(); i3++)
                        {
                            if (i3 == 3)
                                break;
                            Console.WriteLine(S4_3[i3].ToString());//加入顺序加点1-3
                            SkillAddition_0.Add((S4_3[i3].ToString()));
                        }

                    }
                }//技能加点


                for (int i = 0; i < S3.Count(); i++)//判断符文几个
                {
                    if (i == 2)
                        break;

                    var S3_1 = S3[i]["builds"];//进入第一个符文

                    for (int i1 = 0; i1 < S3_1.Count(); i1++)
                    {
                        if (i1 == 2)
                            break;
                        var gogo = "";
                        var DDDDD = new CECECE(new List<string>());


                        Console.WriteLine(S3_1[i1]["primary_page_id"].ToString());//加入 主符文类型
                        gogo = S3_1[i1]["primary_page_id"].ToString();

                        DDDDD.Rank.Add(gogo);
                        var S3_2 = S3_1[i1]["primary_rune_ids"];//第一个符文加入 主符文数组

                        for (int i2 = 0; i2 < S3_2.Count(); i2++)
                        {
                            gogo = S3_2[i2].ToString();

                            DDDDD.Rank.Add(gogo);
                            Console.WriteLine(gogo);//第一个符文加入 主符文1-4

                        }

                        gogo = S3_1[i1]["secondary_page_id"].ToString();

                        DDDDD.Rank.Add(gogo);
                        Console.WriteLine(gogo);//加入 副符文类型

                        var S3_3 = S3_1[i1]["secondary_rune_ids"];//第一个符文加入 副符文数组

                        for (int i3 = 0; i3 < S3_3.Count(); i3++)
                        {
                            gogo = S3_3[i3].ToString();

                            DDDDD.Rank.Add(gogo);
                            Console.WriteLine(gogo);//第一个符文加入 副符文1-2
                        }

                        var S3_4 = S3_1[i1]["stat_mod_ids"];//第一个符文加入 防御生命攻击

                        for (int i4 = 0; i4 < S3_4.Count(); i4++)
                        {
                            gogo = S3_4[i4].ToString();

                            DDDDD.Rank.Add(gogo);
                            Console.WriteLine(gogo);//第一个符文加入 防御生命攻击 1-3
                        }

                        Console.WriteLine("------------------------------------------------------------");


                        Runeimages_1.Add(DDDDD);

                    }
                }

                Console.WriteLine(Runeimages_1.Count());

                foreach (var item in Runeimages_1)
                {
                    foreach (var item1 in item.Rank)
                    {
                        Console.WriteLine(item1);
                    }

                }

                foreach (var item in SkillAddition)
                {
                    Console.WriteLine("主要:" + item);
                }
                foreach (var item in SkillAddition_0)
                {
                    Console.WriteLine("顺序:" + item);
                }

                foreach (var item in Boots)
                {
                    Console.WriteLine("靴子:" + item);
                }
                foreach (var item in Initializing)
                {
                    Console.WriteLine("初始化:" + item);
                }
                foreach (var item in Coreequipment)
                {
                    Console.WriteLine("核心装备:" + item);
                }


                if (form4.label9.InvokeRequired)
                {
                    form4.label9.Invoke(new Action<int>(n =>
                    {
                        var jiadian = "";
                        var jiadian1 = "";
                        foreach (var item in SkillAddition)
                        {
                            jiadian += item + "-";
                            Console.WriteLine("主要:" + item);
                        }
                        foreach (var item in SkillAddition_0)
                        {
                            jiadian1 += item + "-";
                            Console.WriteLine("顺序:" + item);
                        }

                        form4.label9.Text = "推荐加点: " + jiadian.Substring(0, jiadian.Length - 1);

                        form4.label10.Text = "加点顺序:" + jiadian1.Substring(0, jiadian1.Length - 1);


                        Util.Jinengjiadian1 = form4.label9.Text;
                        Util.Jinengjiadian2 = form4.label10.Text;
                        //Form7.form7.label3.Text = form4.label9.Text;

                        //Form7.form7.label6.Text = form4.label10.Text;


                    }), 1);
                }
                else
                {
                    form4.label9.Text = "技能加点";
                }


                if (form4.label1.InvokeRequired)
                {
                    form4.label1.Invoke(new Action<int>(n =>
                    {
                        Settingtherunes(FFFWWWW);
                    }), 1);
                }
                else
                {
                    form4.label1.Text = "符文触发";
                }

                Console.WriteLine("获取" + YXname + "符文成功!");
                fuweishuchu("获取" + YXname + "符文成功!", 2);
                C4_YIJI = true;
                if (form4.button1.InvokeRequired)
                {
                    form4.button1.Invoke(new Action<int>(n =>
                    {
                        form4.button1.Enabled = true;
                        form4.button2.Enabled = true;
                        form4.button3.Enabled = true;
                        form4.button4.Enabled = true;
                        form4.comboBox1.Enabled = true;
                        form4.button5.Visible = false;
                    }), 1);
                }
                //if (Form1.form1.checkBox5.Checked)
                if (Form1.form1.checkBox11.Checked)
                {
                    Form1.form1.T_msg("自动操作点击符文", 3, 1);
                    form4.button1_Click(null, null);
                }





            }
            else
            {

                Util.XIErunin("符文问题", "wTXT", wTXT);
                Util.XIErunin("符文问题", "utimode", utimode);
                Console.WriteLine("获取" + YXname + "符文失败!");
                fuweishuchu("获取" + YXname + "符文失败!", 1);
                if (form4.button5.InvokeRequired)
                {
                    form4.button5.Invoke(new Action<int>(n =>
                    {
                        form4.button5.Visible = true;
                        form4.checkBox2.Visible = true;
                    }), 1);
                }
                else
                {
                    form4.button5.Visible = true;
                    form4.checkBox2.Visible = true;
                }
                if(C4_YIJI)
                {
                    if (form4.button1.InvokeRequired)
                    {
                        form4.button1.Invoke(new Action<int>(n =>
                        {
                            form4.button1.Enabled = true;
                            form4.button2.Enabled = true;
                            form4.button3.Enabled = true;
                            form4.button4.Enabled = true;
                            form4.comboBox1.Enabled = true;
                            form4.button5.Visible = true;
                            form4.checkBox2.Visible = true;
                        }), 1);
                    }
                }
               
            }
        }

        static string TIaoFUW = "";
        static void caozhi(object xuanzhong)
        {



            if (form4.button1.InvokeRequired)
            {
                form4.button1.Invoke(new Action<int>(n =>
                {
                    form4.button1.Enabled = false;
                    form4.button2.Enabled = false;
                    form4.button3.Enabled = false;
                    form4.button4.Enabled = false;
                    form4.comboBox1.Enabled = false;
                    form4.button5.Enabled = false;
                    form4.button5.Visible = false;
                    form4.checkBox2.Visible = false;
                    foreach (Control c in form4.panel1.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel2.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel3.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel4.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    yanyan(0);
                }), 1);
            }
            else
            {
                form4.button1.Enabled = false;
                form4.button2.Enabled = false;
                form4.button3.Enabled = false;
                form4.button4.Enabled = false;
                form4.comboBox1.Enabled = false;
                form4.button5.Enabled = false;
                form4.button5.Visible = false;
                form4.checkBox2.Visible = false;
            }

            var YXname = Util.FuwenHeroIDImage;

            Console.WriteLine("正在为" + YXname + "匹配符文");



            if (form4.pictureBox1.InvokeRequired)
            {
                form4.pictureBox1.Invoke(new Action<int>(n =>
                {
                    form4.pictureBox1.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + Util.FuwenHeroID + ".png");
                }), 1);
            }

            var JIExi = Util.zi_Getwenb("/lol-gameflow/v1/session");

            var jiexi1 = JSON.Jiexun(JIExi)
;
            string utimode = "";

            string MOshi = "正常模式";

            if (jiexi1 != null && jiexi1.Count > 0)
            {


                var moSHI = jiexi1["map"]["gameMode"].ToString();

                moSHI = moSHI.ToLower();

                switch (moSHI)
                {
                    //ARAM URF
                    case "aram"://大乱斗
                        utimode = "https://lol-api-champion.op.gg/api/tw/champions/aram/";
                        MOshi = "大乱斗";
                        break;
                    case "urf"://无限火力
                        utimode = "https://lol-api-champion.op.gg/api/tw/champions/urf/";
                        MOshi = "无限火力";
                        break;
                    default://正常模式
                        utimode = "https://lol-api-champion.op.gg/api/tw/champions/ranked/";
                        MOshi = "正常模式";
                        break;
                }

                //Util.HttpToSocks5Proxy(utimode, "127.0.0.1", 2801).ToString();

                string wTXT = Util.HttpRequest_Get(utimode);


                //string wTXT = Util.fanhui;                

                asdasd(utimode, MOshi, YXname, moSHI, xuanzhong.ToString()).ToString();












            }
            if (form4.button5.InvokeRequired)
            {
                form4.button5.Invoke(new Action<int>(n =>
                {
                    form4.button5.Enabled = true;
                }), 1);
            }



        }
        static void Settingtherunes(string FFFF)
        {
            for (int i = 0; i < Runeimages_1.Count(); i++)
            {




                if (i == 0)
                {

                    form4.pictureBox3.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[0] + ".PNG");//精密

                    string tipOverwrite = GtefuwenID(Runeimages_1[i].Rank[0]);


                    form4.label1.Text = GtefuwenID(Runeimages_1[i].Rank[0]);


                    form4.pictureBox4.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[1] + ".PNG");
                    form4.pictureBox5.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[2] + ".PNG");
                    form4.pictureBox6.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[3] + ".PNG");
                    form4.pictureBox7.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[4] + ".PNG");

                    form4.pictureBox8.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[5] + ".PNG");//主宰
                    form4.label2.Text = GtefuwenID(Runeimages_1[i].Rank[5]);
                    form4.pictureBox9.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[6] + ".PNG");
                    form4.pictureBox10.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[7] + ".PNG");

                    form4.pictureBox13.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[8] + ".PNG");// 进攻
                    form4.pictureBox12.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[9] + ".PNG");// 灵活
                    form4.pictureBox11.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[10] + ".PNG");//防御

                    var fuwei1 = GtefuwenID(Runeimages_1[i].Rank[8]);//进攻
                    var fuwei2 = GtefuwenID(Runeimages_1[i].Rank[9]);//灵活
                    var fuwei3 = GtefuwenID(Runeimages_1[i].Rank[10]);//防御

                    form4.label3.Text = FFFF + " " + form4.label1.Text + " " + form4.label2.Text;//英雄名称+精密 主宰

                    form4.label4.Text = "进攻:[" + fuwei1 + "],灵活:[" + fuwei2 + "],防御:[" + fuwei3 + "]";//显示 进攻 灵活 防御



                    ttpSettings.SetToolTip(form4.pictureBox3, GtefuwenID(Runeimages_1[i].Rank[0]));
                    ttpSettings.SetToolTip(form4.pictureBox4, GtefuwenID(Runeimages_1[i].Rank[1]));
                    ttpSettings.SetToolTip(form4.pictureBox5, GtefuwenID(Runeimages_1[i].Rank[2]));
                    ttpSettings.SetToolTip(form4.pictureBox6, GtefuwenID(Runeimages_1[i].Rank[3]));
                    ttpSettings.SetToolTip(form4.pictureBox7, GtefuwenID(Runeimages_1[i].Rank[4]));
                    ttpSettings.SetToolTip(form4.pictureBox8, GtefuwenID(Runeimages_1[i].Rank[5]));
                    ttpSettings.SetToolTip(form4.pictureBox9, GtefuwenID(Runeimages_1[i].Rank[6]));
                    ttpSettings.SetToolTip(form4.pictureBox10, GtefuwenID(Runeimages_1[i].Rank[7]));
                    ttpSettings.SetToolTip(form4.pictureBox13, GtefuwenID(Runeimages_1[i].Rank[8]));
                    ttpSettings.SetToolTip(form4.pictureBox12, GtefuwenID(Runeimages_1[i].Rank[9]));
                    ttpSettings.SetToolTip(form4.pictureBox11, GtefuwenID(Runeimages_1[i].Rank[10]));




                }

                if (i == 1)
                {
                    form4.pictureBox24.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[0] + ".PNG");//精密

                    form4.label8.Text = GtefuwenID(Runeimages_1[i].Rank[0]);


                    form4.pictureBox23.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[1] + ".PNG");
                    form4.pictureBox22.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[2] + ".PNG");
                    form4.pictureBox21.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[3] + ".PNG");
                    form4.pictureBox20.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[4] + ".PNG");

                    form4.pictureBox19.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[5] + ".PNG");//主宰

                    form4.label7.Text = GtefuwenID(Runeimages_1[i].Rank[5]);

                    form4.pictureBox18.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[6] + ".PNG");
                    form4.pictureBox17.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[7] + ".PNG");

                    form4.pictureBox16.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[8] + ".PNG");// 进攻
                    form4.pictureBox15.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[9] + ".PNG");// 灵活
                    form4.pictureBox14.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[10] + ".PNG");//防御

                    var fuwei1 = GtefuwenID(Runeimages_1[i].Rank[8]);//进攻
                    var fuwei2 = GtefuwenID(Runeimages_1[i].Rank[9]);//灵活
                    var fuwei3 = GtefuwenID(Runeimages_1[i].Rank[10]);//防御

                    form4.label6.Text = FFFF + " " + form4.label8.Text + " " + form4.label7.Text;//英雄名称+精密 主宰

                    form4.label5.Text = "进攻:[" + fuwei1 + "],灵活:[" + fuwei2 + "],防御:[" + fuwei3 + "]";//显示 进攻 灵活 防御

                    ttpSettings.SetToolTip(form4.pictureBox24, GtefuwenID(Runeimages_1[i].Rank[0]));
                    ttpSettings.SetToolTip(form4.pictureBox23, GtefuwenID(Runeimages_1[i].Rank[1]));
                    ttpSettings.SetToolTip(form4.pictureBox22, GtefuwenID(Runeimages_1[i].Rank[2]));
                    ttpSettings.SetToolTip(form4.pictureBox21, GtefuwenID(Runeimages_1[i].Rank[3]));
                    ttpSettings.SetToolTip(form4.pictureBox20, GtefuwenID(Runeimages_1[i].Rank[4]));
                    ttpSettings.SetToolTip(form4.pictureBox19, GtefuwenID(Runeimages_1[i].Rank[5]));
                    ttpSettings.SetToolTip(form4.pictureBox18, GtefuwenID(Runeimages_1[i].Rank[6]));
                    ttpSettings.SetToolTip(form4.pictureBox17, GtefuwenID(Runeimages_1[i].Rank[7]));
                    ttpSettings.SetToolTip(form4.pictureBox16, GtefuwenID(Runeimages_1[i].Rank[8]));
                    ttpSettings.SetToolTip(form4.pictureBox15, GtefuwenID(Runeimages_1[i].Rank[9]));
                    ttpSettings.SetToolTip(form4.pictureBox14, GtefuwenID(Runeimages_1[i].Rank[10]));

                }
                if (i == 2)
                {
                    form4.pictureBox35.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[0] + ".PNG");//精密

                    form4.label14.Text = GtefuwenID(Runeimages_1[i].Rank[0]);


                    form4.pictureBox34.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[1] + ".PNG");
                    form4.pictureBox33.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[2] + ".PNG");
                    form4.pictureBox32.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[3] + ".PNG");
                    form4.pictureBox31.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[4] + ".PNG");

                    form4.pictureBox30.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[5] + ".PNG");//主宰
                    form4.label13.Text = GtefuwenID(Runeimages_1[i].Rank[5]);
                    form4.pictureBox29.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[6] + ".PNG");
                    form4.pictureBox28.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[7] + ".PNG");

                    form4.pictureBox27.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[8] + ".PNG");// 进攻
                    form4.pictureBox26.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[9] + ".PNG");// 灵活
                    form4.pictureBox25.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[10] + ".PNG");//防御

                    var fuwei1 = GtefuwenID(Runeimages_1[i].Rank[8]);//进攻
                    var fuwei2 = GtefuwenID(Runeimages_1[i].Rank[9]);//灵活
                    var fuwei3 = GtefuwenID(Runeimages_1[i].Rank[10]);//防御

                    form4.label12.Text = FFFF + " " + form4.label14.Text + " " + form4.label13.Text;//英雄名称+精密 主宰

                    form4.label11.Text = "进攻:[" + fuwei1 + "],灵活:[" + fuwei2 + "],防御:[" + fuwei3 + "]";//显示 进攻 灵活 防御


                    ttpSettings.SetToolTip(form4.pictureBox35, GtefuwenID(Runeimages_1[i].Rank[0]));
                    ttpSettings.SetToolTip(form4.pictureBox34, GtefuwenID(Runeimages_1[i].Rank[1]));
                    ttpSettings.SetToolTip(form4.pictureBox33, GtefuwenID(Runeimages_1[i].Rank[2]));
                    ttpSettings.SetToolTip(form4.pictureBox32, GtefuwenID(Runeimages_1[i].Rank[3]));
                    ttpSettings.SetToolTip(form4.pictureBox31, GtefuwenID(Runeimages_1[i].Rank[4]));
                    ttpSettings.SetToolTip(form4.pictureBox30, GtefuwenID(Runeimages_1[i].Rank[5]));
                    ttpSettings.SetToolTip(form4.pictureBox29, GtefuwenID(Runeimages_1[i].Rank[6]));
                    ttpSettings.SetToolTip(form4.pictureBox28, GtefuwenID(Runeimages_1[i].Rank[7]));
                    ttpSettings.SetToolTip(form4.pictureBox27, GtefuwenID(Runeimages_1[i].Rank[8]));
                    ttpSettings.SetToolTip(form4.pictureBox26, GtefuwenID(Runeimages_1[i].Rank[9]));
                    ttpSettings.SetToolTip(form4.pictureBox25, GtefuwenID(Runeimages_1[i].Rank[10]));
                }
                if (i == 3)
                {
                    form4.pictureBox46.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[0] + ".PNG");//精密

                    form4.label18.Text = GtefuwenID(Runeimages_1[i].Rank[0]);


                    form4.pictureBox45.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[1] + ".PNG");
                    form4.pictureBox44.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[2] + ".PNG");
                    form4.pictureBox43.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[3] + ".PNG");
                    form4.pictureBox42.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[4] + ".PNG");

                    form4.pictureBox41.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[5] + ".PNG");//主宰
                    form4.label17.Text = GtefuwenID(Runeimages_1[i].Rank[5]);
                    form4.pictureBox40.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[6] + ".PNG");
                    form4.pictureBox39.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[7] + ".PNG");

                    form4.pictureBox38.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[8] + ".PNG");// 进攻
                    form4.pictureBox37.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[9] + ".PNG");// 灵活
                    form4.pictureBox36.Image = Util.ReadImageFile(@"./符文资源/" + Runeimages_1[i].Rank[10] + ".PNG");//防御

                    var fuwei1 = GtefuwenID(Runeimages_1[i].Rank[8]);//进攻
                    var fuwei2 = GtefuwenID(Runeimages_1[i].Rank[9]);//灵活
                    var fuwei3 = GtefuwenID(Runeimages_1[i].Rank[10]);//防御

                    form4.label16.Text = FFFF + " " + form4.label18.Text + " " + form4.label17.Text;//英雄名称+精密 主宰

                    form4.label15.Text = "进攻:[" + fuwei1 + "],灵活:[" + fuwei2 + "],防御:[" + fuwei3 + "]";//显示 进攻 灵活 防御


                    ttpSettings.SetToolTip(form4.pictureBox46, GtefuwenID(Runeimages_1[i].Rank[0]));
                    ttpSettings.SetToolTip(form4.pictureBox45, GtefuwenID(Runeimages_1[i].Rank[1]));
                    ttpSettings.SetToolTip(form4.pictureBox44, GtefuwenID(Runeimages_1[i].Rank[2]));
                    ttpSettings.SetToolTip(form4.pictureBox43, GtefuwenID(Runeimages_1[i].Rank[3]));
                    ttpSettings.SetToolTip(form4.pictureBox42, GtefuwenID(Runeimages_1[i].Rank[4]));
                    ttpSettings.SetToolTip(form4.pictureBox41, GtefuwenID(Runeimages_1[i].Rank[5]));
                    ttpSettings.SetToolTip(form4.pictureBox40, GtefuwenID(Runeimages_1[i].Rank[6]));
                    ttpSettings.SetToolTip(form4.pictureBox39, GtefuwenID(Runeimages_1[i].Rank[7]));
                    ttpSettings.SetToolTip(form4.pictureBox38, GtefuwenID(Runeimages_1[i].Rank[8]));
                    ttpSettings.SetToolTip(form4.pictureBox37, GtefuwenID(Runeimages_1[i].Rank[9]));
                    ttpSettings.SetToolTip(form4.pictureBox36, GtefuwenID(Runeimages_1[i].Rank[10]));
                }

            }
        }
        static string GtefuwenID(string ID)
        {

            foreach (var item in Form1.DATaRunekun)
            {

                if (item.RuneID == ID)
                    return item.RuneName;

            }
            return "未找到";
        }


        /// <summary>
        /// 初始化图片数组
        /// </summary>
        /// 

        PictureBox[] bnt1 = new PictureBox[11];

        PictureBox[] bnt2 = new PictureBox[9];

        PictureBox[] bnt3 = new PictureBox[9];

        PictureBox[] bnt4 = new PictureBox[9];
        private void Initialising()
        {

            for (int i = 0; i < 11; i++)
            {

                //实例化
                bnt1[i] = new PictureBox();
                //定义控件名称
                bnt1[i].Name = "pictureBox_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                bnt1[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                bnt1[i].Parent = panel1;
                //定义坐标
                if (i == 0)
                {
                    bnt1[i].Size = new Size(40, 40);
                    bnt1[i].Location = new Point(3, 16);
                }
                else if (i == 1)
                {
                    bnt1[i].Size = new Size(30, 30);
                    bnt1[i].Location = new Point(bnt1[i - 1].Location.X + 75, 30);
                }
                else if (i == 5)
                {
                    bnt1[i].Size = new Size(40, 40);
                    bnt1[i].Location = new Point(bnt1[i - 1].Location.X + 75, 30);
                }
                else
                {
                    bnt1[i].Size = new Size(30, 30);
                    bnt1[i].Location = new Point(bnt1[i - 1].Location.X + 40, 30);
                }



                //bnt[i].Location = new Point(10 + (i % 9) * 34, 40 + (i / 9) * 50);


                bnt1[i].BorderStyle = BorderStyle.FixedSingle;
                bnt1[i].SizeMode = PictureBoxSizeMode.StretchImage;
                bnt1[i].BackColor = pictureBox2.BackColor;
                bnt1[i].BorderStyle = pictureBox2.BorderStyle;
            }

            for (int i = 0; i < 9; i++)
            {

                //实例化
                bnt2[i] = new PictureBox();
                //定义控件名称
                bnt2[i].Name = "pictureBox_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                bnt2[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                bnt2[i].Parent = groupBox2;
                //定义坐标
                if (i == 0)
                {
                    bnt2[i].Size = new Size(42, 42);
                    bnt2[i].Location = new Point(3, 16);
                }
                else if (i == 1)
                {
                    bnt2[i].Size = new Size(24, 24);
                    bnt2[i].Location = new Point(bnt2[i - 1].Location.X + 44, 30);
                }
                else
                {
                    bnt2[i].Size = new Size(24, 24);
                    bnt2[i].Location = new Point(bnt2[i - 1].Location.X + 28, 30);
                }


                //bnt[i].Location = new Point(10 + (i % 9) * 34, 40 + (i / 9) * 50);


                bnt2[i].BorderStyle = BorderStyle.FixedSingle;
                bnt2[i].SizeMode = PictureBoxSizeMode.StretchImage;
                bnt2[i].BackColor = pictureBox2.BackColor;
                bnt2[i].BorderStyle = pictureBox2.BorderStyle;
            }

            for (int i = 0; i < 9; i++)
            {

                //实例化
                bnt3[i] = new PictureBox();
                //定义控件名称
                bnt3[i].Name = "pictureBox_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                bnt3[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                bnt3[i].Parent = groupBox3;
                //定义坐标
                if (i == 0)
                {
                    bnt3[i].Size = new Size(42, 42);
                    bnt3[i].Location = new Point(3, 16);
                }
                else if (i == 1)
                {
                    bnt3[i].Size = new Size(24, 24);
                    bnt3[i].Location = new Point(bnt3[i - 1].Location.X + 44, 30);
                }
                else
                {
                    bnt3[i].Size = new Size(24, 24);
                    bnt3[i].Location = new Point(bnt3[i - 1].Location.X + 28, 30);
                }


                //bnt[i].Location = new Point(10 + (i % 9) * 34, 40 + (i / 9) * 50);


                bnt3[i].BorderStyle = BorderStyle.FixedSingle;
                bnt3[i].SizeMode = PictureBoxSizeMode.StretchImage;
                bnt3[i].BackColor = pictureBox2.BackColor;
                bnt3[i].BorderStyle = pictureBox2.BorderStyle;
            }
            for (int i = 0; i < 9; i++)
            {

                //实例化
                bnt4[i] = new PictureBox();
                //定义控件名称
                bnt4[i].Name = "pictureBox_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                bnt4[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                bnt4[i].Parent = groupBox4;
                //定义坐标
                if (i == 0)
                {
                    bnt4[i].Size = new Size(42, 42);
                    bnt4[i].Location = new Point(3, 16);
                }
                else if (i == 1)
                {
                    bnt4[i].Size = new Size(24, 24);
                    bnt4[i].Location = new Point(bnt4[i - 1].Location.X + 44, 30);
                }
                else
                {
                    bnt4[i].Size = new Size(24, 24);
                    bnt4[i].Location = new Point(bnt4[i - 1].Location.X + 28, 30);
                }


                //bnt[i].Location = new Point(10 + (i % 9) * 34, 40 + (i / 9) * 50);


                bnt4[i].BorderStyle = BorderStyle.FixedSingle;
                bnt4[i].SizeMode = PictureBoxSizeMode.StretchImage;
                bnt4[i].BackColor = pictureBox2.BackColor;
                bnt4[i].BorderStyle = pictureBox2.BorderStyle;
            }
        }

        private void Form4_FormClosing(object sender, FormClosingEventArgs e)
        {
            Form4.thread.Abort();
        }




        private void TUIfuweizbei(object anni)
        {



            var tijshuju = Resources.调整符文;

            tijshuju = tijshuju.Replace("[符文名称]", Util.FuwenHeroIDImage + "-" + TIaoFUW + "-(" + anni.ToString() + ")");

            int dd = Convert.ToInt32(anni) - 1;

            int i = 0;


            foreach (var item in Runeimages_1[dd].Rank)
            {
                i++;
                tijshuju = tijshuju.Replace("[符文编号" + i.ToString() + "]", item);
                Console.WriteLine("符文编号:{0} 符合替换编号:{1}", i.ToString(), item);

            }
            Util.zi_Sendshuju("/lol-perks/v1/pages", "DELETE", "");

            Util.zi_Sendshuju("/lol-perks/v1/pages", "POST", tijshuju);

            if (checkBox1.Checked)
            {
                var uri = "/lol-item-sets/v1/item-sets/" + Util.accountId + "/sets";

                var zi_shuju = Resources.推荐装备00;


                zi_shuju = zi_shuju.Replace("[召唤师]", Util.accountId);

                zi_shuju = zi_shuju.Replace("[英雄ID]", Util.FuwenHeroID);


                zi_shuju = zi_shuju.Replace("[推荐名称]", Util.FuwenHeroIDImage);




                if (Coreequipment.Count < 1)
                {
                    fuweishuchu("已调整" + Util.FuwenHeroIDImage + "符文(推荐装备失败)!", 1);
                    return;
                }

                int P = 0;//核心

                int P1 = 0;//初始化

                int P2 = 0;//靴子

                foreach (var item in Coreequipment)//核心装备
                {


                    P++;
                    zi_shuju = zi_shuju.Replace("[核心装备" + P.ToString() + "]", item);

                }
                foreach (var item in Initializing)//初始化装备
                {
                    P1++;
                    zi_shuju = zi_shuju.Replace("[初始装备" + P1.ToString() + "]", item);
                }
                foreach (var item in Boots)//核心装备
                {
                    P2++;
                    zi_shuju = zi_shuju.Replace("[靴子" + P2.ToString() + "]", item);
                }

                var fanw = Util.zi_Sendshuju(uri, "PUT", zi_shuju);

                if (fanw == "")
                {
                    fuweishuchu("已调整" + Util.FuwenHeroIDImage + "符文装备!", 3);
                }
                else
                {
                    fuweishuchu("已调整" + Util.FuwenHeroIDImage + "符文(推荐装备失败)!", 1);
                }

            }
            else
            {
                Console.WriteLine("已调整" + Util.FuwenHeroIDImage + "符文!");
                fuweishuchu("已调整" + Util.FuwenHeroIDImage + "符文!", 3);
            }


            if (Form1.form1.checkBox12.Checked)
            {
                var zhaohus = Resources.召唤师技能;
                //1=净化，2=虚弱，4=闪现，6=疾跑,7=治疗，11=惩戒，12=传送，13=清晰术，14=引燃，21=屏障，32=雪球/标记
                if ((Z_zhaohua[1] == "4" && Z_zhaohua[0] != "6")|| (Z_zhaohua[0] != "4" && Z_zhaohua[1] == "6"))
                {
                    zhaohus = zhaohus.Replace("[召唤师技能1]", Z_zhaohua[1]);
                    zhaohus = zhaohus.Replace("[召唤师技能2]", Z_zhaohua[0]);
                }
                else
                {
                    zhaohus = zhaohus.Replace("[召唤师技能1]", Z_zhaohua[0]);
                    zhaohus = zhaohus.Replace("[召唤师技能2]", Z_zhaohua[1]);
                }
                Util.zi_Sendshuju(@"/lol-champ-select/v1/session/my-selection", "PATCH", zhaohus);
                Form1.form1.T_msg("自动点击符文设置召唤师技能", 3, 1);
            }

        }





        private void button1_Click(object sender, EventArgs e)
        {


            yanyan(1);

            Thread thread1 = new Thread(new ParameterizedThreadStart(TUIfuweizbei));
            thread1.IsBackground = true;
            thread1.Start("1");//开始线程
            Util.BulkSave(form4);

            //pictureBox





        }


        private static void yanyan(int MO = 0)
        {

            switch (MO)
            {
                case 0:
                    form4.panel1.BackColor = QQQQ;
                    form4.panel2.BackColor = QQQQ;
                    form4.panel3.BackColor = QQQQ;
                    form4.panel4.BackColor = QQQQ;
                    break;
                case 1:
                    form4.panel1.BackColor = PPPP;
                    form4.panel2.BackColor = QQQQ;
                    form4.panel3.BackColor = QQQQ;
                    form4.panel4.BackColor = QQQQ;
                    foreach (Control c in form4.panel1.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = PPPP;
                        }
                    }
                    foreach (Control c in form4.panel2.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel3.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel4.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    break;
                case 2:
                    form4.panel1.BackColor = QQQQ;
                    form4.panel2.BackColor = PPPP;
                    form4.panel3.BackColor = QQQQ;
                    form4.panel4.BackColor = QQQQ;
                    foreach (Control c in form4.panel1.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel2.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = PPPP;
                        }
                    }
                    foreach (Control c in form4.panel3.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel4.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    break;
                case 3:
                    form4.panel1.BackColor = QQQQ;
                    form4.panel2.BackColor = QQQQ;
                    form4.panel3.BackColor = PPPP;
                    form4.panel4.BackColor = QQQQ;
                    foreach (Control c in form4.panel1.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel2.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel3.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = PPPP;
                        }
                    }
                    foreach (Control c in form4.panel4.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    break;
                case 4:
                    form4.panel1.BackColor = QQQQ;
                    form4.panel2.BackColor = QQQQ;
                    form4.panel3.BackColor = QQQQ;
                    form4.panel4.BackColor = PPPP;
                    foreach (Control c in form4.panel1.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel2.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel3.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = QQQQ;
                        }
                    }
                    foreach (Control c in form4.panel4.Controls)
                    {
                        if (c is PictureBox)
                        {
                            c.BackColor = PPPP;
                        }
                    }
                    break;
                default:
                    form4.panel1.BackColor = QQQQ;
                    form4.panel2.BackColor = QQQQ;
                    form4.panel3.BackColor = QQQQ;
                    form4.panel4.BackColor = QQQQ;
                    break;
            }

        }

        private void button2_Click(object sender, EventArgs e)
        {
            yanyan(2);
            Thread thread1 = new Thread(new ParameterizedThreadStart(TUIfuweizbei));
            thread1.IsBackground = true;
            thread1.Start("2");//开始线程
            Util.BulkSave(form4);
        }

        private void button3_Click(object sender, EventArgs e)
        {
            yanyan(3);
            Thread thread1 = new Thread(new ParameterizedThreadStart(TUIfuweizbei));
            thread1.IsBackground = true;
            thread1.Start("3");//开始线程
            Util.BulkSave(form4);
        }

        private void button4_Click(object sender, EventArgs e)
        {
            yanyan(4);
            Thread thread1 = new Thread(new ParameterizedThreadStart(TUIfuweizbei));
            thread1.IsBackground = true;
            thread1.Start("4");//开始线程
            Util.BulkSave(form4);
        }
        static void chongxin(int gogo)
        {
            var sss = form4.comboBox1.SelectedIndex;


            if (gogo != 0)
            {
                if (form4.comboBox1.Items.Count > 1)
                {
                    Fenzuname = form4.comboBox1.Text;
                }
                else
                {
                    sss = 0;
                }


            }
            else
            {
                sss = 0;
            }





            if (Form4.thread != null)
            {
                Form4.thread.Abort();
                Form4.thread = null;
            }
            Form4.thread = new Thread(new ParameterizedThreadStart(caozhi));
            thread.IsBackground = true;
            thread.Start(sss);//开始线程
                              //Console.WriteLine("选择框操作");
        }


        private void comboBox1_SelectionChangeCommitted(object sender, EventArgs e)
        {
            chongxin(1);
        }

        private void button5_Click(object sender, EventArgs e)
        {
            chongxin(2);
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            Util.BulkSave(form4);
        }

        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            Util.BulkSave(form4);
        }

        private void checkBox3_CheckedChanged(object sender, EventArgs e)
        {
            Util.BulkSave(form4);
        }
    }
}

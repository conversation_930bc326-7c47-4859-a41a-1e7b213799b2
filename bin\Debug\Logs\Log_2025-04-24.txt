2025-04-24 11:46:43: 正在初始化过程!
2025-04-24 11:46:45: 大厅正在初始化中1
2025-04-24 11:46:45: 大厅正在初始化中2
2025-04-24 11:46:47: 窗口隐藏到循环尾1
2025-04-24 11:46:47: 检测到房间!
2025-04-24 11:46:48: 游戏初始化完毕!
2025-04-24 11:46:48: 检测到房间!
2025-04-24 11:46:48: 1正在获取英雄数据中!1
2025-04-24 11:46:48: 检测到房间!
2025-04-24 11:46:51: 2正在获取英雄数据中!2
2025-04-24 11:46:51: 初始化英雄数据成功!
2025-04-24 11:46:51: 检测到房间!
2025-04-24 11:46:51: 正在获取英雄数据中!:240
2025-04-24 11:46:51: 检测到房间!
2025-04-24 11:46:51: 排位英雄数据初始化完毕
2025-04-24 11:46:51: 检测到房间!
2025-04-24 11:47:05: 窗口隐藏2
2025-04-24 11:47:05: 检测到房间!
2025-04-24 11:47:08: 窗口隐藏1获取英雄ID:0
2025-04-24 11:47:09: 进入选择英雄界面！
2025-04-24 11:47:12: 获取英雄ID:119-上次选英雄ID:0
2025-04-24 11:47:12: 正在为Draven选英雄
2025-04-24 11:47:12: 进入选择英雄界面！
2025-04-24 11:47:27: 窗口隐藏2
2025-04-24 11:47:28: 对局正在进行中!
2025-04-24 11:47:28: 窗口隐藏95
2025-04-24 11:47:32: 对局正在进行中!
2025-04-24 11:47:53: 状态:重新连接!
2025-04-24 11:49:03: 对局正在进行中!
2025-04-24 11:57:02: 状态:重新连接!
2025-04-24 11:57:02: 对局正在进行中!
2025-04-24 12:01:06: 状态:重新连接!
2025-04-24 12:01:06: 对局正在进行中!
2025-04-24 12:54:49: 等待游戏结算界面!
2025-04-24 12:54:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:54:53: 状态:游戏结束!-继续开始
2025-04-24 12:54:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:54:57: 状态:游戏结束!-继续开始
2025-04-24 12:54:57: 状态:游戏结束!
2025-04-24 12:55:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:01: 状态:游戏结束!-继续开始
2025-04-24 12:55:01: 状态:游戏结束!
2025-04-24 12:55:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:05: 状态:游戏结束!-继续开始
2025-04-24 12:55:05: 状态:游戏结束!
2025-04-24 12:55:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:09: 状态:游戏结束!-继续开始
2025-04-24 12:55:09: 状态:游戏结束!
2025-04-24 12:55:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:14: 状态:游戏结束!-继续开始
2025-04-24 12:55:14: 状态:游戏结束!
2025-04-24 12:55:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:18: 状态:游戏结束!-继续开始
2025-04-24 12:55:18: 状态:游戏结束!
2025-04-24 12:55:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:22: 状态:游戏结束!-继续开始
2025-04-24 12:55:22: 状态:游戏结束!
2025-04-24 12:55:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:26: 状态:游戏结束!-继续开始
2025-04-24 12:55:26: 状态:游戏结束!
2025-04-24 12:55:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:30: 状态:游戏结束!-继续开始
2025-04-24 12:55:30: 状态:游戏结束!
2025-04-24 12:55:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:34: 状态:游戏结束!-继续开始
2025-04-24 12:55:34: 状态:游戏结束!
2025-04-24 12:55:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:38: 状态:游戏结束!-继续开始
2025-04-24 12:55:38: 状态:游戏结束!
2025-04-24 12:55:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:42: 状态:游戏结束!-继续开始
2025-04-24 12:55:42: 状态:游戏结束!
2025-04-24 12:55:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:46: 状态:游戏结束!-继续开始
2025-04-24 12:55:46: 状态:游戏结束!
2025-04-24 12:55:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:50: 状态:游戏结束!-继续开始
2025-04-24 12:55:50: 状态:游戏结束!
2025-04-24 12:55:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:55: 状态:游戏结束!-继续开始
2025-04-24 12:55:55: 状态:游戏结束!
2025-04-24 12:55:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:55:59: 状态:游戏结束!-继续开始
2025-04-24 12:55:59: 状态:游戏结束!
2025-04-24 12:56:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:03: 状态:游戏结束!-继续开始
2025-04-24 12:56:03: 状态:游戏结束!
2025-04-24 12:56:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:07: 状态:游戏结束!-继续开始
2025-04-24 12:56:07: 状态:游戏结束!
2025-04-24 12:56:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:11: 状态:游戏结束!-继续开始
2025-04-24 12:56:11: 状态:游戏结束!
2025-04-24 12:56:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:15: 状态:游戏结束!-继续开始
2025-04-24 12:56:15: 状态:游戏结束!
2025-04-24 12:56:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:19: 状态:游戏结束!-继续开始
2025-04-24 12:56:19: 状态:游戏结束!
2025-04-24 12:56:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:23: 状态:游戏结束!-继续开始
2025-04-24 12:56:23: 状态:游戏结束!
2025-04-24 12:56:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:27: 状态:游戏结束!-继续开始
2025-04-24 12:56:27: 状态:游戏结束!
2025-04-24 12:56:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:32: 状态:游戏结束!-继续开始
2025-04-24 12:56:32: 状态:游戏结束!
2025-04-24 12:56:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:36: 状态:游戏结束!-继续开始
2025-04-24 12:56:36: 状态:游戏结束!
2025-04-24 12:56:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:40: 状态:游戏结束!-继续开始
2025-04-24 12:56:40: 状态:游戏结束!
2025-04-24 12:56:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:44: 状态:游戏结束!-继续开始
2025-04-24 12:56:44: 状态:游戏结束!
2025-04-24 12:56:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:48: 状态:游戏结束!-继续开始
2025-04-24 12:56:48: 状态:游戏结束!
2025-04-24 12:56:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:52: 状态:游戏结束!-继续开始
2025-04-24 12:56:52: 状态:游戏结束!
2025-04-24 12:56:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:56:56: 状态:游戏结束!-继续开始
2025-04-24 12:56:56: 状态:游戏结束!
2025-04-24 12:56:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:00: 状态:游戏结束!-继续开始
2025-04-24 12:57:00: 状态:游戏结束!
2025-04-24 12:57:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:04: 状态:游戏结束!-继续开始
2025-04-24 12:57:04: 状态:游戏结束!
2025-04-24 12:57:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:08: 状态:游戏结束!-继续开始
2025-04-24 12:57:08: 状态:游戏结束!
2025-04-24 12:57:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:13: 状态:游戏结束!-继续开始
2025-04-24 12:57:13: 状态:游戏结束!
2025-04-24 12:57:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:17: 状态:游戏结束!-继续开始
2025-04-24 12:57:17: 状态:游戏结束!
2025-04-24 12:57:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:21: 状态:游戏结束!-继续开始
2025-04-24 12:57:21: 状态:游戏结束!
2025-04-24 12:57:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:25: 状态:游戏结束!-继续开始
2025-04-24 12:57:25: 状态:游戏结束!
2025-04-24 12:57:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:29: 状态:游戏结束!-继续开始
2025-04-24 12:57:29: 状态:游戏结束!
2025-04-24 12:57:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:33: 状态:游戏结束!-继续开始
2025-04-24 12:57:33: 状态:游戏结束!
2025-04-24 12:57:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:37: 状态:游戏结束!-继续开始
2025-04-24 12:57:37: 状态:游戏结束!
2025-04-24 12:57:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:41: 状态:游戏结束!-继续开始
2025-04-24 12:57:41: 状态:游戏结束!
2025-04-24 12:57:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:45: 状态:游戏结束!-继续开始
2025-04-24 12:57:45: 状态:游戏结束!
2025-04-24 12:57:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:49: 状态:游戏结束!-继续开始
2025-04-24 12:57:49: 状态:游戏结束!
2025-04-24 12:57:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:54: 状态:游戏结束!-继续开始
2025-04-24 12:57:54: 状态:游戏结束!
2025-04-24 12:57:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:57:58: 状态:游戏结束!-继续开始
2025-04-24 12:57:58: 状态:游戏结束!
2025-04-24 12:58:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:02: 状态:游戏结束!-继续开始
2025-04-24 12:58:02: 状态:游戏结束!
2025-04-24 12:58:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:06: 状态:游戏结束!-继续开始
2025-04-24 12:58:06: 状态:游戏结束!
2025-04-24 12:58:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:10: 状态:游戏结束!-继续开始
2025-04-24 12:58:10: 状态:游戏结束!
2025-04-24 12:58:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:14: 状态:游戏结束!-继续开始
2025-04-24 12:58:14: 状态:游戏结束!
2025-04-24 12:58:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:18: 状态:游戏结束!-继续开始
2025-04-24 12:58:18: 状态:游戏结束!
2025-04-24 12:58:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:22: 状态:游戏结束!-继续开始
2025-04-24 12:58:22: 状态:游戏结束!
2025-04-24 12:58:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:27: 状态:游戏结束!-继续开始
2025-04-24 12:58:27: 状态:游戏结束!
2025-04-24 12:58:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:31: 状态:游戏结束!-继续开始
2025-04-24 12:58:31: 状态:游戏结束!
2025-04-24 12:58:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:35: 状态:游戏结束!-继续开始
2025-04-24 12:58:35: 状态:游戏结束!
2025-04-24 12:58:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:39: 状态:游戏结束!-继续开始
2025-04-24 12:58:39: 状态:游戏结束!
2025-04-24 12:58:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:43: 状态:游戏结束!-继续开始
2025-04-24 12:58:43: 状态:游戏结束!
2025-04-24 12:58:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:47: 状态:游戏结束!-继续开始
2025-04-24 12:58:47: 状态:游戏结束!
2025-04-24 12:58:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:51: 状态:游戏结束!-继续开始
2025-04-24 12:58:51: 状态:游戏结束!
2025-04-24 12:58:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:55: 状态:游戏结束!-继续开始
2025-04-24 12:58:55: 状态:游戏结束!
2025-04-24 12:58:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:58:59: 状态:游戏结束!-继续开始
2025-04-24 12:58:59: 状态:游戏结束!
2025-04-24 12:59:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:04: 状态:游戏结束!-继续开始
2025-04-24 12:59:04: 状态:游戏结束!
2025-04-24 12:59:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:08: 状态:游戏结束!-继续开始
2025-04-24 12:59:08: 状态:游戏结束!
2025-04-24 12:59:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:12: 状态:游戏结束!-继续开始
2025-04-24 12:59:12: 状态:游戏结束!
2025-04-24 12:59:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:16: 状态:游戏结束!-继续开始
2025-04-24 12:59:16: 状态:游戏结束!
2025-04-24 12:59:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:20: 状态:游戏结束!-继续开始
2025-04-24 12:59:20: 状态:游戏结束!
2025-04-24 12:59:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:24: 状态:游戏结束!-继续开始
2025-04-24 12:59:24: 状态:游戏结束!
2025-04-24 12:59:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:28: 状态:游戏结束!-继续开始
2025-04-24 12:59:28: 状态:游戏结束!
2025-04-24 12:59:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:32: 状态:游戏结束!-继续开始
2025-04-24 12:59:32: 状态:游戏结束!
2025-04-24 12:59:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:36: 状态:游戏结束!-继续开始
2025-04-24 12:59:36: 状态:游戏结束!
2025-04-24 12:59:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:40: 状态:游戏结束!-继续开始
2025-04-24 12:59:40: 状态:游戏结束!
2025-04-24 12:59:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:45: 状态:游戏结束!-继续开始
2025-04-24 12:59:45: 状态:游戏结束!
2025-04-24 12:59:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:49: 状态:游戏结束!-继续开始
2025-04-24 12:59:49: 状态:游戏结束!
2025-04-24 12:59:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:53: 状态:游戏结束!-继续开始
2025-04-24 12:59:53: 状态:游戏结束!
2025-04-24 12:59:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 12:59:57: 状态:游戏结束!-继续开始
2025-04-24 12:59:57: 状态:游戏结束!
2025-04-24 13:00:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:01: 状态:游戏结束!-继续开始
2025-04-24 13:00:01: 状态:游戏结束!
2025-04-24 13:00:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:05: 状态:游戏结束!-继续开始
2025-04-24 13:00:05: 状态:游戏结束!
2025-04-24 13:00:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:09: 状态:游戏结束!-继续开始
2025-04-24 13:00:09: 状态:游戏结束!
2025-04-24 13:00:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:13: 状态:游戏结束!-继续开始
2025-04-24 13:00:13: 状态:游戏结束!
2025-04-24 13:00:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:17: 状态:游戏结束!-继续开始
2025-04-24 13:00:17: 状态:游戏结束!
2025-04-24 13:00:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:22: 状态:游戏结束!-继续开始
2025-04-24 13:00:22: 状态:游戏结束!
2025-04-24 13:00:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:26: 状态:游戏结束!-继续开始
2025-04-24 13:00:26: 状态:游戏结束!
2025-04-24 13:00:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:30: 状态:游戏结束!-继续开始
2025-04-24 13:00:30: 状态:游戏结束!
2025-04-24 13:00:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:34: 状态:游戏结束!-继续开始
2025-04-24 13:00:34: 状态:游戏结束!
2025-04-24 13:00:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:38: 状态:游戏结束!-继续开始
2025-04-24 13:00:38: 状态:游戏结束!
2025-04-24 13:00:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:42: 状态:游戏结束!-继续开始
2025-04-24 13:00:42: 状态:游戏结束!
2025-04-24 13:00:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:46: 状态:游戏结束!-继续开始
2025-04-24 13:00:46: 状态:游戏结束!
2025-04-24 13:00:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:50: 状态:游戏结束!-继续开始
2025-04-24 13:00:50: 状态:游戏结束!
2025-04-24 13:00:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:54: 状态:游戏结束!-继续开始
2025-04-24 13:00:54: 状态:游戏结束!
2025-04-24 13:00:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:00:59: 状态:游戏结束!-继续开始
2025-04-24 13:00:59: 状态:游戏结束!
2025-04-24 13:01:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:03: 状态:游戏结束!-继续开始
2025-04-24 13:01:03: 状态:游戏结束!
2025-04-24 13:01:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:07: 状态:游戏结束!-继续开始
2025-04-24 13:01:07: 状态:游戏结束!
2025-04-24 13:01:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:11: 状态:游戏结束!-继续开始
2025-04-24 13:01:11: 状态:游戏结束!
2025-04-24 13:01:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:15: 状态:游戏结束!-继续开始
2025-04-24 13:01:15: 状态:游戏结束!
2025-04-24 13:01:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:19: 状态:游戏结束!-继续开始
2025-04-24 13:01:19: 状态:游戏结束!
2025-04-24 13:01:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:23: 状态:游戏结束!-继续开始
2025-04-24 13:01:23: 状态:游戏结束!
2025-04-24 13:01:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:27: 状态:游戏结束!-继续开始
2025-04-24 13:01:27: 状态:游戏结束!
2025-04-24 13:01:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:31: 状态:游戏结束!-继续开始
2025-04-24 13:01:31: 状态:游戏结束!
2025-04-24 13:01:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:36: 状态:游戏结束!-继续开始
2025-04-24 13:01:36: 状态:游戏结束!
2025-04-24 13:01:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:40: 状态:游戏结束!-继续开始
2025-04-24 13:01:40: 状态:游戏结束!
2025-04-24 13:01:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:44: 状态:游戏结束!-继续开始
2025-04-24 13:01:44: 状态:游戏结束!
2025-04-24 13:01:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:48: 状态:游戏结束!-继续开始
2025-04-24 13:01:48: 状态:游戏结束!
2025-04-24 13:01:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:52: 状态:游戏结束!-继续开始
2025-04-24 13:01:52: 状态:游戏结束!
2025-04-24 13:01:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:01:56: 状态:游戏结束!-继续开始
2025-04-24 13:01:56: 状态:游戏结束!
2025-04-24 13:01:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:00: 状态:游戏结束!-继续开始
2025-04-24 13:02:00: 状态:游戏结束!
2025-04-24 13:02:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:04: 状态:游戏结束!-继续开始
2025-04-24 13:02:04: 状态:游戏结束!
2025-04-24 13:02:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:08: 状态:游戏结束!-继续开始
2025-04-24 13:02:08: 状态:游戏结束!
2025-04-24 13:02:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:12: 状态:游戏结束!-继续开始
2025-04-24 13:02:12: 状态:游戏结束!
2025-04-24 13:02:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:17: 状态:游戏结束!-继续开始
2025-04-24 13:02:17: 状态:游戏结束!
2025-04-24 13:02:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:21: 状态:游戏结束!-继续开始
2025-04-24 13:02:21: 状态:游戏结束!
2025-04-24 13:02:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:25: 状态:游戏结束!-继续开始
2025-04-24 13:02:25: 状态:游戏结束!
2025-04-24 13:02:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:29: 状态:游戏结束!-继续开始
2025-04-24 13:02:29: 状态:游戏结束!
2025-04-24 13:02:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:33: 状态:游戏结束!-继续开始
2025-04-24 13:02:33: 状态:游戏结束!
2025-04-24 13:02:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:37: 状态:游戏结束!-继续开始
2025-04-24 13:02:37: 状态:游戏结束!
2025-04-24 13:02:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:41: 状态:游戏结束!-继续开始
2025-04-24 13:02:41: 状态:游戏结束!
2025-04-24 13:02:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:45: 状态:游戏结束!-继续开始
2025-04-24 13:02:45: 状态:游戏结束!
2025-04-24 13:02:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:49: 状态:游戏结束!-继续开始
2025-04-24 13:02:49: 状态:游戏结束!
2025-04-24 13:02:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:54: 状态:游戏结束!-继续开始
2025-04-24 13:02:54: 状态:游戏结束!
2025-04-24 13:02:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:02:58: 状态:游戏结束!-继续开始
2025-04-24 13:02:58: 状态:游戏结束!
2025-04-24 13:03:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:02: 状态:游戏结束!-继续开始
2025-04-24 13:03:02: 状态:游戏结束!
2025-04-24 13:03:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:06: 状态:游戏结束!-继续开始
2025-04-24 13:03:06: 状态:游戏结束!
2025-04-24 13:03:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:10: 状态:游戏结束!-继续开始
2025-04-24 13:03:10: 状态:游戏结束!
2025-04-24 13:03:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:14: 状态:游戏结束!-继续开始
2025-04-24 13:03:14: 状态:游戏结束!
2025-04-24 13:03:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:18: 状态:游戏结束!-继续开始
2025-04-24 13:03:18: 状态:游戏结束!
2025-04-24 13:03:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:22: 状态:游戏结束!-继续开始
2025-04-24 13:03:22: 状态:游戏结束!
2025-04-24 13:03:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:26: 状态:游戏结束!-继续开始
2025-04-24 13:03:26: 状态:游戏结束!
2025-04-24 13:03:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:31: 状态:游戏结束!-继续开始
2025-04-24 13:03:31: 状态:游戏结束!
2025-04-24 13:03:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:35: 状态:游戏结束!-继续开始
2025-04-24 13:03:35: 状态:游戏结束!
2025-04-24 13:03:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:39: 状态:游戏结束!-继续开始
2025-04-24 13:03:39: 状态:游戏结束!
2025-04-24 13:03:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:43: 状态:游戏结束!-继续开始
2025-04-24 13:03:43: 状态:游戏结束!
2025-04-24 13:03:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:47: 状态:游戏结束!-继续开始
2025-04-24 13:03:47: 状态:游戏结束!
2025-04-24 13:03:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:51: 状态:游戏结束!-继续开始
2025-04-24 13:03:51: 状态:游戏结束!
2025-04-24 13:03:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:03:55: 状态:游戏结束!-继续开始
2025-04-24 13:03:55: 状态:游戏结束!
2025-04-24 13:03:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:00: 状态:游戏结束!-继续开始
2025-04-24 13:04:00: 状态:游戏结束!
2025-04-24 13:04:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:04: 状态:游戏结束!-继续开始
2025-04-24 13:04:04: 状态:游戏结束!
2025-04-24 13:04:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:08: 状态:游戏结束!-继续开始
2025-04-24 13:04:08: 状态:游戏结束!
2025-04-24 13:04:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:12: 状态:游戏结束!-继续开始
2025-04-24 13:04:12: 状态:游戏结束!
2025-04-24 13:04:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:16: 状态:游戏结束!-继续开始
2025-04-24 13:04:16: 状态:游戏结束!
2025-04-24 13:04:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:20: 状态:游戏结束!-继续开始
2025-04-24 13:04:20: 状态:游戏结束!
2025-04-24 13:04:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:24: 状态:游戏结束!-继续开始
2025-04-24 13:04:24: 状态:游戏结束!
2025-04-24 13:04:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:28: 状态:游戏结束!-继续开始
2025-04-24 13:04:28: 状态:游戏结束!
2025-04-24 13:04:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:32: 状态:游戏结束!-继续开始
2025-04-24 13:04:32: 状态:游戏结束!
2025-04-24 13:04:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:37: 状态:游戏结束!-继续开始
2025-04-24 13:04:37: 状态:游戏结束!
2025-04-24 13:04:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:41: 状态:游戏结束!-继续开始
2025-04-24 13:04:41: 状态:游戏结束!
2025-04-24 13:04:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:45: 状态:游戏结束!-继续开始
2025-04-24 13:04:45: 状态:游戏结束!
2025-04-24 13:04:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:49: 状态:游戏结束!-继续开始
2025-04-24 13:04:49: 状态:游戏结束!
2025-04-24 13:04:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:53: 状态:游戏结束!-继续开始
2025-04-24 13:04:53: 状态:游戏结束!
2025-04-24 13:04:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:04:57: 状态:游戏结束!-继续开始
2025-04-24 13:04:57: 状态:游戏结束!
2025-04-24 13:05:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:01: 状态:游戏结束!-继续开始
2025-04-24 13:05:01: 状态:游戏结束!
2025-04-24 13:05:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:05: 状态:游戏结束!-继续开始
2025-04-24 13:05:05: 状态:游戏结束!
2025-04-24 13:05:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:09: 状态:游戏结束!-继续开始
2025-04-24 13:05:09: 状态:游戏结束!
2025-04-24 13:05:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:14: 状态:游戏结束!-继续开始
2025-04-24 13:05:14: 状态:游戏结束!
2025-04-24 13:05:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:18: 状态:游戏结束!-继续开始
2025-04-24 13:05:18: 状态:游戏结束!
2025-04-24 13:05:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:22: 状态:游戏结束!-继续开始
2025-04-24 13:05:22: 状态:游戏结束!
2025-04-24 13:05:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:26: 状态:游戏结束!-继续开始
2025-04-24 13:05:26: 状态:游戏结束!
2025-04-24 13:05:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:30: 状态:游戏结束!-继续开始
2025-04-24 13:05:30: 状态:游戏结束!
2025-04-24 13:05:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:34: 状态:游戏结束!-继续开始
2025-04-24 13:05:34: 状态:游戏结束!
2025-04-24 13:05:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:38: 状态:游戏结束!-继续开始
2025-04-24 13:05:38: 状态:游戏结束!
2025-04-24 13:05:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:42: 状态:游戏结束!-继续开始
2025-04-24 13:05:42: 状态:游戏结束!
2025-04-24 13:05:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:46: 状态:游戏结束!-继续开始
2025-04-24 13:05:46: 状态:游戏结束!
2025-04-24 13:05:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:50: 状态:游戏结束!-继续开始
2025-04-24 13:05:50: 状态:游戏结束!
2025-04-24 13:05:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:55: 状态:游戏结束!-继续开始
2025-04-24 13:05:55: 状态:游戏结束!
2025-04-24 13:05:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:05:59: 状态:游戏结束!-继续开始
2025-04-24 13:05:59: 状态:游戏结束!
2025-04-24 13:06:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:03: 状态:游戏结束!-继续开始
2025-04-24 13:06:03: 状态:游戏结束!
2025-04-24 13:06:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:07: 状态:游戏结束!-继续开始
2025-04-24 13:06:07: 状态:游戏结束!
2025-04-24 13:06:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:11: 状态:游戏结束!-继续开始
2025-04-24 13:06:11: 状态:游戏结束!
2025-04-24 13:06:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:15: 状态:游戏结束!-继续开始
2025-04-24 13:06:15: 状态:游戏结束!
2025-04-24 13:06:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:19: 状态:游戏结束!-继续开始
2025-04-24 13:06:19: 状态:游戏结束!
2025-04-24 13:06:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:23: 状态:游戏结束!-继续开始
2025-04-24 13:06:23: 状态:游戏结束!
2025-04-24 13:06:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:27: 状态:游戏结束!-继续开始
2025-04-24 13:06:27: 状态:游戏结束!
2025-04-24 13:06:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:32: 状态:游戏结束!-继续开始
2025-04-24 13:06:32: 状态:游戏结束!
2025-04-24 13:06:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:36: 状态:游戏结束!-继续开始
2025-04-24 13:06:36: 状态:游戏结束!
2025-04-24 13:06:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:40: 状态:游戏结束!-继续开始
2025-04-24 13:06:40: 状态:游戏结束!
2025-04-24 13:06:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:44: 状态:游戏结束!-继续开始
2025-04-24 13:06:44: 状态:游戏结束!
2025-04-24 13:06:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:48: 状态:游戏结束!-继续开始
2025-04-24 13:06:48: 状态:游戏结束!
2025-04-24 13:06:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:52: 状态:游戏结束!-继续开始
2025-04-24 13:06:52: 状态:游戏结束!
2025-04-24 13:06:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:06:56: 状态:游戏结束!-继续开始
2025-04-24 13:06:56: 状态:游戏结束!
2025-04-24 13:06:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:00: 状态:游戏结束!-继续开始
2025-04-24 13:07:00: 状态:游戏结束!
2025-04-24 13:07:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:05: 状态:游戏结束!-继续开始
2025-04-24 13:07:05: 状态:游戏结束!
2025-04-24 13:07:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:09: 状态:游戏结束!-继续开始
2025-04-24 13:07:09: 状态:游戏结束!
2025-04-24 13:07:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:13: 状态:游戏结束!-继续开始
2025-04-24 13:07:13: 状态:游戏结束!
2025-04-24 13:07:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:17: 状态:游戏结束!-继续开始
2025-04-24 13:07:17: 状态:游戏结束!
2025-04-24 13:07:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:21: 状态:游戏结束!-继续开始
2025-04-24 13:07:21: 状态:游戏结束!
2025-04-24 13:07:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:25: 状态:游戏结束!-继续开始
2025-04-24 13:07:25: 状态:游戏结束!
2025-04-24 13:07:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:29: 状态:游戏结束!-继续开始
2025-04-24 13:07:29: 状态:游戏结束!
2025-04-24 13:07:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:33: 状态:游戏结束!-继续开始
2025-04-24 13:07:33: 状态:游戏结束!
2025-04-24 13:07:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:38: 状态:游戏结束!-继续开始
2025-04-24 13:07:38: 状态:游戏结束!
2025-04-24 13:07:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:42: 状态:游戏结束!-继续开始
2025-04-24 13:07:42: 状态:游戏结束!
2025-04-24 13:07:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:46: 状态:游戏结束!-继续开始
2025-04-24 13:07:46: 状态:游戏结束!
2025-04-24 13:07:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:50: 状态:游戏结束!-继续开始
2025-04-24 13:07:50: 状态:游戏结束!
2025-04-24 13:07:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:54: 状态:游戏结束!-继续开始
2025-04-24 13:07:54: 状态:游戏结束!
2025-04-24 13:07:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:07:58: 状态:游戏结束!-继续开始
2025-04-24 13:07:58: 状态:游戏结束!
2025-04-24 13:08:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:02: 状态:游戏结束!-继续开始
2025-04-24 13:08:02: 状态:游戏结束!
2025-04-24 13:08:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:06: 状态:游戏结束!-继续开始
2025-04-24 13:08:06: 状态:游戏结束!
2025-04-24 13:08:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:10: 状态:游戏结束!-继续开始
2025-04-24 13:08:10: 状态:游戏结束!
2025-04-24 13:08:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:15: 状态:游戏结束!-继续开始
2025-04-24 13:08:15: 状态:游戏结束!
2025-04-24 13:08:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:19: 状态:游戏结束!-继续开始
2025-04-24 13:08:19: 状态:游戏结束!
2025-04-24 13:08:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:23: 状态:游戏结束!-继续开始
2025-04-24 13:08:23: 状态:游戏结束!
2025-04-24 13:08:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:27: 状态:游戏结束!-继续开始
2025-04-24 13:08:27: 状态:游戏结束!
2025-04-24 13:08:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:31: 状态:游戏结束!-继续开始
2025-04-24 13:08:31: 状态:游戏结束!
2025-04-24 13:08:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:35: 状态:游戏结束!-继续开始
2025-04-24 13:08:35: 状态:游戏结束!
2025-04-24 13:08:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:39: 状态:游戏结束!-继续开始
2025-04-24 13:08:39: 状态:游戏结束!
2025-04-24 13:08:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:43: 状态:游戏结束!-继续开始
2025-04-24 13:08:43: 状态:游戏结束!
2025-04-24 13:08:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:47: 状态:游戏结束!-继续开始
2025-04-24 13:08:47: 状态:游戏结束!
2025-04-24 13:08:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:51: 状态:游戏结束!-继续开始
2025-04-24 13:08:51: 状态:游戏结束!
2025-04-24 13:08:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:08:56: 状态:游戏结束!-继续开始
2025-04-24 13:08:56: 状态:游戏结束!
2025-04-24 13:08:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:00: 状态:游戏结束!-继续开始
2025-04-24 13:09:00: 状态:游戏结束!
2025-04-24 13:09:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:04: 状态:游戏结束!-继续开始
2025-04-24 13:09:04: 状态:游戏结束!
2025-04-24 13:09:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:08: 状态:游戏结束!-继续开始
2025-04-24 13:09:08: 状态:游戏结束!
2025-04-24 13:09:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:12: 状态:游戏结束!-继续开始
2025-04-24 13:09:12: 状态:游戏结束!
2025-04-24 13:09:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:16: 状态:游戏结束!-继续开始
2025-04-24 13:09:16: 状态:游戏结束!
2025-04-24 13:09:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:20: 状态:游戏结束!-继续开始
2025-04-24 13:09:20: 状态:游戏结束!
2025-04-24 13:09:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:24: 状态:游戏结束!-继续开始
2025-04-24 13:09:24: 状态:游戏结束!
2025-04-24 13:09:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:29: 状态:游戏结束!-继续开始
2025-04-24 13:09:29: 状态:游戏结束!
2025-04-24 13:09:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:33: 状态:游戏结束!-继续开始
2025-04-24 13:09:33: 状态:游戏结束!
2025-04-24 13:09:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:37: 状态:游戏结束!-继续开始
2025-04-24 13:09:37: 状态:游戏结束!
2025-04-24 13:09:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:41: 状态:游戏结束!-继续开始
2025-04-24 13:09:41: 状态:游戏结束!
2025-04-24 13:09:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:45: 状态:游戏结束!-继续开始
2025-04-24 13:09:45: 状态:游戏结束!
2025-04-24 13:09:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:49: 状态:游戏结束!-继续开始
2025-04-24 13:09:49: 状态:游戏结束!
2025-04-24 13:09:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:53: 状态:游戏结束!-继续开始
2025-04-24 13:09:53: 状态:游戏结束!
2025-04-24 13:09:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:09:57: 状态:游戏结束!-继续开始
2025-04-24 13:09:57: 状态:游戏结束!
2025-04-24 13:10:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:01: 状态:游戏结束!-继续开始
2025-04-24 13:10:01: 状态:游戏结束!
2025-04-24 13:10:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:05: 状态:游戏结束!-继续开始
2025-04-24 13:10:05: 状态:游戏结束!
2025-04-24 13:10:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:10: 状态:游戏结束!-继续开始
2025-04-24 13:10:10: 状态:游戏结束!
2025-04-24 13:10:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:14: 状态:游戏结束!-继续开始
2025-04-24 13:10:14: 状态:游戏结束!
2025-04-24 13:10:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:18: 状态:游戏结束!-继续开始
2025-04-24 13:10:18: 状态:游戏结束!
2025-04-24 13:10:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:22: 状态:游戏结束!-继续开始
2025-04-24 13:10:22: 状态:游戏结束!
2025-04-24 13:10:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:26: 状态:游戏结束!-继续开始
2025-04-24 13:10:26: 状态:游戏结束!
2025-04-24 13:10:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:30: 状态:游戏结束!-继续开始
2025-04-24 13:10:30: 状态:游戏结束!
2025-04-24 13:10:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:34: 状态:游戏结束!-继续开始
2025-04-24 13:10:34: 状态:游戏结束!
2025-04-24 13:10:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:38: 状态:游戏结束!-继续开始
2025-04-24 13:10:38: 状态:游戏结束!
2025-04-24 13:10:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:42: 状态:游戏结束!-继续开始
2025-04-24 13:10:42: 状态:游戏结束!
2025-04-24 13:10:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:47: 状态:游戏结束!-继续开始
2025-04-24 13:10:47: 状态:游戏结束!
2025-04-24 13:10:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:51: 状态:游戏结束!-继续开始
2025-04-24 13:10:51: 状态:游戏结束!
2025-04-24 13:10:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:55: 状态:游戏结束!-继续开始
2025-04-24 13:10:55: 状态:游戏结束!
2025-04-24 13:10:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:10:59: 状态:游戏结束!-继续开始
2025-04-24 13:10:59: 状态:游戏结束!
2025-04-24 13:11:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:03: 状态:游戏结束!-继续开始
2025-04-24 13:11:03: 状态:游戏结束!
2025-04-24 13:11:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:07: 状态:游戏结束!-继续开始
2025-04-24 13:11:07: 状态:游戏结束!
2025-04-24 13:11:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:11: 状态:游戏结束!-继续开始
2025-04-24 13:11:11: 状态:游戏结束!
2025-04-24 13:11:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:15: 状态:游戏结束!-继续开始
2025-04-24 13:11:15: 状态:游戏结束!
2025-04-24 13:11:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:19: 状态:游戏结束!-继续开始
2025-04-24 13:11:19: 状态:游戏结束!
2025-04-24 13:11:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:23: 状态:游戏结束!-继续开始
2025-04-24 13:11:23: 状态:游戏结束!
2025-04-24 13:11:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:28: 状态:游戏结束!-继续开始
2025-04-24 13:11:28: 状态:游戏结束!
2025-04-24 13:11:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:32: 状态:游戏结束!-继续开始
2025-04-24 13:11:32: 状态:游戏结束!
2025-04-24 13:11:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:36: 状态:游戏结束!-继续开始
2025-04-24 13:11:36: 状态:游戏结束!
2025-04-24 13:11:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:40: 状态:游戏结束!-继续开始
2025-04-24 13:11:40: 状态:游戏结束!
2025-04-24 13:11:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:44: 状态:游戏结束!-继续开始
2025-04-24 13:11:44: 状态:游戏结束!
2025-04-24 13:11:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:48: 状态:游戏结束!-继续开始
2025-04-24 13:11:48: 状态:游戏结束!
2025-04-24 13:11:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:52: 状态:游戏结束!-继续开始
2025-04-24 13:11:52: 状态:游戏结束!
2025-04-24 13:11:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:11:56: 状态:游戏结束!-继续开始
2025-04-24 13:11:56: 状态:游戏结束!
2025-04-24 13:11:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:00: 状态:游戏结束!-继续开始
2025-04-24 13:12:00: 状态:游戏结束!
2025-04-24 13:12:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:05: 状态:游戏结束!-继续开始
2025-04-24 13:12:05: 状态:游戏结束!
2025-04-24 13:12:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:09: 状态:游戏结束!-继续开始
2025-04-24 13:12:09: 状态:游戏结束!
2025-04-24 13:12:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:13: 状态:游戏结束!-继续开始
2025-04-24 13:12:13: 状态:游戏结束!
2025-04-24 13:12:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:17: 状态:游戏结束!-继续开始
2025-04-24 13:12:17: 状态:游戏结束!
2025-04-24 13:12:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:21: 状态:游戏结束!-继续开始
2025-04-24 13:12:21: 状态:游戏结束!
2025-04-24 13:12:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:25: 状态:游戏结束!-继续开始
2025-04-24 13:12:25: 状态:游戏结束!
2025-04-24 13:12:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:29: 状态:游戏结束!-继续开始
2025-04-24 13:12:29: 状态:游戏结束!
2025-04-24 13:12:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:33: 状态:游戏结束!-继续开始
2025-04-24 13:12:33: 状态:游戏结束!
2025-04-24 13:12:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:37: 状态:游戏结束!-继续开始
2025-04-24 13:12:37: 状态:游戏结束!
2025-04-24 13:12:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:42: 状态:游戏结束!-继续开始
2025-04-24 13:12:42: 状态:游戏结束!
2025-04-24 13:12:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:46: 状态:游戏结束!-继续开始
2025-04-24 13:12:46: 状态:游戏结束!
2025-04-24 13:12:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:50: 状态:游戏结束!-继续开始
2025-04-24 13:12:50: 状态:游戏结束!
2025-04-24 13:12:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:54: 状态:游戏结束!-继续开始
2025-04-24 13:12:54: 状态:游戏结束!
2025-04-24 13:12:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:12:58: 状态:游戏结束!-继续开始
2025-04-24 13:12:58: 状态:游戏结束!
2025-04-24 13:13:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:02: 状态:游戏结束!-继续开始
2025-04-24 13:13:02: 状态:游戏结束!
2025-04-24 13:13:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:06: 状态:游戏结束!-继续开始
2025-04-24 13:13:06: 状态:游戏结束!
2025-04-24 13:13:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:10: 状态:游戏结束!-继续开始
2025-04-24 13:13:10: 状态:游戏结束!
2025-04-24 13:13:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:14: 状态:游戏结束!-继续开始
2025-04-24 13:13:14: 状态:游戏结束!
2025-04-24 13:13:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:19: 状态:游戏结束!-继续开始
2025-04-24 13:13:19: 状态:游戏结束!
2025-04-24 13:13:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:23: 状态:游戏结束!-继续开始
2025-04-24 13:13:23: 状态:游戏结束!
2025-04-24 13:13:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:27: 状态:游戏结束!-继续开始
2025-04-24 13:13:27: 状态:游戏结束!
2025-04-24 13:13:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:31: 状态:游戏结束!-继续开始
2025-04-24 13:13:31: 状态:游戏结束!
2025-04-24 13:13:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:35: 状态:游戏结束!-继续开始
2025-04-24 13:13:35: 状态:游戏结束!
2025-04-24 13:13:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:39: 状态:游戏结束!-继续开始
2025-04-24 13:13:39: 状态:游戏结束!
2025-04-24 13:13:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:43: 状态:游戏结束!-继续开始
2025-04-24 13:13:43: 状态:游戏结束!
2025-04-24 13:13:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:47: 状态:游戏结束!-继续开始
2025-04-24 13:13:47: 状态:游戏结束!
2025-04-24 13:13:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:51: 状态:游戏结束!-继续开始
2025-04-24 13:13:51: 状态:游戏结束!
2025-04-24 13:13:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:13:56: 状态:游戏结束!-继续开始
2025-04-24 13:13:56: 状态:游戏结束!
2025-04-24 13:13:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:00: 状态:游戏结束!-继续开始
2025-04-24 13:14:00: 状态:游戏结束!
2025-04-24 13:14:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:04: 状态:游戏结束!-继续开始
2025-04-24 13:14:04: 状态:游戏结束!
2025-04-24 13:14:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:08: 状态:游戏结束!-继续开始
2025-04-24 13:14:08: 状态:游戏结束!
2025-04-24 13:14:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:12: 状态:游戏结束!-继续开始
2025-04-24 13:14:12: 状态:游戏结束!
2025-04-24 13:14:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:16: 状态:游戏结束!-继续开始
2025-04-24 13:14:16: 状态:游戏结束!
2025-04-24 13:14:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:20: 状态:游戏结束!-继续开始
2025-04-24 13:14:20: 状态:游戏结束!
2025-04-24 13:14:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:24: 状态:游戏结束!-继续开始
2025-04-24 13:14:24: 状态:游戏结束!
2025-04-24 13:14:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:28: 状态:游戏结束!-继续开始
2025-04-24 13:14:28: 状态:游戏结束!
2025-04-24 13:14:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:32: 状态:游戏结束!-继续开始
2025-04-24 13:14:32: 状态:游戏结束!
2025-04-24 13:14:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:37: 状态:游戏结束!-继续开始
2025-04-24 13:14:37: 状态:游戏结束!
2025-04-24 13:14:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:41: 状态:游戏结束!-继续开始
2025-04-24 13:14:41: 状态:游戏结束!
2025-04-24 13:14:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:45: 状态:游戏结束!-继续开始
2025-04-24 13:14:45: 状态:游戏结束!
2025-04-24 13:14:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:49: 状态:游戏结束!-继续开始
2025-04-24 13:14:49: 状态:游戏结束!
2025-04-24 13:14:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:53: 状态:游戏结束!-继续开始
2025-04-24 13:14:53: 状态:游戏结束!
2025-04-24 13:14:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:14:57: 状态:游戏结束!-继续开始
2025-04-24 13:14:57: 状态:游戏结束!
2025-04-24 13:15:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:01: 状态:游戏结束!-继续开始
2025-04-24 13:15:01: 状态:游戏结束!
2025-04-24 13:15:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:05: 状态:游戏结束!-继续开始
2025-04-24 13:15:05: 状态:游戏结束!
2025-04-24 13:15:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:10: 状态:游戏结束!-继续开始
2025-04-24 13:15:10: 状态:游戏结束!
2025-04-24 13:15:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:14: 状态:游戏结束!-继续开始
2025-04-24 13:15:14: 状态:游戏结束!
2025-04-24 13:15:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:18: 状态:游戏结束!-继续开始
2025-04-24 13:15:18: 状态:游戏结束!
2025-04-24 13:15:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:22: 状态:游戏结束!-继续开始
2025-04-24 13:15:22: 状态:游戏结束!
2025-04-24 13:15:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:26: 状态:游戏结束!-继续开始
2025-04-24 13:15:26: 状态:游戏结束!
2025-04-24 13:15:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:30: 状态:游戏结束!-继续开始
2025-04-24 13:15:30: 状态:游戏结束!
2025-04-24 13:15:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:34: 状态:游戏结束!-继续开始
2025-04-24 13:15:34: 状态:游戏结束!
2025-04-24 13:15:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:38: 状态:游戏结束!-继续开始
2025-04-24 13:15:38: 状态:游戏结束!
2025-04-24 13:15:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:42: 状态:游戏结束!-继续开始
2025-04-24 13:15:42: 状态:游戏结束!
2025-04-24 13:15:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:47: 状态:游戏结束!-继续开始
2025-04-24 13:15:47: 状态:游戏结束!
2025-04-24 13:15:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:51: 状态:游戏结束!-继续开始
2025-04-24 13:15:51: 状态:游戏结束!
2025-04-24 13:15:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:55: 状态:游戏结束!-继续开始
2025-04-24 13:15:55: 状态:游戏结束!
2025-04-24 13:15:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:15:59: 状态:游戏结束!-继续开始
2025-04-24 13:15:59: 状态:游戏结束!
2025-04-24 13:16:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:03: 状态:游戏结束!-继续开始
2025-04-24 13:16:03: 状态:游戏结束!
2025-04-24 13:16:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:07: 状态:游戏结束!-继续开始
2025-04-24 13:16:07: 状态:游戏结束!
2025-04-24 13:16:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:11: 状态:游戏结束!-继续开始
2025-04-24 13:16:11: 状态:游戏结束!
2025-04-24 13:16:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:15: 状态:游戏结束!-继续开始
2025-04-24 13:16:15: 状态:游戏结束!
2025-04-24 13:16:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:19: 状态:游戏结束!-继续开始
2025-04-24 13:16:19: 状态:游戏结束!
2025-04-24 13:16:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:24: 状态:游戏结束!-继续开始
2025-04-24 13:16:24: 状态:游戏结束!
2025-04-24 13:16:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:28: 状态:游戏结束!-继续开始
2025-04-24 13:16:28: 状态:游戏结束!
2025-04-24 13:16:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:32: 状态:游戏结束!-继续开始
2025-04-24 13:16:32: 状态:游戏结束!
2025-04-24 13:16:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:36: 状态:游戏结束!-继续开始
2025-04-24 13:16:36: 状态:游戏结束!
2025-04-24 13:16:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:40: 状态:游戏结束!-继续开始
2025-04-24 13:16:40: 状态:游戏结束!
2025-04-24 13:16:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:44: 状态:游戏结束!-继续开始
2025-04-24 13:16:44: 状态:游戏结束!
2025-04-24 13:16:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:48: 状态:游戏结束!-继续开始
2025-04-24 13:16:48: 状态:游戏结束!
2025-04-24 13:16:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:52: 状态:游戏结束!-继续开始
2025-04-24 13:16:52: 状态:游戏结束!
2025-04-24 13:16:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:16:56: 状态:游戏结束!-继续开始
2025-04-24 13:16:56: 状态:游戏结束!
2025-04-24 13:17:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:01: 状态:游戏结束!-继续开始
2025-04-24 13:17:01: 状态:游戏结束!
2025-04-24 13:17:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:05: 状态:游戏结束!-继续开始
2025-04-24 13:17:05: 状态:游戏结束!
2025-04-24 13:17:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:09: 状态:游戏结束!-继续开始
2025-04-24 13:17:09: 状态:游戏结束!
2025-04-24 13:17:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:13: 状态:游戏结束!-继续开始
2025-04-24 13:17:13: 状态:游戏结束!
2025-04-24 13:17:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:17: 状态:游戏结束!-继续开始
2025-04-24 13:17:17: 状态:游戏结束!
2025-04-24 13:17:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:21: 状态:游戏结束!-继续开始
2025-04-24 13:17:21: 状态:游戏结束!
2025-04-24 13:17:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:25: 状态:游戏结束!-继续开始
2025-04-24 13:17:25: 状态:游戏结束!
2025-04-24 13:17:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:29: 状态:游戏结束!-继续开始
2025-04-24 13:17:29: 状态:游戏结束!
2025-04-24 13:17:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:33: 状态:游戏结束!-继续开始
2025-04-24 13:17:33: 状态:游戏结束!
2025-04-24 13:17:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:38: 状态:游戏结束!-继续开始
2025-04-24 13:17:38: 状态:游戏结束!
2025-04-24 13:17:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:42: 状态:游戏结束!-继续开始
2025-04-24 13:17:42: 状态:游戏结束!
2025-04-24 13:17:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:46: 状态:游戏结束!-继续开始
2025-04-24 13:17:46: 状态:游戏结束!
2025-04-24 13:17:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:50: 状态:游戏结束!-继续开始
2025-04-24 13:17:50: 状态:游戏结束!
2025-04-24 13:17:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:54: 状态:游戏结束!-继续开始
2025-04-24 13:17:54: 状态:游戏结束!
2025-04-24 13:17:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:17:58: 状态:游戏结束!-继续开始
2025-04-24 13:17:58: 状态:游戏结束!
2025-04-24 13:18:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:02: 状态:游戏结束!-继续开始
2025-04-24 13:18:02: 状态:游戏结束!
2025-04-24 13:18:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:06: 状态:游戏结束!-继续开始
2025-04-24 13:18:06: 状态:游戏结束!
2025-04-24 13:18:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:10: 状态:游戏结束!-继续开始
2025-04-24 13:18:10: 状态:游戏结束!
2025-04-24 13:18:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:15: 状态:游戏结束!-继续开始
2025-04-24 13:18:15: 状态:游戏结束!
2025-04-24 13:18:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:19: 状态:游戏结束!-继续开始
2025-04-24 13:18:19: 状态:游戏结束!
2025-04-24 13:18:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:23: 状态:游戏结束!-继续开始
2025-04-24 13:18:23: 状态:游戏结束!
2025-04-24 13:18:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:27: 状态:游戏结束!-继续开始
2025-04-24 13:18:27: 状态:游戏结束!
2025-04-24 13:18:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:31: 状态:游戏结束!-继续开始
2025-04-24 13:18:31: 状态:游戏结束!
2025-04-24 13:18:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:35: 状态:游戏结束!-继续开始
2025-04-24 13:18:35: 状态:游戏结束!
2025-04-24 13:18:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:39: 状态:游戏结束!-继续开始
2025-04-24 13:18:39: 状态:游戏结束!
2025-04-24 13:18:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:43: 状态:游戏结束!-继续开始
2025-04-24 13:18:43: 状态:游戏结束!
2025-04-24 13:18:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:47: 状态:游戏结束!-继续开始
2025-04-24 13:18:47: 状态:游戏结束!
2025-04-24 13:18:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:52: 状态:游戏结束!-继续开始
2025-04-24 13:18:52: 状态:游戏结束!
2025-04-24 13:18:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:18:56: 状态:游戏结束!-继续开始
2025-04-24 13:18:56: 状态:游戏结束!
2025-04-24 13:18:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:00: 状态:游戏结束!-继续开始
2025-04-24 13:19:00: 状态:游戏结束!
2025-04-24 13:19:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:04: 状态:游戏结束!-继续开始
2025-04-24 13:19:04: 状态:游戏结束!
2025-04-24 13:19:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:08: 状态:游戏结束!-继续开始
2025-04-24 13:19:08: 状态:游戏结束!
2025-04-24 13:19:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:12: 状态:游戏结束!-继续开始
2025-04-24 13:19:12: 状态:游戏结束!
2025-04-24 13:19:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:16: 状态:游戏结束!-继续开始
2025-04-24 13:19:16: 状态:游戏结束!
2025-04-24 13:19:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:20: 状态:游戏结束!-继续开始
2025-04-24 13:19:20: 状态:游戏结束!
2025-04-24 13:19:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:24: 状态:游戏结束!-继续开始
2025-04-24 13:19:24: 状态:游戏结束!
2025-04-24 13:19:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:29: 状态:游戏结束!-继续开始
2025-04-24 13:19:29: 状态:游戏结束!
2025-04-24 13:19:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:33: 状态:游戏结束!-继续开始
2025-04-24 13:19:33: 状态:游戏结束!
2025-04-24 13:19:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:37: 状态:游戏结束!-继续开始
2025-04-24 13:19:37: 状态:游戏结束!
2025-04-24 13:19:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:41: 状态:游戏结束!-继续开始
2025-04-24 13:19:41: 状态:游戏结束!
2025-04-24 13:19:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:45: 状态:游戏结束!-继续开始
2025-04-24 13:19:45: 状态:游戏结束!
2025-04-24 13:19:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:49: 状态:游戏结束!-继续开始
2025-04-24 13:19:49: 状态:游戏结束!
2025-04-24 13:19:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:53: 状态:游戏结束!-继续开始
2025-04-24 13:19:53: 状态:游戏结束!
2025-04-24 13:19:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:19:57: 状态:游戏结束!-继续开始
2025-04-24 13:19:57: 状态:游戏结束!
2025-04-24 13:20:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:01: 状态:游戏结束!-继续开始
2025-04-24 13:20:01: 状态:游戏结束!
2025-04-24 13:20:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:05: 状态:游戏结束!-继续开始
2025-04-24 13:20:05: 状态:游戏结束!
2025-04-24 13:20:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:10: 状态:游戏结束!-继续开始
2025-04-24 13:20:10: 状态:游戏结束!
2025-04-24 13:20:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:14: 状态:游戏结束!-继续开始
2025-04-24 13:20:14: 状态:游戏结束!
2025-04-24 13:20:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:18: 状态:游戏结束!-继续开始
2025-04-24 13:20:18: 状态:游戏结束!
2025-04-24 13:20:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:22: 状态:游戏结束!-继续开始
2025-04-24 13:20:22: 状态:游戏结束!
2025-04-24 13:20:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:26: 状态:游戏结束!-继续开始
2025-04-24 13:20:26: 状态:游戏结束!
2025-04-24 13:20:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:30: 状态:游戏结束!-继续开始
2025-04-24 13:20:30: 状态:游戏结束!
2025-04-24 13:20:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:34: 状态:游戏结束!-继续开始
2025-04-24 13:20:34: 状态:游戏结束!
2025-04-24 13:20:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:38: 状态:游戏结束!-继续开始
2025-04-24 13:20:38: 状态:游戏结束!
2025-04-24 13:20:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:42: 状态:游戏结束!-继续开始
2025-04-24 13:20:42: 状态:游戏结束!
2025-04-24 13:20:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:47: 状态:游戏结束!-继续开始
2025-04-24 13:20:47: 状态:游戏结束!
2025-04-24 13:20:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:51: 状态:游戏结束!-继续开始
2025-04-24 13:20:51: 状态:游戏结束!
2025-04-24 13:20:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:55: 状态:游戏结束!-继续开始
2025-04-24 13:20:55: 状态:游戏结束!
2025-04-24 13:20:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:20:59: 状态:游戏结束!-继续开始
2025-04-24 13:20:59: 状态:游戏结束!
2025-04-24 13:21:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:03: 状态:游戏结束!-继续开始
2025-04-24 13:21:03: 状态:游戏结束!
2025-04-24 13:21:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:07: 状态:游戏结束!-继续开始
2025-04-24 13:21:07: 状态:游戏结束!
2025-04-24 13:21:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:11: 状态:游戏结束!-继续开始
2025-04-24 13:21:11: 状态:游戏结束!
2025-04-24 13:21:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:15: 状态:游戏结束!-继续开始
2025-04-24 13:21:15: 状态:游戏结束!
2025-04-24 13:21:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:19: 状态:游戏结束!-继续开始
2025-04-24 13:21:19: 状态:游戏结束!
2025-04-24 13:21:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:24: 状态:游戏结束!-继续开始
2025-04-24 13:21:24: 状态:游戏结束!
2025-04-24 13:21:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:28: 状态:游戏结束!-继续开始
2025-04-24 13:21:28: 状态:游戏结束!
2025-04-24 13:21:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:32: 状态:游戏结束!-继续开始
2025-04-24 13:21:32: 状态:游戏结束!
2025-04-24 13:21:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:36: 状态:游戏结束!-继续开始
2025-04-24 13:21:36: 状态:游戏结束!
2025-04-24 13:21:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:40: 状态:游戏结束!-继续开始
2025-04-24 13:21:40: 状态:游戏结束!
2025-04-24 13:21:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:44: 状态:游戏结束!-继续开始
2025-04-24 13:21:44: 状态:游戏结束!
2025-04-24 13:21:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:48: 状态:游戏结束!-继续开始
2025-04-24 13:21:48: 状态:游戏结束!
2025-04-24 13:21:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:52: 状态:游戏结束!-继续开始
2025-04-24 13:21:52: 状态:游戏结束!
2025-04-24 13:21:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:21:56: 状态:游戏结束!-继续开始
2025-04-24 13:21:56: 状态:游戏结束!
2025-04-24 13:21:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:00: 状态:游戏结束!-继续开始
2025-04-24 13:22:00: 状态:游戏结束!
2025-04-24 13:22:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:05: 状态:游戏结束!-继续开始
2025-04-24 13:22:05: 状态:游戏结束!
2025-04-24 13:22:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:09: 状态:游戏结束!-继续开始
2025-04-24 13:22:09: 状态:游戏结束!
2025-04-24 13:22:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:13: 状态:游戏结束!-继续开始
2025-04-24 13:22:13: 状态:游戏结束!
2025-04-24 13:22:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:17: 状态:游戏结束!-继续开始
2025-04-24 13:22:17: 状态:游戏结束!
2025-04-24 13:22:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:21: 状态:游戏结束!-继续开始
2025-04-24 13:22:21: 状态:游戏结束!
2025-04-24 13:22:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:25: 状态:游戏结束!-继续开始
2025-04-24 13:22:25: 状态:游戏结束!
2025-04-24 13:22:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:29: 状态:游戏结束!-继续开始
2025-04-24 13:22:29: 状态:游戏结束!
2025-04-24 13:22:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:33: 状态:游戏结束!-继续开始
2025-04-24 13:22:33: 状态:游戏结束!
2025-04-24 13:22:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:38: 状态:游戏结束!-继续开始
2025-04-24 13:22:38: 状态:游戏结束!
2025-04-24 13:22:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:42: 状态:游戏结束!-继续开始
2025-04-24 13:22:42: 状态:游戏结束!
2025-04-24 13:22:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:46: 状态:游戏结束!-继续开始
2025-04-24 13:22:46: 状态:游戏结束!
2025-04-24 13:22:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:50: 状态:游戏结束!-继续开始
2025-04-24 13:22:50: 状态:游戏结束!
2025-04-24 13:22:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:54: 状态:游戏结束!-继续开始
2025-04-24 13:22:54: 状态:游戏结束!
2025-04-24 13:22:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:22:58: 状态:游戏结束!-继续开始
2025-04-24 13:22:58: 状态:游戏结束!
2025-04-24 13:23:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:02: 状态:游戏结束!-继续开始
2025-04-24 13:23:02: 状态:游戏结束!
2025-04-24 13:23:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:06: 状态:游戏结束!-继续开始
2025-04-24 13:23:06: 状态:游戏结束!
2025-04-24 13:23:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:10: 状态:游戏结束!-继续开始
2025-04-24 13:23:10: 状态:游戏结束!
2025-04-24 13:23:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:14: 状态:游戏结束!-继续开始
2025-04-24 13:23:14: 状态:游戏结束!
2025-04-24 13:23:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:19: 状态:游戏结束!-继续开始
2025-04-24 13:23:19: 状态:游戏结束!
2025-04-24 13:23:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:23: 状态:游戏结束!-继续开始
2025-04-24 13:23:23: 状态:游戏结束!
2025-04-24 13:23:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:27: 状态:游戏结束!-继续开始
2025-04-24 13:23:27: 状态:游戏结束!
2025-04-24 13:23:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:31: 状态:游戏结束!-继续开始
2025-04-24 13:23:31: 状态:游戏结束!
2025-04-24 13:23:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:35: 状态:游戏结束!-继续开始
2025-04-24 13:23:35: 状态:游戏结束!
2025-04-24 13:23:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:39: 状态:游戏结束!-继续开始
2025-04-24 13:23:39: 状态:游戏结束!
2025-04-24 13:23:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:43: 状态:游戏结束!-继续开始
2025-04-24 13:23:43: 状态:游戏结束!
2025-04-24 13:23:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:47: 状态:游戏结束!-继续开始
2025-04-24 13:23:47: 状态:游戏结束!
2025-04-24 13:23:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:51: 状态:游戏结束!-继续开始
2025-04-24 13:23:51: 状态:游戏结束!
2025-04-24 13:23:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:23:56: 状态:游戏结束!-继续开始
2025-04-24 13:23:56: 状态:游戏结束!
2025-04-24 13:23:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:00: 状态:游戏结束!-继续开始
2025-04-24 13:24:00: 状态:游戏结束!
2025-04-24 13:24:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:04: 状态:游戏结束!-继续开始
2025-04-24 13:24:04: 状态:游戏结束!
2025-04-24 13:24:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:08: 状态:游戏结束!-继续开始
2025-04-24 13:24:08: 状态:游戏结束!
2025-04-24 13:24:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:12: 状态:游戏结束!-继续开始
2025-04-24 13:24:12: 状态:游戏结束!
2025-04-24 13:24:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:16: 状态:游戏结束!-继续开始
2025-04-24 13:24:16: 状态:游戏结束!
2025-04-24 13:24:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:20: 状态:游戏结束!-继续开始
2025-04-24 13:24:20: 状态:游戏结束!
2025-04-24 13:24:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:24: 状态:游戏结束!-继续开始
2025-04-24 13:24:24: 状态:游戏结束!
2025-04-24 13:24:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:28: 状态:游戏结束!-继续开始
2025-04-24 13:24:28: 状态:游戏结束!
2025-04-24 13:24:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:33: 状态:游戏结束!-继续开始
2025-04-24 13:24:33: 状态:游戏结束!
2025-04-24 13:24:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:37: 状态:游戏结束!-继续开始
2025-04-24 13:24:37: 状态:游戏结束!
2025-04-24 13:24:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:41: 状态:游戏结束!-继续开始
2025-04-24 13:24:41: 状态:游戏结束!
2025-04-24 13:24:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:45: 状态:游戏结束!-继续开始
2025-04-24 13:24:45: 状态:游戏结束!
2025-04-24 13:24:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:49: 状态:游戏结束!-继续开始
2025-04-24 13:24:49: 状态:游戏结束!
2025-04-24 13:24:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:53: 状态:游戏结束!-继续开始
2025-04-24 13:24:53: 状态:游戏结束!
2025-04-24 13:24:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:24:57: 状态:游戏结束!-继续开始
2025-04-24 13:24:57: 状态:游戏结束!
2025-04-24 13:25:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:01: 状态:游戏结束!-继续开始
2025-04-24 13:25:01: 状态:游戏结束!
2025-04-24 13:25:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:05: 状态:游戏结束!-继续开始
2025-04-24 13:25:05: 状态:游戏结束!
2025-04-24 13:25:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:10: 状态:游戏结束!-继续开始
2025-04-24 13:25:10: 状态:游戏结束!
2025-04-24 13:25:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:14: 状态:游戏结束!-继续开始
2025-04-24 13:25:14: 状态:游戏结束!
2025-04-24 13:25:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:18: 状态:游戏结束!-继续开始
2025-04-24 13:25:18: 状态:游戏结束!
2025-04-24 13:25:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:22: 状态:游戏结束!-继续开始
2025-04-24 13:25:22: 状态:游戏结束!
2025-04-24 13:25:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:26: 状态:游戏结束!-继续开始
2025-04-24 13:25:26: 状态:游戏结束!
2025-04-24 13:25:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:30: 状态:游戏结束!-继续开始
2025-04-24 13:25:30: 状态:游戏结束!
2025-04-24 13:25:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:34: 状态:游戏结束!-继续开始
2025-04-24 13:25:34: 状态:游戏结束!
2025-04-24 13:25:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:38: 状态:游戏结束!-继续开始
2025-04-24 13:25:38: 状态:游戏结束!
2025-04-24 13:25:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:43: 状态:游戏结束!-继续开始
2025-04-24 13:25:43: 状态:游戏结束!
2025-04-24 13:25:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:47: 状态:游戏结束!-继续开始
2025-04-24 13:25:47: 状态:游戏结束!
2025-04-24 13:25:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:51: 状态:游戏结束!-继续开始
2025-04-24 13:25:51: 状态:游戏结束!
2025-04-24 13:25:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:55: 状态:游戏结束!-继续开始
2025-04-24 13:25:55: 状态:游戏结束!
2025-04-24 13:25:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:25:59: 状态:游戏结束!-继续开始
2025-04-24 13:25:59: 状态:游戏结束!
2025-04-24 13:26:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:03: 状态:游戏结束!-继续开始
2025-04-24 13:26:03: 状态:游戏结束!
2025-04-24 13:26:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:07: 状态:游戏结束!-继续开始
2025-04-24 13:26:07: 状态:游戏结束!
2025-04-24 13:26:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:11: 状态:游戏结束!-继续开始
2025-04-24 13:26:11: 状态:游戏结束!
2025-04-24 13:26:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:15: 状态:游戏结束!-继续开始
2025-04-24 13:26:15: 状态:游戏结束!
2025-04-24 13:26:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:20: 状态:游戏结束!-继续开始
2025-04-24 13:26:20: 状态:游戏结束!
2025-04-24 13:26:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:24: 状态:游戏结束!-继续开始
2025-04-24 13:26:24: 状态:游戏结束!
2025-04-24 13:26:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:28: 状态:游戏结束!-继续开始
2025-04-24 13:26:28: 状态:游戏结束!
2025-04-24 13:26:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:32: 状态:游戏结束!-继续开始
2025-04-24 13:26:32: 状态:游戏结束!
2025-04-24 13:26:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:36: 状态:游戏结束!-继续开始
2025-04-24 13:26:36: 状态:游戏结束!
2025-04-24 13:26:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:40: 状态:游戏结束!-继续开始
2025-04-24 13:26:40: 状态:游戏结束!
2025-04-24 13:26:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:44: 状态:游戏结束!-继续开始
2025-04-24 13:26:44: 状态:游戏结束!
2025-04-24 13:26:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:48: 状态:游戏结束!-继续开始
2025-04-24 13:26:48: 状态:游戏结束!
2025-04-24 13:26:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:52: 状态:游戏结束!-继续开始
2025-04-24 13:26:52: 状态:游戏结束!
2025-04-24 13:26:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:26:57: 状态:游戏结束!-继续开始
2025-04-24 13:26:57: 状态:游戏结束!
2025-04-24 13:27:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:01: 状态:游戏结束!-继续开始
2025-04-24 13:27:01: 状态:游戏结束!
2025-04-24 13:27:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:05: 状态:游戏结束!-继续开始
2025-04-24 13:27:05: 状态:游戏结束!
2025-04-24 13:27:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:09: 状态:游戏结束!-继续开始
2025-04-24 13:27:09: 状态:游戏结束!
2025-04-24 13:27:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:13: 状态:游戏结束!-继续开始
2025-04-24 13:27:13: 状态:游戏结束!
2025-04-24 13:27:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:17: 状态:游戏结束!-继续开始
2025-04-24 13:27:17: 状态:游戏结束!
2025-04-24 13:27:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:21: 状态:游戏结束!-继续开始
2025-04-24 13:27:21: 状态:游戏结束!
2025-04-24 13:27:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:25: 状态:游戏结束!-继续开始
2025-04-24 13:27:25: 状态:游戏结束!
2025-04-24 13:27:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:29: 状态:游戏结束!-继续开始
2025-04-24 13:27:29: 状态:游戏结束!
2025-04-24 13:27:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:34: 状态:游戏结束!-继续开始
2025-04-24 13:27:34: 状态:游戏结束!
2025-04-24 13:27:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:38: 状态:游戏结束!-继续开始
2025-04-24 13:27:38: 状态:游戏结束!
2025-04-24 13:27:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:42: 状态:游戏结束!-继续开始
2025-04-24 13:27:42: 状态:游戏结束!
2025-04-24 13:27:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:46: 状态:游戏结束!-继续开始
2025-04-24 13:27:46: 状态:游戏结束!
2025-04-24 13:27:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:50: 状态:游戏结束!-继续开始
2025-04-24 13:27:50: 状态:游戏结束!
2025-04-24 13:27:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:54: 状态:游戏结束!-继续开始
2025-04-24 13:27:54: 状态:游戏结束!
2025-04-24 13:27:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:27:58: 状态:游戏结束!-继续开始
2025-04-24 13:27:58: 状态:游戏结束!
2025-04-24 13:28:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:02: 状态:游戏结束!-继续开始
2025-04-24 13:28:02: 状态:游戏结束!
2025-04-24 13:28:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:06: 状态:游戏结束!-继续开始
2025-04-24 13:28:06: 状态:游戏结束!
2025-04-24 13:28:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:10: 状态:游戏结束!-继续开始
2025-04-24 13:28:10: 状态:游戏结束!
2025-04-24 13:28:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:15: 状态:游戏结束!-继续开始
2025-04-24 13:28:15: 状态:游戏结束!
2025-04-24 13:28:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:19: 状态:游戏结束!-继续开始
2025-04-24 13:28:19: 状态:游戏结束!
2025-04-24 13:28:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:23: 状态:游戏结束!-继续开始
2025-04-24 13:28:23: 状态:游戏结束!
2025-04-24 13:28:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:27: 状态:游戏结束!-继续开始
2025-04-24 13:28:27: 状态:游戏结束!
2025-04-24 13:28:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:31: 状态:游戏结束!-继续开始
2025-04-24 13:28:31: 状态:游戏结束!
2025-04-24 13:28:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:35: 状态:游戏结束!-继续开始
2025-04-24 13:28:35: 状态:游戏结束!
2025-04-24 13:28:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:39: 状态:游戏结束!-继续开始
2025-04-24 13:28:39: 状态:游戏结束!
2025-04-24 13:28:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:43: 状态:游戏结束!-继续开始
2025-04-24 13:28:43: 状态:游戏结束!
2025-04-24 13:28:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:47: 状态:游戏结束!-继续开始
2025-04-24 13:28:47: 状态:游戏结束!
2025-04-24 13:28:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:52: 状态:游戏结束!-继续开始
2025-04-24 13:28:52: 状态:游戏结束!
2025-04-24 13:28:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:28:56: 状态:游戏结束!-继续开始
2025-04-24 13:28:56: 状态:游戏结束!
2025-04-24 13:28:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:00: 状态:游戏结束!-继续开始
2025-04-24 13:29:00: 状态:游戏结束!
2025-04-24 13:29:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:04: 状态:游戏结束!-继续开始
2025-04-24 13:29:04: 状态:游戏结束!
2025-04-24 13:29:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:08: 状态:游戏结束!-继续开始
2025-04-24 13:29:08: 状态:游戏结束!
2025-04-24 13:29:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:12: 状态:游戏结束!-继续开始
2025-04-24 13:29:12: 状态:游戏结束!
2025-04-24 13:29:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:16: 状态:游戏结束!-继续开始
2025-04-24 13:29:16: 状态:游戏结束!
2025-04-24 13:29:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:20: 状态:游戏结束!-继续开始
2025-04-24 13:29:20: 状态:游戏结束!
2025-04-24 13:29:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:24: 状态:游戏结束!-继续开始
2025-04-24 13:29:24: 状态:游戏结束!
2025-04-24 13:29:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:28: 状态:游戏结束!-继续开始
2025-04-24 13:29:28: 状态:游戏结束!
2025-04-24 13:29:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:33: 状态:游戏结束!-继续开始
2025-04-24 13:29:33: 状态:游戏结束!
2025-04-24 13:29:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:37: 状态:游戏结束!-继续开始
2025-04-24 13:29:37: 状态:游戏结束!
2025-04-24 13:29:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:41: 状态:游戏结束!-继续开始
2025-04-24 13:29:41: 状态:游戏结束!
2025-04-24 13:29:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:45: 状态:游戏结束!-继续开始
2025-04-24 13:29:45: 状态:游戏结束!
2025-04-24 13:29:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:49: 状态:游戏结束!-继续开始
2025-04-24 13:29:49: 状态:游戏结束!
2025-04-24 13:29:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:53: 状态:游戏结束!-继续开始
2025-04-24 13:29:53: 状态:游戏结束!
2025-04-24 13:29:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:29:57: 状态:游戏结束!-继续开始
2025-04-24 13:29:57: 状态:游戏结束!
2025-04-24 13:30:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:01: 状态:游戏结束!-继续开始
2025-04-24 13:30:01: 状态:游戏结束!
2025-04-24 13:30:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:05: 状态:游戏结束!-继续开始
2025-04-24 13:30:05: 状态:游戏结束!
2025-04-24 13:30:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:09: 状态:游戏结束!-继续开始
2025-04-24 13:30:09: 状态:游戏结束!
2025-04-24 13:30:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:14: 状态:游戏结束!-继续开始
2025-04-24 13:30:14: 状态:游戏结束!
2025-04-24 13:30:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:18: 状态:游戏结束!-继续开始
2025-04-24 13:30:18: 状态:游戏结束!
2025-04-24 13:30:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:22: 状态:游戏结束!-继续开始
2025-04-24 13:30:22: 状态:游戏结束!
2025-04-24 13:30:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:26: 状态:游戏结束!-继续开始
2025-04-24 13:30:26: 状态:游戏结束!
2025-04-24 13:30:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:30: 状态:游戏结束!-继续开始
2025-04-24 13:30:30: 状态:游戏结束!
2025-04-24 13:30:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:34: 状态:游戏结束!-继续开始
2025-04-24 13:30:34: 状态:游戏结束!
2025-04-24 13:30:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:38: 状态:游戏结束!-继续开始
2025-04-24 13:30:38: 状态:游戏结束!
2025-04-24 13:30:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:42: 状态:游戏结束!-继续开始
2025-04-24 13:30:42: 状态:游戏结束!
2025-04-24 13:30:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:46: 状态:游戏结束!-继续开始
2025-04-24 13:30:46: 状态:游戏结束!
2025-04-24 13:30:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:51: 状态:游戏结束!-继续开始
2025-04-24 13:30:51: 状态:游戏结束!
2025-04-24 13:30:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:55: 状态:游戏结束!-继续开始
2025-04-24 13:30:55: 状态:游戏结束!
2025-04-24 13:30:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:30:59: 状态:游戏结束!-继续开始
2025-04-24 13:30:59: 状态:游戏结束!
2025-04-24 13:31:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:03: 状态:游戏结束!-继续开始
2025-04-24 13:31:03: 状态:游戏结束!
2025-04-24 13:31:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:07: 状态:游戏结束!-继续开始
2025-04-24 13:31:07: 状态:游戏结束!
2025-04-24 13:31:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:11: 状态:游戏结束!-继续开始
2025-04-24 13:31:11: 状态:游戏结束!
2025-04-24 13:31:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:15: 状态:游戏结束!-继续开始
2025-04-24 13:31:15: 状态:游戏结束!
2025-04-24 13:31:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:19: 状态:游戏结束!-继续开始
2025-04-24 13:31:19: 状态:游戏结束!
2025-04-24 13:31:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:23: 状态:游戏结束!-继续开始
2025-04-24 13:31:23: 状态:游戏结束!
2025-04-24 13:31:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:27: 状态:游戏结束!-继续开始
2025-04-24 13:31:27: 状态:游戏结束!
2025-04-24 13:31:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:32: 状态:游戏结束!-继续开始
2025-04-24 13:31:32: 状态:游戏结束!
2025-04-24 13:31:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:36: 状态:游戏结束!-继续开始
2025-04-24 13:31:36: 状态:游戏结束!
2025-04-24 13:31:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:40: 状态:游戏结束!-继续开始
2025-04-24 13:31:40: 状态:游戏结束!
2025-04-24 13:31:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:44: 状态:游戏结束!-继续开始
2025-04-24 13:31:44: 状态:游戏结束!
2025-04-24 13:31:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:48: 状态:游戏结束!-继续开始
2025-04-24 13:31:48: 状态:游戏结束!
2025-04-24 13:31:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:52: 状态:游戏结束!-继续开始
2025-04-24 13:31:52: 状态:游戏结束!
2025-04-24 13:31:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:31:56: 状态:游戏结束!-继续开始
2025-04-24 13:31:56: 状态:游戏结束!
2025-04-24 13:31:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:00: 状态:游戏结束!-继续开始
2025-04-24 13:32:00: 状态:游戏结束!
2025-04-24 13:32:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:04: 状态:游戏结束!-继续开始
2025-04-24 13:32:04: 状态:游戏结束!
2025-04-24 13:32:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:09: 状态:游戏结束!-继续开始
2025-04-24 13:32:09: 状态:游戏结束!
2025-04-24 13:32:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:13: 状态:游戏结束!-继续开始
2025-04-24 13:32:13: 状态:游戏结束!
2025-04-24 13:32:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:17: 状态:游戏结束!-继续开始
2025-04-24 13:32:17: 状态:游戏结束!
2025-04-24 13:32:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:21: 状态:游戏结束!-继续开始
2025-04-24 13:32:21: 状态:游戏结束!
2025-04-24 13:32:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:25: 状态:游戏结束!-继续开始
2025-04-24 13:32:25: 状态:游戏结束!
2025-04-24 13:32:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:29: 状态:游戏结束!-继续开始
2025-04-24 13:32:29: 状态:游戏结束!
2025-04-24 13:32:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:33: 状态:游戏结束!-继续开始
2025-04-24 13:32:33: 状态:游戏结束!
2025-04-24 13:32:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:37: 状态:游戏结束!-继续开始
2025-04-24 13:32:37: 状态:游戏结束!
2025-04-24 13:32:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:41: 状态:游戏结束!-继续开始
2025-04-24 13:32:41: 状态:游戏结束!
2025-04-24 13:32:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:46: 状态:游戏结束!-继续开始
2025-04-24 13:32:46: 状态:游戏结束!
2025-04-24 13:32:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:50: 状态:游戏结束!-继续开始
2025-04-24 13:32:50: 状态:游戏结束!
2025-04-24 13:32:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:54: 状态:游戏结束!-继续开始
2025-04-24 13:32:54: 状态:游戏结束!
2025-04-24 13:32:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:32:58: 状态:游戏结束!-继续开始
2025-04-24 13:32:58: 状态:游戏结束!
2025-04-24 13:33:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:02: 状态:游戏结束!-继续开始
2025-04-24 13:33:02: 状态:游戏结束!
2025-04-24 13:33:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:06: 状态:游戏结束!-继续开始
2025-04-24 13:33:06: 状态:游戏结束!
2025-04-24 13:33:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:10: 状态:游戏结束!-继续开始
2025-04-24 13:33:10: 状态:游戏结束!
2025-04-24 13:33:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:14: 状态:游戏结束!-继续开始
2025-04-24 13:33:14: 状态:游戏结束!
2025-04-24 13:33:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:18: 状态:游戏结束!-继续开始
2025-04-24 13:33:18: 状态:游戏结束!
2025-04-24 13:33:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:22: 状态:游戏结束!-继续开始
2025-04-24 13:33:22: 状态:游戏结束!
2025-04-24 13:33:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:27: 状态:游戏结束!-继续开始
2025-04-24 13:33:27: 状态:游戏结束!
2025-04-24 13:33:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:31: 状态:游戏结束!-继续开始
2025-04-24 13:33:31: 状态:游戏结束!
2025-04-24 13:33:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:35: 状态:游戏结束!-继续开始
2025-04-24 13:33:35: 状态:游戏结束!
2025-04-24 13:33:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:39: 状态:游戏结束!-继续开始
2025-04-24 13:33:39: 状态:游戏结束!
2025-04-24 13:33:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:43: 状态:游戏结束!-继续开始
2025-04-24 13:33:43: 状态:游戏结束!
2025-04-24 13:33:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:47: 状态:游戏结束!-继续开始
2025-04-24 13:33:47: 状态:游戏结束!
2025-04-24 13:33:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:51: 状态:游戏结束!-继续开始
2025-04-24 13:33:51: 状态:游戏结束!
2025-04-24 13:33:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:55: 状态:游戏结束!-继续开始
2025-04-24 13:33:55: 状态:游戏结束!
2025-04-24 13:33:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:33:59: 状态:游戏结束!-继续开始
2025-04-24 13:33:59: 状态:游戏结束!
2025-04-24 13:34:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:04: 状态:游戏结束!-继续开始
2025-04-24 13:34:04: 状态:游戏结束!
2025-04-24 13:34:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:08: 状态:游戏结束!-继续开始
2025-04-24 13:34:08: 状态:游戏结束!
2025-04-24 13:34:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:12: 状态:游戏结束!-继续开始
2025-04-24 13:34:12: 状态:游戏结束!
2025-04-24 13:34:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:16: 状态:游戏结束!-继续开始
2025-04-24 13:34:16: 状态:游戏结束!
2025-04-24 13:34:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:20: 状态:游戏结束!-继续开始
2025-04-24 13:34:20: 状态:游戏结束!
2025-04-24 13:34:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:24: 状态:游戏结束!-继续开始
2025-04-24 13:34:24: 状态:游戏结束!
2025-04-24 13:34:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:28: 状态:游戏结束!-继续开始
2025-04-24 13:34:28: 状态:游戏结束!
2025-04-24 13:34:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:32: 状态:游戏结束!-继续开始
2025-04-24 13:34:32: 状态:游戏结束!
2025-04-24 13:34:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:36: 状态:游戏结束!-继续开始
2025-04-24 13:34:36: 状态:游戏结束!
2025-04-24 13:34:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:41: 状态:游戏结束!-继续开始
2025-04-24 13:34:41: 状态:游戏结束!
2025-04-24 13:34:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:45: 状态:游戏结束!-继续开始
2025-04-24 13:34:45: 状态:游戏结束!
2025-04-24 13:34:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:49: 状态:游戏结束!-继续开始
2025-04-24 13:34:49: 状态:游戏结束!
2025-04-24 13:34:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:53: 状态:游戏结束!-继续开始
2025-04-24 13:34:53: 状态:游戏结束!
2025-04-24 13:34:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:34:57: 状态:游戏结束!-继续开始
2025-04-24 13:34:57: 状态:游戏结束!
2025-04-24 13:35:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:01: 状态:游戏结束!-继续开始
2025-04-24 13:35:01: 状态:游戏结束!
2025-04-24 13:35:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:05: 状态:游戏结束!-继续开始
2025-04-24 13:35:05: 状态:游戏结束!
2025-04-24 13:35:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:09: 状态:游戏结束!-继续开始
2025-04-24 13:35:09: 状态:游戏结束!
2025-04-24 13:35:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:13: 状态:游戏结束!-继续开始
2025-04-24 13:35:13: 状态:游戏结束!
2025-04-24 13:35:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:18: 状态:游戏结束!-继续开始
2025-04-24 13:35:18: 状态:游戏结束!
2025-04-24 13:35:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:22: 状态:游戏结束!-继续开始
2025-04-24 13:35:22: 状态:游戏结束!
2025-04-24 13:35:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:26: 状态:游戏结束!-继续开始
2025-04-24 13:35:26: 状态:游戏结束!
2025-04-24 13:35:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:30: 状态:游戏结束!-继续开始
2025-04-24 13:35:30: 状态:游戏结束!
2025-04-24 13:35:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:34: 状态:游戏结束!-继续开始
2025-04-24 13:35:34: 状态:游戏结束!
2025-04-24 13:35:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:38: 状态:游戏结束!-继续开始
2025-04-24 13:35:38: 状态:游戏结束!
2025-04-24 13:35:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:42: 状态:游戏结束!-继续开始
2025-04-24 13:35:42: 状态:游戏结束!
2025-04-24 13:35:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:46: 状态:游戏结束!-继续开始
2025-04-24 13:35:46: 状态:游戏结束!
2025-04-24 13:35:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:50: 状态:游戏结束!-继续开始
2025-04-24 13:35:50: 状态:游戏结束!
2025-04-24 13:35:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:54: 状态:游戏结束!-继续开始
2025-04-24 13:35:54: 状态:游戏结束!
2025-04-24 13:35:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:35:59: 状态:游戏结束!-继续开始
2025-04-24 13:35:59: 状态:游戏结束!
2025-04-24 13:36:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:03: 状态:游戏结束!-继续开始
2025-04-24 13:36:03: 状态:游戏结束!
2025-04-24 13:36:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:07: 状态:游戏结束!-继续开始
2025-04-24 13:36:07: 状态:游戏结束!
2025-04-24 13:36:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:11: 状态:游戏结束!-继续开始
2025-04-24 13:36:11: 状态:游戏结束!
2025-04-24 13:36:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:15: 状态:游戏结束!-继续开始
2025-04-24 13:36:15: 状态:游戏结束!
2025-04-24 13:36:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:19: 状态:游戏结束!-继续开始
2025-04-24 13:36:19: 状态:游戏结束!
2025-04-24 13:36:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:23: 状态:游戏结束!-继续开始
2025-04-24 13:36:23: 状态:游戏结束!
2025-04-24 13:36:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:27: 状态:游戏结束!-继续开始
2025-04-24 13:36:27: 状态:游戏结束!
2025-04-24 13:36:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:31: 状态:游戏结束!-继续开始
2025-04-24 13:36:31: 状态:游戏结束!
2025-04-24 13:36:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:35: 状态:游戏结束!-继续开始
2025-04-24 13:36:35: 状态:游戏结束!
2025-04-24 13:36:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:40: 状态:游戏结束!-继续开始
2025-04-24 13:36:40: 状态:游戏结束!
2025-04-24 13:36:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:44: 状态:游戏结束!-继续开始
2025-04-24 13:36:44: 状态:游戏结束!
2025-04-24 13:36:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:48: 状态:游戏结束!-继续开始
2025-04-24 13:36:48: 状态:游戏结束!
2025-04-24 13:36:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:52: 状态:游戏结束!-继续开始
2025-04-24 13:36:52: 状态:游戏结束!
2025-04-24 13:36:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:36:56: 状态:游戏结束!-继续开始
2025-04-24 13:36:56: 状态:游戏结束!
2025-04-24 13:36:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:00: 状态:游戏结束!-继续开始
2025-04-24 13:37:00: 状态:游戏结束!
2025-04-24 13:37:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:04: 状态:游戏结束!-继续开始
2025-04-24 13:37:04: 状态:游戏结束!
2025-04-24 13:37:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:08: 状态:游戏结束!-继续开始
2025-04-24 13:37:08: 状态:游戏结束!
2025-04-24 13:37:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:12: 状态:游戏结束!-继续开始
2025-04-24 13:37:12: 状态:游戏结束!
2025-04-24 13:37:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:17: 状态:游戏结束!-继续开始
2025-04-24 13:37:17: 状态:游戏结束!
2025-04-24 13:37:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:21: 状态:游戏结束!-继续开始
2025-04-24 13:37:21: 状态:游戏结束!
2025-04-24 13:37:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:25: 状态:游戏结束!-继续开始
2025-04-24 13:37:25: 状态:游戏结束!
2025-04-24 13:37:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:29: 状态:游戏结束!-继续开始
2025-04-24 13:37:29: 状态:游戏结束!
2025-04-24 13:37:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:33: 状态:游戏结束!-继续开始
2025-04-24 13:37:33: 状态:游戏结束!
2025-04-24 13:37:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:37: 状态:游戏结束!-继续开始
2025-04-24 13:37:37: 状态:游戏结束!
2025-04-24 13:37:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:41: 状态:游戏结束!-继续开始
2025-04-24 13:37:41: 状态:游戏结束!
2025-04-24 13:37:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:45: 状态:游戏结束!-继续开始
2025-04-24 13:37:45: 状态:游戏结束!
2025-04-24 13:37:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:49: 状态:游戏结束!-继续开始
2025-04-24 13:37:49: 状态:游戏结束!
2025-04-24 13:37:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:53: 状态:游戏结束!-继续开始
2025-04-24 13:37:53: 状态:游戏结束!
2025-04-24 13:37:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:37:58: 状态:游戏结束!-继续开始
2025-04-24 13:37:58: 状态:游戏结束!
2025-04-24 13:38:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:02: 状态:游戏结束!-继续开始
2025-04-24 13:38:02: 状态:游戏结束!
2025-04-24 13:38:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:06: 状态:游戏结束!-继续开始
2025-04-24 13:38:06: 状态:游戏结束!
2025-04-24 13:38:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:10: 状态:游戏结束!-继续开始
2025-04-24 13:38:10: 状态:游戏结束!
2025-04-24 13:38:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:14: 状态:游戏结束!-继续开始
2025-04-24 13:38:14: 状态:游戏结束!
2025-04-24 13:38:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:18: 状态:游戏结束!-继续开始
2025-04-24 13:38:18: 状态:游戏结束!
2025-04-24 13:38:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:22: 状态:游戏结束!-继续开始
2025-04-24 13:38:22: 状态:游戏结束!
2025-04-24 13:38:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:26: 状态:游戏结束!-继续开始
2025-04-24 13:38:26: 状态:游戏结束!
2025-04-24 13:38:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:30: 状态:游戏结束!-继续开始
2025-04-24 13:38:30: 状态:游戏结束!
2025-04-24 13:38:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:34: 状态:游戏结束!-继续开始
2025-04-24 13:38:34: 状态:游戏结束!
2025-04-24 13:38:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:39: 状态:游戏结束!-继续开始
2025-04-24 13:38:39: 状态:游戏结束!
2025-04-24 13:38:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:43: 状态:游戏结束!-继续开始
2025-04-24 13:38:43: 状态:游戏结束!
2025-04-24 13:38:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:47: 状态:游戏结束!-继续开始
2025-04-24 13:38:47: 状态:游戏结束!
2025-04-24 13:38:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:51: 状态:游戏结束!-继续开始
2025-04-24 13:38:51: 状态:游戏结束!
2025-04-24 13:38:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:55: 状态:游戏结束!-继续开始
2025-04-24 13:38:55: 状态:游戏结束!
2025-04-24 13:38:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:38:59: 状态:游戏结束!-继续开始
2025-04-24 13:38:59: 状态:游戏结束!
2025-04-24 13:39:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:03: 状态:游戏结束!-继续开始
2025-04-24 13:39:03: 状态:游戏结束!
2025-04-24 13:39:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:07: 状态:游戏结束!-继续开始
2025-04-24 13:39:07: 状态:游戏结束!
2025-04-24 13:39:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:11: 状态:游戏结束!-继续开始
2025-04-24 13:39:11: 状态:游戏结束!
2025-04-24 13:39:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:16: 状态:游戏结束!-继续开始
2025-04-24 13:39:16: 状态:游戏结束!
2025-04-24 13:39:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:20: 状态:游戏结束!-继续开始
2025-04-24 13:39:20: 状态:游戏结束!
2025-04-24 13:39:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:24: 状态:游戏结束!-继续开始
2025-04-24 13:39:24: 状态:游戏结束!
2025-04-24 13:39:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:28: 状态:游戏结束!-继续开始
2025-04-24 13:39:28: 状态:游戏结束!
2025-04-24 13:39:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:32: 状态:游戏结束!-继续开始
2025-04-24 13:39:32: 状态:游戏结束!
2025-04-24 13:39:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:36: 状态:游戏结束!-继续开始
2025-04-24 13:39:36: 状态:游戏结束!
2025-04-24 13:39:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:40: 状态:游戏结束!-继续开始
2025-04-24 13:39:40: 状态:游戏结束!
2025-04-24 13:39:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:44: 状态:游戏结束!-继续开始
2025-04-24 13:39:44: 状态:游戏结束!
2025-04-24 13:39:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:48: 状态:游戏结束!-继续开始
2025-04-24 13:39:48: 状态:游戏结束!
2025-04-24 13:39:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:52: 状态:游戏结束!-继续开始
2025-04-24 13:39:52: 状态:游戏结束!
2025-04-24 13:39:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:39:57: 状态:游戏结束!-继续开始
2025-04-24 13:39:57: 状态:游戏结束!
2025-04-24 13:40:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:01: 状态:游戏结束!-继续开始
2025-04-24 13:40:01: 状态:游戏结束!
2025-04-24 13:40:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:05: 状态:游戏结束!-继续开始
2025-04-24 13:40:05: 状态:游戏结束!
2025-04-24 13:40:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:09: 状态:游戏结束!-继续开始
2025-04-24 13:40:09: 状态:游戏结束!
2025-04-24 13:40:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:13: 状态:游戏结束!-继续开始
2025-04-24 13:40:13: 状态:游戏结束!
2025-04-24 13:40:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:17: 状态:游戏结束!-继续开始
2025-04-24 13:40:17: 状态:游戏结束!
2025-04-24 13:40:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:21: 状态:游戏结束!-继续开始
2025-04-24 13:40:21: 状态:游戏结束!
2025-04-24 13:40:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:25: 状态:游戏结束!-继续开始
2025-04-24 13:40:25: 状态:游戏结束!
2025-04-24 13:40:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:29: 状态:游戏结束!-继续开始
2025-04-24 13:40:29: 状态:游戏结束!
2025-04-24 13:40:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:34: 状态:游戏结束!-继续开始
2025-04-24 13:40:34: 状态:游戏结束!
2025-04-24 13:40:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:38: 状态:游戏结束!-继续开始
2025-04-24 13:40:38: 状态:游戏结束!
2025-04-24 13:40:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:42: 状态:游戏结束!-继续开始
2025-04-24 13:40:42: 状态:游戏结束!
2025-04-24 13:40:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:46: 状态:游戏结束!-继续开始
2025-04-24 13:40:46: 状态:游戏结束!
2025-04-24 13:40:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:50: 状态:游戏结束!-继续开始
2025-04-24 13:40:50: 状态:游戏结束!
2025-04-24 13:40:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:54: 状态:游戏结束!-继续开始
2025-04-24 13:40:54: 状态:游戏结束!
2025-04-24 13:40:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:40:58: 状态:游戏结束!-继续开始
2025-04-24 13:40:58: 状态:游戏结束!
2025-04-24 13:41:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:02: 状态:游戏结束!-继续开始
2025-04-24 13:41:02: 状态:游戏结束!
2025-04-24 13:41:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:06: 状态:游戏结束!-继续开始
2025-04-24 13:41:06: 状态:游戏结束!
2025-04-24 13:41:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:11: 状态:游戏结束!-继续开始
2025-04-24 13:41:11: 状态:游戏结束!
2025-04-24 13:41:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:15: 状态:游戏结束!-继续开始
2025-04-24 13:41:15: 状态:游戏结束!
2025-04-24 13:41:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:19: 状态:游戏结束!-继续开始
2025-04-24 13:41:19: 状态:游戏结束!
2025-04-24 13:41:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:23: 状态:游戏结束!-继续开始
2025-04-24 13:41:23: 状态:游戏结束!
2025-04-24 13:41:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:27: 状态:游戏结束!-继续开始
2025-04-24 13:41:27: 状态:游戏结束!
2025-04-24 13:41:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:31: 状态:游戏结束!-继续开始
2025-04-24 13:41:31: 状态:游戏结束!
2025-04-24 13:41:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:35: 状态:游戏结束!-继续开始
2025-04-24 13:41:35: 状态:游戏结束!
2025-04-24 13:41:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:39: 状态:游戏结束!-继续开始
2025-04-24 13:41:39: 状态:游戏结束!
2025-04-24 13:41:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:43: 状态:游戏结束!-继续开始
2025-04-24 13:41:43: 状态:游戏结束!
2025-04-24 13:41:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:48: 状态:游戏结束!-继续开始
2025-04-24 13:41:48: 状态:游戏结束!
2025-04-24 13:41:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:52: 状态:游戏结束!-继续开始
2025-04-24 13:41:52: 状态:游戏结束!
2025-04-24 13:41:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:41:56: 状态:游戏结束!-继续开始
2025-04-24 13:41:56: 状态:游戏结束!
2025-04-24 13:41:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:00: 状态:游戏结束!-继续开始
2025-04-24 13:42:00: 状态:游戏结束!
2025-04-24 13:42:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:04: 状态:游戏结束!-继续开始
2025-04-24 13:42:04: 状态:游戏结束!
2025-04-24 13:42:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:08: 状态:游戏结束!-继续开始
2025-04-24 13:42:08: 状态:游戏结束!
2025-04-24 13:42:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:12: 状态:游戏结束!-继续开始
2025-04-24 13:42:12: 状态:游戏结束!
2025-04-24 13:42:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:16: 状态:游戏结束!-继续开始
2025-04-24 13:42:16: 状态:游戏结束!
2025-04-24 13:42:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:20: 状态:游戏结束!-继续开始
2025-04-24 13:42:20: 状态:游戏结束!
2025-04-24 13:42:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:24: 状态:游戏结束!-继续开始
2025-04-24 13:42:24: 状态:游戏结束!
2025-04-24 13:42:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:29: 状态:游戏结束!-继续开始
2025-04-24 13:42:29: 状态:游戏结束!
2025-04-24 13:42:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:33: 状态:游戏结束!-继续开始
2025-04-24 13:42:33: 状态:游戏结束!
2025-04-24 13:42:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:37: 状态:游戏结束!-继续开始
2025-04-24 13:42:37: 状态:游戏结束!
2025-04-24 13:42:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:41: 状态:游戏结束!-继续开始
2025-04-24 13:42:41: 状态:游戏结束!
2025-04-24 13:42:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:45: 状态:游戏结束!-继续开始
2025-04-24 13:42:45: 状态:游戏结束!
2025-04-24 13:42:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:49: 状态:游戏结束!-继续开始
2025-04-24 13:42:49: 状态:游戏结束!
2025-04-24 13:42:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:53: 状态:游戏结束!-继续开始
2025-04-24 13:42:53: 状态:游戏结束!
2025-04-24 13:42:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:42:57: 状态:游戏结束!-继续开始
2025-04-24 13:42:57: 状态:游戏结束!
2025-04-24 13:43:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:01: 状态:游戏结束!-继续开始
2025-04-24 13:43:01: 状态:游戏结束!
2025-04-24 13:43:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:06: 状态:游戏结束!-继续开始
2025-04-24 13:43:06: 状态:游戏结束!
2025-04-24 13:43:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:10: 状态:游戏结束!-继续开始
2025-04-24 13:43:10: 状态:游戏结束!
2025-04-24 13:43:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:14: 状态:游戏结束!-继续开始
2025-04-24 13:43:14: 状态:游戏结束!
2025-04-24 13:43:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:18: 状态:游戏结束!-继续开始
2025-04-24 13:43:18: 状态:游戏结束!
2025-04-24 13:43:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:22: 状态:游戏结束!-继续开始
2025-04-24 13:43:22: 状态:游戏结束!
2025-04-24 13:43:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:26: 状态:游戏结束!-继续开始
2025-04-24 13:43:26: 状态:游戏结束!
2025-04-24 13:43:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:30: 状态:游戏结束!-继续开始
2025-04-24 13:43:30: 状态:游戏结束!
2025-04-24 13:43:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:34: 状态:游戏结束!-继续开始
2025-04-24 13:43:34: 状态:游戏结束!
2025-04-24 13:43:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:38: 状态:游戏结束!-继续开始
2025-04-24 13:43:38: 状态:游戏结束!
2025-04-24 13:43:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:43: 状态:游戏结束!-继续开始
2025-04-24 13:43:43: 状态:游戏结束!
2025-04-24 13:43:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:47: 状态:游戏结束!-继续开始
2025-04-24 13:43:47: 状态:游戏结束!
2025-04-24 13:43:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:51: 状态:游戏结束!-继续开始
2025-04-24 13:43:51: 状态:游戏结束!
2025-04-24 13:43:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:55: 状态:游戏结束!-继续开始
2025-04-24 13:43:55: 状态:游戏结束!
2025-04-24 13:43:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:43:59: 状态:游戏结束!-继续开始
2025-04-24 13:43:59: 状态:游戏结束!
2025-04-24 13:44:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:03: 状态:游戏结束!-继续开始
2025-04-24 13:44:03: 状态:游戏结束!
2025-04-24 13:44:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:07: 状态:游戏结束!-继续开始
2025-04-24 13:44:07: 状态:游戏结束!
2025-04-24 13:44:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:11: 状态:游戏结束!-继续开始
2025-04-24 13:44:11: 状态:游戏结束!
2025-04-24 13:44:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:15: 状态:游戏结束!-继续开始
2025-04-24 13:44:15: 状态:游戏结束!
2025-04-24 13:44:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:20: 状态:游戏结束!-继续开始
2025-04-24 13:44:20: 状态:游戏结束!
2025-04-24 13:44:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:24: 状态:游戏结束!-继续开始
2025-04-24 13:44:24: 状态:游戏结束!
2025-04-24 13:44:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:28: 状态:游戏结束!-继续开始
2025-04-24 13:44:28: 状态:游戏结束!
2025-04-24 13:44:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:32: 状态:游戏结束!-继续开始
2025-04-24 13:44:32: 状态:游戏结束!
2025-04-24 13:44:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:36: 状态:游戏结束!-继续开始
2025-04-24 13:44:36: 状态:游戏结束!
2025-04-24 13:44:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:40: 状态:游戏结束!-继续开始
2025-04-24 13:44:40: 状态:游戏结束!
2025-04-24 13:44:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:44: 状态:游戏结束!-继续开始
2025-04-24 13:44:44: 状态:游戏结束!
2025-04-24 13:44:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:48: 状态:游戏结束!-继续开始
2025-04-24 13:44:48: 状态:游戏结束!
2025-04-24 13:44:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:53: 状态:游戏结束!-继续开始
2025-04-24 13:44:53: 状态:游戏结束!
2025-04-24 13:44:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:44:57: 状态:游戏结束!-继续开始
2025-04-24 13:44:57: 状态:游戏结束!
2025-04-24 13:45:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:01: 状态:游戏结束!-继续开始
2025-04-24 13:45:01: 状态:游戏结束!
2025-04-24 13:45:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:05: 状态:游戏结束!-继续开始
2025-04-24 13:45:05: 状态:游戏结束!
2025-04-24 13:45:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:09: 状态:游戏结束!-继续开始
2025-04-24 13:45:09: 状态:游戏结束!
2025-04-24 13:45:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:13: 状态:游戏结束!-继续开始
2025-04-24 13:45:13: 状态:游戏结束!
2025-04-24 13:45:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:17: 状态:游戏结束!-继续开始
2025-04-24 13:45:17: 状态:游戏结束!
2025-04-24 13:45:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:21: 状态:游戏结束!-继续开始
2025-04-24 13:45:21: 状态:游戏结束!
2025-04-24 13:45:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:26: 状态:游戏结束!-继续开始
2025-04-24 13:45:26: 状态:游戏结束!
2025-04-24 13:45:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:30: 状态:游戏结束!-继续开始
2025-04-24 13:45:30: 状态:游戏结束!
2025-04-24 13:45:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:34: 状态:游戏结束!-继续开始
2025-04-24 13:45:34: 状态:游戏结束!
2025-04-24 13:45:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:38: 状态:游戏结束!-继续开始
2025-04-24 13:45:38: 状态:游戏结束!
2025-04-24 13:45:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:42: 状态:游戏结束!-继续开始
2025-04-24 13:45:42: 状态:游戏结束!
2025-04-24 13:45:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:46: 状态:游戏结束!-继续开始
2025-04-24 13:45:46: 状态:游戏结束!
2025-04-24 13:45:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:50: 状态:游戏结束!-继续开始
2025-04-24 13:45:50: 状态:游戏结束!
2025-04-24 13:45:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:54: 状态:游戏结束!-继续开始
2025-04-24 13:45:54: 状态:游戏结束!
2025-04-24 13:45:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:45:58: 状态:游戏结束!-继续开始
2025-04-24 13:45:58: 状态:游戏结束!
2025-04-24 13:46:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:02: 状态:游戏结束!-继续开始
2025-04-24 13:46:03: 状态:游戏结束!
2025-04-24 13:46:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:07: 状态:游戏结束!-继续开始
2025-04-24 13:46:07: 状态:游戏结束!
2025-04-24 13:46:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:11: 状态:游戏结束!-继续开始
2025-04-24 13:46:11: 状态:游戏结束!
2025-04-24 13:46:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:15: 状态:游戏结束!-继续开始
2025-04-24 13:46:15: 状态:游戏结束!
2025-04-24 13:46:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:19: 状态:游戏结束!-继续开始
2025-04-24 13:46:19: 状态:游戏结束!
2025-04-24 13:46:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:23: 状态:游戏结束!-继续开始
2025-04-24 13:46:23: 状态:游戏结束!
2025-04-24 13:46:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:27: 状态:游戏结束!-继续开始
2025-04-24 13:46:27: 状态:游戏结束!
2025-04-24 13:46:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:31: 状态:游戏结束!-继续开始
2025-04-24 13:46:31: 状态:游戏结束!
2025-04-24 13:46:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:35: 状态:游戏结束!-继续开始
2025-04-24 13:46:35: 状态:游戏结束!
2025-04-24 13:46:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:39: 状态:游戏结束!-继续开始
2025-04-24 13:46:39: 状态:游戏结束!
2025-04-24 13:46:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:44: 状态:游戏结束!-继续开始
2025-04-24 13:46:44: 状态:游戏结束!
2025-04-24 13:46:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:48: 状态:游戏结束!-继续开始
2025-04-24 13:46:48: 状态:游戏结束!
2025-04-24 13:46:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:52: 状态:游戏结束!-继续开始
2025-04-24 13:46:52: 状态:游戏结束!
2025-04-24 13:46:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:46:56: 状态:游戏结束!-继续开始
2025-04-24 13:46:56: 状态:游戏结束!
2025-04-24 13:46:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:00: 状态:游戏结束!-继续开始
2025-04-24 13:47:00: 状态:游戏结束!
2025-04-24 13:47:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:04: 状态:游戏结束!-继续开始
2025-04-24 13:47:04: 状态:游戏结束!
2025-04-24 13:47:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:08: 状态:游戏结束!-继续开始
2025-04-24 13:47:08: 状态:游戏结束!
2025-04-24 13:47:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:12: 状态:游戏结束!-继续开始
2025-04-24 13:47:12: 状态:游戏结束!
2025-04-24 13:47:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:16: 状态:游戏结束!-继续开始
2025-04-24 13:47:16: 状态:游戏结束!
2025-04-24 13:47:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:20: 状态:游戏结束!-继续开始
2025-04-24 13:47:20: 状态:游戏结束!
2025-04-24 13:47:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:25: 状态:游戏结束!-继续开始
2025-04-24 13:47:25: 状态:游戏结束!
2025-04-24 13:47:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:29: 状态:游戏结束!-继续开始
2025-04-24 13:47:29: 状态:游戏结束!
2025-04-24 13:47:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:33: 状态:游戏结束!-继续开始
2025-04-24 13:47:33: 状态:游戏结束!
2025-04-24 13:47:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:37: 状态:游戏结束!-继续开始
2025-04-24 13:47:37: 状态:游戏结束!
2025-04-24 13:47:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:41: 状态:游戏结束!-继续开始
2025-04-24 13:47:41: 状态:游戏结束!
2025-04-24 13:47:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:45: 状态:游戏结束!-继续开始
2025-04-24 13:47:45: 状态:游戏结束!
2025-04-24 13:47:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:49: 状态:游戏结束!-继续开始
2025-04-24 13:47:49: 状态:游戏结束!
2025-04-24 13:47:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:53: 状态:游戏结束!-继续开始
2025-04-24 13:47:53: 状态:游戏结束!
2025-04-24 13:47:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:47:57: 状态:游戏结束!-继续开始
2025-04-24 13:47:57: 状态:游戏结束!
2025-04-24 13:48:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:02: 状态:游戏结束!-继续开始
2025-04-24 13:48:02: 状态:游戏结束!
2025-04-24 13:48:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:06: 状态:游戏结束!-继续开始
2025-04-24 13:48:06: 状态:游戏结束!
2025-04-24 13:48:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:10: 状态:游戏结束!-继续开始
2025-04-24 13:48:10: 状态:游戏结束!
2025-04-24 13:48:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:14: 状态:游戏结束!-继续开始
2025-04-24 13:48:14: 状态:游戏结束!
2025-04-24 13:48:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:18: 状态:游戏结束!-继续开始
2025-04-24 13:48:18: 状态:游戏结束!
2025-04-24 13:48:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:22: 状态:游戏结束!-继续开始
2025-04-24 13:48:22: 状态:游戏结束!
2025-04-24 13:48:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:26: 状态:游戏结束!-继续开始
2025-04-24 13:48:26: 状态:游戏结束!
2025-04-24 13:48:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:30: 状态:游戏结束!-继续开始
2025-04-24 13:48:30: 状态:游戏结束!
2025-04-24 13:48:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:34: 状态:游戏结束!-继续开始
2025-04-24 13:48:34: 状态:游戏结束!
2025-04-24 13:48:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:39: 状态:游戏结束!-继续开始
2025-04-24 13:48:39: 状态:游戏结束!
2025-04-24 13:48:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:43: 状态:游戏结束!-继续开始
2025-04-24 13:48:43: 状态:游戏结束!
2025-04-24 13:48:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:47: 状态:游戏结束!-继续开始
2025-04-24 13:48:47: 状态:游戏结束!
2025-04-24 13:48:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:51: 状态:游戏结束!-继续开始
2025-04-24 13:48:51: 状态:游戏结束!
2025-04-24 13:48:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:55: 状态:游戏结束!-继续开始
2025-04-24 13:48:55: 状态:游戏结束!
2025-04-24 13:48:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:48:59: 状态:游戏结束!-继续开始
2025-04-24 13:48:59: 状态:游戏结束!
2025-04-24 13:49:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:03: 状态:游戏结束!-继续开始
2025-04-24 13:49:03: 状态:游戏结束!
2025-04-24 13:49:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:07: 状态:游戏结束!-继续开始
2025-04-24 13:49:07: 状态:游戏结束!
2025-04-24 13:49:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:12: 状态:游戏结束!-继续开始
2025-04-24 13:49:12: 状态:游戏结束!
2025-04-24 13:49:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:16: 状态:游戏结束!-继续开始
2025-04-24 13:49:16: 状态:游戏结束!
2025-04-24 13:49:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:20: 状态:游戏结束!-继续开始
2025-04-24 13:49:20: 状态:游戏结束!
2025-04-24 13:49:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:24: 状态:游戏结束!-继续开始
2025-04-24 13:49:24: 状态:游戏结束!
2025-04-24 13:49:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:28: 状态:游戏结束!-继续开始
2025-04-24 13:49:28: 状态:游戏结束!
2025-04-24 13:49:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:32: 状态:游戏结束!-继续开始
2025-04-24 13:49:32: 状态:游戏结束!
2025-04-24 13:49:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:36: 状态:游戏结束!-继续开始
2025-04-24 13:49:36: 状态:游戏结束!
2025-04-24 13:49:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:40: 状态:游戏结束!-继续开始
2025-04-24 13:49:40: 状态:游戏结束!
2025-04-24 13:49:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:44: 状态:游戏结束!-继续开始
2025-04-24 13:49:44: 状态:游戏结束!
2025-04-24 13:49:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:48: 状态:游戏结束!-继续开始
2025-04-24 13:49:48: 状态:游戏结束!
2025-04-24 13:49:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:53: 状态:游戏结束!-继续开始
2025-04-24 13:49:53: 状态:游戏结束!
2025-04-24 13:49:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:49:57: 状态:游戏结束!-继续开始
2025-04-24 13:49:57: 状态:游戏结束!
2025-04-24 13:50:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:01: 状态:游戏结束!-继续开始
2025-04-24 13:50:01: 状态:游戏结束!
2025-04-24 13:50:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:05: 状态:游戏结束!-继续开始
2025-04-24 13:50:05: 状态:游戏结束!
2025-04-24 13:50:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:09: 状态:游戏结束!-继续开始
2025-04-24 13:50:09: 状态:游戏结束!
2025-04-24 13:50:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:13: 状态:游戏结束!-继续开始
2025-04-24 13:50:13: 状态:游戏结束!
2025-04-24 13:50:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:17: 状态:游戏结束!-继续开始
2025-04-24 13:50:17: 状态:游戏结束!
2025-04-24 13:50:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:21: 状态:游戏结束!-继续开始
2025-04-24 13:50:21: 状态:游戏结束!
2025-04-24 13:50:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:25: 状态:游戏结束!-继续开始
2025-04-24 13:50:25: 状态:游戏结束!
2025-04-24 13:50:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:30: 状态:游戏结束!-继续开始
2025-04-24 13:50:30: 状态:游戏结束!
2025-04-24 13:50:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:34: 状态:游戏结束!-继续开始
2025-04-24 13:50:34: 状态:游戏结束!
2025-04-24 13:50:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:38: 状态:游戏结束!-继续开始
2025-04-24 13:50:38: 状态:游戏结束!
2025-04-24 13:50:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:42: 状态:游戏结束!-继续开始
2025-04-24 13:50:42: 状态:游戏结束!
2025-04-24 13:50:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:46: 状态:游戏结束!-继续开始
2025-04-24 13:50:46: 状态:游戏结束!
2025-04-24 13:50:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:50: 状态:游戏结束!-继续开始
2025-04-24 13:50:50: 状态:游戏结束!
2025-04-24 13:50:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:54: 状态:游戏结束!-继续开始
2025-04-24 13:50:54: 状态:游戏结束!
2025-04-24 13:50:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:50:58: 状态:游戏结束!-继续开始
2025-04-24 13:50:58: 状态:游戏结束!
2025-04-24 13:51:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:02: 状态:游戏结束!-继续开始
2025-04-24 13:51:02: 状态:游戏结束!
2025-04-24 13:51:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:07: 状态:游戏结束!-继续开始
2025-04-24 13:51:07: 状态:游戏结束!
2025-04-24 13:51:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:11: 状态:游戏结束!-继续开始
2025-04-24 13:51:11: 状态:游戏结束!
2025-04-24 13:51:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:15: 状态:游戏结束!-继续开始
2025-04-24 13:51:15: 状态:游戏结束!
2025-04-24 13:51:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:19: 状态:游戏结束!-继续开始
2025-04-24 13:51:19: 状态:游戏结束!
2025-04-24 13:51:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:23: 状态:游戏结束!-继续开始
2025-04-24 13:51:23: 状态:游戏结束!
2025-04-24 13:51:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:27: 状态:游戏结束!-继续开始
2025-04-24 13:51:27: 状态:游戏结束!
2025-04-24 13:51:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:31: 状态:游戏结束!-继续开始
2025-04-24 13:51:31: 状态:游戏结束!
2025-04-24 13:51:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:35: 状态:游戏结束!-继续开始
2025-04-24 13:51:35: 状态:游戏结束!
2025-04-24 13:51:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:39: 状态:游戏结束!-继续开始
2025-04-24 13:51:39: 状态:游戏结束!
2025-04-24 13:51:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:44: 状态:游戏结束!-继续开始
2025-04-24 13:51:44: 状态:游戏结束!
2025-04-24 13:51:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 13:51:48: 状态:游戏结束!-继续开始
2025-04-24 13:51:48: 状态:游戏结束!
2025-04-24 13:51:51: 无法获取游戏会话信息，跳过点赞
2025-04-24 13:51:52: 状态:游戏结束!-继续开始
2025-04-24 13:51:52: 状态:游戏结束!
2025-04-24 13:51:52: 大厅等待中
2025-04-24 13:51:52: 检测到房间!
2025-04-24 13:51:56: 窗口隐藏1获取英雄ID:0
2025-04-24 13:51:58: 进入选择英雄界面！
2025-04-24 13:52:46: 获取英雄ID:92-上次选英雄ID:0
2025-04-24 13:52:46: 正在为Riven选英雄
2025-04-24 13:52:46: 进入选择英雄界面！
2025-04-24 13:53:00: 窗口隐藏2
2025-04-24 13:53:02: 对局正在进行中!
2025-04-24 13:53:02: 窗口隐藏95
2025-04-24 13:53:04: 对局正在进行中!
2025-04-24 14:14:50: 等待游戏结算界面!
2025-04-24 14:14:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:14:54: 状态:游戏结束!-继续开始
2025-04-24 14:14:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:14:58: 状态:游戏结束!-继续开始
2025-04-24 14:14:58: 状态:游戏结束!
2025-04-24 14:15:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:02: 状态:游戏结束!-继续开始
2025-04-24 14:15:02: 状态:游戏结束!
2025-04-24 14:15:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:06: 状态:游戏结束!-继续开始
2025-04-24 14:15:06: 状态:游戏结束!
2025-04-24 14:15:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:10: 状态:游戏结束!-继续开始
2025-04-24 14:15:10: 状态:游戏结束!
2025-04-24 14:15:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:15: 状态:游戏结束!-继续开始
2025-04-24 14:15:15: 状态:游戏结束!
2025-04-24 14:15:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:19: 状态:游戏结束!-继续开始
2025-04-24 14:15:19: 状态:游戏结束!
2025-04-24 14:15:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:23: 状态:游戏结束!-继续开始
2025-04-24 14:15:23: 状态:游戏结束!
2025-04-24 14:15:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:27: 状态:游戏结束!-继续开始
2025-04-24 14:15:27: 状态:游戏结束!
2025-04-24 14:15:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:31: 状态:游戏结束!-继续开始
2025-04-24 14:15:31: 状态:游戏结束!
2025-04-24 14:15:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:35: 状态:游戏结束!-继续开始
2025-04-24 14:15:35: 状态:游戏结束!
2025-04-24 14:15:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:39: 状态:游戏结束!-继续开始
2025-04-24 14:15:39: 状态:游戏结束!
2025-04-24 14:15:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:43: 状态:游戏结束!-继续开始
2025-04-24 14:15:43: 状态:游戏结束!
2025-04-24 14:15:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:48: 状态:游戏结束!-继续开始
2025-04-24 14:15:48: 状态:游戏结束!
2025-04-24 14:15:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:52: 状态:游戏结束!-继续开始
2025-04-24 14:15:52: 状态:游戏结束!
2025-04-24 14:15:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:15:56: 状态:游戏结束!-继续开始
2025-04-24 14:15:56: 状态:游戏结束!
2025-04-24 14:15:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:00: 状态:游戏结束!-继续开始
2025-04-24 14:16:00: 状态:游戏结束!
2025-04-24 14:16:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:04: 状态:游戏结束!-继续开始
2025-04-24 14:16:04: 状态:游戏结束!
2025-04-24 14:16:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:08: 状态:游戏结束!-继续开始
2025-04-24 14:16:08: 状态:游戏结束!
2025-04-24 14:16:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:12: 状态:游戏结束!-继续开始
2025-04-24 14:16:12: 状态:游戏结束!
2025-04-24 14:16:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:16: 状态:游戏结束!-继续开始
2025-04-24 14:16:16: 状态:游戏结束!
2025-04-24 14:16:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:21: 状态:游戏结束!-继续开始
2025-04-24 14:16:21: 状态:游戏结束!
2025-04-24 14:16:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:25: 状态:游戏结束!-继续开始
2025-04-24 14:16:25: 状态:游戏结束!
2025-04-24 14:16:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:29: 状态:游戏结束!-继续开始
2025-04-24 14:16:29: 状态:游戏结束!
2025-04-24 14:16:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:33: 状态:游戏结束!-继续开始
2025-04-24 14:16:33: 状态:游戏结束!
2025-04-24 14:16:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:37: 状态:游戏结束!-继续开始
2025-04-24 14:16:37: 状态:游戏结束!
2025-04-24 14:16:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:41: 状态:游戏结束!-继续开始
2025-04-24 14:16:41: 状态:游戏结束!
2025-04-24 14:16:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:45: 状态:游戏结束!-继续开始
2025-04-24 14:16:45: 状态:游戏结束!
2025-04-24 14:16:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:49: 状态:游戏结束!-继续开始
2025-04-24 14:16:49: 状态:游戏结束!
2025-04-24 14:16:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:54: 状态:游戏结束!-继续开始
2025-04-24 14:16:54: 状态:游戏结束!
2025-04-24 14:16:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:16:58: 状态:游戏结束!-继续开始
2025-04-24 14:16:58: 状态:游戏结束!
2025-04-24 14:17:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:02: 状态:游戏结束!-继续开始
2025-04-24 14:17:02: 状态:游戏结束!
2025-04-24 14:17:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:06: 状态:游戏结束!-继续开始
2025-04-24 14:17:06: 状态:游戏结束!
2025-04-24 14:17:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:10: 状态:游戏结束!-继续开始
2025-04-24 14:17:10: 状态:游戏结束!
2025-04-24 14:17:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:14: 状态:游戏结束!-继续开始
2025-04-24 14:17:14: 状态:游戏结束!
2025-04-24 14:17:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:18: 状态:游戏结束!-继续开始
2025-04-24 14:17:18: 状态:游戏结束!
2025-04-24 14:17:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:22: 状态:游戏结束!-继续开始
2025-04-24 14:17:22: 状态:游戏结束!
2025-04-24 14:17:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:27: 状态:游戏结束!-继续开始
2025-04-24 14:17:27: 状态:游戏结束!
2025-04-24 14:17:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:31: 状态:游戏结束!-继续开始
2025-04-24 14:17:31: 状态:游戏结束!
2025-04-24 14:17:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:35: 状态:游戏结束!-继续开始
2025-04-24 14:17:35: 状态:游戏结束!
2025-04-24 14:17:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:39: 状态:游戏结束!-继续开始
2025-04-24 14:17:39: 状态:游戏结束!
2025-04-24 14:17:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:43: 状态:游戏结束!-继续开始
2025-04-24 14:17:43: 状态:游戏结束!
2025-04-24 14:17:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:47: 状态:游戏结束!-继续开始
2025-04-24 14:17:47: 状态:游戏结束!
2025-04-24 14:17:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:51: 状态:游戏结束!-继续开始
2025-04-24 14:17:51: 状态:游戏结束!
2025-04-24 14:17:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:17:55: 状态:游戏结束!-继续开始
2025-04-24 14:17:55: 状态:游戏结束!
2025-04-24 14:17:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:00: 状态:游戏结束!-继续开始
2025-04-24 14:18:00: 状态:游戏结束!
2025-04-24 14:18:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:04: 状态:游戏结束!-继续开始
2025-04-24 14:18:04: 状态:游戏结束!
2025-04-24 14:18:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:08: 状态:游戏结束!-继续开始
2025-04-24 14:18:08: 状态:游戏结束!
2025-04-24 14:18:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:12: 状态:游戏结束!-继续开始
2025-04-24 14:18:12: 状态:游戏结束!
2025-04-24 14:18:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:16: 状态:游戏结束!-继续开始
2025-04-24 14:18:16: 状态:游戏结束!
2025-04-24 14:18:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:20: 状态:游戏结束!-继续开始
2025-04-24 14:18:20: 状态:游戏结束!
2025-04-24 14:18:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:24: 状态:游戏结束!-继续开始
2025-04-24 14:18:24: 状态:游戏结束!
2025-04-24 14:18:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:28: 状态:游戏结束!-继续开始
2025-04-24 14:18:28: 状态:游戏结束!
2025-04-24 14:18:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:33: 状态:游戏结束!-继续开始
2025-04-24 14:18:33: 状态:游戏结束!
2025-04-24 14:18:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:37: 状态:游戏结束!-继续开始
2025-04-24 14:18:37: 状态:游戏结束!
2025-04-24 14:18:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:41: 状态:游戏结束!-继续开始
2025-04-24 14:18:41: 状态:游戏结束!
2025-04-24 14:18:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:45: 状态:游戏结束!-继续开始
2025-04-24 14:18:45: 状态:游戏结束!
2025-04-24 14:18:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:49: 状态:游戏结束!-继续开始
2025-04-24 14:18:49: 状态:游戏结束!
2025-04-24 14:18:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:53: 状态:游戏结束!-继续开始
2025-04-24 14:18:53: 状态:游戏结束!
2025-04-24 14:18:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:18:57: 状态:游戏结束!-继续开始
2025-04-24 14:18:57: 状态:游戏结束!
2025-04-24 14:19:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:01: 状态:游戏结束!-继续开始
2025-04-24 14:19:01: 状态:游戏结束!
2025-04-24 14:19:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:06: 状态:游戏结束!-继续开始
2025-04-24 14:19:06: 状态:游戏结束!
2025-04-24 14:19:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:10: 状态:游戏结束!-继续开始
2025-04-24 14:19:10: 状态:游戏结束!
2025-04-24 14:19:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:14: 状态:游戏结束!-继续开始
2025-04-24 14:19:14: 状态:游戏结束!
2025-04-24 14:19:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:18: 状态:游戏结束!-继续开始
2025-04-24 14:19:18: 状态:游戏结束!
2025-04-24 14:19:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:22: 状态:游戏结束!-继续开始
2025-04-24 14:19:22: 状态:游戏结束!
2025-04-24 14:19:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:26: 状态:游戏结束!-继续开始
2025-04-24 14:19:26: 状态:游戏结束!
2025-04-24 14:19:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:30: 状态:游戏结束!-继续开始
2025-04-24 14:19:30: 状态:游戏结束!
2025-04-24 14:19:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:34: 状态:游戏结束!-继续开始
2025-04-24 14:19:34: 状态:游戏结束!
2025-04-24 14:19:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:39: 状态:游戏结束!-继续开始
2025-04-24 14:19:39: 状态:游戏结束!
2025-04-24 14:19:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:43: 状态:游戏结束!-继续开始
2025-04-24 14:19:43: 状态:游戏结束!
2025-04-24 14:19:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:47: 状态:游戏结束!-继续开始
2025-04-24 14:19:47: 状态:游戏结束!
2025-04-24 14:19:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:51: 状态:游戏结束!-继续开始
2025-04-24 14:19:51: 状态:游戏结束!
2025-04-24 14:19:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:55: 状态:游戏结束!-继续开始
2025-04-24 14:19:55: 状态:游戏结束!
2025-04-24 14:19:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:19:59: 状态:游戏结束!-继续开始
2025-04-24 14:19:59: 状态:游戏结束!
2025-04-24 14:20:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:03: 状态:游戏结束!-继续开始
2025-04-24 14:20:03: 状态:游戏结束!
2025-04-24 14:20:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:08: 状态:游戏结束!-继续开始
2025-04-24 14:20:08: 状态:游戏结束!
2025-04-24 14:20:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:12: 状态:游戏结束!-继续开始
2025-04-24 14:20:12: 状态:游戏结束!
2025-04-24 14:20:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:16: 状态:游戏结束!-继续开始
2025-04-24 14:20:16: 状态:游戏结束!
2025-04-24 14:20:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:20: 状态:游戏结束!-继续开始
2025-04-24 14:20:20: 状态:游戏结束!
2025-04-24 14:20:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:24: 状态:游戏结束!-继续开始
2025-04-24 14:20:24: 状态:游戏结束!
2025-04-24 14:20:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:28: 状态:游戏结束!-继续开始
2025-04-24 14:20:28: 状态:游戏结束!
2025-04-24 14:20:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:32: 状态:游戏结束!-继续开始
2025-04-24 14:20:32: 状态:游戏结束!
2025-04-24 14:20:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:36: 状态:游戏结束!-继续开始
2025-04-24 14:20:36: 状态:游戏结束!
2025-04-24 14:20:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:41: 状态:游戏结束!-继续开始
2025-04-24 14:20:41: 状态:游戏结束!
2025-04-24 14:20:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:47: 状态:游戏结束!-继续开始
2025-04-24 14:20:47: 状态:游戏结束!
2025-04-24 14:20:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:51: 状态:游戏结束!-继续开始
2025-04-24 14:20:51: 状态:游戏结束!
2025-04-24 14:20:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:20:55: 状态:游戏结束!-继续开始
2025-04-24 14:20:55: 状态:游戏结束!
2025-04-24 14:20:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:00: 状态:游戏结束!-继续开始
2025-04-24 14:21:00: 状态:游戏结束!
2025-04-24 14:21:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:04: 状态:游戏结束!-继续开始
2025-04-24 14:21:04: 状态:游戏结束!
2025-04-24 14:21:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:08: 状态:游戏结束!-继续开始
2025-04-24 14:21:08: 状态:游戏结束!
2025-04-24 14:21:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:12: 状态:游戏结束!-继续开始
2025-04-24 14:21:12: 状态:游戏结束!
2025-04-24 14:21:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:16: 状态:游戏结束!-继续开始
2025-04-24 14:21:16: 状态:游戏结束!
2025-04-24 14:21:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:20: 状态:游戏结束!-继续开始
2025-04-24 14:21:20: 状态:游戏结束!
2025-04-24 14:21:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:24: 状态:游戏结束!-继续开始
2025-04-24 14:21:24: 状态:游戏结束!
2025-04-24 14:21:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:28: 状态:游戏结束!-继续开始
2025-04-24 14:21:28: 状态:游戏结束!
2025-04-24 14:21:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:32: 状态:游戏结束!-继续开始
2025-04-24 14:21:32: 状态:游戏结束!
2025-04-24 14:21:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:37: 状态:游戏结束!-继续开始
2025-04-24 14:21:37: 状态:游戏结束!
2025-04-24 14:21:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:41: 状态:游戏结束!-继续开始
2025-04-24 14:21:41: 状态:游戏结束!
2025-04-24 14:21:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:45: 状态:游戏结束!-继续开始
2025-04-24 14:21:45: 状态:游戏结束!
2025-04-24 14:21:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:49: 状态:游戏结束!-继续开始
2025-04-24 14:21:49: 状态:游戏结束!
2025-04-24 14:21:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:54: 状态:游戏结束!-继续开始
2025-04-24 14:21:54: 状态:游戏结束!
2025-04-24 14:21:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:21:58: 状态:游戏结束!-继续开始
2025-04-24 14:21:58: 状态:游戏结束!
2025-04-24 14:22:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:02: 状态:游戏结束!-继续开始
2025-04-24 14:22:02: 状态:游戏结束!
2025-04-24 14:22:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:06: 状态:游戏结束!-继续开始
2025-04-24 14:22:06: 状态:游戏结束!
2025-04-24 14:22:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:10: 状态:游戏结束!-继续开始
2025-04-24 14:22:10: 状态:游戏结束!
2025-04-24 14:22:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:14: 状态:游戏结束!-继续开始
2025-04-24 14:22:14: 状态:游戏结束!
2025-04-24 14:22:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:18: 状态:游戏结束!-继续开始
2025-04-24 14:22:18: 状态:游戏结束!
2025-04-24 14:22:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:22: 状态:游戏结束!-继续开始
2025-04-24 14:22:22: 状态:游戏结束!
2025-04-24 14:22:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:26: 状态:游戏结束!-继续开始
2025-04-24 14:22:26: 状态:游戏结束!
2025-04-24 14:22:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:31: 状态:游戏结束!-继续开始
2025-04-24 14:22:31: 状态:游戏结束!
2025-04-24 14:22:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:35: 状态:游戏结束!-继续开始
2025-04-24 14:22:35: 状态:游戏结束!
2025-04-24 14:22:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:39: 状态:游戏结束!-继续开始
2025-04-24 14:22:39: 状态:游戏结束!
2025-04-24 14:22:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:43: 状态:游戏结束!-继续开始
2025-04-24 14:22:43: 状态:游戏结束!
2025-04-24 14:22:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:47: 状态:游戏结束!-继续开始
2025-04-24 14:22:47: 状态:游戏结束!
2025-04-24 14:22:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:51: 状态:游戏结束!-继续开始
2025-04-24 14:22:51: 状态:游戏结束!
2025-04-24 14:22:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:55: 状态:游戏结束!-继续开始
2025-04-24 14:22:55: 状态:游戏结束!
2025-04-24 14:22:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:22:59: 状态:游戏结束!-继续开始
2025-04-24 14:22:59: 状态:游戏结束!
2025-04-24 14:23:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:03: 状态:游戏结束!-继续开始
2025-04-24 14:23:03: 状态:游戏结束!
2025-04-24 14:23:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:08: 状态:游戏结束!-继续开始
2025-04-24 14:23:08: 状态:游戏结束!
2025-04-24 14:23:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:12: 状态:游戏结束!-继续开始
2025-04-24 14:23:12: 状态:游戏结束!
2025-04-24 14:23:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:16: 状态:游戏结束!-继续开始
2025-04-24 14:23:16: 状态:游戏结束!
2025-04-24 14:23:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:20: 状态:游戏结束!-继续开始
2025-04-24 14:23:20: 状态:游戏结束!
2025-04-24 14:23:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:24: 状态:游戏结束!-继续开始
2025-04-24 14:23:24: 状态:游戏结束!
2025-04-24 14:23:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:28: 状态:游戏结束!-继续开始
2025-04-24 14:23:28: 状态:游戏结束!
2025-04-24 14:23:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:32: 状态:游戏结束!-继续开始
2025-04-24 14:23:32: 状态:游戏结束!
2025-04-24 14:23:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:36: 状态:游戏结束!-继续开始
2025-04-24 14:23:36: 状态:游戏结束!
2025-04-24 14:23:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:40: 状态:游戏结束!-继续开始
2025-04-24 14:23:40: 状态:游戏结束!
2025-04-24 14:23:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:44: 状态:游戏结束!-继续开始
2025-04-24 14:23:44: 状态:游戏结束!
2025-04-24 14:23:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:49: 状态:游戏结束!-继续开始
2025-04-24 14:23:49: 状态:游戏结束!
2025-04-24 14:23:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:53: 状态:游戏结束!-继续开始
2025-04-24 14:23:53: 状态:游戏结束!
2025-04-24 14:23:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:23:57: 状态:游戏结束!-继续开始
2025-04-24 14:23:57: 状态:游戏结束!
2025-04-24 14:24:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:01: 状态:游戏结束!-继续开始
2025-04-24 14:24:01: 状态:游戏结束!
2025-04-24 14:24:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:05: 状态:游戏结束!-继续开始
2025-04-24 14:24:05: 状态:游戏结束!
2025-04-24 14:24:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:09: 状态:游戏结束!-继续开始
2025-04-24 14:24:09: 状态:游戏结束!
2025-04-24 14:24:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:13: 状态:游戏结束!-继续开始
2025-04-24 14:24:13: 状态:游戏结束!
2025-04-24 14:24:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:17: 状态:游戏结束!-继续开始
2025-04-24 14:24:17: 状态:游戏结束!
2025-04-24 14:24:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:21: 状态:游戏结束!-继续开始
2025-04-24 14:24:21: 状态:游戏结束!
2025-04-24 14:24:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:26: 状态:游戏结束!-继续开始
2025-04-24 14:24:26: 状态:游戏结束!
2025-04-24 14:24:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:30: 状态:游戏结束!-继续开始
2025-04-24 14:24:30: 状态:游戏结束!
2025-04-24 14:24:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:34: 状态:游戏结束!-继续开始
2025-04-24 14:24:34: 状态:游戏结束!
2025-04-24 14:24:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:38: 状态:游戏结束!-继续开始
2025-04-24 14:24:38: 状态:游戏结束!
2025-04-24 14:24:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:42: 状态:游戏结束!-继续开始
2025-04-24 14:24:42: 状态:游戏结束!
2025-04-24 14:24:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:46: 状态:游戏结束!-继续开始
2025-04-24 14:24:46: 状态:游戏结束!
2025-04-24 14:24:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:50: 状态:游戏结束!-继续开始
2025-04-24 14:24:50: 状态:游戏结束!
2025-04-24 14:24:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:54: 状态:游戏结束!-继续开始
2025-04-24 14:24:54: 状态:游戏结束!
2025-04-24 14:24:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:24:59: 状态:游戏结束!-继续开始
2025-04-24 14:24:59: 状态:游戏结束!
2025-04-24 14:25:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:03: 状态:游戏结束!-继续开始
2025-04-24 14:25:03: 状态:游戏结束!
2025-04-24 14:25:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:07: 状态:游戏结束!-继续开始
2025-04-24 14:25:07: 状态:游戏结束!
2025-04-24 14:25:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:11: 状态:游戏结束!-继续开始
2025-04-24 14:25:11: 状态:游戏结束!
2025-04-24 14:25:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:15: 状态:游戏结束!-继续开始
2025-04-24 14:25:15: 状态:游戏结束!
2025-04-24 14:25:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:19: 状态:游戏结束!-继续开始
2025-04-24 14:25:19: 状态:游戏结束!
2025-04-24 14:25:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:23: 状态:游戏结束!-继续开始
2025-04-24 14:25:23: 状态:游戏结束!
2025-04-24 14:25:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:28: 状态:游戏结束!-继续开始
2025-04-24 14:25:28: 状态:游戏结束!
2025-04-24 14:25:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:32: 状态:游戏结束!-继续开始
2025-04-24 14:25:32: 状态:游戏结束!
2025-04-24 14:25:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:36: 状态:游戏结束!-继续开始
2025-04-24 14:25:36: 状态:游戏结束!
2025-04-24 14:25:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:40: 状态:游戏结束!-继续开始
2025-04-24 14:25:40: 状态:游戏结束!
2025-04-24 14:25:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:44: 状态:游戏结束!-继续开始
2025-04-24 14:25:44: 状态:游戏结束!
2025-04-24 14:25:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:48: 状态:游戏结束!-继续开始
2025-04-24 14:25:48: 状态:游戏结束!
2025-04-24 14:25:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:52: 状态:游戏结束!-继续开始
2025-04-24 14:25:52: 状态:游戏结束!
2025-04-24 14:25:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:25:57: 状态:游戏结束!-继续开始
2025-04-24 14:25:57: 状态:游戏结束!
2025-04-24 14:26:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:01: 状态:游戏结束!-继续开始
2025-04-24 14:26:01: 状态:游戏结束!
2025-04-24 14:26:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:05: 状态:游戏结束!-继续开始
2025-04-24 14:26:05: 状态:游戏结束!
2025-04-24 14:26:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:09: 状态:游戏结束!-继续开始
2025-04-24 14:26:09: 状态:游戏结束!
2025-04-24 14:26:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:13: 状态:游戏结束!-继续开始
2025-04-24 14:26:13: 状态:游戏结束!
2025-04-24 14:26:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:17: 状态:游戏结束!-继续开始
2025-04-24 14:26:17: 状态:游戏结束!
2025-04-24 14:26:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:21: 状态:游戏结束!-继续开始
2025-04-24 14:26:21: 状态:游戏结束!
2025-04-24 14:26:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:25: 状态:游戏结束!-继续开始
2025-04-24 14:26:25: 状态:游戏结束!
2025-04-24 14:26:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:30: 状态:游戏结束!-继续开始
2025-04-24 14:26:30: 状态:游戏结束!
2025-04-24 14:26:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:34: 状态:游戏结束!-继续开始
2025-04-24 14:26:34: 状态:游戏结束!
2025-04-24 14:26:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:38: 状态:游戏结束!-继续开始
2025-04-24 14:26:38: 状态:游戏结束!
2025-04-24 14:26:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:42: 状态:游戏结束!-继续开始
2025-04-24 14:26:42: 状态:游戏结束!
2025-04-24 14:26:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:46: 状态:游戏结束!-继续开始
2025-04-24 14:26:46: 状态:游戏结束!
2025-04-24 14:26:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:50: 状态:游戏结束!-继续开始
2025-04-24 14:26:50: 状态:游戏结束!
2025-04-24 14:26:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:54: 状态:游戏结束!-继续开始
2025-04-24 14:26:54: 状态:游戏结束!
2025-04-24 14:26:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:26:58: 状态:游戏结束!-继续开始
2025-04-24 14:26:58: 状态:游戏结束!
2025-04-24 14:27:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:03: 状态:游戏结束!-继续开始
2025-04-24 14:27:03: 状态:游戏结束!
2025-04-24 14:27:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:07: 状态:游戏结束!-继续开始
2025-04-24 14:27:07: 状态:游戏结束!
2025-04-24 14:27:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:11: 状态:游戏结束!-继续开始
2025-04-24 14:27:11: 状态:游戏结束!
2025-04-24 14:27:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:15: 状态:游戏结束!-继续开始
2025-04-24 14:27:15: 状态:游戏结束!
2025-04-24 14:27:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:19: 状态:游戏结束!-继续开始
2025-04-24 14:27:19: 状态:游戏结束!
2025-04-24 14:27:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:23: 状态:游戏结束!-继续开始
2025-04-24 14:27:23: 状态:游戏结束!
2025-04-24 14:27:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:27: 状态:游戏结束!-继续开始
2025-04-24 14:27:27: 状态:游戏结束!
2025-04-24 14:27:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:31: 状态:游戏结束!-继续开始
2025-04-24 14:27:31: 状态:游戏结束!
2025-04-24 14:27:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:36: 状态:游戏结束!-继续开始
2025-04-24 14:27:36: 状态:游戏结束!
2025-04-24 14:27:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:40: 状态:游戏结束!-继续开始
2025-04-24 14:27:40: 状态:游戏结束!
2025-04-24 14:27:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:44: 状态:游戏结束!-继续开始
2025-04-24 14:27:44: 状态:游戏结束!
2025-04-24 14:27:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:48: 状态:游戏结束!-继续开始
2025-04-24 14:27:48: 状态:游戏结束!
2025-04-24 14:27:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:52: 状态:游戏结束!-继续开始
2025-04-24 14:27:52: 状态:游戏结束!
2025-04-24 14:27:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:27:56: 状态:游戏结束!-继续开始
2025-04-24 14:27:56: 状态:游戏结束!
2025-04-24 14:27:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:00: 状态:游戏结束!-继续开始
2025-04-24 14:28:00: 状态:游戏结束!
2025-04-24 14:28:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:04: 状态:游戏结束!-继续开始
2025-04-24 14:28:04: 状态:游戏结束!
2025-04-24 14:28:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:09: 状态:游戏结束!-继续开始
2025-04-24 14:28:09: 状态:游戏结束!
2025-04-24 14:28:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:13: 状态:游戏结束!-继续开始
2025-04-24 14:28:13: 状态:游戏结束!
2025-04-24 14:28:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:17: 状态:游戏结束!-继续开始
2025-04-24 14:28:17: 状态:游戏结束!
2025-04-24 14:28:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:21: 状态:游戏结束!-继续开始
2025-04-24 14:28:21: 状态:游戏结束!
2025-04-24 14:28:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:25: 状态:游戏结束!-继续开始
2025-04-24 14:28:25: 状态:游戏结束!
2025-04-24 14:28:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:29: 状态:游戏结束!-继续开始
2025-04-24 14:28:29: 状态:游戏结束!
2025-04-24 14:28:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:33: 状态:游戏结束!-继续开始
2025-04-24 14:28:33: 状态:游戏结束!
2025-04-24 14:28:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:37: 状态:游戏结束!-继续开始
2025-04-24 14:28:37: 状态:游戏结束!
2025-04-24 14:28:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:42: 状态:游戏结束!-继续开始
2025-04-24 14:28:42: 状态:游戏结束!
2025-04-24 14:28:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:46: 状态:游戏结束!-继续开始
2025-04-24 14:28:46: 状态:游戏结束!
2025-04-24 14:28:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:50: 状态:游戏结束!-继续开始
2025-04-24 14:28:50: 状态:游戏结束!
2025-04-24 14:28:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:54: 状态:游戏结束!-继续开始
2025-04-24 14:28:54: 状态:游戏结束!
2025-04-24 14:28:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:28:58: 状态:游戏结束!-继续开始
2025-04-24 14:28:58: 状态:游戏结束!
2025-04-24 14:29:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:02: 状态:游戏结束!-继续开始
2025-04-24 14:29:02: 状态:游戏结束!
2025-04-24 14:29:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:06: 状态:游戏结束!-继续开始
2025-04-24 14:29:06: 状态:游戏结束!
2025-04-24 14:29:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:10: 状态:游戏结束!-继续开始
2025-04-24 14:29:10: 状态:游戏结束!
2025-04-24 14:29:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:15: 状态:游戏结束!-继续开始
2025-04-24 14:29:15: 状态:游戏结束!
2025-04-24 14:29:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:19: 状态:游戏结束!-继续开始
2025-04-24 14:29:19: 状态:游戏结束!
2025-04-24 14:29:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:23: 状态:游戏结束!-继续开始
2025-04-24 14:29:23: 状态:游戏结束!
2025-04-24 14:29:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:27: 状态:游戏结束!-继续开始
2025-04-24 14:29:27: 状态:游戏结束!
2025-04-24 14:29:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:31: 状态:游戏结束!-继续开始
2025-04-24 14:29:31: 状态:游戏结束!
2025-04-24 14:29:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:35: 状态:游戏结束!-继续开始
2025-04-24 14:29:35: 状态:游戏结束!
2025-04-24 14:29:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:39: 状态:游戏结束!-继续开始
2025-04-24 14:29:39: 状态:游戏结束!
2025-04-24 14:29:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:43: 状态:游戏结束!-继续开始
2025-04-24 14:29:43: 状态:游戏结束!
2025-04-24 14:29:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:48: 状态:游戏结束!-继续开始
2025-04-24 14:29:48: 状态:游戏结束!
2025-04-24 14:29:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:52: 状态:游戏结束!-继续开始
2025-04-24 14:29:52: 状态:游戏结束!
2025-04-24 14:29:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:29:56: 状态:游戏结束!-继续开始
2025-04-24 14:29:56: 状态:游戏结束!
2025-04-24 14:29:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:00: 状态:游戏结束!-继续开始
2025-04-24 14:30:00: 状态:游戏结束!
2025-04-24 14:30:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:04: 状态:游戏结束!-继续开始
2025-04-24 14:30:04: 状态:游戏结束!
2025-04-24 14:30:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:08: 状态:游戏结束!-继续开始
2025-04-24 14:30:08: 状态:游戏结束!
2025-04-24 14:30:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:12: 状态:游戏结束!-继续开始
2025-04-24 14:30:12: 状态:游戏结束!
2025-04-24 14:30:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:16: 状态:游戏结束!-继续开始
2025-04-24 14:30:16: 状态:游戏结束!
2025-04-24 14:30:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:21: 状态:游戏结束!-继续开始
2025-04-24 14:30:21: 状态:游戏结束!
2025-04-24 14:30:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:25: 状态:游戏结束!-继续开始
2025-04-24 14:30:25: 状态:游戏结束!
2025-04-24 14:30:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:29: 状态:游戏结束!-继续开始
2025-04-24 14:30:29: 状态:游戏结束!
2025-04-24 14:30:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:33: 状态:游戏结束!-继续开始
2025-04-24 14:30:33: 状态:游戏结束!
2025-04-24 14:30:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:37: 状态:游戏结束!-继续开始
2025-04-24 14:30:37: 状态:游戏结束!
2025-04-24 14:30:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:41: 状态:游戏结束!-继续开始
2025-04-24 14:30:41: 状态:游戏结束!
2025-04-24 14:30:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:45: 状态:游戏结束!-继续开始
2025-04-24 14:30:45: 状态:游戏结束!
2025-04-24 14:30:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:49: 状态:游戏结束!-继续开始
2025-04-24 14:30:49: 状态:游戏结束!
2025-04-24 14:30:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:53: 状态:游戏结束!-继续开始
2025-04-24 14:30:53: 状态:游戏结束!
2025-04-24 14:30:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:30:58: 状态:游戏结束!-继续开始
2025-04-24 14:30:58: 状态:游戏结束!
2025-04-24 14:31:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:02: 状态:游戏结束!-继续开始
2025-04-24 14:31:02: 状态:游戏结束!
2025-04-24 14:31:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:06: 状态:游戏结束!-继续开始
2025-04-24 14:31:06: 状态:游戏结束!
2025-04-24 14:31:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:10: 状态:游戏结束!-继续开始
2025-04-24 14:31:10: 状态:游戏结束!
2025-04-24 14:31:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:14: 状态:游戏结束!-继续开始
2025-04-24 14:31:14: 状态:游戏结束!
2025-04-24 14:31:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:18: 状态:游戏结束!-继续开始
2025-04-24 14:31:18: 状态:游戏结束!
2025-04-24 14:31:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:22: 状态:游戏结束!-继续开始
2025-04-24 14:31:22: 状态:游戏结束!
2025-04-24 14:31:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:27: 状态:游戏结束!-继续开始
2025-04-24 14:31:27: 状态:游戏结束!
2025-04-24 14:31:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:31: 状态:游戏结束!-继续开始
2025-04-24 14:31:31: 状态:游戏结束!
2025-04-24 14:31:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:35: 状态:游戏结束!-继续开始
2025-04-24 14:31:35: 状态:游戏结束!
2025-04-24 14:31:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:39: 状态:游戏结束!-继续开始
2025-04-24 14:31:39: 状态:游戏结束!
2025-04-24 14:31:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:43: 状态:游戏结束!-继续开始
2025-04-24 14:31:43: 状态:游戏结束!
2025-04-24 14:31:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:47: 状态:游戏结束!-继续开始
2025-04-24 14:31:47: 状态:游戏结束!
2025-04-24 14:31:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:51: 状态:游戏结束!-继续开始
2025-04-24 14:31:51: 状态:游戏结束!
2025-04-24 14:31:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:55: 状态:游戏结束!-继续开始
2025-04-24 14:31:55: 状态:游戏结束!
2025-04-24 14:31:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:31:59: 状态:游戏结束!-继续开始
2025-04-24 14:31:59: 状态:游戏结束!
2025-04-24 14:32:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:04: 状态:游戏结束!-继续开始
2025-04-24 14:32:04: 状态:游戏结束!
2025-04-24 14:32:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:08: 状态:游戏结束!-继续开始
2025-04-24 14:32:08: 状态:游戏结束!
2025-04-24 14:32:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:12: 状态:游戏结束!-继续开始
2025-04-24 14:32:12: 状态:游戏结束!
2025-04-24 14:32:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:16: 状态:游戏结束!-继续开始
2025-04-24 14:32:16: 状态:游戏结束!
2025-04-24 14:32:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:20: 状态:游戏结束!-继续开始
2025-04-24 14:32:20: 状态:游戏结束!
2025-04-24 14:32:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:24: 状态:游戏结束!-继续开始
2025-04-24 14:32:24: 状态:游戏结束!
2025-04-24 14:32:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:28: 状态:游戏结束!-继续开始
2025-04-24 14:32:28: 状态:游戏结束!
2025-04-24 14:32:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:32: 状态:游戏结束!-继续开始
2025-04-24 14:32:32: 状态:游戏结束!
2025-04-24 14:32:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:37: 状态:游戏结束!-继续开始
2025-04-24 14:32:37: 状态:游戏结束!
2025-04-24 14:32:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:41: 状态:游戏结束!-继续开始
2025-04-24 14:32:41: 状态:游戏结束!
2025-04-24 14:32:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:45: 状态:游戏结束!-继续开始
2025-04-24 14:32:45: 状态:游戏结束!
2025-04-24 14:32:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:49: 状态:游戏结束!-继续开始
2025-04-24 14:32:49: 状态:游戏结束!
2025-04-24 14:32:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:53: 状态:游戏结束!-继续开始
2025-04-24 14:32:53: 状态:游戏结束!
2025-04-24 14:32:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:32:57: 状态:游戏结束!-继续开始
2025-04-24 14:32:57: 状态:游戏结束!
2025-04-24 14:33:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:01: 状态:游戏结束!-继续开始
2025-04-24 14:33:01: 状态:游戏结束!
2025-04-24 14:33:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:05: 状态:游戏结束!-继续开始
2025-04-24 14:33:05: 状态:游戏结束!
2025-04-24 14:33:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:10: 状态:游戏结束!-继续开始
2025-04-24 14:33:10: 状态:游戏结束!
2025-04-24 14:33:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:14: 状态:游戏结束!-继续开始
2025-04-24 14:33:14: 状态:游戏结束!
2025-04-24 14:33:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:18: 状态:游戏结束!-继续开始
2025-04-24 14:33:18: 状态:游戏结束!
2025-04-24 14:33:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:22: 状态:游戏结束!-继续开始
2025-04-24 14:33:22: 状态:游戏结束!
2025-04-24 14:33:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:26: 状态:游戏结束!-继续开始
2025-04-24 14:33:26: 状态:游戏结束!
2025-04-24 14:33:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:30: 状态:游戏结束!-继续开始
2025-04-24 14:33:30: 状态:游戏结束!
2025-04-24 14:33:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:34: 状态:游戏结束!-继续开始
2025-04-24 14:33:34: 状态:游戏结束!
2025-04-24 14:33:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:38: 状态:游戏结束!-继续开始
2025-04-24 14:33:38: 状态:游戏结束!
2025-04-24 14:33:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:43: 状态:游戏结束!-继续开始
2025-04-24 14:33:43: 状态:游戏结束!
2025-04-24 14:33:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:47: 状态:游戏结束!-继续开始
2025-04-24 14:33:47: 状态:游戏结束!
2025-04-24 14:33:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:51: 状态:游戏结束!-继续开始
2025-04-24 14:33:51: 状态:游戏结束!
2025-04-24 14:33:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:55: 状态:游戏结束!-继续开始
2025-04-24 14:33:55: 状态:游戏结束!
2025-04-24 14:33:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:33:59: 状态:游戏结束!-继续开始
2025-04-24 14:33:59: 状态:游戏结束!
2025-04-24 14:34:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:03: 状态:游戏结束!-继续开始
2025-04-24 14:34:03: 状态:游戏结束!
2025-04-24 14:34:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:07: 状态:游戏结束!-继续开始
2025-04-24 14:34:07: 状态:游戏结束!
2025-04-24 14:34:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:11: 状态:游戏结束!-继续开始
2025-04-24 14:34:11: 状态:游戏结束!
2025-04-24 14:34:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:15: 状态:游戏结束!-继续开始
2025-04-24 14:34:15: 状态:游戏结束!
2025-04-24 14:34:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:20: 状态:游戏结束!-继续开始
2025-04-24 14:34:20: 状态:游戏结束!
2025-04-24 14:34:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:24: 状态:游戏结束!-继续开始
2025-04-24 14:34:24: 状态:游戏结束!
2025-04-24 14:34:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:28: 状态:游戏结束!-继续开始
2025-04-24 14:34:28: 状态:游戏结束!
2025-04-24 14:34:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:32: 状态:游戏结束!-继续开始
2025-04-24 14:34:32: 状态:游戏结束!
2025-04-24 14:34:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:36: 状态:游戏结束!-继续开始
2025-04-24 14:34:36: 状态:游戏结束!
2025-04-24 14:34:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:40: 状态:游戏结束!-继续开始
2025-04-24 14:34:40: 状态:游戏结束!
2025-04-24 14:34:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:44: 状态:游戏结束!-继续开始
2025-04-24 14:34:44: 状态:游戏结束!
2025-04-24 14:34:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:48: 状态:游戏结束!-继续开始
2025-04-24 14:34:48: 状态:游戏结束!
2025-04-24 14:34:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:53: 状态:游戏结束!-继续开始
2025-04-24 14:34:53: 状态:游戏结束!
2025-04-24 14:34:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:34:57: 状态:游戏结束!-继续开始
2025-04-24 14:34:57: 状态:游戏结束!
2025-04-24 14:35:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:01: 状态:游戏结束!-继续开始
2025-04-24 14:35:01: 状态:游戏结束!
2025-04-24 14:35:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:05: 状态:游戏结束!-继续开始
2025-04-24 14:35:05: 状态:游戏结束!
2025-04-24 14:35:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:09: 状态:游戏结束!-继续开始
2025-04-24 14:35:09: 状态:游戏结束!
2025-04-24 14:35:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:13: 状态:游戏结束!-继续开始
2025-04-24 14:35:13: 状态:游戏结束!
2025-04-24 14:35:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:17: 状态:游戏结束!-继续开始
2025-04-24 14:35:17: 状态:游戏结束!
2025-04-24 14:35:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:21: 状态:游戏结束!-继续开始
2025-04-24 14:35:21: 状态:游戏结束!
2025-04-24 14:35:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:26: 状态:游戏结束!-继续开始
2025-04-24 14:35:26: 状态:游戏结束!
2025-04-24 14:35:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:30: 状态:游戏结束!-继续开始
2025-04-24 14:35:30: 状态:游戏结束!
2025-04-24 14:35:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:34: 状态:游戏结束!-继续开始
2025-04-24 14:35:34: 状态:游戏结束!
2025-04-24 14:35:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:38: 状态:游戏结束!-继续开始
2025-04-24 14:35:38: 状态:游戏结束!
2025-04-24 14:35:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:42: 状态:游戏结束!-继续开始
2025-04-24 14:35:42: 状态:游戏结束!
2025-04-24 14:35:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:46: 状态:游戏结束!-继续开始
2025-04-24 14:35:46: 状态:游戏结束!
2025-04-24 14:35:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:50: 状态:游戏结束!-继续开始
2025-04-24 14:35:50: 状态:游戏结束!
2025-04-24 14:35:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:54: 状态:游戏结束!-继续开始
2025-04-24 14:35:54: 状态:游戏结束!
2025-04-24 14:35:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:35:58: 状态:游戏结束!-继续开始
2025-04-24 14:35:58: 状态:游戏结束!
2025-04-24 14:36:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:03: 状态:游戏结束!-继续开始
2025-04-24 14:36:03: 状态:游戏结束!
2025-04-24 14:36:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:07: 状态:游戏结束!-继续开始
2025-04-24 14:36:07: 状态:游戏结束!
2025-04-24 14:36:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:11: 状态:游戏结束!-继续开始
2025-04-24 14:36:11: 状态:游戏结束!
2025-04-24 14:36:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:15: 状态:游戏结束!-继续开始
2025-04-24 14:36:15: 状态:游戏结束!
2025-04-24 14:36:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:19: 状态:游戏结束!-继续开始
2025-04-24 14:36:19: 状态:游戏结束!
2025-04-24 14:36:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:23: 状态:游戏结束!-继续开始
2025-04-24 14:36:23: 状态:游戏结束!
2025-04-24 14:36:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:27: 状态:游戏结束!-继续开始
2025-04-24 14:36:27: 状态:游戏结束!
2025-04-24 14:36:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:31: 状态:游戏结束!-继续开始
2025-04-24 14:36:31: 状态:游戏结束!
2025-04-24 14:36:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:36: 状态:游戏结束!-继续开始
2025-04-24 14:36:36: 状态:游戏结束!
2025-04-24 14:36:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:40: 状态:游戏结束!-继续开始
2025-04-24 14:36:40: 状态:游戏结束!
2025-04-24 14:36:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:44: 状态:游戏结束!-继续开始
2025-04-24 14:36:44: 状态:游戏结束!
2025-04-24 14:36:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:48: 状态:游戏结束!-继续开始
2025-04-24 14:36:48: 状态:游戏结束!
2025-04-24 14:36:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:52: 状态:游戏结束!-继续开始
2025-04-24 14:36:52: 状态:游戏结束!
2025-04-24 14:36:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:36:56: 状态:游戏结束!-继续开始
2025-04-24 14:36:56: 状态:游戏结束!
2025-04-24 14:36:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:00: 状态:游戏结束!-继续开始
2025-04-24 14:37:00: 状态:游戏结束!
2025-04-24 14:37:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:04: 状态:游戏结束!-继续开始
2025-04-24 14:37:04: 状态:游戏结束!
2025-04-24 14:37:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:09: 状态:游戏结束!-继续开始
2025-04-24 14:37:09: 状态:游戏结束!
2025-04-24 14:37:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:13: 状态:游戏结束!-继续开始
2025-04-24 14:37:13: 状态:游戏结束!
2025-04-24 14:37:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:17: 状态:游戏结束!-继续开始
2025-04-24 14:37:17: 状态:游戏结束!
2025-04-24 14:37:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:21: 状态:游戏结束!-继续开始
2025-04-24 14:37:21: 状态:游戏结束!
2025-04-24 14:37:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:25: 状态:游戏结束!-继续开始
2025-04-24 14:37:25: 状态:游戏结束!
2025-04-24 14:37:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:29: 状态:游戏结束!-继续开始
2025-04-24 14:37:29: 状态:游戏结束!
2025-04-24 14:37:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:33: 状态:游戏结束!-继续开始
2025-04-24 14:37:33: 状态:游戏结束!
2025-04-24 14:37:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:37: 状态:游戏结束!-继续开始
2025-04-24 14:37:37: 状态:游戏结束!
2025-04-24 14:37:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:41: 状态:游戏结束!-继续开始
2025-04-24 14:37:41: 状态:游戏结束!
2025-04-24 14:37:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:45: 状态:游戏结束!-继续开始
2025-04-24 14:37:45: 状态:游戏结束!
2025-04-24 14:37:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:50: 状态:游戏结束!-继续开始
2025-04-24 14:37:50: 状态:游戏结束!
2025-04-24 14:37:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:54: 状态:游戏结束!-继续开始
2025-04-24 14:37:54: 状态:游戏结束!
2025-04-24 14:37:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:37:58: 状态:游戏结束!-继续开始
2025-04-24 14:37:58: 状态:游戏结束!
2025-04-24 14:38:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:02: 状态:游戏结束!-继续开始
2025-04-24 14:38:02: 状态:游戏结束!
2025-04-24 14:38:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:06: 状态:游戏结束!-继续开始
2025-04-24 14:38:06: 状态:游戏结束!
2025-04-24 14:38:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:10: 状态:游戏结束!-继续开始
2025-04-24 14:38:10: 状态:游戏结束!
2025-04-24 14:38:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:14: 状态:游戏结束!-继续开始
2025-04-24 14:38:14: 状态:游戏结束!
2025-04-24 14:38:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:18: 状态:游戏结束!-继续开始
2025-04-24 14:38:18: 状态:游戏结束!
2025-04-24 14:38:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:22: 状态:游戏结束!-继续开始
2025-04-24 14:38:22: 状态:游戏结束!
2025-04-24 14:38:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:27: 状态:游戏结束!-继续开始
2025-04-24 14:38:27: 状态:游戏结束!
2025-04-24 14:38:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:31: 状态:游戏结束!-继续开始
2025-04-24 14:38:31: 状态:游戏结束!
2025-04-24 14:38:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:35: 状态:游戏结束!-继续开始
2025-04-24 14:38:35: 状态:游戏结束!
2025-04-24 14:38:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:39: 状态:游戏结束!-继续开始
2025-04-24 14:38:39: 状态:游戏结束!
2025-04-24 14:38:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:43: 状态:游戏结束!-继续开始
2025-04-24 14:38:43: 状态:游戏结束!
2025-04-24 14:38:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:47: 状态:游戏结束!-继续开始
2025-04-24 14:38:47: 状态:游戏结束!
2025-04-24 14:38:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:51: 状态:游戏结束!-继续开始
2025-04-24 14:38:51: 状态:游戏结束!
2025-04-24 14:38:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:55: 状态:游戏结束!-继续开始
2025-04-24 14:38:55: 状态:游戏结束!
2025-04-24 14:38:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:38:59: 状态:游戏结束!-继续开始
2025-04-24 14:38:59: 状态:游戏结束!
2025-04-24 14:39:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:04: 状态:游戏结束!-继续开始
2025-04-24 14:39:04: 状态:游戏结束!
2025-04-24 14:39:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:08: 状态:游戏结束!-继续开始
2025-04-24 14:39:08: 状态:游戏结束!
2025-04-24 14:39:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:12: 状态:游戏结束!-继续开始
2025-04-24 14:39:12: 状态:游戏结束!
2025-04-24 14:39:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:16: 状态:游戏结束!-继续开始
2025-04-24 14:39:16: 状态:游戏结束!
2025-04-24 14:39:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:20: 状态:游戏结束!-继续开始
2025-04-24 14:39:20: 状态:游戏结束!
2025-04-24 14:39:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:24: 状态:游戏结束!-继续开始
2025-04-24 14:39:24: 状态:游戏结束!
2025-04-24 14:39:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:28: 状态:游戏结束!-继续开始
2025-04-24 14:39:28: 状态:游戏结束!
2025-04-24 14:39:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:32: 状态:游戏结束!-继续开始
2025-04-24 14:39:32: 状态:游戏结束!
2025-04-24 14:39:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:36: 状态:游戏结束!-继续开始
2025-04-24 14:39:36: 状态:游戏结束!
2025-04-24 14:39:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:40: 状态:游戏结束!-继续开始
2025-04-24 14:39:40: 状态:游戏结束!
2025-04-24 14:39:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:45: 状态:游戏结束!-继续开始
2025-04-24 14:39:45: 状态:游戏结束!
2025-04-24 14:39:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:49: 状态:游戏结束!-继续开始
2025-04-24 14:39:49: 状态:游戏结束!
2025-04-24 14:39:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:53: 状态:游戏结束!-继续开始
2025-04-24 14:39:53: 状态:游戏结束!
2025-04-24 14:39:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:39:57: 状态:游戏结束!-继续开始
2025-04-24 14:39:57: 状态:游戏结束!
2025-04-24 14:40:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:01: 状态:游戏结束!-继续开始
2025-04-24 14:40:01: 状态:游戏结束!
2025-04-24 14:40:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:05: 状态:游戏结束!-继续开始
2025-04-24 14:40:05: 状态:游戏结束!
2025-04-24 14:40:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:09: 状态:游戏结束!-继续开始
2025-04-24 14:40:09: 状态:游戏结束!
2025-04-24 14:40:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:13: 状态:游戏结束!-继续开始
2025-04-24 14:40:13: 状态:游戏结束!
2025-04-24 14:40:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:17: 状态:游戏结束!-继续开始
2025-04-24 14:40:17: 状态:游戏结束!
2025-04-24 14:40:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:22: 状态:游戏结束!-继续开始
2025-04-24 14:40:22: 状态:游戏结束!
2025-04-24 14:40:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:26: 状态:游戏结束!-继续开始
2025-04-24 14:40:26: 状态:游戏结束!
2025-04-24 14:40:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:30: 状态:游戏结束!-继续开始
2025-04-24 14:40:30: 状态:游戏结束!
2025-04-24 14:40:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:34: 状态:游戏结束!-继续开始
2025-04-24 14:40:34: 状态:游戏结束!
2025-04-24 14:40:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:38: 状态:游戏结束!-继续开始
2025-04-24 14:40:38: 状态:游戏结束!
2025-04-24 14:40:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:42: 状态:游戏结束!-继续开始
2025-04-24 14:40:42: 状态:游戏结束!
2025-04-24 14:40:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:46: 状态:游戏结束!-继续开始
2025-04-24 14:40:46: 状态:游戏结束!
2025-04-24 14:40:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:50: 状态:游戏结束!-继续开始
2025-04-24 14:40:50: 状态:游戏结束!
2025-04-24 14:40:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:54: 状态:游戏结束!-继续开始
2025-04-24 14:40:54: 状态:游戏结束!
2025-04-24 14:40:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:40:59: 状态:游戏结束!-继续开始
2025-04-24 14:40:59: 状态:游戏结束!
2025-04-24 14:41:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:03: 状态:游戏结束!-继续开始
2025-04-24 14:41:03: 状态:游戏结束!
2025-04-24 14:41:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:07: 状态:游戏结束!-继续开始
2025-04-24 14:41:07: 状态:游戏结束!
2025-04-24 14:41:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:11: 状态:游戏结束!-继续开始
2025-04-24 14:41:11: 状态:游戏结束!
2025-04-24 14:41:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:15: 状态:游戏结束!-继续开始
2025-04-24 14:41:15: 状态:游戏结束!
2025-04-24 14:41:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:19: 状态:游戏结束!-继续开始
2025-04-24 14:41:19: 状态:游戏结束!
2025-04-24 14:41:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:23: 状态:游戏结束!-继续开始
2025-04-24 14:41:23: 状态:游戏结束!
2025-04-24 14:41:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:27: 状态:游戏结束!-继续开始
2025-04-24 14:41:27: 状态:游戏结束!
2025-04-24 14:41:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:31: 状态:游戏结束!-继续开始
2025-04-24 14:41:31: 状态:游戏结束!
2025-04-24 14:41:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:36: 状态:游戏结束!-继续开始
2025-04-24 14:41:36: 状态:游戏结束!
2025-04-24 14:41:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:40: 状态:游戏结束!-继续开始
2025-04-24 14:41:40: 状态:游戏结束!
2025-04-24 14:41:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:44: 状态:游戏结束!-继续开始
2025-04-24 14:41:44: 状态:游戏结束!
2025-04-24 14:41:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:48: 状态:游戏结束!-继续开始
2025-04-24 14:41:48: 状态:游戏结束!
2025-04-24 14:41:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:52: 状态:游戏结束!-继续开始
2025-04-24 14:41:52: 状态:游戏结束!
2025-04-24 14:41:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:41:56: 状态:游戏结束!-继续开始
2025-04-24 14:41:56: 状态:游戏结束!
2025-04-24 14:41:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:00: 状态:游戏结束!-继续开始
2025-04-24 14:42:00: 状态:游戏结束!
2025-04-24 14:42:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:04: 状态:游戏结束!-继续开始
2025-04-24 14:42:04: 状态:游戏结束!
2025-04-24 14:42:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:08: 状态:游戏结束!-继续开始
2025-04-24 14:42:08: 状态:游戏结束!
2025-04-24 14:42:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:12: 状态:游戏结束!-继续开始
2025-04-24 14:42:12: 状态:游戏结束!
2025-04-24 14:42:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:17: 状态:游戏结束!-继续开始
2025-04-24 14:42:17: 状态:游戏结束!
2025-04-24 14:42:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:21: 状态:游戏结束!-继续开始
2025-04-24 14:42:21: 状态:游戏结束!
2025-04-24 14:42:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:25: 状态:游戏结束!-继续开始
2025-04-24 14:42:25: 状态:游戏结束!
2025-04-24 14:42:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:29: 状态:游戏结束!-继续开始
2025-04-24 14:42:29: 状态:游戏结束!
2025-04-24 14:42:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:33: 状态:游戏结束!-继续开始
2025-04-24 14:42:33: 状态:游戏结束!
2025-04-24 14:42:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:37: 状态:游戏结束!-继续开始
2025-04-24 14:42:37: 状态:游戏结束!
2025-04-24 14:42:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:41: 状态:游戏结束!-继续开始
2025-04-24 14:42:41: 状态:游戏结束!
2025-04-24 14:42:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:45: 状态:游戏结束!-继续开始
2025-04-24 14:42:45: 状态:游戏结束!
2025-04-24 14:42:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:49: 状态:游戏结束!-继续开始
2025-04-24 14:42:49: 状态:游戏结束!
2025-04-24 14:42:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:54: 状态:游戏结束!-继续开始
2025-04-24 14:42:54: 状态:游戏结束!
2025-04-24 14:42:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:42:58: 状态:游戏结束!-继续开始
2025-04-24 14:42:58: 状态:游戏结束!
2025-04-24 14:43:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:02: 状态:游戏结束!-继续开始
2025-04-24 14:43:02: 状态:游戏结束!
2025-04-24 14:43:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:06: 状态:游戏结束!-继续开始
2025-04-24 14:43:06: 状态:游戏结束!
2025-04-24 14:43:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:10: 状态:游戏结束!-继续开始
2025-04-24 14:43:10: 状态:游戏结束!
2025-04-24 14:43:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:14: 状态:游戏结束!-继续开始
2025-04-24 14:43:14: 状态:游戏结束!
2025-04-24 14:43:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:18: 状态:游戏结束!-继续开始
2025-04-24 14:43:18: 状态:游戏结束!
2025-04-24 14:43:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:22: 状态:游戏结束!-继续开始
2025-04-24 14:43:22: 状态:游戏结束!
2025-04-24 14:43:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:26: 状态:游戏结束!-继续开始
2025-04-24 14:43:26: 状态:游戏结束!
2025-04-24 14:43:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:31: 状态:游戏结束!-继续开始
2025-04-24 14:43:31: 状态:游戏结束!
2025-04-24 14:43:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:35: 状态:游戏结束!-继续开始
2025-04-24 14:43:35: 状态:游戏结束!
2025-04-24 14:43:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:39: 状态:游戏结束!-继续开始
2025-04-24 14:43:39: 状态:游戏结束!
2025-04-24 14:43:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:43: 状态:游戏结束!-继续开始
2025-04-24 14:43:43: 状态:游戏结束!
2025-04-24 14:43:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:47: 状态:游戏结束!-继续开始
2025-04-24 14:43:47: 状态:游戏结束!
2025-04-24 14:43:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:51: 状态:游戏结束!-继续开始
2025-04-24 14:43:51: 状态:游戏结束!
2025-04-24 14:43:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:55: 状态:游戏结束!-继续开始
2025-04-24 14:43:55: 状态:游戏结束!
2025-04-24 14:43:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:43:59: 状态:游戏结束!-继续开始
2025-04-24 14:43:59: 状态:游戏结束!
2025-04-24 14:44:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:03: 状态:游戏结束!-继续开始
2025-04-24 14:44:03: 状态:游戏结束!
2025-04-24 14:44:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:08: 状态:游戏结束!-继续开始
2025-04-24 14:44:08: 状态:游戏结束!
2025-04-24 14:44:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:12: 状态:游戏结束!-继续开始
2025-04-24 14:44:12: 状态:游戏结束!
2025-04-24 14:44:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:16: 状态:游戏结束!-继续开始
2025-04-24 14:44:16: 状态:游戏结束!
2025-04-24 14:44:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:20: 状态:游戏结束!-继续开始
2025-04-24 14:44:20: 状态:游戏结束!
2025-04-24 14:44:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:24: 状态:游戏结束!-继续开始
2025-04-24 14:44:24: 状态:游戏结束!
2025-04-24 14:44:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:28: 状态:游戏结束!-继续开始
2025-04-24 14:44:28: 状态:游戏结束!
2025-04-24 14:44:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:32: 状态:游戏结束!-继续开始
2025-04-24 14:44:32: 状态:游戏结束!
2025-04-24 14:44:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:36: 状态:游戏结束!-继续开始
2025-04-24 14:44:36: 状态:游戏结束!
2025-04-24 14:44:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:41: 状态:游戏结束!-继续开始
2025-04-24 14:44:41: 状态:游戏结束!
2025-04-24 14:44:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:45: 状态:游戏结束!-继续开始
2025-04-24 14:44:45: 状态:游戏结束!
2025-04-24 14:44:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:49: 状态:游戏结束!-继续开始
2025-04-24 14:44:49: 状态:游戏结束!
2025-04-24 14:44:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:53: 状态:游戏结束!-继续开始
2025-04-24 14:44:53: 状态:游戏结束!
2025-04-24 14:44:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:44:57: 状态:游戏结束!-继续开始
2025-04-24 14:44:57: 状态:游戏结束!
2025-04-24 14:45:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:01: 状态:游戏结束!-继续开始
2025-04-24 14:45:01: 状态:游戏结束!
2025-04-24 14:45:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:05: 状态:游戏结束!-继续开始
2025-04-24 14:45:05: 状态:游戏结束!
2025-04-24 14:45:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:09: 状态:游戏结束!-继续开始
2025-04-24 14:45:09: 状态:游戏结束!
2025-04-24 14:45:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:13: 状态:游戏结束!-继续开始
2025-04-24 14:45:13: 状态:游戏结束!
2025-04-24 14:45:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:17: 状态:游戏结束!-继续开始
2025-04-24 14:45:17: 状态:游戏结束!
2025-04-24 14:45:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:22: 状态:游戏结束!-继续开始
2025-04-24 14:45:22: 状态:游戏结束!
2025-04-24 14:45:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:26: 状态:游戏结束!-继续开始
2025-04-24 14:45:26: 状态:游戏结束!
2025-04-24 14:45:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:30: 状态:游戏结束!-继续开始
2025-04-24 14:45:30: 状态:游戏结束!
2025-04-24 14:45:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:34: 状态:游戏结束!-继续开始
2025-04-24 14:45:34: 状态:游戏结束!
2025-04-24 14:45:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:38: 状态:游戏结束!-继续开始
2025-04-24 14:45:38: 状态:游戏结束!
2025-04-24 14:45:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:42: 状态:游戏结束!-继续开始
2025-04-24 14:45:42: 状态:游戏结束!
2025-04-24 14:45:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:46: 状态:游戏结束!-继续开始
2025-04-24 14:45:46: 状态:游戏结束!
2025-04-24 14:45:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:50: 状态:游戏结束!-继续开始
2025-04-24 14:45:50: 状态:游戏结束!
2025-04-24 14:45:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:54: 状态:游戏结束!-继续开始
2025-04-24 14:45:54: 状态:游戏结束!
2025-04-24 14:45:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:45:59: 状态:游戏结束!-继续开始
2025-04-24 14:45:59: 状态:游戏结束!
2025-04-24 14:46:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:03: 状态:游戏结束!-继续开始
2025-04-24 14:46:03: 状态:游戏结束!
2025-04-24 14:46:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:07: 状态:游戏结束!-继续开始
2025-04-24 14:46:07: 状态:游戏结束!
2025-04-24 14:46:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:11: 状态:游戏结束!-继续开始
2025-04-24 14:46:11: 状态:游戏结束!
2025-04-24 14:46:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:15: 状态:游戏结束!-继续开始
2025-04-24 14:46:15: 状态:游戏结束!
2025-04-24 14:46:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:19: 状态:游戏结束!-继续开始
2025-04-24 14:46:19: 状态:游戏结束!
2025-04-24 14:46:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:23: 状态:游戏结束!-继续开始
2025-04-24 14:46:23: 状态:游戏结束!
2025-04-24 14:46:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:27: 状态:游戏结束!-继续开始
2025-04-24 14:46:27: 状态:游戏结束!
2025-04-24 14:46:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:31: 状态:游戏结束!-继续开始
2025-04-24 14:46:31: 状态:游戏结束!
2025-04-24 14:46:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:36: 状态:游戏结束!-继续开始
2025-04-24 14:46:36: 状态:游戏结束!
2025-04-24 14:46:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:40: 状态:游戏结束!-继续开始
2025-04-24 14:46:40: 状态:游戏结束!
2025-04-24 14:46:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:44: 状态:游戏结束!-继续开始
2025-04-24 14:46:44: 状态:游戏结束!
2025-04-24 14:46:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:48: 状态:游戏结束!-继续开始
2025-04-24 14:46:48: 状态:游戏结束!
2025-04-24 14:46:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:52: 状态:游戏结束!-继续开始
2025-04-24 14:46:52: 状态:游戏结束!
2025-04-24 14:46:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:46:56: 状态:游戏结束!-继续开始
2025-04-24 14:46:56: 状态:游戏结束!
2025-04-24 14:46:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:00: 状态:游戏结束!-继续开始
2025-04-24 14:47:00: 状态:游戏结束!
2025-04-24 14:47:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:04: 状态:游戏结束!-继续开始
2025-04-24 14:47:04: 状态:游戏结束!
2025-04-24 14:47:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:08: 状态:游戏结束!-继续开始
2025-04-24 14:47:08: 状态:游戏结束!
2025-04-24 14:47:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:13: 状态:游戏结束!-继续开始
2025-04-24 14:47:13: 状态:游戏结束!
2025-04-24 14:47:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:17: 状态:游戏结束!-继续开始
2025-04-24 14:47:17: 状态:游戏结束!
2025-04-24 14:47:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:21: 状态:游戏结束!-继续开始
2025-04-24 14:47:21: 状态:游戏结束!
2025-04-24 14:47:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:25: 状态:游戏结束!-继续开始
2025-04-24 14:47:25: 状态:游戏结束!
2025-04-24 14:47:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:29: 状态:游戏结束!-继续开始
2025-04-24 14:47:29: 状态:游戏结束!
2025-04-24 14:47:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:33: 状态:游戏结束!-继续开始
2025-04-24 14:47:33: 状态:游戏结束!
2025-04-24 14:47:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:37: 状态:游戏结束!-继续开始
2025-04-24 14:47:37: 状态:游戏结束!
2025-04-24 14:47:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:41: 状态:游戏结束!-继续开始
2025-04-24 14:47:41: 状态:游戏结束!
2025-04-24 14:47:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:45: 状态:游戏结束!-继续开始
2025-04-24 14:47:45: 状态:游戏结束!
2025-04-24 14:47:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:50: 状态:游戏结束!-继续开始
2025-04-24 14:47:50: 状态:游戏结束!
2025-04-24 14:47:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:54: 状态:游戏结束!-继续开始
2025-04-24 14:47:54: 状态:游戏结束!
2025-04-24 14:47:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:47:58: 状态:游戏结束!-继续开始
2025-04-24 14:47:58: 状态:游戏结束!
2025-04-24 14:48:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:02: 状态:游戏结束!-继续开始
2025-04-24 14:48:02: 状态:游戏结束!
2025-04-24 14:48:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:07: 状态:游戏结束!-继续开始
2025-04-24 14:48:07: 状态:游戏结束!
2025-04-24 14:48:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:11: 状态:游戏结束!-继续开始
2025-04-24 14:48:11: 状态:游戏结束!
2025-04-24 14:48:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:15: 状态:游戏结束!-继续开始
2025-04-24 14:48:15: 状态:游戏结束!
2025-04-24 14:48:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:19: 状态:游戏结束!-继续开始
2025-04-24 14:48:19: 状态:游戏结束!
2025-04-24 14:48:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:23: 状态:游戏结束!-继续开始
2025-04-24 14:48:23: 状态:游戏结束!
2025-04-24 14:48:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:27: 状态:游戏结束!-继续开始
2025-04-24 14:48:27: 状态:游戏结束!
2025-04-24 14:48:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:31: 状态:游戏结束!-继续开始
2025-04-24 14:48:31: 状态:游戏结束!
2025-04-24 14:48:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:35: 状态:游戏结束!-继续开始
2025-04-24 14:48:35: 状态:游戏结束!
2025-04-24 14:48:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:40: 状态:游戏结束!-继续开始
2025-04-24 14:48:40: 状态:游戏结束!
2025-04-24 14:48:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:44: 状态:游戏结束!-继续开始
2025-04-24 14:48:44: 状态:游戏结束!
2025-04-24 14:48:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:48: 状态:游戏结束!-继续开始
2025-04-24 14:48:48: 状态:游戏结束!
2025-04-24 14:48:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:52: 状态:游戏结束!-继续开始
2025-04-24 14:48:52: 状态:游戏结束!
2025-04-24 14:48:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:48:56: 状态:游戏结束!-继续开始
2025-04-24 14:48:56: 状态:游戏结束!
2025-04-24 14:48:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:00: 状态:游戏结束!-继续开始
2025-04-24 14:49:00: 状态:游戏结束!
2025-04-24 14:49:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:04: 状态:游戏结束!-继续开始
2025-04-24 14:49:04: 状态:游戏结束!
2025-04-24 14:49:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:08: 状态:游戏结束!-继续开始
2025-04-24 14:49:08: 状态:游戏结束!
2025-04-24 14:49:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:12: 状态:游戏结束!-继续开始
2025-04-24 14:49:12: 状态:游戏结束!
2025-04-24 14:49:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:17: 状态:游戏结束!-继续开始
2025-04-24 14:49:17: 状态:游戏结束!
2025-04-24 14:49:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:21: 状态:游戏结束!-继续开始
2025-04-24 14:49:21: 状态:游戏结束!
2025-04-24 14:49:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:25: 状态:游戏结束!-继续开始
2025-04-24 14:49:25: 状态:游戏结束!
2025-04-24 14:49:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:29: 状态:游戏结束!-继续开始
2025-04-24 14:49:29: 状态:游戏结束!
2025-04-24 14:49:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:33: 状态:游戏结束!-继续开始
2025-04-24 14:49:33: 状态:游戏结束!
2025-04-24 14:49:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:37: 状态:游戏结束!-继续开始
2025-04-24 14:49:37: 状态:游戏结束!
2025-04-24 14:49:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:41: 状态:游戏结束!-继续开始
2025-04-24 14:49:41: 状态:游戏结束!
2025-04-24 14:49:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:45: 状态:游戏结束!-继续开始
2025-04-24 14:49:45: 状态:游戏结束!
2025-04-24 14:49:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:49: 状态:游戏结束!-继续开始
2025-04-24 14:49:49: 状态:游戏结束!
2025-04-24 14:49:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:54: 状态:游戏结束!-继续开始
2025-04-24 14:49:54: 状态:游戏结束!
2025-04-24 14:49:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:49:58: 状态:游戏结束!-继续开始
2025-04-24 14:49:58: 状态:游戏结束!
2025-04-24 14:50:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:02: 状态:游戏结束!-继续开始
2025-04-24 14:50:02: 状态:游戏结束!
2025-04-24 14:50:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:06: 状态:游戏结束!-继续开始
2025-04-24 14:50:06: 状态:游戏结束!
2025-04-24 14:50:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:10: 状态:游戏结束!-继续开始
2025-04-24 14:50:10: 状态:游戏结束!
2025-04-24 14:50:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:14: 状态:游戏结束!-继续开始
2025-04-24 14:50:14: 状态:游戏结束!
2025-04-24 14:50:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:18: 状态:游戏结束!-继续开始
2025-04-24 14:50:18: 状态:游戏结束!
2025-04-24 14:50:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:22: 状态:游戏结束!-继续开始
2025-04-24 14:50:22: 状态:游戏结束!
2025-04-24 14:50:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:26: 状态:游戏结束!-继续开始
2025-04-24 14:50:26: 状态:游戏结束!
2025-04-24 14:50:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:30: 状态:游戏结束!-继续开始
2025-04-24 14:50:30: 状态:游戏结束!
2025-04-24 14:50:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:35: 状态:游戏结束!-继续开始
2025-04-24 14:50:35: 状态:游戏结束!
2025-04-24 14:50:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:39: 状态:游戏结束!-继续开始
2025-04-24 14:50:39: 状态:游戏结束!
2025-04-24 14:50:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:43: 状态:游戏结束!-继续开始
2025-04-24 14:50:43: 状态:游戏结束!
2025-04-24 14:50:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:47: 状态:游戏结束!-继续开始
2025-04-24 14:50:47: 状态:游戏结束!
2025-04-24 14:50:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:51: 状态:游戏结束!-继续开始
2025-04-24 14:50:51: 状态:游戏结束!
2025-04-24 14:50:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:55: 状态:游戏结束!-继续开始
2025-04-24 14:50:55: 状态:游戏结束!
2025-04-24 14:50:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:50:59: 状态:游戏结束!-继续开始
2025-04-24 14:50:59: 状态:游戏结束!
2025-04-24 14:51:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:03: 状态:游戏结束!-继续开始
2025-04-24 14:51:03: 状态:游戏结束!
2025-04-24 14:51:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:07: 状态:游戏结束!-继续开始
2025-04-24 14:51:07: 状态:游戏结束!
2025-04-24 14:51:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:11: 状态:游戏结束!-继续开始
2025-04-24 14:51:11: 状态:游戏结束!
2025-04-24 14:51:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:16: 状态:游戏结束!-继续开始
2025-04-24 14:51:16: 状态:游戏结束!
2025-04-24 14:51:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:20: 状态:游戏结束!-继续开始
2025-04-24 14:51:20: 状态:游戏结束!
2025-04-24 14:51:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:24: 状态:游戏结束!-继续开始
2025-04-24 14:51:24: 状态:游戏结束!
2025-04-24 14:51:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:28: 状态:游戏结束!-继续开始
2025-04-24 14:51:28: 状态:游戏结束!
2025-04-24 14:51:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:32: 状态:游戏结束!-继续开始
2025-04-24 14:51:32: 状态:游戏结束!
2025-04-24 14:51:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:36: 状态:游戏结束!-继续开始
2025-04-24 14:51:36: 状态:游戏结束!
2025-04-24 14:51:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:40: 状态:游戏结束!-继续开始
2025-04-24 14:51:40: 状态:游戏结束!
2025-04-24 14:51:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:44: 状态:游戏结束!-继续开始
2025-04-24 14:51:44: 状态:游戏结束!
2025-04-24 14:51:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:48: 状态:游戏结束!-继续开始
2025-04-24 14:51:48: 状态:游戏结束!
2025-04-24 14:51:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:53: 状态:游戏结束!-继续开始
2025-04-24 14:51:53: 状态:游戏结束!
2025-04-24 14:51:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:51:57: 状态:游戏结束!-继续开始
2025-04-24 14:51:57: 状态:游戏结束!
2025-04-24 14:52:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:01: 状态:游戏结束!-继续开始
2025-04-24 14:52:01: 状态:游戏结束!
2025-04-24 14:52:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:05: 状态:游戏结束!-继续开始
2025-04-24 14:52:05: 状态:游戏结束!
2025-04-24 14:52:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:09: 状态:游戏结束!-继续开始
2025-04-24 14:52:09: 状态:游戏结束!
2025-04-24 14:52:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:13: 状态:游戏结束!-继续开始
2025-04-24 14:52:13: 状态:游戏结束!
2025-04-24 14:52:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:17: 状态:游戏结束!-继续开始
2025-04-24 14:52:17: 状态:游戏结束!
2025-04-24 14:52:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:21: 状态:游戏结束!-继续开始
2025-04-24 14:52:21: 状态:游戏结束!
2025-04-24 14:52:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:26: 状态:游戏结束!-继续开始
2025-04-24 14:52:26: 状态:游戏结束!
2025-04-24 14:52:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:30: 状态:游戏结束!-继续开始
2025-04-24 14:52:30: 状态:游戏结束!
2025-04-24 14:52:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:34: 状态:游戏结束!-继续开始
2025-04-24 14:52:34: 状态:游戏结束!
2025-04-24 14:52:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:38: 状态:游戏结束!-继续开始
2025-04-24 14:52:38: 状态:游戏结束!
2025-04-24 14:52:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:42: 状态:游戏结束!-继续开始
2025-04-24 14:52:42: 状态:游戏结束!
2025-04-24 14:52:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:46: 状态:游戏结束!-继续开始
2025-04-24 14:52:46: 状态:游戏结束!
2025-04-24 14:52:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:50: 状态:游戏结束!-继续开始
2025-04-24 14:52:50: 状态:游戏结束!
2025-04-24 14:52:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:54: 状态:游戏结束!-继续开始
2025-04-24 14:52:54: 状态:游戏结束!
2025-04-24 14:52:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:52:58: 状态:游戏结束!-继续开始
2025-04-24 14:52:58: 状态:游戏结束!
2025-04-24 14:53:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:03: 状态:游戏结束!-继续开始
2025-04-24 14:53:03: 状态:游戏结束!
2025-04-24 14:53:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:07: 状态:游戏结束!-继续开始
2025-04-24 14:53:07: 状态:游戏结束!
2025-04-24 14:53:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:11: 状态:游戏结束!-继续开始
2025-04-24 14:53:11: 状态:游戏结束!
2025-04-24 14:53:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:15: 状态:游戏结束!-继续开始
2025-04-24 14:53:15: 状态:游戏结束!
2025-04-24 14:53:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:19: 状态:游戏结束!-继续开始
2025-04-24 14:53:19: 状态:游戏结束!
2025-04-24 14:53:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:23: 状态:游戏结束!-继续开始
2025-04-24 14:53:23: 状态:游戏结束!
2025-04-24 14:53:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:27: 状态:游戏结束!-继续开始
2025-04-24 14:53:27: 状态:游戏结束!
2025-04-24 14:53:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:31: 状态:游戏结束!-继续开始
2025-04-24 14:53:31: 状态:游戏结束!
2025-04-24 14:53:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:35: 状态:游戏结束!-继续开始
2025-04-24 14:53:35: 状态:游戏结束!
2025-04-24 14:53:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:40: 状态:游戏结束!-继续开始
2025-04-24 14:53:40: 状态:游戏结束!
2025-04-24 14:53:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:44: 状态:游戏结束!-继续开始
2025-04-24 14:53:44: 状态:游戏结束!
2025-04-24 14:53:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:48: 状态:游戏结束!-继续开始
2025-04-24 14:53:48: 状态:游戏结束!
2025-04-24 14:53:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:52: 状态:游戏结束!-继续开始
2025-04-24 14:53:52: 状态:游戏结束!
2025-04-24 14:53:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:53:56: 状态:游戏结束!-继续开始
2025-04-24 14:53:56: 状态:游戏结束!
2025-04-24 14:53:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:54:00: 状态:游戏结束!-继续开始
2025-04-24 14:54:00: 状态:游戏结束!
2025-04-24 14:54:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:54:04: 状态:游戏结束!-继续开始
2025-04-24 14:54:04: 状态:游戏结束!
2025-04-24 14:54:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:54:08: 状态:游戏结束!-继续开始
2025-04-24 14:54:08: 状态:游戏结束!
2025-04-24 14:54:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:54:13: 状态:游戏结束!-继续开始
2025-04-24 14:54:13: 状态:游戏结束!
2025-04-24 14:54:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 14:54:17: 状态:游戏结束!-继续开始
2025-04-24 14:54:17: 状态:游戏结束!
2025-04-24 14:54:20: 无法获取游戏会话信息，跳过点赞
2025-04-24 14:54:21: 状态:游戏结束!-继续开始
2025-04-24 14:54:21: 状态:游戏结束!
2025-04-24 14:54:21: 大厅等待中
2025-04-24 14:54:21: 检测到房间!
2025-04-24 14:54:31: 窗口隐藏1获取英雄ID:0
2025-04-24 14:54:33: 进入选择英雄界面！
2025-04-24 14:54:38: 获取英雄ID:92-上次选英雄ID:0
2025-04-24 14:54:38: 正在为Riven选英雄
2025-04-24 14:54:39: 进入选择英雄界面！
2025-04-24 14:54:54: 窗口隐藏2
2025-04-24 14:54:55: 对局正在进行中!
2025-04-24 14:54:55: 窗口隐藏95
2025-04-24 14:54:57: 对局正在进行中!
2025-04-24 15:10:52: 等待游戏结算界面!
2025-04-24 15:10:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:10:56: 状态:游戏结束!-继续开始
2025-04-24 15:10:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:00: 状态:游戏结束!-继续开始
2025-04-24 15:11:00: 状态:游戏结束!
2025-04-24 15:11:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:04: 状态:游戏结束!-继续开始
2025-04-24 15:11:04: 状态:游戏结束!
2025-04-24 15:11:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:08: 状态:游戏结束!-继续开始
2025-04-24 15:11:08: 状态:游戏结束!
2025-04-24 15:11:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:12: 状态:游戏结束!-继续开始
2025-04-24 15:11:12: 状态:游戏结束!
2025-04-24 15:11:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:17: 状态:游戏结束!-继续开始
2025-04-24 15:11:17: 状态:游戏结束!
2025-04-24 15:11:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:21: 状态:游戏结束!-继续开始
2025-04-24 15:11:21: 状态:游戏结束!
2025-04-24 15:11:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:25: 状态:游戏结束!-继续开始
2025-04-24 15:11:25: 状态:游戏结束!
2025-04-24 15:11:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:29: 状态:游戏结束!-继续开始
2025-04-24 15:11:29: 状态:游戏结束!
2025-04-24 15:11:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:33: 状态:游戏结束!-继续开始
2025-04-24 15:11:33: 状态:游戏结束!
2025-04-24 15:11:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:37: 状态:游戏结束!-继续开始
2025-04-24 15:11:37: 状态:游戏结束!
2025-04-24 15:11:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:41: 状态:游戏结束!-继续开始
2025-04-24 15:11:41: 状态:游戏结束!
2025-04-24 15:11:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:45: 状态:游戏结束!-继续开始
2025-04-24 15:11:45: 状态:游戏结束!
2025-04-24 15:11:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:49: 状态:游戏结束!-继续开始
2025-04-24 15:11:49: 状态:游戏结束!
2025-04-24 15:11:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:54: 状态:游戏结束!-继续开始
2025-04-24 15:11:54: 状态:游戏结束!
2025-04-24 15:11:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:11:58: 状态:游戏结束!-继续开始
2025-04-24 15:11:58: 状态:游戏结束!
2025-04-24 15:12:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:02: 状态:游戏结束!-继续开始
2025-04-24 15:12:02: 状态:游戏结束!
2025-04-24 15:12:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:06: 状态:游戏结束!-继续开始
2025-04-24 15:12:06: 状态:游戏结束!
2025-04-24 15:12:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:10: 状态:游戏结束!-继续开始
2025-04-24 15:12:10: 状态:游戏结束!
2025-04-24 15:12:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:14: 状态:游戏结束!-继续开始
2025-04-24 15:12:14: 状态:游戏结束!
2025-04-24 15:12:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:18: 状态:游戏结束!-继续开始
2025-04-24 15:12:18: 状态:游戏结束!
2025-04-24 15:12:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:22: 状态:游戏结束!-继续开始
2025-04-24 15:12:22: 状态:游戏结束!
2025-04-24 15:12:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:26: 状态:游戏结束!-继续开始
2025-04-24 15:12:26: 状态:游戏结束!
2025-04-24 15:12:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:31: 状态:游戏结束!-继续开始
2025-04-24 15:12:31: 状态:游戏结束!
2025-04-24 15:12:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:35: 状态:游戏结束!-继续开始
2025-04-24 15:12:35: 状态:游戏结束!
2025-04-24 15:12:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:39: 状态:游戏结束!-继续开始
2025-04-24 15:12:39: 状态:游戏结束!
2025-04-24 15:12:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:43: 状态:游戏结束!-继续开始
2025-04-24 15:12:43: 状态:游戏结束!
2025-04-24 15:12:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:47: 状态:游戏结束!-继续开始
2025-04-24 15:12:47: 状态:游戏结束!
2025-04-24 15:12:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:51: 状态:游戏结束!-继续开始
2025-04-24 15:12:51: 状态:游戏结束!
2025-04-24 15:12:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:55: 状态:游戏结束!-继续开始
2025-04-24 15:12:55: 状态:游戏结束!
2025-04-24 15:12:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:12:59: 状态:游戏结束!-继续开始
2025-04-24 15:12:59: 状态:游戏结束!
2025-04-24 15:13:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:03: 状态:游戏结束!-继续开始
2025-04-24 15:13:03: 状态:游戏结束!
2025-04-24 15:13:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:08: 状态:游戏结束!-继续开始
2025-04-24 15:13:08: 状态:游戏结束!
2025-04-24 15:13:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:12: 状态:游戏结束!-继续开始
2025-04-24 15:13:12: 状态:游戏结束!
2025-04-24 15:13:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:16: 状态:游戏结束!-继续开始
2025-04-24 15:13:16: 状态:游戏结束!
2025-04-24 15:13:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:20: 状态:游戏结束!-继续开始
2025-04-24 15:13:20: 状态:游戏结束!
2025-04-24 15:13:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:24: 状态:游戏结束!-继续开始
2025-04-24 15:13:24: 状态:游戏结束!
2025-04-24 15:13:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:28: 状态:游戏结束!-继续开始
2025-04-24 15:13:28: 状态:游戏结束!
2025-04-24 15:13:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:32: 状态:游戏结束!-继续开始
2025-04-24 15:13:32: 状态:游戏结束!
2025-04-24 15:13:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:36: 状态:游戏结束!-继续开始
2025-04-24 15:13:36: 状态:游戏结束!
2025-04-24 15:13:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:40: 状态:游戏结束!-继续开始
2025-04-24 15:13:40: 状态:游戏结束!
2025-04-24 15:13:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:45: 状态:游戏结束!-继续开始
2025-04-24 15:13:45: 状态:游戏结束!
2025-04-24 15:13:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:49: 状态:游戏结束!-继续开始
2025-04-24 15:13:49: 状态:游戏结束!
2025-04-24 15:13:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:53: 状态:游戏结束!-继续开始
2025-04-24 15:13:53: 状态:游戏结束!
2025-04-24 15:13:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:13:57: 状态:游戏结束!-继续开始
2025-04-24 15:13:57: 状态:游戏结束!
2025-04-24 15:14:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:01: 状态:游戏结束!-继续开始
2025-04-24 15:14:01: 状态:游戏结束!
2025-04-24 15:14:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:05: 状态:游戏结束!-继续开始
2025-04-24 15:14:05: 状态:游戏结束!
2025-04-24 15:14:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:09: 状态:游戏结束!-继续开始
2025-04-24 15:14:09: 状态:游戏结束!
2025-04-24 15:14:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:13: 状态:游戏结束!-继续开始
2025-04-24 15:14:13: 状态:游戏结束!
2025-04-24 15:14:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:17: 状态:游戏结束!-继续开始
2025-04-24 15:14:17: 状态:游戏结束!
2025-04-24 15:14:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:21: 状态:游戏结束!-继续开始
2025-04-24 15:14:21: 状态:游戏结束!
2025-04-24 15:14:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:26: 状态:游戏结束!-继续开始
2025-04-24 15:14:26: 状态:游戏结束!
2025-04-24 15:14:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:30: 状态:游戏结束!-继续开始
2025-04-24 15:14:30: 状态:游戏结束!
2025-04-24 15:14:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:34: 状态:游戏结束!-继续开始
2025-04-24 15:14:34: 状态:游戏结束!
2025-04-24 15:14:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:38: 状态:游戏结束!-继续开始
2025-04-24 15:14:38: 状态:游戏结束!
2025-04-24 15:14:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:42: 状态:游戏结束!-继续开始
2025-04-24 15:14:42: 状态:游戏结束!
2025-04-24 15:14:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:46: 状态:游戏结束!-继续开始
2025-04-24 15:14:46: 状态:游戏结束!
2025-04-24 15:14:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:50: 状态:游戏结束!-继续开始
2025-04-24 15:14:50: 状态:游戏结束!
2025-04-24 15:14:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:54: 状态:游戏结束!-继续开始
2025-04-24 15:14:54: 状态:游戏结束!
2025-04-24 15:14:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:14:58: 状态:游戏结束!-继续开始
2025-04-24 15:14:58: 状态:游戏结束!
2025-04-24 15:15:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:02: 状态:游戏结束!-继续开始
2025-04-24 15:15:02: 状态:游戏结束!
2025-04-24 15:15:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:07: 状态:游戏结束!-继续开始
2025-04-24 15:15:07: 状态:游戏结束!
2025-04-24 15:15:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:11: 状态:游戏结束!-继续开始
2025-04-24 15:15:11: 状态:游戏结束!
2025-04-24 15:15:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:15: 状态:游戏结束!-继续开始
2025-04-24 15:15:15: 状态:游戏结束!
2025-04-24 15:15:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:19: 状态:游戏结束!-继续开始
2025-04-24 15:15:19: 状态:游戏结束!
2025-04-24 15:15:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:23: 状态:游戏结束!-继续开始
2025-04-24 15:15:23: 状态:游戏结束!
2025-04-24 15:15:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:27: 状态:游戏结束!-继续开始
2025-04-24 15:15:27: 状态:游戏结束!
2025-04-24 15:15:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:31: 状态:游戏结束!-继续开始
2025-04-24 15:15:31: 状态:游戏结束!
2025-04-24 15:15:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:35: 状态:游戏结束!-继续开始
2025-04-24 15:15:35: 状态:游戏结束!
2025-04-24 15:15:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:39: 状态:游戏结束!-继续开始
2025-04-24 15:15:39: 状态:游戏结束!
2025-04-24 15:15:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:44: 状态:游戏结束!-继续开始
2025-04-24 15:15:44: 状态:游戏结束!
2025-04-24 15:15:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:48: 状态:游戏结束!-继续开始
2025-04-24 15:15:48: 状态:游戏结束!
2025-04-24 15:15:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:52: 状态:游戏结束!-继续开始
2025-04-24 15:15:52: 状态:游戏结束!
2025-04-24 15:15:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:15:56: 状态:游戏结束!-继续开始
2025-04-24 15:15:56: 状态:游戏结束!
2025-04-24 15:15:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:00: 状态:游戏结束!-继续开始
2025-04-24 15:16:00: 状态:游戏结束!
2025-04-24 15:16:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:04: 状态:游戏结束!-继续开始
2025-04-24 15:16:04: 状态:游戏结束!
2025-04-24 15:16:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:08: 状态:游戏结束!-继续开始
2025-04-24 15:16:08: 状态:游戏结束!
2025-04-24 15:16:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:12: 状态:游戏结束!-继续开始
2025-04-24 15:16:12: 状态:游戏结束!
2025-04-24 15:16:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:16: 状态:游戏结束!-继续开始
2025-04-24 15:16:16: 状态:游戏结束!
2025-04-24 15:16:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:20: 状态:游戏结束!-继续开始
2025-04-24 15:16:20: 状态:游戏结束!
2025-04-24 15:16:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:25: 状态:游戏结束!-继续开始
2025-04-24 15:16:25: 状态:游戏结束!
2025-04-24 15:16:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:29: 状态:游戏结束!-继续开始
2025-04-24 15:16:29: 状态:游戏结束!
2025-04-24 15:16:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:33: 状态:游戏结束!-继续开始
2025-04-24 15:16:33: 状态:游戏结束!
2025-04-24 15:16:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:37: 状态:游戏结束!-继续开始
2025-04-24 15:16:37: 状态:游戏结束!
2025-04-24 15:16:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:41: 状态:游戏结束!-继续开始
2025-04-24 15:16:41: 状态:游戏结束!
2025-04-24 15:16:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:45: 状态:游戏结束!-继续开始
2025-04-24 15:16:45: 状态:游戏结束!
2025-04-24 15:16:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:49: 状态:游戏结束!-继续开始
2025-04-24 15:16:49: 状态:游戏结束!
2025-04-24 15:16:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:53: 状态:游戏结束!-继续开始
2025-04-24 15:16:53: 状态:游戏结束!
2025-04-24 15:16:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:16:58: 状态:游戏结束!-继续开始
2025-04-24 15:16:58: 状态:游戏结束!
2025-04-24 15:17:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:02: 状态:游戏结束!-继续开始
2025-04-24 15:17:02: 状态:游戏结束!
2025-04-24 15:17:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:06: 状态:游戏结束!-继续开始
2025-04-24 15:17:06: 状态:游戏结束!
2025-04-24 15:17:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:10: 状态:游戏结束!-继续开始
2025-04-24 15:17:10: 状态:游戏结束!
2025-04-24 15:17:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:14: 状态:游戏结束!-继续开始
2025-04-24 15:17:14: 状态:游戏结束!
2025-04-24 15:17:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:18: 状态:游戏结束!-继续开始
2025-04-24 15:17:18: 状态:游戏结束!
2025-04-24 15:17:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:22: 状态:游戏结束!-继续开始
2025-04-24 15:17:22: 状态:游戏结束!
2025-04-24 15:17:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:26: 状态:游戏结束!-继续开始
2025-04-24 15:17:26: 状态:游戏结束!
2025-04-24 15:17:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:31: 状态:游戏结束!-继续开始
2025-04-24 15:17:31: 状态:游戏结束!
2025-04-24 15:17:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:35: 状态:游戏结束!-继续开始
2025-04-24 15:17:35: 状态:游戏结束!
2025-04-24 15:17:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:39: 状态:游戏结束!-继续开始
2025-04-24 15:17:39: 状态:游戏结束!
2025-04-24 15:17:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:43: 状态:游戏结束!-继续开始
2025-04-24 15:17:43: 状态:游戏结束!
2025-04-24 15:17:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:47: 状态:游戏结束!-继续开始
2025-04-24 15:17:47: 状态:游戏结束!
2025-04-24 15:17:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:51: 状态:游戏结束!-继续开始
2025-04-24 15:17:51: 状态:游戏结束!
2025-04-24 15:17:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:55: 状态:游戏结束!-继续开始
2025-04-24 15:17:55: 状态:游戏结束!
2025-04-24 15:17:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:17:59: 状态:游戏结束!-继续开始
2025-04-24 15:17:59: 状态:游戏结束!
2025-04-24 15:18:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:03: 状态:游戏结束!-继续开始
2025-04-24 15:18:03: 状态:游戏结束!
2025-04-24 15:18:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:07: 状态:游戏结束!-继续开始
2025-04-24 15:18:07: 状态:游戏结束!
2025-04-24 15:18:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:12: 状态:游戏结束!-继续开始
2025-04-24 15:18:12: 状态:游戏结束!
2025-04-24 15:18:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:16: 状态:游戏结束!-继续开始
2025-04-24 15:18:16: 状态:游戏结束!
2025-04-24 15:18:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:20: 状态:游戏结束!-继续开始
2025-04-24 15:18:20: 状态:游戏结束!
2025-04-24 15:18:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:24: 状态:游戏结束!-继续开始
2025-04-24 15:18:24: 状态:游戏结束!
2025-04-24 15:18:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:28: 状态:游戏结束!-继续开始
2025-04-24 15:18:28: 状态:游戏结束!
2025-04-24 15:18:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:32: 状态:游戏结束!-继续开始
2025-04-24 15:18:32: 状态:游戏结束!
2025-04-24 15:18:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:36: 状态:游戏结束!-继续开始
2025-04-24 15:18:36: 状态:游戏结束!
2025-04-24 15:18:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:40: 状态:游戏结束!-继续开始
2025-04-24 15:18:40: 状态:游戏结束!
2025-04-24 15:18:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:44: 状态:游戏结束!-继续开始
2025-04-24 15:18:44: 状态:游戏结束!
2025-04-24 15:18:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:49: 状态:游戏结束!-继续开始
2025-04-24 15:18:49: 状态:游戏结束!
2025-04-24 15:18:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:53: 状态:游戏结束!-继续开始
2025-04-24 15:18:53: 状态:游戏结束!
2025-04-24 15:18:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:18:57: 状态:游戏结束!-继续开始
2025-04-24 15:18:57: 状态:游戏结束!
2025-04-24 15:19:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:01: 状态:游戏结束!-继续开始
2025-04-24 15:19:01: 状态:游戏结束!
2025-04-24 15:19:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:05: 状态:游戏结束!-继续开始
2025-04-24 15:19:05: 状态:游戏结束!
2025-04-24 15:19:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:09: 状态:游戏结束!-继续开始
2025-04-24 15:19:09: 状态:游戏结束!
2025-04-24 15:19:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:13: 状态:游戏结束!-继续开始
2025-04-24 15:19:13: 状态:游戏结束!
2025-04-24 15:19:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:17: 状态:游戏结束!-继续开始
2025-04-24 15:19:17: 状态:游戏结束!
2025-04-24 15:19:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:21: 状态:游戏结束!-继续开始
2025-04-24 15:19:21: 状态:游戏结束!
2025-04-24 15:19:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:25: 状态:游戏结束!-继续开始
2025-04-24 15:19:25: 状态:游戏结束!
2025-04-24 15:19:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:30: 状态:游戏结束!-继续开始
2025-04-24 15:19:30: 状态:游戏结束!
2025-04-24 15:19:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:34: 状态:游戏结束!-继续开始
2025-04-24 15:19:34: 状态:游戏结束!
2025-04-24 15:19:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:38: 状态:游戏结束!-继续开始
2025-04-24 15:19:38: 状态:游戏结束!
2025-04-24 15:19:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:42: 状态:游戏结束!-继续开始
2025-04-24 15:19:42: 状态:游戏结束!
2025-04-24 15:19:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:46: 状态:游戏结束!-继续开始
2025-04-24 15:19:46: 状态:游戏结束!
2025-04-24 15:19:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:50: 状态:游戏结束!-继续开始
2025-04-24 15:19:50: 状态:游戏结束!
2025-04-24 15:19:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:54: 状态:游戏结束!-继续开始
2025-04-24 15:19:54: 状态:游戏结束!
2025-04-24 15:19:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:19:58: 状态:游戏结束!-继续开始
2025-04-24 15:19:58: 状态:游戏结束!
2025-04-24 15:20:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:03: 状态:游戏结束!-继续开始
2025-04-24 15:20:03: 状态:游戏结束!
2025-04-24 15:20:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:07: 状态:游戏结束!-继续开始
2025-04-24 15:20:07: 状态:游戏结束!
2025-04-24 15:20:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:11: 状态:游戏结束!-继续开始
2025-04-24 15:20:11: 状态:游戏结束!
2025-04-24 15:20:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:15: 状态:游戏结束!-继续开始
2025-04-24 15:20:15: 状态:游戏结束!
2025-04-24 15:20:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:19: 状态:游戏结束!-继续开始
2025-04-24 15:20:19: 状态:游戏结束!
2025-04-24 15:20:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:23: 状态:游戏结束!-继续开始
2025-04-24 15:20:23: 状态:游戏结束!
2025-04-24 15:20:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:27: 状态:游戏结束!-继续开始
2025-04-24 15:20:27: 状态:游戏结束!
2025-04-24 15:20:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:31: 状态:游戏结束!-继续开始
2025-04-24 15:20:31: 状态:游戏结束!
2025-04-24 15:20:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:35: 状态:游戏结束!-继续开始
2025-04-24 15:20:35: 状态:游戏结束!
2025-04-24 15:20:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:40: 状态:游戏结束!-继续开始
2025-04-24 15:20:40: 状态:游戏结束!
2025-04-24 15:20:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:44: 状态:游戏结束!-继续开始
2025-04-24 15:20:44: 状态:游戏结束!
2025-04-24 15:20:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:48: 状态:游戏结束!-继续开始
2025-04-24 15:20:48: 状态:游戏结束!
2025-04-24 15:20:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:52: 状态:游戏结束!-继续开始
2025-04-24 15:20:52: 状态:游戏结束!
2025-04-24 15:20:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:20:56: 状态:游戏结束!-继续开始
2025-04-24 15:20:56: 状态:游戏结束!
2025-04-24 15:20:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:00: 状态:游戏结束!-继续开始
2025-04-24 15:21:00: 状态:游戏结束!
2025-04-24 15:21:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:04: 状态:游戏结束!-继续开始
2025-04-24 15:21:04: 状态:游戏结束!
2025-04-24 15:21:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:08: 状态:游戏结束!-继续开始
2025-04-24 15:21:08: 状态:游戏结束!
2025-04-24 15:21:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:12: 状态:游戏结束!-继续开始
2025-04-24 15:21:12: 状态:游戏结束!
2025-04-24 15:21:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:17: 状态:游戏结束!-继续开始
2025-04-24 15:21:17: 状态:游戏结束!
2025-04-24 15:21:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:21: 状态:游戏结束!-继续开始
2025-04-24 15:21:21: 状态:游戏结束!
2025-04-24 15:21:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:25: 状态:游戏结束!-继续开始
2025-04-24 15:21:25: 状态:游戏结束!
2025-04-24 15:21:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:29: 状态:游戏结束!-继续开始
2025-04-24 15:21:29: 状态:游戏结束!
2025-04-24 15:21:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:33: 状态:游戏结束!-继续开始
2025-04-24 15:21:33: 状态:游戏结束!
2025-04-24 15:21:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:37: 状态:游戏结束!-继续开始
2025-04-24 15:21:37: 状态:游戏结束!
2025-04-24 15:21:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:41: 状态:游戏结束!-继续开始
2025-04-24 15:21:41: 状态:游戏结束!
2025-04-24 15:21:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:45: 状态:游戏结束!-继续开始
2025-04-24 15:21:45: 状态:游戏结束!
2025-04-24 15:21:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:49: 状态:游戏结束!-继续开始
2025-04-24 15:21:49: 状态:游戏结束!
2025-04-24 15:21:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:54: 状态:游戏结束!-继续开始
2025-04-24 15:21:54: 状态:游戏结束!
2025-04-24 15:21:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:21:58: 状态:游戏结束!-继续开始
2025-04-24 15:21:58: 状态:游戏结束!
2025-04-24 15:22:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:02: 状态:游戏结束!-继续开始
2025-04-24 15:22:02: 状态:游戏结束!
2025-04-24 15:22:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:06: 状态:游戏结束!-继续开始
2025-04-24 15:22:06: 状态:游戏结束!
2025-04-24 15:22:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:10: 状态:游戏结束!-继续开始
2025-04-24 15:22:10: 状态:游戏结束!
2025-04-24 15:22:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:14: 状态:游戏结束!-继续开始
2025-04-24 15:22:14: 状态:游戏结束!
2025-04-24 15:22:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:18: 状态:游戏结束!-继续开始
2025-04-24 15:22:18: 状态:游戏结束!
2025-04-24 15:22:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:22: 状态:游戏结束!-继续开始
2025-04-24 15:22:22: 状态:游戏结束!
2025-04-24 15:22:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:26: 状态:游戏结束!-继续开始
2025-04-24 15:22:26: 状态:游戏结束!
2025-04-24 15:22:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:31: 状态:游戏结束!-继续开始
2025-04-24 15:22:31: 状态:游戏结束!
2025-04-24 15:22:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:35: 状态:游戏结束!-继续开始
2025-04-24 15:22:35: 状态:游戏结束!
2025-04-24 15:22:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:39: 状态:游戏结束!-继续开始
2025-04-24 15:22:39: 状态:游戏结束!
2025-04-24 15:22:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:43: 状态:游戏结束!-继续开始
2025-04-24 15:22:43: 状态:游戏结束!
2025-04-24 15:22:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:47: 状态:游戏结束!-继续开始
2025-04-24 15:22:47: 状态:游戏结束!
2025-04-24 15:22:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:51: 状态:游戏结束!-继续开始
2025-04-24 15:22:51: 状态:游戏结束!
2025-04-24 15:22:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:55: 状态:游戏结束!-继续开始
2025-04-24 15:22:55: 状态:游戏结束!
2025-04-24 15:22:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:22:59: 状态:游戏结束!-继续开始
2025-04-24 15:22:59: 状态:游戏结束!
2025-04-24 15:23:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:03: 状态:游戏结束!-继续开始
2025-04-24 15:23:03: 状态:游戏结束!
2025-04-24 15:23:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:08: 状态:游戏结束!-继续开始
2025-04-24 15:23:08: 状态:游戏结束!
2025-04-24 15:23:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:12: 状态:游戏结束!-继续开始
2025-04-24 15:23:12: 状态:游戏结束!
2025-04-24 15:23:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:16: 状态:游戏结束!-继续开始
2025-04-24 15:23:16: 状态:游戏结束!
2025-04-24 15:23:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:20: 状态:游戏结束!-继续开始
2025-04-24 15:23:20: 状态:游戏结束!
2025-04-24 15:23:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:24: 状态:游戏结束!-继续开始
2025-04-24 15:23:24: 状态:游戏结束!
2025-04-24 15:23:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:28: 状态:游戏结束!-继续开始
2025-04-24 15:23:28: 状态:游戏结束!
2025-04-24 15:23:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:32: 状态:游戏结束!-继续开始
2025-04-24 15:23:32: 状态:游戏结束!
2025-04-24 15:23:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:36: 状态:游戏结束!-继续开始
2025-04-24 15:23:36: 状态:游戏结束!
2025-04-24 15:23:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:41: 状态:游戏结束!-继续开始
2025-04-24 15:23:41: 状态:游戏结束!
2025-04-24 15:23:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:45: 状态:游戏结束!-继续开始
2025-04-24 15:23:45: 状态:游戏结束!
2025-04-24 15:23:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:49: 状态:游戏结束!-继续开始
2025-04-24 15:23:49: 状态:游戏结束!
2025-04-24 15:23:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:53: 状态:游戏结束!-继续开始
2025-04-24 15:23:53: 状态:游戏结束!
2025-04-24 15:23:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:23:57: 状态:游戏结束!-继续开始
2025-04-24 15:23:57: 状态:游戏结束!
2025-04-24 15:24:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:01: 状态:游戏结束!-继续开始
2025-04-24 15:24:01: 状态:游戏结束!
2025-04-24 15:24:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:05: 状态:游戏结束!-继续开始
2025-04-24 15:24:05: 状态:游戏结束!
2025-04-24 15:24:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:09: 状态:游戏结束!-继续开始
2025-04-24 15:24:09: 状态:游戏结束!
2025-04-24 15:24:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:13: 状态:游戏结束!-继续开始
2025-04-24 15:24:13: 状态:游戏结束!
2025-04-24 15:24:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:17: 状态:游戏结束!-继续开始
2025-04-24 15:24:17: 状态:游戏结束!
2025-04-24 15:24:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:22: 状态:游戏结束!-继续开始
2025-04-24 15:24:22: 状态:游戏结束!
2025-04-24 15:24:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:26: 状态:游戏结束!-继续开始
2025-04-24 15:24:26: 状态:游戏结束!
2025-04-24 15:24:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:30: 状态:游戏结束!-继续开始
2025-04-24 15:24:30: 状态:游戏结束!
2025-04-24 15:24:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:34: 状态:游戏结束!-继续开始
2025-04-24 15:24:34: 状态:游戏结束!
2025-04-24 15:24:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:38: 状态:游戏结束!-继续开始
2025-04-24 15:24:38: 状态:游戏结束!
2025-04-24 15:24:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:42: 状态:游戏结束!-继续开始
2025-04-24 15:24:42: 状态:游戏结束!
2025-04-24 15:24:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:46: 状态:游戏结束!-继续开始
2025-04-24 15:24:46: 状态:游戏结束!
2025-04-24 15:24:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:50: 状态:游戏结束!-继续开始
2025-04-24 15:24:50: 状态:游戏结束!
2025-04-24 15:24:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:54: 状态:游戏结束!-继续开始
2025-04-24 15:24:54: 状态:游戏结束!
2025-04-24 15:24:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:24:59: 状态:游戏结束!-继续开始
2025-04-24 15:24:59: 状态:游戏结束!
2025-04-24 15:25:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:03: 状态:游戏结束!-继续开始
2025-04-24 15:25:03: 状态:游戏结束!
2025-04-24 15:25:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:07: 状态:游戏结束!-继续开始
2025-04-24 15:25:07: 状态:游戏结束!
2025-04-24 15:25:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:11: 状态:游戏结束!-继续开始
2025-04-24 15:25:11: 状态:游戏结束!
2025-04-24 15:25:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:15: 状态:游戏结束!-继续开始
2025-04-24 15:25:15: 状态:游戏结束!
2025-04-24 15:25:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:19: 状态:游戏结束!-继续开始
2025-04-24 15:25:19: 状态:游戏结束!
2025-04-24 15:25:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:23: 状态:游戏结束!-继续开始
2025-04-24 15:25:23: 状态:游戏结束!
2025-04-24 15:25:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:27: 状态:游戏结束!-继续开始
2025-04-24 15:25:27: 状态:游戏结束!
2025-04-24 15:25:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:31: 状态:游戏结束!-继续开始
2025-04-24 15:25:31: 状态:游戏结束!
2025-04-24 15:25:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:35: 状态:游戏结束!-继续开始
2025-04-24 15:25:35: 状态:游戏结束!
2025-04-24 15:25:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:40: 状态:游戏结束!-继续开始
2025-04-24 15:25:40: 状态:游戏结束!
2025-04-24 15:25:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:44: 状态:游戏结束!-继续开始
2025-04-24 15:25:44: 状态:游戏结束!
2025-04-24 15:25:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:48: 状态:游戏结束!-继续开始
2025-04-24 15:25:48: 状态:游戏结束!
2025-04-24 15:25:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:52: 状态:游戏结束!-继续开始
2025-04-24 15:25:52: 状态:游戏结束!
2025-04-24 15:25:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:25:56: 状态:游戏结束!-继续开始
2025-04-24 15:25:56: 状态:游戏结束!
2025-04-24 15:25:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:00: 状态:游戏结束!-继续开始
2025-04-24 15:26:00: 状态:游戏结束!
2025-04-24 15:26:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:04: 状态:游戏结束!-继续开始
2025-04-24 15:26:04: 状态:游戏结束!
2025-04-24 15:26:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:08: 状态:游戏结束!-继续开始
2025-04-24 15:26:08: 状态:游戏结束!
2025-04-24 15:26:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:12: 状态:游戏结束!-继续开始
2025-04-24 15:26:12: 状态:游戏结束!
2025-04-24 15:26:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:17: 状态:游戏结束!-继续开始
2025-04-24 15:26:17: 状态:游戏结束!
2025-04-24 15:26:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:21: 状态:游戏结束!-继续开始
2025-04-24 15:26:21: 状态:游戏结束!
2025-04-24 15:26:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:25: 状态:游戏结束!-继续开始
2025-04-24 15:26:25: 状态:游戏结束!
2025-04-24 15:26:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:29: 状态:游戏结束!-继续开始
2025-04-24 15:26:29: 状态:游戏结束!
2025-04-24 15:26:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:33: 状态:游戏结束!-继续开始
2025-04-24 15:26:33: 状态:游戏结束!
2025-04-24 15:26:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:37: 状态:游戏结束!-继续开始
2025-04-24 15:26:37: 状态:游戏结束!
2025-04-24 15:26:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:41: 状态:游戏结束!-继续开始
2025-04-24 15:26:41: 状态:游戏结束!
2025-04-24 15:26:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:45: 状态:游戏结束!-继续开始
2025-04-24 15:26:45: 状态:游戏结束!
2025-04-24 15:26:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:49: 状态:游戏结束!-继续开始
2025-04-24 15:26:49: 状态:游戏结束!
2025-04-24 15:26:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:53: 状态:游戏结束!-继续开始
2025-04-24 15:26:53: 状态:游戏结束!
2025-04-24 15:26:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:26:58: 状态:游戏结束!-继续开始
2025-04-24 15:26:58: 状态:游戏结束!
2025-04-24 15:27:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:02: 状态:游戏结束!-继续开始
2025-04-24 15:27:02: 状态:游戏结束!
2025-04-24 15:27:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:06: 状态:游戏结束!-继续开始
2025-04-24 15:27:06: 状态:游戏结束!
2025-04-24 15:27:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:10: 状态:游戏结束!-继续开始
2025-04-24 15:27:10: 状态:游戏结束!
2025-04-24 15:27:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:14: 状态:游戏结束!-继续开始
2025-04-24 15:27:14: 状态:游戏结束!
2025-04-24 15:27:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:18: 状态:游戏结束!-继续开始
2025-04-24 15:27:18: 状态:游戏结束!
2025-04-24 15:27:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:22: 状态:游戏结束!-继续开始
2025-04-24 15:27:22: 状态:游戏结束!
2025-04-24 15:27:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:26: 状态:游戏结束!-继续开始
2025-04-24 15:27:26: 状态:游戏结束!
2025-04-24 15:27:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:31: 状态:游戏结束!-继续开始
2025-04-24 15:27:31: 状态:游戏结束!
2025-04-24 15:27:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:35: 状态:游戏结束!-继续开始
2025-04-24 15:27:35: 状态:游戏结束!
2025-04-24 15:27:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:39: 状态:游戏结束!-继续开始
2025-04-24 15:27:39: 状态:游戏结束!
2025-04-24 15:27:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:43: 状态:游戏结束!-继续开始
2025-04-24 15:27:43: 状态:游戏结束!
2025-04-24 15:27:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:47: 状态:游戏结束!-继续开始
2025-04-24 15:27:47: 状态:游戏结束!
2025-04-24 15:27:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:51: 状态:游戏结束!-继续开始
2025-04-24 15:27:51: 状态:游戏结束!
2025-04-24 15:27:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:55: 状态:游戏结束!-继续开始
2025-04-24 15:27:55: 状态:游戏结束!
2025-04-24 15:27:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:27:59: 状态:游戏结束!-继续开始
2025-04-24 15:27:59: 状态:游戏结束!
2025-04-24 15:28:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:03: 状态:游戏结束!-继续开始
2025-04-24 15:28:03: 状态:游戏结束!
2025-04-24 15:28:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:07: 状态:游戏结束!-继续开始
2025-04-24 15:28:07: 状态:游戏结束!
2025-04-24 15:28:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:12: 状态:游戏结束!-继续开始
2025-04-24 15:28:12: 状态:游戏结束!
2025-04-24 15:28:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:16: 状态:游戏结束!-继续开始
2025-04-24 15:28:16: 状态:游戏结束!
2025-04-24 15:28:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:20: 状态:游戏结束!-继续开始
2025-04-24 15:28:20: 状态:游戏结束!
2025-04-24 15:28:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:24: 状态:游戏结束!-继续开始
2025-04-24 15:28:24: 状态:游戏结束!
2025-04-24 15:28:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:28: 状态:游戏结束!-继续开始
2025-04-24 15:28:28: 状态:游戏结束!
2025-04-24 15:28:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:32: 状态:游戏结束!-继续开始
2025-04-24 15:28:32: 状态:游戏结束!
2025-04-24 15:28:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:36: 状态:游戏结束!-继续开始
2025-04-24 15:28:36: 状态:游戏结束!
2025-04-24 15:28:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:40: 状态:游戏结束!-继续开始
2025-04-24 15:28:40: 状态:游戏结束!
2025-04-24 15:28:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:44: 状态:游戏结束!-继续开始
2025-04-24 15:28:44: 状态:游戏结束!
2025-04-24 15:28:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:49: 状态:游戏结束!-继续开始
2025-04-24 15:28:49: 状态:游戏结束!
2025-04-24 15:28:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:53: 状态:游戏结束!-继续开始
2025-04-24 15:28:53: 状态:游戏结束!
2025-04-24 15:28:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:28:57: 状态:游戏结束!-继续开始
2025-04-24 15:28:57: 状态:游戏结束!
2025-04-24 15:29:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:01: 状态:游戏结束!-继续开始
2025-04-24 15:29:01: 状态:游戏结束!
2025-04-24 15:29:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:05: 状态:游戏结束!-继续开始
2025-04-24 15:29:05: 状态:游戏结束!
2025-04-24 15:29:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:09: 状态:游戏结束!-继续开始
2025-04-24 15:29:09: 状态:游戏结束!
2025-04-24 15:29:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:13: 状态:游戏结束!-继续开始
2025-04-24 15:29:13: 状态:游戏结束!
2025-04-24 15:29:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:17: 状态:游戏结束!-继续开始
2025-04-24 15:29:17: 状态:游戏结束!
2025-04-24 15:29:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:21: 状态:游戏结束!-继续开始
2025-04-24 15:29:21: 状态:游戏结束!
2025-04-24 15:29:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:25: 状态:游戏结束!-继续开始
2025-04-24 15:29:25: 状态:游戏结束!
2025-04-24 15:29:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:30: 状态:游戏结束!-继续开始
2025-04-24 15:29:30: 状态:游戏结束!
2025-04-24 15:29:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:34: 状态:游戏结束!-继续开始
2025-04-24 15:29:34: 状态:游戏结束!
2025-04-24 15:29:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:38: 状态:游戏结束!-继续开始
2025-04-24 15:29:38: 状态:游戏结束!
2025-04-24 15:29:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:42: 状态:游戏结束!-继续开始
2025-04-24 15:29:42: 状态:游戏结束!
2025-04-24 15:29:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:46: 状态:游戏结束!-继续开始
2025-04-24 15:29:46: 状态:游戏结束!
2025-04-24 15:29:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:50: 状态:游戏结束!-继续开始
2025-04-24 15:29:50: 状态:游戏结束!
2025-04-24 15:29:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:54: 状态:游戏结束!-继续开始
2025-04-24 15:29:54: 状态:游戏结束!
2025-04-24 15:29:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:29:58: 状态:游戏结束!-继续开始
2025-04-24 15:29:58: 状态:游戏结束!
2025-04-24 15:30:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:02: 状态:游戏结束!-继续开始
2025-04-24 15:30:02: 状态:游戏结束!
2025-04-24 15:30:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:07: 状态:游戏结束!-继续开始
2025-04-24 15:30:07: 状态:游戏结束!
2025-04-24 15:30:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:11: 状态:游戏结束!-继续开始
2025-04-24 15:30:11: 状态:游戏结束!
2025-04-24 15:30:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:15: 状态:游戏结束!-继续开始
2025-04-24 15:30:15: 状态:游戏结束!
2025-04-24 15:30:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:19: 状态:游戏结束!-继续开始
2025-04-24 15:30:19: 状态:游戏结束!
2025-04-24 15:30:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:23: 状态:游戏结束!-继续开始
2025-04-24 15:30:23: 状态:游戏结束!
2025-04-24 15:30:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:27: 状态:游戏结束!-继续开始
2025-04-24 15:30:27: 状态:游戏结束!
2025-04-24 15:30:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:31: 状态:游戏结束!-继续开始
2025-04-24 15:30:31: 状态:游戏结束!
2025-04-24 15:30:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:35: 状态:游戏结束!-继续开始
2025-04-24 15:30:35: 状态:游戏结束!
2025-04-24 15:30:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:39: 状态:游戏结束!-继续开始
2025-04-24 15:30:39: 状态:游戏结束!
2025-04-24 15:30:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:43: 状态:游戏结束!-继续开始
2025-04-24 15:30:43: 状态:游戏结束!
2025-04-24 15:30:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:48: 状态:游戏结束!-继续开始
2025-04-24 15:30:48: 状态:游戏结束!
2025-04-24 15:30:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:52: 状态:游戏结束!-继续开始
2025-04-24 15:30:52: 状态:游戏结束!
2025-04-24 15:30:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:30:56: 状态:游戏结束!-继续开始
2025-04-24 15:30:56: 状态:游戏结束!
2025-04-24 15:30:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:00: 状态:游戏结束!-继续开始
2025-04-24 15:31:00: 状态:游戏结束!
2025-04-24 15:31:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:04: 状态:游戏结束!-继续开始
2025-04-24 15:31:04: 状态:游戏结束!
2025-04-24 15:31:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:08: 状态:游戏结束!-继续开始
2025-04-24 15:31:08: 状态:游戏结束!
2025-04-24 15:31:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:12: 状态:游戏结束!-继续开始
2025-04-24 15:31:12: 状态:游戏结束!
2025-04-24 15:31:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:16: 状态:游戏结束!-继续开始
2025-04-24 15:31:16: 状态:游戏结束!
2025-04-24 15:31:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:20: 状态:游戏结束!-继续开始
2025-04-24 15:31:20: 状态:游戏结束!
2025-04-24 15:31:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:25: 状态:游戏结束!-继续开始
2025-04-24 15:31:25: 状态:游戏结束!
2025-04-24 15:31:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:31:29: 状态:游戏结束!-继续开始
2025-04-24 15:31:29: 状态:游戏结束!
2025-04-24 15:31:32: 无法获取游戏会话信息，跳过点赞
2025-04-24 15:31:33: 状态:游戏结束!-继续开始
2025-04-24 15:31:33: 状态:游戏结束!
2025-04-24 15:31:33: 大厅等待中
2025-04-24 15:31:33: 检测到房间!
2025-04-24 15:33:31: 窗口隐藏1获取英雄ID:0
2025-04-24 15:33:31: 进入选择英雄界面！
2025-04-24 15:33:35: 获取英雄ID:92-上次选英雄ID:0
2025-04-24 15:33:35: 正在为Riven选英雄
2025-04-24 15:33:35: 进入选择英雄界面！
2025-04-24 15:33:50: 窗口隐藏2
2025-04-24 15:33:50: 进入选择英雄界面！
2025-04-24 15:33:51: 对局正在进行中!
2025-04-24 15:33:51: 窗口隐藏95
2025-04-24 15:33:53: 对局正在进行中!
2025-04-24 15:56:23: 等待游戏结算界面!
2025-04-24 15:56:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:27: 状态:游戏结束!-继续开始
2025-04-24 15:56:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:31: 状态:游戏结束!-继续开始
2025-04-24 15:56:31: 状态:游戏结束!
2025-04-24 15:56:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:35: 状态:游戏结束!-继续开始
2025-04-24 15:56:35: 状态:游戏结束!
2025-04-24 15:56:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:39: 状态:游戏结束!-继续开始
2025-04-24 15:56:39: 状态:游戏结束!
2025-04-24 15:56:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:43: 状态:游戏结束!-继续开始
2025-04-24 15:56:43: 状态:游戏结束!
2025-04-24 15:56:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:47: 状态:游戏结束!-继续开始
2025-04-24 15:56:47: 状态:游戏结束!
2025-04-24 15:56:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:52: 状态:游戏结束!-继续开始
2025-04-24 15:56:52: 状态:游戏结束!
2025-04-24 15:56:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:56:56: 状态:游戏结束!-继续开始
2025-04-24 15:56:56: 状态:游戏结束!
2025-04-24 15:56:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:00: 状态:游戏结束!-继续开始
2025-04-24 15:57:00: 状态:游戏结束!
2025-04-24 15:57:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:04: 状态:游戏结束!-继续开始
2025-04-24 15:57:04: 状态:游戏结束!
2025-04-24 15:57:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:08: 状态:游戏结束!-继续开始
2025-04-24 15:57:08: 状态:游戏结束!
2025-04-24 15:57:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:12: 状态:游戏结束!-继续开始
2025-04-24 15:57:12: 状态:游戏结束!
2025-04-24 15:57:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:16: 状态:游戏结束!-继续开始
2025-04-24 15:57:16: 状态:游戏结束!
2025-04-24 15:57:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:20: 状态:游戏结束!-继续开始
2025-04-24 15:57:20: 状态:游戏结束!
2025-04-24 15:57:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:24: 状态:游戏结束!-继续开始
2025-04-24 15:57:24: 状态:游戏结束!
2025-04-24 15:57:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:29: 状态:游戏结束!-继续开始
2025-04-24 15:57:29: 状态:游戏结束!
2025-04-24 15:57:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:33: 状态:游戏结束!-继续开始
2025-04-24 15:57:33: 状态:游戏结束!
2025-04-24 15:57:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:37: 状态:游戏结束!-继续开始
2025-04-24 15:57:37: 状态:游戏结束!
2025-04-24 15:57:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:41: 状态:游戏结束!-继续开始
2025-04-24 15:57:41: 状态:游戏结束!
2025-04-24 15:57:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:45: 状态:游戏结束!-继续开始
2025-04-24 15:57:45: 状态:游戏结束!
2025-04-24 15:57:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:49: 状态:游戏结束!-继续开始
2025-04-24 15:57:49: 状态:游戏结束!
2025-04-24 15:57:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:53: 状态:游戏结束!-继续开始
2025-04-24 15:57:53: 状态:游戏结束!
2025-04-24 15:57:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:57:57: 状态:游戏结束!-继续开始
2025-04-24 15:57:57: 状态:游戏结束!
2025-04-24 15:58:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:01: 状态:游戏结束!-继续开始
2025-04-24 15:58:01: 状态:游戏结束!
2025-04-24 15:58:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:06: 状态:游戏结束!-继续开始
2025-04-24 15:58:06: 状态:游戏结束!
2025-04-24 15:58:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:10: 状态:游戏结束!-继续开始
2025-04-24 15:58:10: 状态:游戏结束!
2025-04-24 15:58:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:14: 状态:游戏结束!-继续开始
2025-04-24 15:58:14: 状态:游戏结束!
2025-04-24 15:58:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:18: 状态:游戏结束!-继续开始
2025-04-24 15:58:18: 状态:游戏结束!
2025-04-24 15:58:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:22: 状态:游戏结束!-继续开始
2025-04-24 15:58:22: 状态:游戏结束!
2025-04-24 15:58:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:26: 状态:游戏结束!-继续开始
2025-04-24 15:58:26: 状态:游戏结束!
2025-04-24 15:58:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:30: 状态:游戏结束!-继续开始
2025-04-24 15:58:30: 状态:游戏结束!
2025-04-24 15:58:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:34: 状态:游戏结束!-继续开始
2025-04-24 15:58:34: 状态:游戏结束!
2025-04-24 15:58:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:38: 状态:游戏结束!-继续开始
2025-04-24 15:58:38: 状态:游戏结束!
2025-04-24 15:58:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:42: 状态:游戏结束!-继续开始
2025-04-24 15:58:42: 状态:游戏结束!
2025-04-24 15:58:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:47: 状态:游戏结束!-继续开始
2025-04-24 15:58:47: 状态:游戏结束!
2025-04-24 15:58:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:51: 状态:游戏结束!-继续开始
2025-04-24 15:58:51: 状态:游戏结束!
2025-04-24 15:58:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:55: 状态:游戏结束!-继续开始
2025-04-24 15:58:55: 状态:游戏结束!
2025-04-24 15:58:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:58:59: 状态:游戏结束!-继续开始
2025-04-24 15:58:59: 状态:游戏结束!
2025-04-24 15:59:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:03: 状态:游戏结束!-继续开始
2025-04-24 15:59:03: 状态:游戏结束!
2025-04-24 15:59:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:07: 状态:游戏结束!-继续开始
2025-04-24 15:59:07: 状态:游戏结束!
2025-04-24 15:59:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:11: 状态:游戏结束!-继续开始
2025-04-24 15:59:11: 状态:游戏结束!
2025-04-24 15:59:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:15: 状态:游戏结束!-继续开始
2025-04-24 15:59:15: 状态:游戏结束!
2025-04-24 15:59:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:19: 状态:游戏结束!-继续开始
2025-04-24 15:59:19: 状态:游戏结束!
2025-04-24 15:59:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:24: 状态:游戏结束!-继续开始
2025-04-24 15:59:24: 状态:游戏结束!
2025-04-24 15:59:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:28: 状态:游戏结束!-继续开始
2025-04-24 15:59:28: 状态:游戏结束!
2025-04-24 15:59:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:32: 状态:游戏结束!-继续开始
2025-04-24 15:59:32: 状态:游戏结束!
2025-04-24 15:59:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:36: 状态:游戏结束!-继续开始
2025-04-24 15:59:36: 状态:游戏结束!
2025-04-24 15:59:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:40: 状态:游戏结束!-继续开始
2025-04-24 15:59:40: 状态:游戏结束!
2025-04-24 15:59:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:44: 状态:游戏结束!-继续开始
2025-04-24 15:59:44: 状态:游戏结束!
2025-04-24 15:59:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:48: 状态:游戏结束!-继续开始
2025-04-24 15:59:48: 状态:游戏结束!
2025-04-24 15:59:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:52: 状态:游戏结束!-继续开始
2025-04-24 15:59:52: 状态:游戏结束!
2025-04-24 15:59:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 15:59:56: 状态:游戏结束!-继续开始
2025-04-24 15:59:56: 状态:游戏结束!
2025-04-24 15:59:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:00: 状态:游戏结束!-继续开始
2025-04-24 16:00:00: 状态:游戏结束!
2025-04-24 16:00:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:05: 状态:游戏结束!-继续开始
2025-04-24 16:00:05: 状态:游戏结束!
2025-04-24 16:00:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:09: 状态:游戏结束!-继续开始
2025-04-24 16:00:09: 状态:游戏结束!
2025-04-24 16:00:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:13: 状态:游戏结束!-继续开始
2025-04-24 16:00:13: 状态:游戏结束!
2025-04-24 16:00:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:17: 状态:游戏结束!-继续开始
2025-04-24 16:00:17: 状态:游戏结束!
2025-04-24 16:00:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:21: 状态:游戏结束!-继续开始
2025-04-24 16:00:21: 状态:游戏结束!
2025-04-24 16:00:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:25: 状态:游戏结束!-继续开始
2025-04-24 16:00:25: 状态:游戏结束!
2025-04-24 16:00:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:29: 状态:游戏结束!-继续开始
2025-04-24 16:00:29: 状态:游戏结束!
2025-04-24 16:00:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:33: 状态:游戏结束!-继续开始
2025-04-24 16:00:33: 状态:游戏结束!
2025-04-24 16:00:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:38: 状态:游戏结束!-继续开始
2025-04-24 16:00:38: 状态:游戏结束!
2025-04-24 16:00:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:42: 状态:游戏结束!-继续开始
2025-04-24 16:00:42: 状态:游戏结束!
2025-04-24 16:00:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:46: 状态:游戏结束!-继续开始
2025-04-24 16:00:46: 状态:游戏结束!
2025-04-24 16:00:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:50: 状态:游戏结束!-继续开始
2025-04-24 16:00:50: 状态:游戏结束!
2025-04-24 16:00:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:54: 状态:游戏结束!-继续开始
2025-04-24 16:00:54: 状态:游戏结束!
2025-04-24 16:00:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:00:58: 状态:游戏结束!-继续开始
2025-04-24 16:00:58: 状态:游戏结束!
2025-04-24 16:01:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:02: 状态:游戏结束!-继续开始
2025-04-24 16:01:02: 状态:游戏结束!
2025-04-24 16:01:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:06: 状态:游戏结束!-继续开始
2025-04-24 16:01:06: 状态:游戏结束!
2025-04-24 16:01:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:10: 状态:游戏结束!-继续开始
2025-04-24 16:01:10: 状态:游戏结束!
2025-04-24 16:01:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:15: 状态:游戏结束!-继续开始
2025-04-24 16:01:15: 状态:游戏结束!
2025-04-24 16:01:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:19: 状态:游戏结束!-继续开始
2025-04-24 16:01:19: 状态:游戏结束!
2025-04-24 16:01:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:23: 状态:游戏结束!-继续开始
2025-04-24 16:01:23: 状态:游戏结束!
2025-04-24 16:01:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:27: 状态:游戏结束!-继续开始
2025-04-24 16:01:27: 状态:游戏结束!
2025-04-24 16:01:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:31: 状态:游戏结束!-继续开始
2025-04-24 16:01:31: 状态:游戏结束!
2025-04-24 16:01:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:35: 状态:游戏结束!-继续开始
2025-04-24 16:01:35: 状态:游戏结束!
2025-04-24 16:01:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:39: 状态:游戏结束!-继续开始
2025-04-24 16:01:39: 状态:游戏结束!
2025-04-24 16:01:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:43: 状态:游戏结束!-继续开始
2025-04-24 16:01:43: 状态:游戏结束!
2025-04-24 16:01:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:47: 状态:游戏结束!-继续开始
2025-04-24 16:01:47: 状态:游戏结束!
2025-04-24 16:01:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:52: 状态:游戏结束!-继续开始
2025-04-24 16:01:52: 状态:游戏结束!
2025-04-24 16:01:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:01:56: 状态:游戏结束!-继续开始
2025-04-24 16:01:56: 状态:游戏结束!
2025-04-24 16:01:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:00: 状态:游戏结束!-继续开始
2025-04-24 16:02:00: 状态:游戏结束!
2025-04-24 16:02:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:04: 状态:游戏结束!-继续开始
2025-04-24 16:02:04: 状态:游戏结束!
2025-04-24 16:02:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:08: 状态:游戏结束!-继续开始
2025-04-24 16:02:08: 状态:游戏结束!
2025-04-24 16:02:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:12: 状态:游戏结束!-继续开始
2025-04-24 16:02:12: 状态:游戏结束!
2025-04-24 16:02:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:16: 状态:游戏结束!-继续开始
2025-04-24 16:02:16: 状态:游戏结束!
2025-04-24 16:02:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:20: 状态:游戏结束!-继续开始
2025-04-24 16:02:20: 状态:游戏结束!
2025-04-24 16:02:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:24: 状态:游戏结束!-继续开始
2025-04-24 16:02:24: 状态:游戏结束!
2025-04-24 16:02:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:29: 状态:游戏结束!-继续开始
2025-04-24 16:02:29: 状态:游戏结束!
2025-04-24 16:02:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:33: 状态:游戏结束!-继续开始
2025-04-24 16:02:33: 状态:游戏结束!
2025-04-24 16:02:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:37: 状态:游戏结束!-继续开始
2025-04-24 16:02:37: 状态:游戏结束!
2025-04-24 16:02:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:41: 状态:游戏结束!-继续开始
2025-04-24 16:02:41: 状态:游戏结束!
2025-04-24 16:02:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:45: 状态:游戏结束!-继续开始
2025-04-24 16:02:45: 状态:游戏结束!
2025-04-24 16:02:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:49: 状态:游戏结束!-继续开始
2025-04-24 16:02:49: 状态:游戏结束!
2025-04-24 16:02:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:53: 状态:游戏结束!-继续开始
2025-04-24 16:02:53: 状态:游戏结束!
2025-04-24 16:02:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:02:57: 状态:游戏结束!-继续开始
2025-04-24 16:02:57: 状态:游戏结束!
2025-04-24 16:03:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:01: 状态:游戏结束!-继续开始
2025-04-24 16:03:01: 状态:游戏结束!
2025-04-24 16:03:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:05: 状态:游戏结束!-继续开始
2025-04-24 16:03:05: 状态:游戏结束!
2025-04-24 16:03:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:10: 状态:游戏结束!-继续开始
2025-04-24 16:03:10: 状态:游戏结束!
2025-04-24 16:03:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:14: 状态:游戏结束!-继续开始
2025-04-24 16:03:14: 状态:游戏结束!
2025-04-24 16:03:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:18: 状态:游戏结束!-继续开始
2025-04-24 16:03:18: 状态:游戏结束!
2025-04-24 16:03:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:22: 状态:游戏结束!-继续开始
2025-04-24 16:03:22: 状态:游戏结束!
2025-04-24 16:03:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:26: 状态:游戏结束!-继续开始
2025-04-24 16:03:26: 状态:游戏结束!
2025-04-24 16:03:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:30: 状态:游戏结束!-继续开始
2025-04-24 16:03:30: 状态:游戏结束!
2025-04-24 16:03:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:34: 状态:游戏结束!-继续开始
2025-04-24 16:03:34: 状态:游戏结束!
2025-04-24 16:03:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:38: 状态:游戏结束!-继续开始
2025-04-24 16:03:38: 状态:游戏结束!
2025-04-24 16:03:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:42: 状态:游戏结束!-继续开始
2025-04-24 16:03:42: 状态:游戏结束!
2025-04-24 16:03:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:47: 状态:游戏结束!-继续开始
2025-04-24 16:03:47: 状态:游戏结束!
2025-04-24 16:03:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:51: 状态:游戏结束!-继续开始
2025-04-24 16:03:51: 状态:游戏结束!
2025-04-24 16:03:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:55: 状态:游戏结束!-继续开始
2025-04-24 16:03:55: 状态:游戏结束!
2025-04-24 16:03:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:03:59: 状态:游戏结束!-继续开始
2025-04-24 16:03:59: 状态:游戏结束!
2025-04-24 16:04:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:03: 状态:游戏结束!-继续开始
2025-04-24 16:04:03: 状态:游戏结束!
2025-04-24 16:04:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:07: 状态:游戏结束!-继续开始
2025-04-24 16:04:07: 状态:游戏结束!
2025-04-24 16:04:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:11: 状态:游戏结束!-继续开始
2025-04-24 16:04:11: 状态:游戏结束!
2025-04-24 16:04:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:15: 状态:游戏结束!-继续开始
2025-04-24 16:04:15: 状态:游戏结束!
2025-04-24 16:04:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:19: 状态:游戏结束!-继续开始
2025-04-24 16:04:19: 状态:游戏结束!
2025-04-24 16:04:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:23: 状态:游戏结束!-继续开始
2025-04-24 16:04:23: 状态:游戏结束!
2025-04-24 16:04:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:28: 状态:游戏结束!-继续开始
2025-04-24 16:04:28: 状态:游戏结束!
2025-04-24 16:04:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:32: 状态:游戏结束!-继续开始
2025-04-24 16:04:32: 状态:游戏结束!
2025-04-24 16:04:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:36: 状态:游戏结束!-继续开始
2025-04-24 16:04:36: 状态:游戏结束!
2025-04-24 16:04:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:40: 状态:游戏结束!-继续开始
2025-04-24 16:04:40: 状态:游戏结束!
2025-04-24 16:04:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:44: 状态:游戏结束!-继续开始
2025-04-24 16:04:44: 状态:游戏结束!
2025-04-24 16:04:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:48: 状态:游戏结束!-继续开始
2025-04-24 16:04:48: 状态:游戏结束!
2025-04-24 16:04:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:52: 状态:游戏结束!-继续开始
2025-04-24 16:04:52: 状态:游戏结束!
2025-04-24 16:04:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:04:56: 状态:游戏结束!-继续开始
2025-04-24 16:04:56: 状态:游戏结束!
2025-04-24 16:04:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:01: 状态:游戏结束!-继续开始
2025-04-24 16:05:01: 状态:游戏结束!
2025-04-24 16:05:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:05: 状态:游戏结束!-继续开始
2025-04-24 16:05:05: 状态:游戏结束!
2025-04-24 16:05:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:09: 状态:游戏结束!-继续开始
2025-04-24 16:05:09: 状态:游戏结束!
2025-04-24 16:05:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:13: 状态:游戏结束!-继续开始
2025-04-24 16:05:13: 状态:游戏结束!
2025-04-24 16:05:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:17: 状态:游戏结束!-继续开始
2025-04-24 16:05:17: 状态:游戏结束!
2025-04-24 16:05:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:21: 状态:游戏结束!-继续开始
2025-04-24 16:05:21: 状态:游戏结束!
2025-04-24 16:05:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:25: 状态:游戏结束!-继续开始
2025-04-24 16:05:25: 状态:游戏结束!
2025-04-24 16:05:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:29: 状态:游戏结束!-继续开始
2025-04-24 16:05:29: 状态:游戏结束!
2025-04-24 16:05:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:33: 状态:游戏结束!-继续开始
2025-04-24 16:05:33: 状态:游戏结束!
2025-04-24 16:05:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:37: 状态:游戏结束!-继续开始
2025-04-24 16:05:37: 状态:游戏结束!
2025-04-24 16:05:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:42: 状态:游戏结束!-继续开始
2025-04-24 16:05:42: 状态:游戏结束!
2025-04-24 16:05:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:46: 状态:游戏结束!-继续开始
2025-04-24 16:05:46: 状态:游戏结束!
2025-04-24 16:05:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:50: 状态:游戏结束!-继续开始
2025-04-24 16:05:50: 状态:游戏结束!
2025-04-24 16:05:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:54: 状态:游戏结束!-继续开始
2025-04-24 16:05:54: 状态:游戏结束!
2025-04-24 16:05:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:05:58: 状态:游戏结束!-继续开始
2025-04-24 16:05:58: 状态:游戏结束!
2025-04-24 16:06:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:02: 状态:游戏结束!-继续开始
2025-04-24 16:06:02: 状态:游戏结束!
2025-04-24 16:06:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:06: 状态:游戏结束!-继续开始
2025-04-24 16:06:06: 状态:游戏结束!
2025-04-24 16:06:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:10: 状态:游戏结束!-继续开始
2025-04-24 16:06:10: 状态:游戏结束!
2025-04-24 16:06:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:14: 状态:游戏结束!-继续开始
2025-04-24 16:06:14: 状态:游戏结束!
2025-04-24 16:06:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:19: 状态:游戏结束!-继续开始
2025-04-24 16:06:19: 状态:游戏结束!
2025-04-24 16:06:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:23: 状态:游戏结束!-继续开始
2025-04-24 16:06:23: 状态:游戏结束!
2025-04-24 16:06:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:27: 状态:游戏结束!-继续开始
2025-04-24 16:06:27: 状态:游戏结束!
2025-04-24 16:06:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:31: 状态:游戏结束!-继续开始
2025-04-24 16:06:31: 状态:游戏结束!
2025-04-24 16:06:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:35: 状态:游戏结束!-继续开始
2025-04-24 16:06:35: 状态:游戏结束!
2025-04-24 16:06:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:39: 状态:游戏结束!-继续开始
2025-04-24 16:06:39: 状态:游戏结束!
2025-04-24 16:06:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:43: 状态:游戏结束!-继续开始
2025-04-24 16:06:43: 状态:游戏结束!
2025-04-24 16:06:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:47: 状态:游戏结束!-继续开始
2025-04-24 16:06:47: 状态:游戏结束!
2025-04-24 16:06:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:51: 状态:游戏结束!-继续开始
2025-04-24 16:06:51: 状态:游戏结束!
2025-04-24 16:06:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:06:56: 状态:游戏结束!-继续开始
2025-04-24 16:06:56: 状态:游戏结束!
2025-04-24 16:06:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:00: 状态:游戏结束!-继续开始
2025-04-24 16:07:00: 状态:游戏结束!
2025-04-24 16:07:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:04: 状态:游戏结束!-继续开始
2025-04-24 16:07:04: 状态:游戏结束!
2025-04-24 16:07:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:08: 状态:游戏结束!-继续开始
2025-04-24 16:07:08: 状态:游戏结束!
2025-04-24 16:07:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:12: 状态:游戏结束!-继续开始
2025-04-24 16:07:12: 状态:游戏结束!
2025-04-24 16:07:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:16: 状态:游戏结束!-继续开始
2025-04-24 16:07:16: 状态:游戏结束!
2025-04-24 16:07:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:20: 状态:游戏结束!-继续开始
2025-04-24 16:07:20: 状态:游戏结束!
2025-04-24 16:07:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:24: 状态:游戏结束!-继续开始
2025-04-24 16:07:24: 状态:游戏结束!
2025-04-24 16:07:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:28: 状态:游戏结束!-继续开始
2025-04-24 16:07:28: 状态:游戏结束!
2025-04-24 16:07:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:33: 状态:游戏结束!-继续开始
2025-04-24 16:07:33: 状态:游戏结束!
2025-04-24 16:07:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:37: 状态:游戏结束!-继续开始
2025-04-24 16:07:37: 状态:游戏结束!
2025-04-24 16:07:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:41: 状态:游戏结束!-继续开始
2025-04-24 16:07:41: 状态:游戏结束!
2025-04-24 16:07:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:45: 状态:游戏结束!-继续开始
2025-04-24 16:07:45: 状态:游戏结束!
2025-04-24 16:07:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:49: 状态:游戏结束!-继续开始
2025-04-24 16:07:49: 状态:游戏结束!
2025-04-24 16:07:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:53: 状态:游戏结束!-继续开始
2025-04-24 16:07:53: 状态:游戏结束!
2025-04-24 16:07:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:07:57: 状态:游戏结束!-继续开始
2025-04-24 16:07:57: 状态:游戏结束!
2025-04-24 16:08:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:01: 状态:游戏结束!-继续开始
2025-04-24 16:08:01: 状态:游戏结束!
2025-04-24 16:08:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:05: 状态:游戏结束!-继续开始
2025-04-24 16:08:05: 状态:游戏结束!
2025-04-24 16:08:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:09: 状态:游戏结束!-继续开始
2025-04-24 16:08:09: 状态:游戏结束!
2025-04-24 16:08:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:14: 状态:游戏结束!-继续开始
2025-04-24 16:08:14: 状态:游戏结束!
2025-04-24 16:08:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:18: 状态:游戏结束!-继续开始
2025-04-24 16:08:18: 状态:游戏结束!
2025-04-24 16:08:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:22: 状态:游戏结束!-继续开始
2025-04-24 16:08:22: 状态:游戏结束!
2025-04-24 16:08:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:26: 状态:游戏结束!-继续开始
2025-04-24 16:08:26: 状态:游戏结束!
2025-04-24 16:08:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:30: 状态:游戏结束!-继续开始
2025-04-24 16:08:30: 状态:游戏结束!
2025-04-24 16:08:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:34: 状态:游戏结束!-继续开始
2025-04-24 16:08:34: 状态:游戏结束!
2025-04-24 16:08:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:38: 状态:游戏结束!-继续开始
2025-04-24 16:08:38: 状态:游戏结束!
2025-04-24 16:08:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:42: 状态:游戏结束!-继续开始
2025-04-24 16:08:42: 状态:游戏结束!
2025-04-24 16:08:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:46: 状态:游戏结束!-继续开始
2025-04-24 16:08:46: 状态:游戏结束!
2025-04-24 16:08:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:51: 状态:游戏结束!-继续开始
2025-04-24 16:08:51: 状态:游戏结束!
2025-04-24 16:08:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:55: 状态:游戏结束!-继续开始
2025-04-24 16:08:55: 状态:游戏结束!
2025-04-24 16:08:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:08:59: 状态:游戏结束!-继续开始
2025-04-24 16:08:59: 状态:游戏结束!
2025-04-24 16:09:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:03: 状态:游戏结束!-继续开始
2025-04-24 16:09:03: 状态:游戏结束!
2025-04-24 16:09:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:07: 状态:游戏结束!-继续开始
2025-04-24 16:09:07: 状态:游戏结束!
2025-04-24 16:09:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:11: 状态:游戏结束!-继续开始
2025-04-24 16:09:11: 状态:游戏结束!
2025-04-24 16:09:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:15: 状态:游戏结束!-继续开始
2025-04-24 16:09:15: 状态:游戏结束!
2025-04-24 16:09:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:19: 状态:游戏结束!-继续开始
2025-04-24 16:09:19: 状态:游戏结束!
2025-04-24 16:09:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:23: 状态:游戏结束!-继续开始
2025-04-24 16:09:23: 状态:游戏结束!
2025-04-24 16:09:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:28: 状态:游戏结束!-继续开始
2025-04-24 16:09:28: 状态:游戏结束!
2025-04-24 16:09:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:32: 状态:游戏结束!-继续开始
2025-04-24 16:09:32: 状态:游戏结束!
2025-04-24 16:09:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:36: 状态:游戏结束!-继续开始
2025-04-24 16:09:36: 状态:游戏结束!
2025-04-24 16:09:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:40: 状态:游戏结束!-继续开始
2025-04-24 16:09:40: 状态:游戏结束!
2025-04-24 16:09:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:44: 状态:游戏结束!-继续开始
2025-04-24 16:09:44: 状态:游戏结束!
2025-04-24 16:09:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:48: 状态:游戏结束!-继续开始
2025-04-24 16:09:48: 状态:游戏结束!
2025-04-24 16:09:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:52: 状态:游戏结束!-继续开始
2025-04-24 16:09:52: 状态:游戏结束!
2025-04-24 16:09:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:09:56: 状态:游戏结束!-继续开始
2025-04-24 16:09:56: 状态:游戏结束!
2025-04-24 16:10:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:01: 状态:游戏结束!-继续开始
2025-04-24 16:10:01: 状态:游戏结束!
2025-04-24 16:10:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:05: 状态:游戏结束!-继续开始
2025-04-24 16:10:05: 状态:游戏结束!
2025-04-24 16:10:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:09: 状态:游戏结束!-继续开始
2025-04-24 16:10:09: 状态:游戏结束!
2025-04-24 16:10:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:13: 状态:游戏结束!-继续开始
2025-04-24 16:10:13: 状态:游戏结束!
2025-04-24 16:10:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:17: 状态:游戏结束!-继续开始
2025-04-24 16:10:17: 状态:游戏结束!
2025-04-24 16:10:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:21: 状态:游戏结束!-继续开始
2025-04-24 16:10:21: 状态:游戏结束!
2025-04-24 16:10:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:25: 状态:游戏结束!-继续开始
2025-04-24 16:10:25: 状态:游戏结束!
2025-04-24 16:10:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:29: 状态:游戏结束!-继续开始
2025-04-24 16:10:29: 状态:游戏结束!
2025-04-24 16:10:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:33: 状态:游戏结束!-继续开始
2025-04-24 16:10:33: 状态:游戏结束!
2025-04-24 16:10:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:37: 状态:游戏结束!-继续开始
2025-04-24 16:10:37: 状态:游戏结束!
2025-04-24 16:10:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:42: 状态:游戏结束!-继续开始
2025-04-24 16:10:42: 状态:游戏结束!
2025-04-24 16:10:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:46: 状态:游戏结束!-继续开始
2025-04-24 16:10:46: 状态:游戏结束!
2025-04-24 16:10:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:50: 状态:游戏结束!-继续开始
2025-04-24 16:10:50: 状态:游戏结束!
2025-04-24 16:10:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:54: 状态:游戏结束!-继续开始
2025-04-24 16:10:54: 状态:游戏结束!
2025-04-24 16:10:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:10:58: 状态:游戏结束!-继续开始
2025-04-24 16:10:58: 状态:游戏结束!
2025-04-24 16:11:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:02: 状态:游戏结束!-继续开始
2025-04-24 16:11:02: 状态:游戏结束!
2025-04-24 16:11:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:06: 状态:游戏结束!-继续开始
2025-04-24 16:11:06: 状态:游戏结束!
2025-04-24 16:11:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:10: 状态:游戏结束!-继续开始
2025-04-24 16:11:10: 状态:游戏结束!
2025-04-24 16:11:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:14: 状态:游戏结束!-继续开始
2025-04-24 16:11:14: 状态:游戏结束!
2025-04-24 16:11:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:19: 状态:游戏结束!-继续开始
2025-04-24 16:11:19: 状态:游戏结束!
2025-04-24 16:11:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:23: 状态:游戏结束!-继续开始
2025-04-24 16:11:23: 状态:游戏结束!
2025-04-24 16:11:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-24 16:11:27: 状态:游戏结束!-继续开始
2025-04-24 16:11:27: 状态:游戏结束!
2025-04-24 16:11:42: 窗口隐藏3
2025-04-24 16:11:42: 无法获取游戏会话信息，跳过点赞
2025-04-24 16:11:42: 窗口隐藏2
2025-04-24 16:11:44: 状态:游戏结束!-继续开始
2025-04-24 16:11:44: 状态:游戏结束!
2025-04-24 16:11:44: 检测到房间!
2025-04-24 16:11:51: 窗口隐藏1获取英雄ID:0
2025-04-24 16:11:52: 进入选择英雄界面！
2025-04-24 16:11:59: 获取英雄ID:42-上次选英雄ID:0
2025-04-24 16:11:59: 正在为Corki选英雄
2025-04-24 16:11:59: 进入选择英雄界面！
2025-04-24 16:12:00: 获取英雄ID:236-上次选英雄ID:42
2025-04-24 16:12:00: 正在为Lucian选英雄
2025-04-24 16:12:00: 进入选择英雄界面！
2025-04-24 16:12:00: 获取英雄ID:42-上次选英雄ID:236
2025-04-24 16:12:00: 正在为Corki选英雄
2025-04-24 16:12:00: 进入选择英雄界面！
2025-04-24 16:12:01: 获取英雄ID:236-上次选英雄ID:42
2025-04-24 16:12:01: 正在为Lucian选英雄
2025-04-24 16:12:01: 进入选择英雄界面！
2025-04-24 16:12:01: 获取英雄ID:42-上次选英雄ID:236
2025-04-24 16:12:01: 正在为Corki选英雄
2025-04-24 16:12:01: 进入选择英雄界面！
2025-04-24 16:12:03: 获取英雄ID:92-上次选英雄ID:42
2025-04-24 16:12:03: 正在为Riven选英雄
2025-04-24 16:12:03: 进入选择英雄界面！
2025-04-24 16:12:17: 窗口隐藏2
2025-04-24 16:12:19: 对局正在进行中!
2025-04-24 16:12:19: 窗口隐藏95
2025-04-24 16:12:20: 对局正在进行中!
2025-04-24 16:13:22: 大厅等待中
2025-04-24 18:46:59: 检测到房间!
2025-04-24 19:06:18: 已更新主程序ES文件, MD5: d7a04aa5488282282ccd6836295ff9f2
2025-04-24 19:06:18: 已更新支持图Img文件, MD5: 3f5dd9b4112f89bd14c6be1557f603f0
2025-04-24 19:06:18: 已更新UpdateNotice文件, MD5: e29a4bd8d4c24435787ccf7129478d10
2025-04-24 19:06:18: 检测到房间!
2025-04-24 19:06:18: 已更新核心DATA1文件: 0012386a85cc28f59ea688c359fd21d2, MD5: 03e7a1026f07fcf9d438f9bfdef18e96
2025-04-24 19:06:18: 已更新脚本DATA2文件: 0012386a85cc28f59ea688c359fd21d2, MD5: 4742665639bade09591dbc341de7d153
2025-04-24 19:06:18: 检测到房间!
2025-04-24 19:06:18: 已更新核心DATA1文件: 6c8a62586b867050d365a6d61307b6f4, MD5: 03e7a1026f07fcf9d438f9bfdef18e96
2025-04-24 19:06:18: 已更新脚本DATA2文件: 6c8a62586b867050d365a6d61307b6f4, MD5: 8d8d5e6886547122b26bffe0f51fc8dd
2025-04-24 19:06:18: 检测到房间!
2025-04-24 19:06:18: 已更新核心DATA1文件: a8600d3910beb62131dbf66ca0749c50, MD5: 03e7a1026f07fcf9d438f9bfdef18e96
2025-04-24 19:06:18: 检测到房间!
2025-04-24 19:06:18: 已更新脚本DATA2文件: a8600d3910beb62131dbf66ca0749c50, MD5: 8d8d5e6886547122b26bffe0f51fc8dd
2025-04-24 19:06:18: 检测到房间!
2025-04-24 19:06:19: 已更新核心DATA1文件: daef65b2e1e9dec361e54cb4abe16f4c, MD5: b052a1715037c4100171c1e13069f216
2025-04-24 19:06:19: 已更新脚本DATA2文件: daef65b2e1e9dec361e54cb4abe16f4c, MD5: f42aa020f273b38d25d4b01b92eed46e
2025-04-24 19:06:19: 已更新vgc文件: 5531e1cf3c9eb72f4a632bb8ff886117, MD5: 0df621697c519bf29911c522c3eb342e
2025-04-24 19:06:19: 检测到房间!
2025-04-24 19:06:19: 已更新vgc文件: a032a9769a55a8ff984237e30e65424e, MD5: c6ab0c5fdb744eb47fba052f451fbdb2
2025-04-24 19:06:19: 检测到房间!
2025-04-24 19:42:26: 窗口隐藏3

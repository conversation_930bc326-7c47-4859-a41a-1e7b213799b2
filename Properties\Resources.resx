﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="1111" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\6b40efe83aa62b02819f43b5eb25cdcc.jpg;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cert" xml:space="preserve">
    <value>@echo off
%1 %2
mshta vbscript:createobject("shell.application").shellexecute("%~s0","goto runas","goto notadmin","runas",1)(window.close)&amp;&amp;exit

cd /d %~dp0cert
pause
setlocal enabledelayedexpansion
certmgr.exe -add -c root.spc -s -r localMachine root&gt;&gt;%temp%\config.tmp
for /f "delims=" %%a in (%temp%\config.tmp) do (set var=%%a)
echo %var% | find "CertMgr Failed" &gt; NUL &amp;&amp; goto no
echo %var% | find "CertMgr Succeeded" &gt; NUL &amp;&amp; goto yes
del /f /q %temp%\config.tmp
 
:no
del /f /s %temp%\your.app.name.key
cls
mshta vbscript:msgbox("证书导入失败，请尝试重新关闭杀毒软件并以管理员权限运行此脚本！",64+4096,"证书操作")(window.close)
exit
 
:yes
echo CertMgr Succeeded&gt;&gt;%temp%\your.app.name.key
//mshta vbscript:msgbox("证书导入成功！",64+4096,"证书操作")(window.close)
pause</value>
  </data>
  <data name="Create5v5JSON" xml:space="preserve">
    <value>{
    "customGameLobby":{
        "configuration":{
            "gameMode":"PRACTICETOOL",
            "gameMutator":"",
            "gameServerRegion":"",
            "mapId":11,
            "mutators":{
                "id":1
            },
            "spectatorPolicy":"AllAllowed",
            "teamSize":[teamSize]
        },
        "lobbyName":"[lobbyName]",
        "lobbyPassword":null
    },
    "isCustom":true
}</value>
  </data>
  <data name="LoginJson" xml:space="preserve">
    <value>{
 "language" : zh_TW,
 "password" : [password],
 "persistLogin" : false,
 "region" : "",
 "type" : "auth",
 "username" : [username]
}</value>
  </data>
  <data name="LOL内容" xml:space="preserve">
    <value>auto_patching_enabled_by_player: false
dependencies:
    Direct X 9:
        hash: "d0d3fa05f9a44f3a4c51fdbba45bff137fdfe30d800ca67c1db028aa1d32c9e6"
        phase: "Succeeded"
        version: "1.0.0"
    vanguard: true
locale_data:
    available_locales:
    - "ar_AE"
    - "cs_CZ"
    - "de_DE"
    - "el_GR"
    - "en_AU"
    - "en_GB"
    - "en_PH"
    - "en_SG"
    - "en_US"
    - "es_AR"
    - "es_ES"
    - "es_MX"
    - "fr_FR"
    - "hu_HU"
    - "it_IT"
    - "ja_JP"
    - "ko_KR"
    - "pl_PL"
    - "pt_BR"
    - "ro_RO"
    - "ru_RU"
    - "th_TH"
    - "tr_TR"
    - "vi_VN"
    - "zh_MY"
    - "zh_TW"
    default_locale: "zh_TW"
patching_policy: "manual"
patchline_patching_ask_policy: "ask"
product_install_full_path: "[lolPath]"
product_install_root: "[parentDirectory]"
settings:
    create_shortcut: false
    create_uninstall_key: true
    locale: "zh_TW"
shortcut_name: "\u82F1\u96C4\u806F\u76DF.lnk"
should_repair: false</value>
  </data>
  <data name="sk" xml:space="preserve">
    <value>{"availability":"offline"}</value>
  </data>
  <data name="_2222" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\2222.jpg;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_6666" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\6666.jpg;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="人机一般" xml:space="preserve">
    <value>{"queueId":850}</value>
  </data>
  <data name="人机入门" xml:space="preserve">
    <value>{"queueId":830}</value>
  </data>
  <data name="人机新手" xml:space="preserve">
    <value>{"queueId":840}</value>
  </data>
  <data name="保存配置" xml:space="preserve">
    <value>sc create ltsdrv binpath= "[当前]\MapSave.sys" type= kernel start= demand &amp; sc start ltsdrv &amp; sc stop ltsdrv&amp; sc delete ltsdrv</value>
  </data>
  <data name="修Hosts" xml:space="preserve">
    <value># Copyright (c) 1993-2009 Microsoft Corp.
#
# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
#
# This file contains the mappings of IP addresses to host names. Each
# entry should be kept on an individual line. The IP address should
# be placed in the first column followed by the corresponding host name.
# The IP address and the host name should be separated by at least one
# space.
#
# Additionally, comments (such as these) may be inserted on individual
# lines or following the machine name denoted by a '#' symbol.
#
# For example:
#
#      ************     rhino.acme.com          # source server
#       ***********     x.acme.com              # x client host

# localhost name resolution is handled within DNS itself.
#	127.0.0.1       localhost
#	::1             localhost</value>
  </data>
  <data name="关闭" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\关闭.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="区服" xml:space="preserve">
    <value>install:
    globals:
        locale: "zh_CN"
        region: {区服}
    lifecycle:
        enable_run_in_background: false
    riot-client-app-command:
        upgraded: true
    rso-auth:
        install-identifier: ""
    telemetry:
        installation_id: ""
        singular_api_key: ""
        singular_customuserid_url: ""
        singular_event_url: ""
        singular_launch_url: ""
        singular_v1_enabled: false</value>
  </data>
  <data name="单个选英雄" xml:space="preserve">
    <value>{
  "championId": [championId]
}</value>
  </data>
  <data name="召唤师技能" xml:space="preserve">
    <value>{
  "spell1Id": [召唤师技能1],
  "spell2Id": [召唤师技能2]
}</value>
  </data>
  <data name="推荐装备" xml:space="preserve">
    <value>{
  "accountId": [召唤师],
  "itemSets": [
    {
      "associatedChampions": [
        [英雄ID]
      ],
      "associatedMaps": [],
      "blocks": [
        {
          "hideIfSummonerSpell": "",
          "items": [
            {
              "count": 1,
              "id": "[初始装备1]"
            }, {
              "count": 1,
              "id": "[初始装备2]"
            }, {
              "count": 1,
              "id": "[初始装备3]"
            }, {
              "count": 1,
              "id": "[初始装备4]"
            }, {
              "count": 1,
              "id": "[初始装备5]"
            }, {
              "count": 1,
              "id": "[初始装备6]"
            }, {
              "count": 1,
              "id": "[初始装备7]"
            }, {
              "count": 1,
              "id": "[初始装备8]"
            }, {
              "count": 1,
              "id": "[初始装备9]"
            }, {
              "count": 1,
              "id": "[初始装备10]"
            }, {
              "count": 1,
              "id": "[初始装备11]"
            }, {
              "count": 1,
              "id": "[初始装备12]"
            }, {
              "count": 1,
              "id": "[初始装备13]"
            }, {
              "count": 1,
              "id": "[初始装备14]"
            }, {
              "count": 1,
              "id": "[初始装备15]"
            }
          ],
          "showIfSummonerSpell": "",
          "type": "Recommended"
        },{
          "hideIfSummonerSpell": "",
          "items": [
            {
              "count": 1,
              "id": "3026"
            }, {
              "count": 1,
              "id": "3143"
            }, {
              "count": 1,
              "id": "3083"
            }, {
              "count": 1,
              "id": "3065"
            }, {
              "count": 1,
              "id": "3075"
            }, {
              "count": 1,
              "id": "3068"
            }, {
              "count": 1,
              "id": "3742"
            }, {
              "count": 1,
              "id": "3194"
            }, {
              "count": 1,
              "id": "3001"
            }, {
              "count": 1,
              "id": "3139"
            }
          ],
          "showIfSummonerSpell": "",
          "type": "Defensive"
        }
      ],
      "map": "any",
      "mode": "any",
      "preferredItemSlots": [],
      "sortrank": 0,
      "startedFrom": "blank",
      "title": "[推荐名称]",
      "type": "custom",
      "uid": "GO"
    }
  ],
  "timestamp": 0
}</value>
  </data>
  <data name="推荐装备00" xml:space="preserve">
    <value>{
  "accountId": [召唤师],
  "itemSets": [
    {
      "associatedChampions": [
        [英雄ID]
      ],
      "associatedMaps": [],
      "blocks": [
        {
          "hideIfSummonerSpell": "",
          "items": [
            {
              "count": 1,
              "id": "[初始装备1]"
            }, {
              "count": 1,
              "id": "[初始装备2]"
            }, {
              "count": 1,
              "id": "[初始装备3]"
            }, {
              "count": 1,
              "id": "[初始装备4]"
            }, {
              "count": 1,
              "id": "[初始装备5]"
            }, {
              "count": 1,
              "id": "[初始装备6]"
            }, {
              "count": 1,
              "id": "[初始装备7]"
            }, {
              "count": 1,
              "id": "[初始装备8]"
            }, {
              "count": 1,
              "id": "[初始装备9]"
            }, {
              "count": 1,
              "id": "[初始装备10]"
            }
          ],
          "showIfSummonerSpell": "",
          "type": "Initial"
        },{
          "hideIfSummonerSpell": "",
          "items": [
            {
              "count": 1,
              "id": "[核心装备1]"
            }, {
              "count": 1,
              "id": "[核心装备2]"
            }, {
              "count": 1,
              "id": "[核心装备3]"
            }, {
              "count": 1,
              "id": "[核心装备4]"
            }, {
              "count": 1,
              "id": "[核心装备5]"
            }, {
              "count": 1,
              "id": "[核心装备6]"
            }, {
              "count": 1,
              "id": "[核心装备7]"
            }, {
              "count": 1,
              "id": "[核心装备8]"
            }, {
              "count": 1,
              "id": "[核心装备9]"
            }, {
              "count": 1,
              "id": "[核心装备10]"
            }, {
              "count": 1,
              "id": "[核心装备11]"
            }, {
              "count": 1,
              "id": "[核心装备12]"
            }, {
              "count": 1,
              "id": "[核心装备13]"
            }, {
              "count": 1,
              "id": "[核心装备14]"
            }, {
              "count": 1,
              "id": "[核心装备15]"
            }
          ],
          "showIfSummonerSpell": "",
          "type": "Core Equipment"
        },{
          "hideIfSummonerSpell": "",
          "items": [
            {
              "count": 1,
              "id": "[靴子1]"
            }, {
              "count": 1,
              "id": "[靴子2]"
            }, {
              "count": 1,
              "id": "[靴子3]"
            }
          ],
          "showIfSummonerSpell": "",
          "type": "Boots"
        },{
          "hideIfSummonerSpell": "",
          "items": [
            {
              "count": 1,
              "id": "3026"
            }, {
              "count": 1,
              "id": "3143"
            }, {
              "count": 1,
              "id": "3083"
            }, {
              "count": 1,
              "id": "3065"
            }, {
              "count": 1,
              "id": "3075"
            }, {
              "count": 1,
              "id": "3068"
            }, {
              "count": 1,
              "id": "3742"
            }, {
              "count": 1,
              "id": "3194"
            }, {
              "count": 1,
              "id": "3001"
            }, {
              "count": 1,
              "id": "3139"
            }
          ],
          "showIfSummonerSpell": "",
          "type": "Defensive"
        }
      ],
      "map": "any",
      "mode": "any",
      "preferredItemSlots": [],
      "sortrank": 0,
      "startedFrom": "blank",
      "title": "[推荐名称]",
      "type": "custom",
      "uid": "GO"
    }
  ],
  "timestamp": 0
}</value>
  </data>
  <data name="提示" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\提示.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="提示1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\提示1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="提示2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\提示2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="提示3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\提示3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="测试测试" xml:space="preserve">
    <value>{
  "selectedSkinId": 0,
  "spell1Id": 4,
  "spell2Id": 7,
  "wardSkinId": 0
}</value>
  </data>
  <data name="游戏其他设置" xml:space="preserve">
    <value>{"FloatingText":{"Dodge_Enabled":true,"EnemyPhysicalDamage_Enabled":true,"Experience_Enabled":false,"Gold_Enabled":true,"Heal_Enabled":true,"Invulnerable_Enabled":true,"Level_Enabled":true,"ManaDamage_Enabled":false,"PhysicalDamage_Enabled":true,"QuestReceived_Enabled":true,"Score_Enabled":true,"Special_Enabled":true},"General":{"AutoAcquireTarget":false,"BindSysKeys":false,"CursorOverride":false,"CursorScale":0.5,"EnableAudio":false,"EnableTargetedAttackMove":false,"GameMouseSpeed":10,"HideEyeCandy":false,"OSXMouseAcceleration":false,"PredictMovement":false,"PreferDX9LegacyMode":false,"PreferOpenGLLegacyMode":false,"RelativeTeamColors":true,"ShowCursorLocator":false,"ShowGodray":true,"ShowTurretRangeIndicators":false,"SnapCameraOnRespawn":true,"ThemeMusic":0,"WaitForVerticalSync":false,"WindowMode":2},"HUD":{"AutoDisplayTarget":true,"CameraLockMode":0,"ChatScale":100,"DisableHudSpellClick":false,"DrawHealthBars":true,"EmotePopupUIDisplayMode":0,"EmoteSize":0,"EnableLineMissileVis":true,"EternalsMilestoneDisplayMode":0,"FlashScreenWhenDamaged":true,"FlashScreenWhenStunned":true,"FlipMiniMap":false,"GlobalScale":0.5,"KeyboardScrollSpeed":0.5,"MapScrollSpeed":0.5,"MiddleClickDragScrollEnabled":false,"MinimapMoveSelf":true,"MinimapScale":1.0,"MirroredScoreboard":false,"NumericCooldownFormat":1,"ObjectTooltips":true,"ScrollSmoothingEnabled":false,"ShowAllChannelChat":false,"ShowAlliedChat":true,"ShowAttackRadius":true,"ShowNeutralCamps":true,"ShowSpellCosts":false,"ShowSummonerNames":true,"ShowSummonerNamesInScoreboard":true,"ShowTeamFramesOnLeft":false,"ShowTimestamps":false,"SmartCastOnKeyRelease":false,"SmartCastWithIndicator_CastWhenNewSpellSelected":false},"LossOfControl":{"LossOfControlEnabled":true,"ShowSlows":false},"Performance":{"EnableHUDAnimations":true},"Voice":{"ShowVoiceChatHalos":true,"ShowVoicePanelWithScoreboard":true},"Volume":{"AmbienceMute":false,"AmbienceVolume":0.75,"AnnouncerMute":false,"AnnouncerVolume":0.75,"MasterMute":false,"MasterVolume":1.0,"MusicMute":false,"MusicVolume":0.75,"PingsMute":false,"PingsVolume":0.75,"SfxMute":false,"SfxVolume":0.75,"VoiceMute":false,"VoiceVolume":0.75}}</value>
  </data>
  <data name="游戏热键设置" xml:space="preserve">
    <value>{"GameEvents":{"evntPlayerPing":"[Alt][Button 1]","evntPlayerPingCursor":"[&lt;Unbound&gt;]","evntPlayerPingCursorDanger":"[&lt;Unbound&gt;]","evntPlayerPingDanger":"[Ctrl][Button 1]","evtCameraLockToggle":"[`]","evtCameraSnap":"[&lt;Unbound&gt;]","evtCastAvatarSpell1":"[d]","evtCastAvatarSpell2":"[f]","evtCastSpell1":"[q]","evtCastSpell2":"[w]","evtCastSpell3":"[e]","evtCastSpell4":"[r]","evtChampMasteryDisplay":"[Ctrl][6]","evtChampionOnly":"[&lt;Unbound&gt;]","evtChatHistory":"[z]","evtDragScrollLock":"[&lt;Unbound&gt;]","evtDrawHud":"[&lt;Unbound&gt;]","evtEmoteDance":"[Ctrl][3]","evtEmoteJoke":"[Ctrl][1]","evtEmoteLaugh":"[Ctrl][4]","evtEmoteTaunt":"[Ctrl][2]","evtEmoteToggle":"[Ctrl][5]","evtLevelSpell1":"[Alt][1]","evtLevelSpell2":"[Alt][2]","evtLevelSpell3":"[Alt][3]","evtLevelSpell4":"[Alt][4]","evtNormalCastAvatarSpell1":"null","evtNormalCastAvatarSpell2":"null","evtNormalCastItem1":"null","evtNormalCastItem2":"null","evtNormalCastItem3":"null","evtNormalCastItem4":"null","evtNormalCastItem5":"null","evtNormalCastItem6":"null","evtNormalCastSpell1":"null","evtNormalCastSpell2":"null","evtNormalCastSpell3":"null","evtNormalCastSpell4":"null","evtNormalCastVisionItem":"null","evtOnUIMouse4Pan":"[Button 3]","evtOpenShop":"[p]","evtPetMoveClick":"[Alt] [Button 2], [Ctrl] [Button 2]","evtPlayerAttackMove":"[a],[x]","evtPlayerAttackMoveClick":"[Shift] [Button 2]","evtPlayerAttackOnlyClick":"[&lt;Unbound&gt;]","evtPlayerHoldPosition":"[j]","evtPlayerMoveClick":"[Button 2]","evtPlayerPingAreaIsWarded":"[h]","evtPlayerPingComeHere":"[&lt;Unbound&gt;]","evtPlayerPingMIA":"[&lt;Unbound&gt;]","evtPlayerPingOMW":"[&lt;Unbound&gt;]","evtPlayerPingRadialDanger":"[&lt;Unbound&gt;]","evtPlayerStopPosition":"[&lt;Unbound&gt;]","evtPushToTalk":"[&lt;Unbound&gt;]","evtRadialEmoteInstantOpen":"[t]","evtRadialEmoteOpen":"null","evtRadialEmotePlaySlot0":"null","evtRadialEmotePlaySlot1":"null","evtRadialEmotePlaySlot2":"null","evtRadialEmotePlaySlot3":"null","evtRadialEmotePlaySlot4":"null","evtScrollDown":"[Down Arrow]","evtScrollLeft":"[Left Arrow]","evtScrollRight":"[Right Arrow]","evtScrollUp":"[Up Arrow]","evtSelectAlly1":"[F2]","evtSelectAlly2":"[F3]","evtSelectAlly3":"[F4]","evtSelectAlly4":"[F5]","evtSelectSelf":"[F1]","evtSelfCastAvatarSpell1":"[Alt][d],","evtSelfCastAvatarSpell2":"[Alt][f],","evtSelfCastItem1":"[&lt;Unbound&gt;]","evtSelfCastItem2":"[&lt;Unbound&gt;]","evtSelfCastItem3":"[&lt;Unbound&gt;]","evtSelfCastItem4":"[Alt][5]","evtSelfCastItem5":"[Alt][6]","evtSelfCastItem6":"[Alt][7]","evtSelfCastSpell1":"[Alt][q],","evtSelfCastSpell2":"[Alt][w],","evtSelfCastSpell3":"[Alt][e],","evtSelfCastSpell4":"[Alt][r],","evtSelfCastVisionItem":"[&lt;Unbound&gt;]","evtShowCharacterMenu":"[&lt;Unbound&gt;]","evtShowHealthBars":"[&lt;Unbound&gt;]","evtShowScoreBoard":"[o]","evtShowSummonerNames":"[&lt;Unbound&gt;]","evtShowVoicePanel":"[m]","evtSmartCastAvatarSpell1":"[Shift][d]","evtSmartCastAvatarSpell2":"[Shift][f]","evtSmartCastItem1":"[Shift][1]","evtSmartCastItem2":"[Shift][2]","evtSmartCastItem3":"[Shift][3]","evtSmartCastItem4":"[Shift][5]","evtSmartCastItem5":"[Shift][6]","evtSmartCastItem6":"[Shift][7]","evtSmartCastSpell1":"[Shift][q]","evtSmartCastSpell2":"[Shift][w]","evtSmartCastSpell3":"[Shift][e]","evtSmartCastSpell4":"[Shift][r]","evtSmartCastVisionItem":"[Shift][4]","evtSmartCastWithIndicatorAvatarSpell1":"null","evtSmartCastWithIndicatorAvatarSpell2":"null","evtSmartCastWithIndicatorItem1":"null","evtSmartCastWithIndicatorItem2":"null","evtSmartCastWithIndicatorItem3":"null","evtSmartCastWithIndicatorItem4":"null","evtSmartCastWithIndicatorItem5":"null","evtSmartCastWithIndicatorItem6":"null","evtSmartCastWithIndicatorSpell1":"null","evtSmartCastWithIndicatorSpell2":"null","evtSmartCastWithIndicatorSpell3":"null","evtSmartCastWithIndicatorSpell4":"null","evtSmartCastWithIndicatorVisionItem":"null","evtSmartPlusSelfCastAvatarSpell1":"[&lt;Unbound&gt;]","evtSmartPlusSelfCastAvatarSpell2":"[&lt;Unbound&gt;]","evtSmartPlusSelfCastItem1":"null","evtSmartPlusSelfCastItem2":"null","evtSmartPlusSelfCastItem3":"null","evtSmartPlusSelfCastItem4":"null","evtSmartPlusSelfCastItem5":"null","evtSmartPlusSelfCastItem6":"null","evtSmartPlusSelfCastSpell1":"[&lt;Unbound&gt;]","evtSmartPlusSelfCastSpell2":"[&lt;Unbound&gt;]","evtSmartPlusSelfCastSpell3":"[&lt;Unbound&gt;]","evtSmartPlusSelfCastSpell4":"[&lt;Unbound&gt;]","evtSmartPlusSelfCastVisionItem":"null","evtSmartPlusSelfCastWithIndicatorAvatarSpell1":"null","evtSmartPlusSelfCastWithIndicatorAvatarSpell2":"null","evtSmartPlusSelfCastWithIndicatorItem1":"null","evtSmartPlusSelfCastWithIndicatorItem2":"null","evtSmartPlusSelfCastWithIndicatorItem3":"null","evtSmartPlusSelfCastWithIndicatorItem4":"null","evtSmartPlusSelfCastWithIndicatorItem5":"null","evtSmartPlusSelfCastWithIndicatorItem6":"null","evtSmartPlusSelfCastWithIndicatorSpell1":"null","evtSmartPlusSelfCastWithIndicatorSpell2":"null","evtSmartPlusSelfCastWithIndicatorSpell3":"null","evtSmartPlusSelfCastWithIndicatorSpell4":"null","evtSmartPlusSelfCastWithIndicatorVisionItem":"null","evtSysMenu":"[Esc]","evtToggleMinionHealthBars":"[&lt;Unbound&gt;]","evtUseItem1":"[1]","evtUseItem2":"[2]","evtUseItem3":"[3]","evtUseItem4":"[5]","evtUseItem5":"[6]","evtUseItem6":"[7]","evtUseItem7":"[b]","evtUseVisionItem":"[4]"},"HUDEvents":{"evtHoldShowScoreBoard":"[Tab]","evtToggleDeathRecapShowcase":"[n]","evtToggleFPSAndLatency":"[Ctrl] [f]","evtToggleMouseClip":"[F9]","evtTogglePlayerStats":"[Ctrl][c]"},"Quickbinds":{"evtCastAvatarSpell1smart":true,"evtCastAvatarSpell2smart":true,"evtCastSpell1smart":true,"evtCastSpell2smart":true,"evtCastSpell3smart":true,"evtCastSpell4smart":true,"evtUseItem1smart":true,"evtUseItem2smart":true,"evtUseItem3smart":true,"evtUseItem4smart":true,"evtUseItem5smart":true,"evtUseItem6smart":true,"evtUseVisionItemsmart":false},"ShopEvents":{"evtShopFocusSearch":"[Ctrl] [l], [Ctrl] [Return]","evtShopSwitchTabs":"[Ctrl] [TAB]"}}</value>
  </data>
  <data name="符文" xml:space="preserve">
    <value>&lt;div class="perk-page__item perk-page__item--mark"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?perk-page__item--active"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?perk-page__item--active"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?perk-page__item--active"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?perk-page__item--active"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?&lt;div class="perk-page__item perk-page__item--mark"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?perk-page__item--active"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?perk-page__item--active"&gt;[\s\S]*?&lt;img src="//(.*?)" class="[\s\S]*?&lt;img src="//(.*?)" class="active tip"[\s\S]*?&lt;img src="//(.*?)" class="active tip"[\s\S]*?&lt;img src="//(.*?)" class="active tip[\s\S]*?&lt;strong&gt;(.*?)&lt;/strong&gt;[\s\S]*?&lt;strong&gt;(.*?)&lt;/strong&gt;</value>
  </data>
  <data name="结束点赞" xml:space="preserve">
    <value>{
  "gameId": [游戏ID],
  "honorCategory": "[文本]",
  "summonerId": [召唤师ID]
}</value>
  </data>
  <data name="聊天" xml:space="preserve">
    <value>{
  "body": "[body]",
  "fromId": "",
  "fromPid": "",
  "fromSummonerId": 0,
  "id": "",
  "isHistorical": false,
  "timestamp": "",
  "type": "chat"
}</value>
  </data>
  <data name="聊天11" xml:space="preserve">
    <value>{
  "body": "[body]"
}</value>
  </data>
  <data name="设置召唤师技能1" xml:space="preserve">
    <value>{
  "spell1Id": [召唤师技能]
}</value>
  </data>
  <data name="设置召唤师技能2" xml:space="preserve">
    <value>{
  "spell2Id": [召唤师技能]
}</value>
  </data>
  <data name="语言" xml:space="preserve">
    <value>auto_patching_enabled_by_player: false
dependencies:
    Direct X 9:
        hash: ""
        phase: "Imported"
        version: "1.0.0"
locale_data:
    available_locales:
    - "[语言]"
    default_locale: "[语言]"
patching_policy: "manual"
patchline_patching_ask_policy: "ask"
product_install_full_path: ""
product_install_root: ""
settings:
    create_shortcut: null
    create_uninstall_key: null
    locale: "[语言]"
should_repair: false</value>
  </data>
  <data name="调整符文" xml:space="preserve">
    <value>{
    "autoModifiedSelections": [],
    "current": true,
    "id": 0,
    "isActive": true,
    "isDeletable": true,
    "isEditable": true,
    "isValid": true,
    "lastModified": 1600229550499,
    "name": "[符文名称]",
    "order": 0,
    "primaryStyleId": [符文编号1],
    "selectedPerkIds": [
      [符文编号2],
      [符文编号3],
      [符文编号4],
      [符文编号5],
      [符文编号7],
      [符文编号8],
      [符文编号9],
      [符文编号10],
      [符文编号11]
    ],
    "subStyleId": [符文编号6]
  }</value>
  </data>
  <data name="选英雄" xml:space="preserve">
    <value>{
  "actorCellId": [actorCellId],
  "championId": [championId],
  "completed": [completed],
  "id": [id],
  "isAllyAction": [isAllyAction],
  "type": "[type]"
}</value>
  </data>
  <data name="锁定" xml:space="preserve">
    <value>{"completed":true,"championId":[championId]}</value>
  </data>
  <data name="预选位置" xml:space="preserve">
    <value>{
  "firstPreference": "[首选]",
  "secondPreference": "[备选]"
}</value>
    <comment>TOP-上单，JUNGLE-打野，MIDDLE-中单，BOTTOM-射手，UTILITY-辅助</comment>
  </data>
  <data name="首次地区" xml:space="preserve">
    <value>install:
    globals:
        locale: "{汉化}"
        region: "{区服}"
    lifecycle:
        enable_run_in_background: false
    riot-client-app-command:
        upgraded: true
    rso-auth:
        install-identifier: ""
    telemetry:
        installation_id: ""
        singular_api_key: ""
        singular_customuserid_url: ""
        singular_event_url: ""
        singular_launch_url: ""
        singular_v1_enabled: false</value>
  </data>
  <data name="LOL" xml:space="preserve">
    <value>auto_patching_enabled_by_player: false
dependencies:
    Direct X 9:
        hash: "d0d3fa05f9a44f3a4c51fdbba45bff137fdfe30d800ca67c1db028aa1d32c9e6"
        phase: "Succeeded"
        version: "1.0.0"
    vanguard: true
locale_data:
    available_locales:
    - "ar_AE"
    - "cs_CZ"
    - "de_DE"
    - "el_GR"
    - "en_AU"
    - "en_GB"
    - "en_PH"
    - "en_SG"
    - "en_US"
    - "es_AR"
    - "es_ES"
    - "es_MX"
    - "fr_FR"
    - "hu_HU"
    - "it_IT"
    - "ja_JP"
    - "ko_KR"
    - "pl_PL"
    - "pt_BR"
    - "ro_RO"
    - "ru_RU"
    - "th_TH"
    - "tr_TR"
    - "vi_VN"
    - "zh_MY"
    - "zh_TW"
    default_locale: "zh_TW"
patching_policy: "manual"
patchline_patching_ask_policy: "ask"
product_install_full_path: "[lolPath]"
product_install_root: "[parentDirectory]"
settings:
    create_shortcut: false
    create_uninstall_key: true
    locale: "zh_TW"
shortcut_name: "\u82F1\u96C4\u806F\u76DF.lnk"
should_repair: false</value>
  </data>
  <data name="DATA" xml:space="preserve">
    <value>@echo off
REM 检查是否以管理员身份运行
fltmc &gt;nul 2&gt;&amp;1
if %errorLevel% neq 0 (
    echo 请以管理员身份运行此脚本！
    pause
    exit /b
)

REM 获取当前目录
set "currentDir=%~dp0"

REM 设置游戏安装路径
set "gameInstallPath=[路径]"

REM 复制游戏文件
xcopy /Y "%currentDir%League of Legends.exe" "%gameInstallPath%\Game\League of Legends.exe*"

REM 修改配置文件
if exist "%currentDir%EngineSoul\T.data" (
    powershell -Command "(Get-Content "%currentDir%EngineSoul\T.data") -replace '^GamePath=.*$', 'GamePath=%gameInstallPath%\Game\League of Legends.exe' | Set-Content "%currentDir%EngineSoul\T.data""
) else (
    REM 如果配置文件不存在，直接创建
    echo GamePath=%gameInstallPath%\Game\League of Legends.exe&gt; "%currentDir%EngineSoul\T.data"
)

REM 目标文件夹路径
set "destinationDir=%SystemDrive%\ProgramData\EngineSoul"

REM 判断目标文件夹是否存在
if exist "%destinationDir%" (
    echo 目标文件夹已存在，不进行复制操作。
) else (
    REM 复制文件夹
    xcopy "%currentDir%EngineSoul" "%destinationDir%" /E /I /H /Y
    echo 复制完成。
)

REM 生成 VBScript 用于创建桌面快捷方式
echo Set WshShell = WScript.CreateObject("WScript.Shell") &gt; createDesktopShortcut.vbs
echo strDesktopPath = WshShell.SpecialFolders("Desktop") &gt;&gt; createDesktopShortcut.vbs
REM --- 注意: 下一行假设 %shortcutName% 变量已在外部定义 ---
echo Set oShellLink = WshShell.CreateShortcut(strDesktopPath ^&amp; "\" ^&amp; "%shortcutName%" ^&amp; ".lnk") &gt;&gt; createDesktopShortcut.vbs
echo oShellLink.TargetPath = "%destinationDir%" &gt;&gt; createDesktopShortcut.vbs
echo oShellLink.WorkingDirectory = "%destinationDir%" &gt;&gt; createDesktopShortcut.vbs REM 设置工作目录通常是个好习惯
echo oShellLink.WindowStyle = 1 &gt;&gt; createDesktopShortcut.vbs REM 设置窗口样式 (1=普通, 3=最大化, 7=最小化)
REM echo oShellLink.IconLocation = "%destinationDir%, 0" &gt;&gt; createDesktopShortcut.vbs REM 可选: 设置图标
echo oShellLink.Description = "快捷方式指向 %destinationDir%" &gt;&gt; createDesktopShortcut.vbs REM 可选: 设置描述
echo oShellLink.Save &gt;&gt; createDesktopShortcut.vbs

REM 执行 VBScript 脚本
cscript //nologo createDesktopShortcut.vbs

REM 删除临时 VBScript 脚本
del createDesktopShortcut.vbs

REM 调用DLL函数
rundll32.exe %~dp0ES.dll,T1

copy /Y "%~dp0DATA2" "%destinationDir%\"</value>
  </data>
  <data name="安装vgc" xml:space="preserve">
    <value>@echo off
setlocal enabledelayedexpansion

:: 输出版权信息
echo 【此代码由HuaJin编写，意见反馈请联系QQ：2270035360】
echo.

:: 检查setup.exe文件是否存在，如果不存在则从网络下载
set "setup_file=setup.exe"
set "download_url=https://riot-client.secure.dyn.riotcdn.net/channels/public/rccontent/vanguard/********/setup.exe"

if not exist "!setup_file!" (
    echo "setup.exe" 文件不存在，正在从网络下载...
    powershell -Command "Invoke-WebRequest -Uri '!download_url!' -OutFile '!setup_file!'"

    if errorlevel 1 (
        echo 文件下载失败，请检查网络连接或下载链接。
        exit /b 1
    ) else (
        echo "setup.exe" 文件已成功下载。
    )
) else (
    echo "setup.exe" 文件已存在，继续执行后续操作。
)

:: 定义需要检查和删除的路径
set "paths=C:\ProgramData\Riot Games;C:\Riot Games;C:\Users\<USER>\AppData\Local\Riot Games;C:\Users\<USER>\AppData\Roaming\riot-client-ux"

:: 清理路径
echo 正在检查并删除不再需要的路径...
for %%p in (%paths%) do (
    if exist "%%p" (
        echo 删除 "%%p" ...
        rd /s /q "%%p"
    )
)

:: 免重启加载 Vanguard 驱动
echo 正在加载 Vanguard 驱动...

:: 启动安装程序并等待完成
"%setup_file%" /wait

:: 检查 Vanguard Tray 是否运行
tasklist | findstr /i "vgtray.exe" &gt;nul
if errorlevel 1 (
    if exist "C:\Program Files\Riot Vanguard\vgtray.exe" (
        echo 启动 Vanguard Tray ...
        start "" "C:\Program Files\Riot Vanguard\vgtray.exe"
    )
)

:: 启动 VGC 服务
echo 正在启动 Vanguard 服务...
net start vgc &gt;nul 2&gt;&amp;1
if %errorlevel% neq 0 (
    echo Vanguard 服务启动失败，请检查服务状态。
) else (
    echo Vanguard 服务已成功启动。
)

:: 检查是否已安装反作弊文件
if exist "C:\Program Files\Riot Vanguard\vgc.exe" (
    goto end
)

:: 提示用户
echo 本机没有安装反作弊文件，请查看说明文件。
start "" 使用说明.txt

:end
echo 执行完毕。
exit /b</value>
  </data>
</root>
﻿using MSTSCLib;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static WindowsFormsApp1.Util;

namespace WindowsFormsApp1
{
    public partial class RemoteFrom : Form
    {

        private string myServer = "";
        private string MyUserName = "";
        private string MyPassword = "";
        private int Myport = 0;
        private bool Gxong = false;
        public RemoteFrom(string Server, string UserName, string Password, int port, int x, int y,bool gongxi)
        {


            InitializeComponent();
            Myport = port;
            myServer = Server;
            MyUserName = UserName;
            MyPassword = Password;

            this.Size = new Size(x + 25, y + 50);
            rdp.Size = new Size(x, y);
            Gxong = gongxi;
            this.Text = Server;
       

        }
        public RemoteFrom()
        {
            InitializeComponent();


        }


        private string key = "nichoubi";
        private void RemoteFrom_Load(object sender, EventArgs e)
        {


                this.Location= new Point(0, 0);
            rdp.Server = myServer;
            IMsRdpClientAdvancedSettings7 settings =
    (IMsRdpClientAdvancedSettings7)rdp.AdvancedSettings;
            rdp.AdvancedSettings2.RedirectDrives = Gxong;


            settings.RDPPort = Myport;

            rdp.UserName = MyUserName;
            IMsTscNonScriptable secured = (IMsTscNonScriptable)rdp.GetOcx();
            secured.ClearTextPassword = Util.XXTEA2.Decrypt(MyPassword, key);
            rdp.Connect();
        }

       
    }
}

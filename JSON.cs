﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WindowsFormsApp1
{
    public class JSON
    {
        /// <summary>
        /// 快速解析[JObject] 使用方式 对象["gameName"].ToString() 和精易模块解析一样 数组对象[整数][字符串].ToString()!
        /// 参数开头类型{  }
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static JObject Jiexun(string args)
        {

            try
            {

                return (JObject)JsonConvert.DeserializeObject(args);


            }
            catch (Exception)
            {
                return new JObject();
            }
        }
        /// <summary>
        /// 快速解析[JArray] 使用方式 对象["gameName"].ToString() 和精易模块解析一样 数组对象[整数][字符串].ToString()!
        /// 参数开头类型{ [ ] }
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static JArray Jiexun_1(string args)
        {

            try
            {

                return (JArray)JsonConvert.DeserializeObject(args);


            }
            catch (Exception)
            {
                return new JArray();
            }
            
        }

    }

    

}

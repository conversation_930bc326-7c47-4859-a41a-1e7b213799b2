﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;
using MythCapture.Properties;
using System.Net.NetworkInformation;
using Microsoft.Win32;

namespace WindowsFormsApp1
{
    static class Program
    {
        //开启控制台
        [DllImport("kernel32.dll")]
        public static extern Boolean AllocConsole();

        [STAThread]

        /// 该函数设置由不同线程产生的窗口的显示状态  
        /// </summary>  
        /// <param name="hWnd">窗口句柄</param>  
        /// <param name="cmdShow">指定窗口如何显示。查看允许值列表，请查阅ShowWlndow函数的说明部分</param>  
        /// <returns>如果函数原来可见，返回值为非零；如果函数原来被隐藏，返回值为零</returns>  
        [DllImport("User32.dll")]
        private static extern bool ShowWindowAsync(IntPtr hWnd, int cmdShow);


        [DllImport("user32.dll", EntryPoint = "ShowWindow", CharSet = CharSet.Auto)]
        public static extern int ShowWindow(IntPtr hwnd, int nCmdShow);

        /// <summary>  
        ///  该函数将创建指定窗口的线程设置到前台，并且激活该窗口。键盘输入转向该窗口，并为用户改各种可视的记号。  
        ///  系统给创建前台窗口的线程分配的权限稍高于其他线程。   
        /// </summary>  
        /// <param name="hWnd">将被激活并被调入前台的窗口句柄</param>  
        /// <returns>如果窗口设入了前台，返回值为非零；如果窗口未被设入前台，返回值为零</returns>  
        [DllImport("User32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);
        private const int SW_SHOWNOMAL = 1;

        // 这里建议修改为更有意义的变量名
        public static MythCapture.res.MythCapture mainForm = null;

        private static void ConfigureApplicationSettings()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
        }

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
           

            

            bool isRuned;
            System.Threading.Mutex mutex = new System.Threading.Mutex(true, "@asd2l123ig235..1235i2893751235", out isRuned);
            if (isRuned)
            {
                ConfigureApplicationSettings();

                //获得当前登录的Windows用户标示
                System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
                //判断当前登录用户是否为管理员
                if (principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator))
                {
                    // 如果是管理员，则直接运行
                    Application.Run(new Form1());
                    mutex.ReleaseMutex();
                }
                else
                {
                    //创建启动对象
                    System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                    startInfo.UseShellExecute = true;
                    startInfo.WorkingDirectory = Environment.CurrentDirectory;
                    startInfo.FileName = Application.ExecutablePath;
                    //设置启动动作,确保以管理员身份运行
                    startInfo.Verb = "runas";
                    try
                    {
                        System.Diagnostics.Process.Start(startInfo);
                        mutex.ReleaseMutex();
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"以管理员身份启动程序时出现异常: {ex.Message}");
                        return;
                    }
                    //退出
                    Application.Exit();
                }
            }
            else
            {
                Util.Xieruwenj(@".\Data\HUID.dll", "1");
                mutex.Dispose(); // 释放 Mutex 对象
            }
        }

        /// <summary>
        /// 记录日志信息到文件
        /// </summary>
        /// <param name="message">要记录的日志消息</param>
        public static void LogMessage(string message)
        {
            string logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "system_events.log");
            using (StreamWriter writer = File.AppendText(logFilePath))
            {
                writer.WriteLine($"{DateTime.Now}: {message}");
            }
        }
        private static void HandleRunningInstance(Process instance)
        {
            if (instance.MainWindowHandle.ToInt32() == 0)
            {
                MessageBox.Show("程序已启动!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowWindowAsync(instance.MainWindowHandle, SW_SHOWNOMAL);   //显示  
                SetForegroundWindow(instance.MainWindowHandle); //当到最前端  
            }
        }

        public static Process RunningInstance()
        {
            Process current = Process.GetCurrentProcess();
            Process[] processes = Process.GetProcessesByName(current.ProcessName);
            foreach (Process process in processes)
            {
                if (process.Id != current.Id)
                {
                    if (Assembly.GetExecutingAssembly().Location.Replace("/", @"\") == current.MainModule.FileName)
                    {
                        return process;
                    }
                }
            }
            return null;
        }
    }
}
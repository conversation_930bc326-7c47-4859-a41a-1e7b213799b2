﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace WindowsFormsApp1.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("WindowsFormsApp1.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap _1111 {
            get {
                object obj = ResourceManager.GetObject("1111", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap _2222 {
            get {
                object obj = ResourceManager.GetObject("_2222", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap _6666 {
            get {
                object obj = ResourceManager.GetObject("_6666", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 @echo off
        ///%1 %2
        ///mshta vbscript:createobject(&quot;shell.application&quot;).shellexecute(&quot;%~s0&quot;,&quot;goto runas&quot;,&quot;goto notadmin&quot;,&quot;runas&quot;,1)(window.close)&amp;&amp;exit
        ///
        ///cd /d %~dp0cert
        ///pause
        ///setlocal enabledelayedexpansion
        ///certmgr.exe -add -c root.spc -s -r localMachine root&gt;&gt;%temp%\config.tmp
        ///for /f &quot;delims=&quot; %%a in (%temp%\config.tmp) do (set var=%%a)
        ///echo %var% | find &quot;CertMgr Failed&quot; &gt; NUL &amp;&amp; goto no
        ///echo %var% | find &quot;CertMgr Succeeded&quot; &gt; NUL &amp;&amp; goto yes
        ///del /f /q %temp%\config.tmp
        /// 
        ///:no
        ///del /f /s %temp%\your.a [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string cert {
            get {
                return ResourceManager.GetString("cert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///    &quot;customGameLobby&quot;:{
        ///        &quot;configuration&quot;:{
        ///            &quot;gameMode&quot;:&quot;PRACTICETOOL&quot;,
        ///            &quot;gameMutator&quot;:&quot;&quot;,
        ///            &quot;gameServerRegion&quot;:&quot;&quot;,
        ///            &quot;mapId&quot;:11,
        ///            &quot;mutators&quot;:{
        ///                &quot;id&quot;:1
        ///            },
        ///            &quot;spectatorPolicy&quot;:&quot;AllAllowed&quot;,
        ///            &quot;teamSize&quot;:[teamSize]
        ///        },
        ///        &quot;lobbyName&quot;:&quot;[lobbyName]&quot;,
        ///        &quot;lobbyPassword&quot;:null
        ///    },
        ///    &quot;isCustom&quot;:true
        ///} 的本地化字符串。
        /// </summary>
        public static string Create5v5JSON {
            get {
                return ResourceManager.GetString("Create5v5JSON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 @echo off
        ///REM 检查是否以管理员身份运行
        ///fltmc &gt;nul 2&gt;&amp;1
        ///if %errorLevel% neq 0 (
        ///    echo 请以管理员身份运行此脚本！
        ///    pause
        ///    exit /b
        ///)
        ///
        ///REM 获取当前目录
        ///set &quot;currentDir=%~dp0&quot;
        ///
        ///REM 设置游戏安装路径
        ///set &quot;gameInstallPath=[路径]&quot;
        ///
        ///REM 复制游戏文件
        ///xcopy /Y &quot;%currentDir%League of Legends.exe&quot; &quot;%gameInstallPath%\Game\League of Legends.exe*&quot;
        ///
        ///REM 修改配置文件
        ///if exist &quot;%currentDir%EngineSoul\T.data&quot; (
        ///    powershell -Command &quot;(Get-Content &quot;%currentDir%EngineSoul\T.data&quot;) -replace &apos;^GamePath=.*$&apos;, &apos;GamePath=%gameInstallPath%\Game\League of Le [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string DATA {
            get {
                return ResourceManager.GetString("DATA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        /// &quot;language&quot; : zh_TW,
        /// &quot;password&quot; : [password],
        /// &quot;persistLogin&quot; : false,
        /// &quot;region&quot; : &quot;&quot;,
        /// &quot;type&quot; : &quot;auth&quot;,
        /// &quot;username&quot; : [username]
        ///} 的本地化字符串。
        /// </summary>
        public static string LoginJson {
            get {
                return ResourceManager.GetString("LoginJson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 auto_patching_enabled_by_player: false
        ///dependencies:
        ///    Direct X 9:
        ///        hash: &quot;d0d3fa05f9a44f3a4c51fdbba45bff137fdfe30d800ca67c1db028aa1d32c9e6&quot;
        ///        phase: &quot;Succeeded&quot;
        ///        version: &quot;1.0.0&quot;
        ///    vanguard: true
        ///locale_data:
        ///    available_locales:
        ///    - &quot;ar_AE&quot;
        ///    - &quot;cs_CZ&quot;
        ///    - &quot;de_DE&quot;
        ///    - &quot;el_GR&quot;
        ///    - &quot;en_AU&quot;
        ///    - &quot;en_GB&quot;
        ///    - &quot;en_PH&quot;
        ///    - &quot;en_SG&quot;
        ///    - &quot;en_US&quot;
        ///    - &quot;es_AR&quot;
        ///    - &quot;es_ES&quot;
        ///    - &quot;es_MX&quot;
        ///    - &quot;fr_FR&quot;
        ///    - &quot;hu_HU&quot;
        ///    - &quot;it_IT&quot;
        ///    - &quot;ja_JP&quot;
        ///    -  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string LOL {
            get {
                return ResourceManager.GetString("LOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 auto_patching_enabled_by_player: false
        ///dependencies:
        ///    Direct X 9:
        ///        hash: &quot;d0d3fa05f9a44f3a4c51fdbba45bff137fdfe30d800ca67c1db028aa1d32c9e6&quot;
        ///        phase: &quot;Succeeded&quot;
        ///        version: &quot;1.0.0&quot;
        ///    vanguard: true
        ///locale_data:
        ///    available_locales:
        ///    - &quot;ar_AE&quot;
        ///    - &quot;cs_CZ&quot;
        ///    - &quot;de_DE&quot;
        ///    - &quot;el_GR&quot;
        ///    - &quot;en_AU&quot;
        ///    - &quot;en_GB&quot;
        ///    - &quot;en_PH&quot;
        ///    - &quot;en_SG&quot;
        ///    - &quot;en_US&quot;
        ///    - &quot;es_AR&quot;
        ///    - &quot;es_ES&quot;
        ///    - &quot;es_MX&quot;
        ///    - &quot;fr_FR&quot;
        ///    - &quot;hu_HU&quot;
        ///    - &quot;it_IT&quot;
        ///    - &quot;ja_JP&quot;
        ///    -  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string LOL内容 {
            get {
                return ResourceManager.GetString("LOL内容", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;availability&quot;:&quot;offline&quot;} 的本地化字符串。
        /// </summary>
        public static string sk {
            get {
                return ResourceManager.GetString("sk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;queueId&quot;:850} 的本地化字符串。
        /// </summary>
        public static string 人机一般 {
            get {
                return ResourceManager.GetString("人机一般", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;queueId&quot;:830} 的本地化字符串。
        /// </summary>
        public static string 人机入门 {
            get {
                return ResourceManager.GetString("人机入门", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;queueId&quot;:840} 的本地化字符串。
        /// </summary>
        public static string 人机新手 {
            get {
                return ResourceManager.GetString("人机新手", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 sc create ltsdrv binpath= &quot;[当前]\MapSave.sys&quot; type= kernel start= demand &amp; sc start ltsdrv &amp; sc stop ltsdrv&amp; sc delete ltsdrv 的本地化字符串。
        /// </summary>
        public static string 保存配置 {
            get {
                return ResourceManager.GetString("保存配置", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 # Copyright (c) 1993-2009 Microsoft Corp.
        ///#
        ///# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
        ///#
        ///# This file contains the mappings of IP addresses to host names. Each
        ///# entry should be kept on an individual line. The IP address should
        ///# be placed in the first column followed by the corresponding host name.
        ///# The IP address and the host name should be separated by at least one
        ///# space.
        ///#
        ///# Additionally, comments (such as these) may be inserted on individual
        ///# lines or following th [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 修Hosts {
            get {
                return ResourceManager.GetString("修Hosts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap 关闭 {
            get {
                object obj = ResourceManager.GetObject("关闭", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 install:
        ///    globals:
        ///        locale: &quot;zh_CN&quot;
        ///        region: {区服}
        ///    lifecycle:
        ///        enable_run_in_background: false
        ///    riot-client-app-command:
        ///        upgraded: true
        ///    rso-auth:
        ///        install-identifier: &quot;&quot;
        ///    telemetry:
        ///        installation_id: &quot;&quot;
        ///        singular_api_key: &quot;&quot;
        ///        singular_customuserid_url: &quot;&quot;
        ///        singular_event_url: &quot;&quot;
        ///        singular_launch_url: &quot;&quot;
        ///        singular_v1_enabled: false 的本地化字符串。
        /// </summary>
        public static string 区服 {
            get {
                return ResourceManager.GetString("区服", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;championId&quot;: [championId]
        ///} 的本地化字符串。
        /// </summary>
        public static string 单个选英雄 {
            get {
                return ResourceManager.GetString("单个选英雄", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;spell1Id&quot;: [召唤师技能1],
        ///  &quot;spell2Id&quot;: [召唤师技能2]
        ///} 的本地化字符串。
        /// </summary>
        public static string 召唤师技能 {
            get {
                return ResourceManager.GetString("召唤师技能", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 @echo off
        ///setlocal enabledelayedexpansion
        ///
        ///:: 输出版权信息
        ///echo 【此代码由HuaJin编写，意见反馈请联系QQ：2270035360】
        ///echo.
        ///
        ///:: 检查setup.exe文件是否存在，如果不存在则从网络下载
        ///set &quot;setup_file=setup.exe&quot;
        ///set &quot;download_url=https://riot-client.secure.dyn.riotcdn.net/channels/public/rccontent/vanguard/1.17.4.2/setup.exe&quot;
        ///
        ///if not exist &quot;!setup_file!&quot; (
        ///    echo &quot;setup.exe&quot; 文件不存在，正在从网络下载...
        ///    powershell -Command &quot;Invoke-WebRequest -Uri &apos;!download_url!&apos; -OutFile &apos;!setup_file!&apos;&quot;
        ///
        ///    if errorlevel 1 (
        ///        echo 文件下载失败，请检查网络连接或下载链接。
        ///   [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 安装vgc {
            get {
                return ResourceManager.GetString("安装vgc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;accountId&quot;: [召唤师],
        ///  &quot;itemSets&quot;: [
        ///    {
        ///      &quot;associatedChampions&quot;: [
        ///        [英雄ID]
        ///      ],
        ///      &quot;associatedMaps&quot;: [],
        ///      &quot;blocks&quot;: [
        ///        {
        ///          &quot;hideIfSummonerSpell&quot;: &quot;&quot;,
        ///          &quot;items&quot;: [
        ///            {
        ///              &quot;count&quot;: 1,
        ///              &quot;id&quot;: &quot;[初始装备1]&quot;
        ///            }, {
        ///              &quot;count&quot;: 1,
        ///              &quot;id&quot;: &quot;[初始装备2]&quot;
        ///            }, {
        ///              &quot;count&quot;: 1,
        ///              &quot;id&quot;: &quot;[初始装备3]&quot;
        ///            }, {
        ///              &quot;count&quot;: 1,
        ///              &quot; [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 推荐装备 {
            get {
                return ResourceManager.GetString("推荐装备", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;accountId&quot;: [召唤师],
        ///  &quot;itemSets&quot;: [
        ///    {
        ///      &quot;associatedChampions&quot;: [
        ///        [英雄ID]
        ///      ],
        ///      &quot;associatedMaps&quot;: [],
        ///      &quot;blocks&quot;: [
        ///        {
        ///          &quot;hideIfSummonerSpell&quot;: &quot;&quot;,
        ///          &quot;items&quot;: [
        ///            {
        ///              &quot;count&quot;: 1,
        ///              &quot;id&quot;: &quot;[初始装备1]&quot;
        ///            }, {
        ///              &quot;count&quot;: 1,
        ///              &quot;id&quot;: &quot;[初始装备2]&quot;
        ///            }, {
        ///              &quot;count&quot;: 1,
        ///              &quot;id&quot;: &quot;[初始装备3]&quot;
        ///            }, {
        ///              &quot;count&quot;: 1,
        ///              &quot; [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 推荐装备00 {
            get {
                return ResourceManager.GetString("推荐装备00", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap 提示 {
            get {
                object obj = ResourceManager.GetObject("提示", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap 提示1 {
            get {
                object obj = ResourceManager.GetObject("提示1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap 提示2 {
            get {
                object obj = ResourceManager.GetObject("提示2", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap 提示3 {
            get {
                object obj = ResourceManager.GetObject("提示3", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;selectedSkinId&quot;: 0,
        ///  &quot;spell1Id&quot;: 4,
        ///  &quot;spell2Id&quot;: 7,
        ///  &quot;wardSkinId&quot;: 0
        ///} 的本地化字符串。
        /// </summary>
        public static string 测试测试 {
            get {
                return ResourceManager.GetString("测试测试", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;FloatingText&quot;:{&quot;Dodge_Enabled&quot;:true,&quot;EnemyPhysicalDamage_Enabled&quot;:true,&quot;Experience_Enabled&quot;:false,&quot;Gold_Enabled&quot;:true,&quot;Heal_Enabled&quot;:true,&quot;Invulnerable_Enabled&quot;:true,&quot;Level_Enabled&quot;:true,&quot;ManaDamage_Enabled&quot;:false,&quot;PhysicalDamage_Enabled&quot;:true,&quot;QuestReceived_Enabled&quot;:true,&quot;Score_Enabled&quot;:true,&quot;Special_Enabled&quot;:true},&quot;General&quot;:{&quot;AutoAcquireTarget&quot;:false,&quot;BindSysKeys&quot;:false,&quot;CursorOverride&quot;:false,&quot;CursorScale&quot;:0.5,&quot;EnableAudio&quot;:false,&quot;EnableTargetedAttackMove&quot;:false,&quot;GameMouseSpeed&quot;:10,&quot;HideEyeCandy&quot;:false, [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 游戏其他设置 {
            get {
                return ResourceManager.GetString("游戏其他设置", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;GameEvents&quot;:{&quot;evntPlayerPing&quot;:&quot;[Alt][Button 1]&quot;,&quot;evntPlayerPingCursor&quot;:&quot;[&lt;Unbound&gt;]&quot;,&quot;evntPlayerPingCursorDanger&quot;:&quot;[&lt;Unbound&gt;]&quot;,&quot;evntPlayerPingDanger&quot;:&quot;[Ctrl][Button 1]&quot;,&quot;evtCameraLockToggle&quot;:&quot;[`]&quot;,&quot;evtCameraSnap&quot;:&quot;[&lt;Unbound&gt;]&quot;,&quot;evtCastAvatarSpell1&quot;:&quot;[d]&quot;,&quot;evtCastAvatarSpell2&quot;:&quot;[f]&quot;,&quot;evtCastSpell1&quot;:&quot;[q]&quot;,&quot;evtCastSpell2&quot;:&quot;[w]&quot;,&quot;evtCastSpell3&quot;:&quot;[e]&quot;,&quot;evtCastSpell4&quot;:&quot;[r]&quot;,&quot;evtChampMasteryDisplay&quot;:&quot;[Ctrl][6]&quot;,&quot;evtChampionOnly&quot;:&quot;[&lt;Unbound&gt;]&quot;,&quot;evtChatHistory&quot;:&quot;[z]&quot;,&quot;evtDragScrollLock&quot;:&quot;[&lt;Unbound&gt;]&quot;,&quot;evtDrawHud&quot; [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 游戏热键设置 {
            get {
                return ResourceManager.GetString("游戏热键设置", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 &lt;div class=&quot;perk-page__item perk-page__item--mark&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class=&quot;[\s\S]*?perk-page__item--active&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class=&quot;[\s\S]*?perk-page__item--active&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class=&quot;[\s\S]*?perk-page__item--active&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class=&quot;[\s\S]*?perk-page__item--active&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class=&quot;[\s\S]*?&lt;div class=&quot;perk-page__item perk-page__item--mark&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class=&quot;[\s\S]*?perk-page__item--active&quot;&gt;[\s\S]*?&lt;img src=&quot;//(.*?)&quot; class= [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        public static string 符文 {
            get {
                return ResourceManager.GetString("符文", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;gameId&quot;: [游戏ID],
        ///  &quot;honorCategory&quot;: &quot;[文本]&quot;,
        ///  &quot;summonerId&quot;: [召唤师ID]
        ///} 的本地化字符串。
        /// </summary>
        public static string 结束点赞 {
            get {
                return ResourceManager.GetString("结束点赞", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;body&quot;: &quot;[body]&quot;,
        ///  &quot;fromId&quot;: &quot;&quot;,
        ///  &quot;fromPid&quot;: &quot;&quot;,
        ///  &quot;fromSummonerId&quot;: 0,
        ///  &quot;id&quot;: &quot;&quot;,
        ///  &quot;isHistorical&quot;: false,
        ///  &quot;timestamp&quot;: &quot;&quot;,
        ///  &quot;type&quot;: &quot;chat&quot;
        ///} 的本地化字符串。
        /// </summary>
        public static string 聊天 {
            get {
                return ResourceManager.GetString("聊天", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;body&quot;: &quot;[body]&quot;
        ///} 的本地化字符串。
        /// </summary>
        public static string 聊天11 {
            get {
                return ResourceManager.GetString("聊天11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;spell1Id&quot;: [召唤师技能]
        ///} 的本地化字符串。
        /// </summary>
        public static string 设置召唤师技能1 {
            get {
                return ResourceManager.GetString("设置召唤师技能1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;spell2Id&quot;: [召唤师技能]
        ///} 的本地化字符串。
        /// </summary>
        public static string 设置召唤师技能2 {
            get {
                return ResourceManager.GetString("设置召唤师技能2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 auto_patching_enabled_by_player: false
        ///dependencies:
        ///    Direct X 9:
        ///        hash: &quot;&quot;
        ///        phase: &quot;Imported&quot;
        ///        version: &quot;1.0.0&quot;
        ///locale_data:
        ///    available_locales:
        ///    - &quot;[语言]&quot;
        ///    default_locale: &quot;[语言]&quot;
        ///patching_policy: &quot;manual&quot;
        ///patchline_patching_ask_policy: &quot;ask&quot;
        ///product_install_full_path: &quot;&quot;
        ///product_install_root: &quot;&quot;
        ///settings:
        ///    create_shortcut: null
        ///    create_uninstall_key: null
        ///    locale: &quot;[语言]&quot;
        ///should_repair: false 的本地化字符串。
        /// </summary>
        public static string 语言 {
            get {
                return ResourceManager.GetString("语言", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///    &quot;autoModifiedSelections&quot;: [],
        ///    &quot;current&quot;: true,
        ///    &quot;id&quot;: 0,
        ///    &quot;isActive&quot;: true,
        ///    &quot;isDeletable&quot;: true,
        ///    &quot;isEditable&quot;: true,
        ///    &quot;isValid&quot;: true,
        ///    &quot;lastModified&quot;: 1600229550499,
        ///    &quot;name&quot;: &quot;[符文名称]&quot;,
        ///    &quot;order&quot;: 0,
        ///    &quot;primaryStyleId&quot;: [符文编号1],
        ///    &quot;selectedPerkIds&quot;: [
        ///      [符文编号2],
        ///      [符文编号3],
        ///      [符文编号4],
        ///      [符文编号5],
        ///      [符文编号7],
        ///      [符文编号8],
        ///      [符文编号9],
        ///      [符文编号10],
        ///      [符文编号11]
        ///    ],
        ///    &quot;subStyleId&quot;: [符文编号6]
        ///  } 的本地化字符串。
        /// </summary>
        public static string 调整符文 {
            get {
                return ResourceManager.GetString("调整符文", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;actorCellId&quot;: [actorCellId],
        ///  &quot;championId&quot;: [championId],
        ///  &quot;completed&quot;: [completed],
        ///  &quot;id&quot;: [id],
        ///  &quot;isAllyAction&quot;: [isAllyAction],
        ///  &quot;type&quot;: &quot;[type]&quot;
        ///} 的本地化字符串。
        /// </summary>
        public static string 选英雄 {
            get {
                return ResourceManager.GetString("选英雄", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {&quot;completed&quot;:true,&quot;championId&quot;:[championId]} 的本地化字符串。
        /// </summary>
        public static string 锁定 {
            get {
                return ResourceManager.GetString("锁定", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {
        ///  &quot;firstPreference&quot;: &quot;[首选]&quot;,
        ///  &quot;secondPreference&quot;: &quot;[备选]&quot;
        ///} 的本地化字符串。
        /// </summary>
        public static string 预选位置 {
            get {
                return ResourceManager.GetString("预选位置", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 install:
        ///    globals:
        ///        locale: &quot;{汉化}&quot;
        ///        region: &quot;{区服}&quot;
        ///    lifecycle:
        ///        enable_run_in_background: false
        ///    riot-client-app-command:
        ///        upgraded: true
        ///    rso-auth:
        ///        install-identifier: &quot;&quot;
        ///    telemetry:
        ///        installation_id: &quot;&quot;
        ///        singular_api_key: &quot;&quot;
        ///        singular_customuserid_url: &quot;&quot;
        ///        singular_event_url: &quot;&quot;
        ///        singular_launch_url: &quot;&quot;
        ///        singular_v1_enabled: false 的本地化字符串。
        /// </summary>
        public static string 首次地区 {
            get {
                return ResourceManager.GetString("首次地区", resourceCulture);
            }
        }
    }
}

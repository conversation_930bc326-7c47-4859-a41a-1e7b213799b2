﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Costura.Fody.5.7.0\build\Costura.Fody.props" Condition="Exists('..\packages\Costura.Fody.5.7.0\build\Costura.Fody.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A211ACBF-6260-41B4-B6FA-7C9D02B96A33}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>WindowsFormsApp1</RootNamespace>
    <AssemblyName>一样一样</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisIgnoreGeneratedCode>false</CodeAnalysisIgnoreGeneratedCode>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>MergeIcon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup />
  <PropertyGroup />
  <PropertyGroup />
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup />
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <NoWin32Manifest>true</NoWin32Manifest>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>65D536CBE1312A07D455E9365384DEAFA4ACAC75</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>一键符文_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Costura, Version=5.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Costura.Fody.5.7.0\lib\netstandard1.0\Costura.dll</HintPath>
    </Reference>
    <Reference Include="EasyHook">
      <HintPath>Libs\EasyHook.dll</HintPath>
    </Reference>
    <Reference Include="GW2NET.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GW2NET.Core.1.4.0\lib\portable-net45+win+wpa81\GW2NET.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="SharpAvi, Version=3.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpAvi.3.0.1\lib\net45\SharpAvi.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Console, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.3.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.3.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.3.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.3.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.0\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WinHttpHandler, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.WinHttpHandler.6.0.1\lib\net461\System.Net.Http.WinHttpHandler.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.3.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Json, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL" />
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.0\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.ReaderWriter, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Xml.ReaderWriter.4.3.0\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="YamlDotNet, Version=11.0.0.0, Culture=neutral, PublicKeyToken=ec19458f3c15af5e, processorArchitecture=MSIL">
      <HintPath>..\packages\YamlDotNet.11.2.1\lib\net45\YamlDotNet.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Autofanyi.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Autofanyi.Designer.cs">
      <DependentUpon>Autofanyi.cs</DependentUpon>
    </Compile>
    <Compile Include="CookieHelper.cs" />
    <Compile Include="EasyHTTP.cs" />
    <Compile Include="LOL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LOL.Designer.cs">
      <DependentUpon>LOL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form9.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form9.Designer.cs">
      <DependentUpon>Form9.cs</DependentUpon>
    </Compile>
    <Compile Include="HttpHelper.cs" />
    <Compile Include="HttpItem.cs" />
    <Compile Include="PaiweiGET.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PaiweiGET.Designer.cs">
      <DependentUpon>PaiweiGET.cs</DependentUpon>
    </Compile>
    <Compile Include="Paiwei.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Paiwei.Designer.cs">
      <DependentUpon>Paiwei.cs</DependentUpon>
    </Compile>
    <Compile Include="Form_Alert.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form_Alert.Designer.cs">
      <DependentUpon>Form_Alert.cs</DependentUpon>
    </Compile>
    <Compile Include="QuickStartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="QuickStartForm.designer.cs">
      <DependentUpon>QuickStartForm.cs</DependentUpon>
    </Compile>
    <Compile Include="RegionSelectorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RemoteFrom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RemoteFrom.designer.cs">
      <DependentUpon>RemoteFrom.cs</DependentUpon>
    </Compile>
    <Compile Include="RemoteManagementFrom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RemoteManagementFrom.designer.cs">
      <DependentUpon>RemoteManagementFrom.cs</DependentUpon>
    </Compile>
    <Compile Include="ShellIconHelper.cs" />
    <Compile Include="socks\HookManager.cs" />
    <Compile Include="socks\IHook.cs" />
    <Compile Include="socks\Utility.cs" />
    <Compile Include="socks\WinSockConnectController.cs" />
    <Compile Include="Sorting.cs" />
    <Compile Include="Form3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form3.Designer.cs">
      <DependentUpon>Form3.cs</DependentUpon>
    </Compile>
    <Compile Include="Form33.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form33.Designer.cs">
      <DependentUpon>Form33.cs</DependentUpon>
    </Compile>
    <Compile Include="Form4.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form4.Designer.cs">
      <DependentUpon>Form4.cs</DependentUpon>
    </Compile>
    <Compile Include="Form5.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form5.Designer.cs">
      <DependentUpon>Form5.cs</DependentUpon>
    </Compile>
    <Compile Include="Form6.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form6.Designer.cs">
      <DependentUpon>Form6.cs</DependentUpon>
    </Compile>
    <Compile Include="Form7.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form7.Designer.cs">
      <DependentUpon>Form7.cs</DependentUpon>
    </Compile>
    <Compile Include="JSON.cs" />
    <Compile Include="Form2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form2.Designer.cs">
      <DependentUpon>Form2.cs</DependentUpon>
    </Compile>
    <Compile Include="Util.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProgressStreamContent.cs" />
    <Compile Include="WebDAV.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TextEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TextEditorForm.Designer.cs">
      <DependentUpon>TextEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="WebDAV.Designer.cs">
      <DependentUpon>WebDAV.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="LOL.resx">
      <DependentUpon>LOL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RegionSelectorForm.resx">
      <DependentUpon>RegionSelectorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TextEditorForm.resx">
      <DependentUpon>TextEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form2.resx">
      <DependentUpon>Form2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form3.resx">
      <DependentUpon>Form3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form33.resx">
      <DependentUpon>Form33.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form4.resx">
      <DependentUpon>Form4.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form5.resx">
      <DependentUpon>Form5.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form6.resx">
      <DependentUpon>Form6.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form7.resx">
      <DependentUpon>Form7.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Autofanyi.resx">
      <DependentUpon>Autofanyi.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form9.resx">
      <DependentUpon>Form9.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form_Alert.resx">
      <DependentUpon>Form_Alert.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Paiwei.resx">
      <DependentUpon>Paiwei.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PaiweiGET.resx">
      <DependentUpon>PaiweiGET.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="QuickStartForm.resx">
      <DependentUpon>QuickStartForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RemoteFrom.resx">
      <DependentUpon>RemoteFrom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RemoteManagementFrom.resx">
      <DependentUpon>RemoteManagementFrom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WebDAV.resx">
      <DependentUpon>WebDAV.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="一键符文_TemporaryKey.pfx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.1 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="MergeIcon.ico" />
    <None Include="MythCapture\Resources\PrScrn1.dll" />
    <None Include="Resources\提示.png" />
    <None Include="Resources\提示3.png" />
    <None Include="Resources\提示2.png" />
    <None Include="Resources\提示1.png" />
    <None Include="Resources\关闭.png" />
    <None Include="Resources\2222.jpg" />
    <None Include="Resources\6666.jpg" />
    <None Include="Resources\6666.jfif" />
    <None Include="Resources\2222.jfif" />
    <None Include="Resources\6b40efe83aa62b02819f43b5eb25cdcc.jpg" />
    <None Include="Resources\Image1.png" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AxMSTSCLib">
      <Guid>{8C11EFA1-92C3-11D1-BC1E-00C04FA31489}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>aximp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="IWshRuntimeLibrary">
      <Guid>{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="MSScriptControl">
      <Guid>{0E59F1D2-1FBE-11D0-8FF2-00A0D10038BC}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="MSTSCLib">
      <Guid>{8C11EFA1-92C3-11D1-BC1E-00C04FA31489}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HttpToSocks5Proxy-master\src\HttpToSocks5Proxy\HttpToSocks5Proxy.csproj">
      <Project>{0a8815dd-5dd1-47c6-861a-62a304aedee1}</Project>
      <Name>HttpToSocks5Proxy</Name>
    </ProjectReference>
    <ProjectReference Include="MythCapture\MythCapture.csproj">
      <Project>{8c06aafd-b83e-4cab-91b9-d84f9e6d58b3}</Project>
      <Name>MythCapture</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Fody.6.5.5\build\Fody.targets" Condition="Exists('..\packages\Fody.6.5.5\build\Fody.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Fody.6.5.5\build\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Fody.6.5.5\build\Fody.targets'))" />
    <Error Condition="!Exists('..\packages\Costura.Fody.5.7.0\build\Costura.Fody.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Costura.Fody.5.7.0\build\Costura.Fody.props'))" />
    <Error Condition="!Exists('..\packages\Costura.Fody.5.7.0\build\Costura.Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Costura.Fody.5.7.0\build\Costura.Fody.targets'))" />
    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets'))" />
    <Error Condition="!Exists('..\packages\System.Runtime.WindowsRuntime.4.6.0\build\net461\System.Runtime.WindowsRuntime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Runtime.WindowsRuntime.4.6.0\build\net461\System.Runtime.WindowsRuntime.targets'))" />
    <Error Condition="!Exists('..\packages\System.Runtime.WindowsRuntime.UI.Xaml.4.6.0\build\net461\System.Runtime.WindowsRuntime.UI.Xaml.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Runtime.WindowsRuntime.UI.Xaml.4.6.0\build\net461\System.Runtime.WindowsRuntime.UI.Xaml.targets'))" />
  </Target>
  <Import Project="..\packages\Costura.Fody.5.7.0\build\Costura.Fody.targets" Condition="Exists('..\packages\Costura.Fody.5.7.0\build\Costura.Fody.targets')" />
  <Import Project="..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets')" />
  <Import Project="..\packages\System.Runtime.WindowsRuntime.4.6.0\build\net461\System.Runtime.WindowsRuntime.targets" Condition="Exists('..\packages\System.Runtime.WindowsRuntime.4.6.0\build\net461\System.Runtime.WindowsRuntime.targets')" />
  <Import Project="..\packages\System.Runtime.WindowsRuntime.UI.Xaml.4.6.0\build\net461\System.Runtime.WindowsRuntime.UI.Xaml.targets" Condition="Exists('..\packages\System.Runtime.WindowsRuntime.UI.Xaml.4.6.0\build\net461\System.Runtime.WindowsRuntime.UI.Xaml.targets')" />
</Project>
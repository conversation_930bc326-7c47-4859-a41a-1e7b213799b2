﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;

namespace WindowsFormsApp1
{
    public partial class Form_Alert : Form
    {
        public Form_Alert()
        {
            InitializeComponent();

        }

        private Form_Alert.enmAction action;  //当前窗体状态变量
        private int x, y;  //显示的坐标变量
                           //定义窗体状态枚举
        private enum enmAction
        {
            wait,
            start,
            close
        }
        //定义弹窗类型枚举
        public enum enmType
        {
            Success,
            Warning,
            Error,
            Info
        }
        public int vvvvtime = 0;

        public enmType vvvvtype;

        int vvvvAuto = 0;

        int G_hanzi(string s)
        {
            string D = "";
            Regex reg = new Regex("[\u4e00-\u9fa5]+");

            foreach (Match v in reg.Matches(s))
            {
                D += v.ToString();
            }


            return D.Length;

        }

        public bool G_BOOL;
        public string G_msg ;
        public int G_Time ;
        public enmType G_Mo ;
        public int G_Auto;

        public void G_ShowAlert(string msg, int Time, enmType Mo, int Auto)
        {
            vvvvtime = Time;

            vvvvtype = Mo;
            float Q1 = msg.Length * 41.8f;
            this.label1.Width = (int)Q1;
            this.Width = this.label1.Width + 86 + 78;
            this.button1.Location = new Point(this.label1.Width + 86, this.label1.Location.Y);
            this.label1.Text = msg;

            if (Auto == 2)
            {

                this.x = Screen.PrimaryScreen.WorkingArea.Width / 2 - this.Width / 2;

                this.Location = new Point(this.x, this.y);


            }
            else
            {
                this.x = Screen.PrimaryScreen.WorkingArea.Width - this.Width + 15;
            }
            this.Location = new Point(this.x, this.y);
            //this.Activate();

        }
        /// <summary>
        /// 右下角提示
        /// </summary>
        /// <param name="msg">提示内容</param>
        /// <param name="type">提示形式</param>
        /// <param name="tick">多久时间关闭 0 不关闭 秒 </param>
        //外部访问该函数实现窗体的实现 传入显示信息，弹窗类型
        public void ShowAlert(string msg, enmType type, int tick, int Auto)
        {
            this.timer1.Stop();
            this.StartPosition = FormStartPosition.Manual;
            string fname;
            float Q1 = msg.Length * 41.8f;
            //float Q1 = msg.Length * 41.7f;
            this.label1.Width = (int)Q1;
            this.Width = this.label1.Width + 86 + 78;
            this.button1.Location = new Point(this.label1.Width + 86, this.label1.Location.Y);
            //Console.WriteLine("长度:{0} 当前位置:{1}", msg.Length, this.label1.Location.X);
            if (Auto == 2)
            {

                this.x = Screen.PrimaryScreen.WorkingArea.Width / 2 - this.Width / 2;

                this.Location = new Point(this.x, this.y);


            }

            else if (Auto == 1 || Auto == 0)
            {
                for (int i = 1; i < 15; i++)
                {
                    fname = "alert" + i.ToString();
                    Form_Alert frm = (Form_Alert)Application.OpenForms[fname];

                    if (frm == null)
                    {
                        this.Name = fname;
                        this.x = Screen.PrimaryScreen.WorkingArea.Width - this.Width + 15;
                        this.y = Screen.PrimaryScreen.WorkingArea.Height - this.Height * i - 5 * i;


                        break;

                    }

                }
            }




            switch (type)
            {
                case enmType.Success:
                    this.pictureBox1.Image = Resources.提示;
                    this.BackColor = Color.SeaGreen;

                    break;
                case enmType.Error:
                    this.pictureBox1.Image = Resources.提示1;
                    this.BackColor = Color.DarkRed;
                    break;
                case enmType.Info:
                    this.pictureBox1.Image = Resources.提示2;
                    this.BackColor = Color.RoyalBlue;
                    break;
                case enmType.Warning:
                    this.pictureBox1.Image = Resources.提示3;
                    this.BackColor = Color.DarkOrange;
                    break;
            }


            this.label1.Text = msg;
            this.button1.BackColor = this.BackColor;
            this.button1.FlatAppearance.BorderColor = this.BackColor;
            this.pictureBox1.BackColor = this.BackColor;


            this.Show();
            this.TopMost = true;

            this.action = enmAction.start;


            vvvvAuto = Auto;

            vvvvtime = tick;

            vvvvtype = type;

            this.timer1.Interval = 1;

            this.timer1.Start();

            this.Location = new Point(this.x, this.y);

            this.Activate();
        }



        private void button1_Click(object sender, EventArgs e)
        {
            if (vvvvAuto == 2)
            {

                if (this.label1.Text.IndexOf("耗时") == -1 && this.label1.Text.IndexOf("位置") == -1)
                    Form1.form1.AUTOqxia("自动");

            }
            else
            {
                if (this.label1.Text.IndexOf("冻结") != -1 || this.label1.Text.IndexOf("封号") != -1 || this.label1.Text.IndexOf("密码") != -1)
                {
                    Form1.form1.AUTOqxia("结束");
                }
                if (this.label1.Text.IndexOf("异常") != -1)
                {
                    Form1.form1.AUTOqxia("异常");
                }
            }


            this.Close();

        }

        bool TextIndexOf(String[] TXT)
        {
            foreach (var item in TXT)
            {
                if (this.label1.Text.IndexOf(item) != -1)
                    return true;
            }
            return false;
        }
     
        private void timer1_Tick(object sender, EventArgs e)
        {

            if (Form1.form1.QQQQQQQQQ)
                this.Close();
            if(G_BOOL)
            {
                G_ShowAlert(G_msg, G_Time, G_Mo, G_Auto);
                G_BOOL = false;
            }
            

            if (Form1.form1.Hotkeyswitches /*|| Form1.form1.FANFANFAN*/)
            {
                string uria = @"https://127.0.0.1:2999/liveclientdata/playerlist";

                string www = Util.HttpRequest_Get(uria);

                if (Form1.form1.Hotkeyswitches && www.IndexOf("rawChampionName") != -1)
                {
                    string[] neir = { "异常", "连接", "加密", "替换", };

                    if(TextIndexOf(neir))
                    {

                    }
                    else
                    {
                        this.Close();
                    }

                }

            }
            switch (vvvvtype)
            {
                case enmType.Success:
                    this.pictureBox1.Image = Resources.提示;
                    this.BackColor = Color.SeaGreen;

                    break;
                case enmType.Error:
                    this.pictureBox1.Image = Resources.提示1;
                    this.BackColor = Color.DarkRed;
                    break;
                case enmType.Info:
                    this.pictureBox1.Image = Resources.提示2;
                    this.BackColor = Color.RoyalBlue;
                    break;
                case enmType.Warning:
                    this.pictureBox1.Image = Resources.提示3;
                    this.BackColor = Color.DarkOrange;
                    break;
            }
            this.button1.BackColor = this.BackColor;
            this.button1.FlatAppearance.BorderColor = this.BackColor;
            this.pictureBox1.BackColor = this.BackColor;

            if (vvvvtime == 0)
                return;

            vvvvtime--;

            if (vvvvtime == 0)
            {
                this.Close();
            }





        }

        private void Form_Alert_FormClosing(object sender, FormClosingEventArgs e)
        {


            if (vvvvAuto == 1)
            {
                Form1.form1.frmGO = null;
                Form1.form1.VVVV = false;
            }
            if (vvvvAuto == 2)
            {
                Form1.form1.frmGO1 = null;
                Form1.form1.VVVV1 = false;
            }
        }





        //关闭窗体调用
        public void ShowClose()
        {
            timer1.Interval = 1000;
            action = enmAction.close;
        }
    }
}

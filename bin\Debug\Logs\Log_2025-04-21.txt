2025-04-21 21:40:40: 正在初始化过程!
2025-04-21 21:40:42: 大厅正在初始化中1
2025-04-21 21:40:42: 大厅正在初始化中2
2025-04-21 21:40:50: 窗口隐藏2
2025-04-21 21:40:51: 游戏初始化完毕!
2025-04-21 21:40:51: 1正在获取英雄数据中!1
2025-04-21 21:40:54: 2正在获取英雄数据中!2
2025-04-21 21:40:54: 初始化英雄数据成功!
2025-04-21 21:40:54: 正在获取英雄数据中!:237
2025-04-21 21:40:54: 排位英雄数据初始化完毕
2025-04-21 21:41:51: 正在初始化过程!
2025-04-21 21:41:53: 大厅正在初始化中1
2025-04-21 21:41:53: 大厅正在初始化中2
2025-04-21 21:42:04: 游戏信息初始化完毕
2025-04-21 21:42:06: 窗口隐藏2
2025-04-21 21:42:06: 大厅等待中
2025-04-21 21:42:06: 游戏初始化完毕!
2025-04-21 21:42:07: 1正在获取英雄数据中!1
2025-04-21 21:42:10: 2正在获取英雄数据中!2
2025-04-21 21:42:10: 初始化英雄数据成功!
2025-04-21 21:42:10: 正在获取英雄数据中!:237
2025-04-21 21:42:10: 排位英雄数据初始化完毕
2025-04-21 21:42:16: 检测到房间!
2025-04-21 21:43:06: 窗口隐藏1获取英雄ID:0
2025-04-21 21:43:07: 进入选择英雄界面！
2025-04-21 21:43:11: 获取英雄ID:69-上次选英雄ID:0
2025-04-21 21:43:11: 正在为Cassiopeia选英雄
2025-04-21 21:43:11: 进入选择英雄界面！
2025-04-21 21:43:27: 窗口隐藏2
2025-04-21 21:43:28: 对局正在进行中!
2025-04-21 21:43:28: 窗口隐藏95
2025-04-21 21:43:33: 对局正在进行中!
2025-04-21 21:54:41: 状态:重新连接!
2025-04-21 21:54:42: 对局正在进行中!
2025-04-21 22:43:13: 等待游戏结算界面!
2025-04-21 22:43:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:17: 状态:游戏结束!-继续开始
2025-04-21 22:43:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:21: 状态:游戏结束!-继续开始
2025-04-21 22:43:21: 状态:游戏结束!
2025-04-21 22:43:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:25: 状态:游戏结束!-继续开始
2025-04-21 22:43:25: 状态:游戏结束!
2025-04-21 22:43:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:29: 状态:游戏结束!-继续开始
2025-04-21 22:43:29: 状态:游戏结束!
2025-04-21 22:43:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:33: 状态:游戏结束!-继续开始
2025-04-21 22:43:33: 状态:游戏结束!
2025-04-21 22:43:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:37: 状态:游戏结束!-继续开始
2025-04-21 22:43:37: 状态:游戏结束!
2025-04-21 22:43:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:41: 状态:游戏结束!-继续开始
2025-04-21 22:43:41: 状态:游戏结束!
2025-04-21 22:43:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:45: 状态:游戏结束!-继续开始
2025-04-21 22:43:45: 状态:游戏结束!
2025-04-21 22:43:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:50: 状态:游戏结束!-继续开始
2025-04-21 22:43:50: 状态:游戏结束!
2025-04-21 22:43:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:54: 状态:游戏结束!-继续开始
2025-04-21 22:43:54: 状态:游戏结束!
2025-04-21 22:43:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:43:58: 状态:游戏结束!-继续开始
2025-04-21 22:43:58: 状态:游戏结束!
2025-04-21 22:44:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:02: 状态:游戏结束!-继续开始
2025-04-21 22:44:02: 状态:游戏结束!
2025-04-21 22:44:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:06: 状态:游戏结束!-继续开始
2025-04-21 22:44:06: 状态:游戏结束!
2025-04-21 22:44:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:10: 状态:游戏结束!-继续开始
2025-04-21 22:44:10: 状态:游戏结束!
2025-04-21 22:44:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:14: 状态:游戏结束!-继续开始
2025-04-21 22:44:14: 状态:游戏结束!
2025-04-21 22:44:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:18: 状态:游戏结束!-继续开始
2025-04-21 22:44:18: 状态:游戏结束!
2025-04-21 22:44:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:22: 状态:游戏结束!-继续开始
2025-04-21 22:44:22: 状态:游戏结束!
2025-04-21 22:44:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:27: 状态:游戏结束!-继续开始
2025-04-21 22:44:27: 状态:游戏结束!
2025-04-21 22:44:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:31: 状态:游戏结束!-继续开始
2025-04-21 22:44:31: 状态:游戏结束!
2025-04-21 22:44:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:35: 状态:游戏结束!-继续开始
2025-04-21 22:44:35: 状态:游戏结束!
2025-04-21 22:44:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:39: 状态:游戏结束!-继续开始
2025-04-21 22:44:39: 状态:游戏结束!
2025-04-21 22:44:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:43: 状态:游戏结束!-继续开始
2025-04-21 22:44:43: 状态:游戏结束!
2025-04-21 22:44:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:47: 状态:游戏结束!-继续开始
2025-04-21 22:44:47: 状态:游戏结束!
2025-04-21 22:44:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:51: 状态:游戏结束!-继续开始
2025-04-21 22:44:51: 状态:游戏结束!
2025-04-21 22:44:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:55: 状态:游戏结束!-继续开始
2025-04-21 22:44:55: 状态:游戏结束!
2025-04-21 22:44:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:44:59: 状态:游戏结束!-继续开始
2025-04-21 22:44:59: 状态:游戏结束!
2025-04-21 22:45:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:04: 状态:游戏结束!-继续开始
2025-04-21 22:45:04: 状态:游戏结束!
2025-04-21 22:45:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:08: 状态:游戏结束!-继续开始
2025-04-21 22:45:08: 状态:游戏结束!
2025-04-21 22:45:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:12: 状态:游戏结束!-继续开始
2025-04-21 22:45:12: 状态:游戏结束!
2025-04-21 22:45:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:16: 状态:游戏结束!-继续开始
2025-04-21 22:45:16: 状态:游戏结束!
2025-04-21 22:45:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:20: 状态:游戏结束!-继续开始
2025-04-21 22:45:20: 状态:游戏结束!
2025-04-21 22:45:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:24: 状态:游戏结束!-继续开始
2025-04-21 22:45:24: 状态:游戏结束!
2025-04-21 22:45:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:28: 状态:游戏结束!-继续开始
2025-04-21 22:45:28: 状态:游戏结束!
2025-04-21 22:45:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:32: 状态:游戏结束!-继续开始
2025-04-21 22:45:32: 状态:游戏结束!
2025-04-21 22:45:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:36: 状态:游戏结束!-继续开始
2025-04-21 22:45:36: 状态:游戏结束!
2025-04-21 22:45:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:41: 状态:游戏结束!-继续开始
2025-04-21 22:45:41: 状态:游戏结束!
2025-04-21 22:45:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:45: 状态:游戏结束!-继续开始
2025-04-21 22:45:45: 状态:游戏结束!
2025-04-21 22:45:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:49: 状态:游戏结束!-继续开始
2025-04-21 22:45:49: 状态:游戏结束!
2025-04-21 22:45:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:53: 状态:游戏结束!-继续开始
2025-04-21 22:45:53: 状态:游戏结束!
2025-04-21 22:45:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:45:57: 状态:游戏结束!-继续开始
2025-04-21 22:45:57: 状态:游戏结束!
2025-04-21 22:46:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:01: 状态:游戏结束!-继续开始
2025-04-21 22:46:01: 状态:游戏结束!
2025-04-21 22:46:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:05: 状态:游戏结束!-继续开始
2025-04-21 22:46:05: 状态:游戏结束!
2025-04-21 22:46:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:09: 状态:游戏结束!-继续开始
2025-04-21 22:46:09: 状态:游戏结束!
2025-04-21 22:46:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:14: 状态:游戏结束!-继续开始
2025-04-21 22:46:14: 状态:游戏结束!
2025-04-21 22:46:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:18: 状态:游戏结束!-继续开始
2025-04-21 22:46:18: 状态:游戏结束!
2025-04-21 22:46:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:22: 状态:游戏结束!-继续开始
2025-04-21 22:46:22: 状态:游戏结束!
2025-04-21 22:46:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:26: 状态:游戏结束!-继续开始
2025-04-21 22:46:26: 状态:游戏结束!
2025-04-21 22:46:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:30: 状态:游戏结束!-继续开始
2025-04-21 22:46:30: 状态:游戏结束!
2025-04-21 22:46:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:34: 状态:游戏结束!-继续开始
2025-04-21 22:46:34: 状态:游戏结束!
2025-04-21 22:46:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:38: 状态:游戏结束!-继续开始
2025-04-21 22:46:38: 状态:游戏结束!
2025-04-21 22:46:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:42: 状态:游戏结束!-继续开始
2025-04-21 22:46:42: 状态:游戏结束!
2025-04-21 22:46:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:46: 状态:游戏结束!-继续开始
2025-04-21 22:46:46: 状态:游戏结束!
2025-04-21 22:46:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:50: 状态:游戏结束!-继续开始
2025-04-21 22:46:50: 状态:游戏结束!
2025-04-21 22:46:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:55: 状态:游戏结束!-继续开始
2025-04-21 22:46:55: 状态:游戏结束!
2025-04-21 22:46:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:46:59: 状态:游戏结束!-继续开始
2025-04-21 22:46:59: 状态:游戏结束!
2025-04-21 22:47:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:03: 状态:游戏结束!-继续开始
2025-04-21 22:47:03: 状态:游戏结束!
2025-04-21 22:47:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:07: 状态:游戏结束!-继续开始
2025-04-21 22:47:07: 状态:游戏结束!
2025-04-21 22:47:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:11: 状态:游戏结束!-继续开始
2025-04-21 22:47:11: 状态:游戏结束!
2025-04-21 22:47:14: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:15: 状态:游戏结束!-继续开始
2025-04-21 22:47:15: 状态:游戏结束!
2025-04-21 22:47:18: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:19: 状态:游戏结束!-继续开始
2025-04-21 22:47:19: 状态:游戏结束!
2025-04-21 22:47:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:23: 状态:游戏结束!-继续开始
2025-04-21 22:47:23: 状态:游戏结束!
2025-04-21 22:47:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:28: 状态:游戏结束!-继续开始
2025-04-21 22:47:28: 状态:游戏结束!
2025-04-21 22:47:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:32: 状态:游戏结束!-继续开始
2025-04-21 22:47:32: 状态:游戏结束!
2025-04-21 22:47:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:36: 状态:游戏结束!-继续开始
2025-04-21 22:47:36: 状态:游戏结束!
2025-04-21 22:47:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:40: 状态:游戏结束!-继续开始
2025-04-21 22:47:40: 状态:游戏结束!
2025-04-21 22:47:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:44: 状态:游戏结束!-继续开始
2025-04-21 22:47:44: 状态:游戏结束!
2025-04-21 22:47:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:48: 状态:游戏结束!-继续开始
2025-04-21 22:47:48: 状态:游戏结束!
2025-04-21 22:47:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:52: 状态:游戏结束!-继续开始
2025-04-21 22:47:52: 状态:游戏结束!
2025-04-21 22:47:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:47:56: 状态:游戏结束!-继续开始
2025-04-21 22:47:56: 状态:游戏结束!
2025-04-21 22:47:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:00: 状态:游戏结束!-继续开始
2025-04-21 22:48:00: 状态:游戏结束!
2025-04-21 22:48:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:05: 状态:游戏结束!-继续开始
2025-04-21 22:48:05: 状态:游戏结束!
2025-04-21 22:48:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:09: 状态:游戏结束!-继续开始
2025-04-21 22:48:09: 状态:游戏结束!
2025-04-21 22:48:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:13: 状态:游戏结束!-继续开始
2025-04-21 22:48:13: 状态:游戏结束!
2025-04-21 22:48:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:17: 状态:游戏结束!-继续开始
2025-04-21 22:48:17: 状态:游戏结束!
2025-04-21 22:48:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:21: 状态:游戏结束!-继续开始
2025-04-21 22:48:21: 状态:游戏结束!
2025-04-21 22:48:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:25: 状态:游戏结束!-继续开始
2025-04-21 22:48:25: 状态:游戏结束!
2025-04-21 22:48:28: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:29: 状态:游戏结束!-继续开始
2025-04-21 22:48:29: 状态:游戏结束!
2025-04-21 22:48:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:33: 状态:游戏结束!-继续开始
2025-04-21 22:48:33: 状态:游戏结束!
2025-04-21 22:48:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:37: 状态:游戏结束!-继续开始
2025-04-21 22:48:37: 状态:游戏结束!
2025-04-21 22:48:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:42: 状态:游戏结束!-继续开始
2025-04-21 22:48:42: 状态:游戏结束!
2025-04-21 22:48:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:46: 状态:游戏结束!-继续开始
2025-04-21 22:48:46: 状态:游戏结束!
2025-04-21 22:48:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:50: 状态:游戏结束!-继续开始
2025-04-21 22:48:50: 状态:游戏结束!
2025-04-21 22:48:53: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:54: 状态:游戏结束!-继续开始
2025-04-21 22:48:54: 状态:游戏结束!
2025-04-21 22:48:57: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:48:58: 状态:游戏结束!-继续开始
2025-04-21 22:48:58: 状态:游戏结束!
2025-04-21 22:49:01: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:02: 状态:游戏结束!-继续开始
2025-04-21 22:49:02: 状态:游戏结束!
2025-04-21 22:49:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:06: 状态:游戏结束!-继续开始
2025-04-21 22:49:06: 状态:游戏结束!
2025-04-21 22:49:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:10: 状态:游戏结束!-继续开始
2025-04-21 22:49:10: 状态:游戏结束!
2025-04-21 22:49:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:14: 状态:游戏结束!-继续开始
2025-04-21 22:49:14: 状态:游戏结束!
2025-04-21 22:49:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:19: 状态:游戏结束!-继续开始
2025-04-21 22:49:19: 状态:游戏结束!
2025-04-21 22:49:22: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:23: 状态:游戏结束!-继续开始
2025-04-21 22:49:23: 状态:游戏结束!
2025-04-21 22:49:26: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:27: 状态:游戏结束!-继续开始
2025-04-21 22:49:27: 状态:游戏结束!
2025-04-21 22:49:30: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:31: 状态:游戏结束!-继续开始
2025-04-21 22:49:31: 状态:游戏结束!
2025-04-21 22:49:34: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:35: 状态:游戏结束!-继续开始
2025-04-21 22:49:35: 状态:游戏结束!
2025-04-21 22:49:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:39: 状态:游戏结束!-继续开始
2025-04-21 22:49:39: 状态:游戏结束!
2025-04-21 22:49:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:43: 状态:游戏结束!-继续开始
2025-04-21 22:49:43: 状态:游戏结束!
2025-04-21 22:49:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:47: 状态:游戏结束!-继续开始
2025-04-21 22:49:47: 状态:游戏结束!
2025-04-21 22:49:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:51: 状态:游戏结束!-继续开始
2025-04-21 22:49:51: 状态:游戏结束!
2025-04-21 22:49:55: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:49:56: 状态:游戏结束!-继续开始
2025-04-21 22:49:56: 状态:游戏结束!
2025-04-21 22:49:59: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:00: 状态:游戏结束!-继续开始
2025-04-21 22:50:00: 状态:游戏结束!
2025-04-21 22:50:03: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:04: 状态:游戏结束!-继续开始
2025-04-21 22:50:04: 状态:游戏结束!
2025-04-21 22:50:07: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:08: 状态:游戏结束!-继续开始
2025-04-21 22:50:08: 状态:游戏结束!
2025-04-21 22:50:11: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:12: 状态:游戏结束!-继续开始
2025-04-21 22:50:12: 状态:游戏结束!
2025-04-21 22:50:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:16: 状态:游戏结束!-继续开始
2025-04-21 22:50:16: 状态:游戏结束!
2025-04-21 22:50:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:20: 状态:游戏结束!-继续开始
2025-04-21 22:50:20: 状态:游戏结束!
2025-04-21 22:50:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:24: 状态:游戏结束!-继续开始
2025-04-21 22:50:24: 状态:游戏结束!
2025-04-21 22:50:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:29: 状态:游戏结束!-继续开始
2025-04-21 22:50:29: 状态:游戏结束!
2025-04-21 22:50:32: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:33: 状态:游戏结束!-继续开始
2025-04-21 22:50:33: 状态:游戏结束!
2025-04-21 22:50:36: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:37: 状态:游戏结束!-继续开始
2025-04-21 22:50:37: 状态:游戏结束!
2025-04-21 22:50:40: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:41: 状态:游戏结束!-继续开始
2025-04-21 22:50:41: 状态:游戏结束!
2025-04-21 22:50:44: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:45: 状态:游戏结束!-继续开始
2025-04-21 22:50:45: 状态:游戏结束!
2025-04-21 22:50:48: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:49: 状态:游戏结束!-继续开始
2025-04-21 22:50:49: 状态:游戏结束!
2025-04-21 22:50:52: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:53: 状态:游戏结束!-继续开始
2025-04-21 22:50:53: 状态:游戏结束!
2025-04-21 22:50:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:50:57: 状态:游戏结束!-继续开始
2025-04-21 22:50:57: 状态:游戏结束!
2025-04-21 22:51:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:01: 状态:游戏结束!-继续开始
2025-04-21 22:51:01: 状态:游戏结束!
2025-04-21 22:51:05: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:06: 状态:游戏结束!-继续开始
2025-04-21 22:51:06: 状态:游戏结束!
2025-04-21 22:51:09: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:10: 状态:游戏结束!-继续开始
2025-04-21 22:51:10: 状态:游戏结束!
2025-04-21 22:51:13: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:14: 状态:游戏结束!-继续开始
2025-04-21 22:51:14: 状态:游戏结束!
2025-04-21 22:51:17: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:18: 状态:游戏结束!-继续开始
2025-04-21 22:51:18: 状态:游戏结束!
2025-04-21 22:51:21: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:22: 状态:游戏结束!-继续开始
2025-04-21 22:51:22: 状态:游戏结束!
2025-04-21 22:51:25: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:26: 状态:游戏结束!-继续开始
2025-04-21 22:51:26: 状态:游戏结束!
2025-04-21 22:51:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:30: 状态:游戏结束!-继续开始
2025-04-21 22:51:30: 状态:游戏结束!
2025-04-21 22:51:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:34: 状态:游戏结束!-继续开始
2025-04-21 22:51:34: 状态:游戏结束!
2025-04-21 22:51:38: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:39: 状态:游戏结束!-继续开始
2025-04-21 22:51:39: 状态:游戏结束!
2025-04-21 22:51:42: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:43: 状态:游戏结束!-继续开始
2025-04-21 22:51:43: 状态:游戏结束!
2025-04-21 22:51:46: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:47: 状态:游戏结束!-继续开始
2025-04-21 22:51:47: 状态:游戏结束!
2025-04-21 22:51:50: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:51: 状态:游戏结束!-继续开始
2025-04-21 22:51:51: 状态:游戏结束!
2025-04-21 22:51:54: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:55: 状态:游戏结束!-继续开始
2025-04-21 22:51:55: 状态:游戏结束!
2025-04-21 22:51:58: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:51:59: 状态:游戏结束!-继续开始
2025-04-21 22:51:59: 状态:游戏结束!
2025-04-21 22:52:02: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:03: 状态:游戏结束!-继续开始
2025-04-21 22:52:03: 状态:游戏结束!
2025-04-21 22:52:06: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:07: 状态:游戏结束!-继续开始
2025-04-21 22:52:07: 状态:游戏结束!
2025-04-21 22:52:10: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:11: 状态:游戏结束!-继续开始
2025-04-21 22:52:11: 状态:游戏结束!
2025-04-21 22:52:15: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:16: 状态:游戏结束!-继续开始
2025-04-21 22:52:16: 状态:游戏结束!
2025-04-21 22:52:19: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:20: 状态:游戏结束!-继续开始
2025-04-21 22:52:20: 状态:游戏结束!
2025-04-21 22:52:23: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:24: 状态:游戏结束!-继续开始
2025-04-21 22:52:24: 状态:游戏结束!
2025-04-21 22:52:27: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:28: 状态:游戏结束!-继续开始
2025-04-21 22:52:28: 状态:游戏结束!
2025-04-21 22:52:31: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:32: 状态:游戏结束!-继续开始
2025-04-21 22:52:32: 状态:游戏结束!
2025-04-21 22:52:35: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:36: 状态:游戏结束!-继续开始
2025-04-21 22:52:36: 状态:游戏结束!
2025-04-21 22:52:39: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:40: 状态:游戏结束!-继续开始
2025-04-21 22:52:40: 状态:游戏结束!
2025-04-21 22:52:43: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:44: 状态:游戏结束!-继续开始
2025-04-21 22:52:44: 状态:游戏结束!
2025-04-21 22:52:47: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:48: 状态:游戏结束!-继续开始
2025-04-21 22:52:48: 状态:游戏结束!
2025-04-21 22:52:51: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:53: 状态:游戏结束!-继续开始
2025-04-21 22:52:53: 状态:游戏结束!
2025-04-21 22:52:56: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:52:57: 状态:游戏结束!-继续开始
2025-04-21 22:52:57: 状态:游戏结束!
2025-04-21 22:53:00: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:01: 状态:游戏结束!-继续开始
2025-04-21 22:53:01: 状态:游戏结束!
2025-04-21 22:53:04: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:05: 状态:游戏结束!-继续开始
2025-04-21 22:53:05: 状态:游戏结束!
2025-04-21 22:53:08: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:09: 状态:游戏结束!-继续开始
2025-04-21 22:53:09: 状态:游戏结束!
2025-04-21 22:53:12: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:13: 状态:游戏结束!-继续开始
2025-04-21 22:53:13: 状态:游戏结束!
2025-04-21 22:53:16: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:17: 状态:游戏结束!-继续开始
2025-04-21 22:53:17: 状态:游戏结束!
2025-04-21 22:53:20: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:21: 状态:游戏结束!-继续开始
2025-04-21 22:53:21: 状态:游戏结束!
2025-04-21 22:53:24: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:25: 状态:游戏结束!-继续开始
2025-04-21 22:53:25: 状态:游戏结束!
2025-04-21 22:53:29: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:30: 状态:游戏结束!-继续开始
2025-04-21 22:53:30: 状态:游戏结束!
2025-04-21 22:53:33: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:34: 状态:游戏结束!-继续开始
2025-04-21 22:53:34: 状态:游戏结束!
2025-04-21 22:53:37: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:38: 状态:游戏结束!-继续开始
2025-04-21 22:53:38: 状态:游戏结束!
2025-04-21 22:53:41: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:42: 状态:游戏结束!-继续开始
2025-04-21 22:53:42: 状态:游戏结束!
2025-04-21 22:53:45: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:46: 状态:游戏结束!-继续开始
2025-04-21 22:53:46: 状态:游戏结束!
2025-04-21 22:53:49: 点赞过程出现异常，跳过点赞: 未将对象引用设置到对象的实例。
2025-04-21 22:53:50: 状态:游戏结束!-继续开始
2025-04-21 22:53:50: 状态:游戏结束!
2025-04-21 22:53:53: 无法获取游戏会话信息，跳过点赞
2025-04-21 22:53:54: 状态:游戏结束!-继续开始
2025-04-21 22:53:54: 状态:游戏结束!
2025-04-21 22:53:54: 大厅等待中
2025-04-21 22:53:57: 检测到房间!
2025-04-21 22:54:01: 窗口隐藏1获取英雄ID:0
2025-04-21 22:54:04: 进入选择英雄界面！
2025-04-21 22:54:11: 获取英雄ID:7-上次选英雄ID:0
2025-04-21 22:54:11: 正在为Leblanc选英雄
2025-04-21 22:54:11: 进入选择英雄界面！
2025-04-21 22:54:27: 对局正在进行中!
2025-04-21 22:54:27: 窗口隐藏95
2025-04-21 22:54:27: 窗口隐藏2
2025-04-21 22:54:29: 对局正在进行中!
2025-04-21 22:54:42: 状态:重新连接!
2025-04-21 22:54:47: 对局正在进行中!
2025-04-21 23:07:54: 大厅等待中
2025-04-21 23:21:48: 检测到房间!
2025-04-21 23:21:53: 窗口隐藏1获取英雄ID:0
2025-04-21 23:21:55: 进入选择英雄界面！
2025-04-21 23:22:42: 获取英雄ID:58-上次选英雄ID:0
2025-04-21 23:22:42: 正在为Renekton选英雄
2025-04-21 23:22:42: 进入选择英雄界面！
2025-04-21 23:22:56: 窗口隐藏2
2025-04-21 23:22:57: 对局正在进行中!
2025-04-21 23:22:57: 窗口隐藏95
2025-04-21 23:22:59: 对局正在进行中!
2025-04-21 23:47:37: 状态:重新连接!
2025-04-21 23:51:08: 对局正在进行中!

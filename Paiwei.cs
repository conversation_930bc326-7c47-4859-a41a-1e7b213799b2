﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;

namespace WindowsFormsApp1
{
    public partial class Paiwei : Form
    {
        public static Paiwei paiwei;

        public  PictureBox[] TOP = new PictureBox[3];
        public PictureBox[] JG = new PictureBox[3];
        public PictureBox[] MID = new PictureBox[3];
        public PictureBox[] ADC = new PictureBox[3];
        public PictureBox[] SUP = new PictureBox[3];
        //public static Paiwei PAIWEI;
        public Paiwei()
        {
            InitializeComponent();
            Creatingcomponents();//创建自定义组件!

        }

        private void Creatingcomponents()
        {
            for (int i = 0; i < TOP.Count(); i++)
            {
                //实例化
                TOP[i] = new PictureBox();
                //定义控件名称
                TOP[i].Name = "TOP_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                TOP[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                TOP[i].Parent = groupBox11;
                //定义坐标
                TOP[i].Location = new Point(20 + (i % 3) * 100, 20 + (i / 3) * 100);
                //调整大小
                //bnt[i].AutoSize = true;
                TOP[i].Size = new Size(60, 60);

                TOP[i].SizeMode = PictureBoxSizeMode.StretchImage;

                TOP[i].Image = Resources._6666;

                TOP[i].BorderStyle = BorderStyle.FixedSingle;
                //批量添加事件
                TOP[i].MouseUp += new MouseEventHandler(picturebox_mouseup);
            }

            for (int i = 0; i < JG.Count(); i++)
            {
                //实例化
                JG[i] = new PictureBox();
                //定义控件名称
                JG[i].Name = "JG_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                JG[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                JG[i].Parent = groupBox22;
                //定义坐标
                JG[i].Location = new Point(20 + (i % 3) * 100, 20 + (i / 3) * 100);
                //调整大小
                //bnt[i].AutoSize = true;
                JG[i].Size = new Size(60, 60);

                JG[i].SizeMode = PictureBoxSizeMode.StretchImage;

                JG[i].Image = Resources._6666;

                JG[i].BorderStyle = BorderStyle.FixedSingle;
                //批量添加事件
                JG[i].MouseUp += new MouseEventHandler(picturebox_mouseup);
            }

            for (int i = 0; i < MID.Count(); i++)
            {
                //实例化
                MID[i] = new PictureBox();
                //定义控件名称
                MID[i].Name = "MID_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                MID[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                MID[i].Parent = groupBox33;
                //定义坐标
                MID[i].Location = new Point(20 + (i % 3) * 100, 20 + (i / 3) * 100);
                //调整大小
                //bnt[i].AutoSize = true;
                MID[i].Size = new Size(60, 60);

                MID[i].SizeMode = PictureBoxSizeMode.StretchImage;

                MID[i].Image = Resources._6666;

                MID[i].BorderStyle = BorderStyle.FixedSingle;
                //批量添加事件
                MID[i].MouseUp += new MouseEventHandler(picturebox_mouseup);
            }

            for (int i = 0; i < ADC.Count(); i++)
            {
                //实例化
                ADC[i] = new PictureBox();
                //定义控件名称
                ADC[i].Name = "ADC_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                ADC[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                ADC[i].Parent = groupBox44;
                //定义坐标
                ADC[i].Location = new Point(20 + (i % 3) * 100, 20 + (i / 3) * 100);
                //调整大小
                //bnt[i].AutoSize = true;
                ADC[i].Size = new Size(60, 60);

                ADC[i].SizeMode = PictureBoxSizeMode.StretchImage;

                ADC[i].Image = Resources._6666;

                ADC[i].BorderStyle = BorderStyle.FixedSingle;
                //批量添加事件
                ADC[i].MouseUp += new MouseEventHandler(picturebox_mouseup);
            }

            for (int i = 0; i < SUP.Count(); i++)
            {
                //实例化
                SUP[i] = new PictureBox();
                //定义控件名称
                SUP[i].Name = "SUP_" + i.ToString();
                //定义text属性，可以用string数组初始化为指定值
                SUP[i].Text = i.ToString();
                //注：如果不指定父容器，则坐标是相对于主窗体的
                SUP[i].Parent = groupBox55;
                //定义坐标
                SUP[i].Location = new Point(20 + (i % 3) * 100, 20 + (i / 3) * 100);
                //调整大小
                //bnt[i].AutoSize = true;
                SUP[i].Size = new Size(60, 60);

                SUP[i].SizeMode = PictureBoxSizeMode.StretchImage;

                SUP[i].Image = Resources._6666;

                SUP[i].BorderStyle = BorderStyle.FixedSingle;
                //批量添加事件
                SUP[i].MouseUp += new MouseEventHandler(picturebox_mouseup);
            }
        }
        void picturebox_mouseup(object sender, MouseEventArgs e)
        {
            PictureBox PP = ((PictureBox)sender);


            if (e.Button == MouseButtons.Right)
            {
                XINHAO = "";
                XINHAO_ban = PP.Name.ToString();

                contextMenuStrip4.Show(PP, new Point(e.X, e.Y));
            }

            if (e.Button == MouseButtons.Left)
            {
                Form1.BanID = -1;

                string name = PP.Name.ToString();

                XINHAO_ban = name;

                if (XINHAO_ban.IndexOf("0") != -1)
                {
                    XINHAOINT_ban = 0;
                }
                if (XINHAO_ban.IndexOf("1") != -1)
                {
                    XINHAOINT_ban = 1;
                }
                if (XINHAO_ban.IndexOf("2") != -1)
                {
                    XINHAOINT_ban = 2;
                }
                Form1.form33.TIAOTIAO();
                Form1.form33.ShowDialog();
            }
        }



        public  string XINHAO_ban = "";
        public  int XINHAOINT_ban = -1;






        public static string XINHAO = "";
        public static int XINHAOINT = 0;

        bool CHUSHU = false;
        private void Paiwei_Load(object sender, EventArgs e)
        {
            if(!CHUSHU)
            {
                TOP1.MouseUp += new MouseEventHandler(WEITOP);
                TOP2.MouseUp += new MouseEventHandler(WEITOP);
                TOP3.MouseUp += new MouseEventHandler(WEITOP);


                JG1.MouseUp += new MouseEventHandler(WEITOP);
                JG2.MouseUp += new MouseEventHandler(WEITOP);
                JG3.MouseUp += new MouseEventHandler(WEITOP);

                MID1.MouseUp += new MouseEventHandler(WEITOP);
                MID2.MouseUp += new MouseEventHandler(WEITOP);
                MID3.MouseUp += new MouseEventHandler(WEITOP);

                ADC1.MouseUp += new MouseEventHandler(WEITOP);
                ADC2.MouseUp += new MouseEventHandler(WEITOP);
                ADC3.MouseUp += new MouseEventHandler(WEITOP);

                SUP1.MouseUp += new MouseEventHandler(WEITOP);
                SUP2.MouseUp += new MouseEventHandler(WEITOP);
                SUP3.MouseUp += new MouseEventHandler(WEITOP);
                CHUSHU = true;
              
            }

           

        }
        void WEITOP(object sender, MouseEventArgs e)
        {

            PictureBox PP = ((PictureBox)sender);


            if (e.Button == MouseButtons.Right)
            {

                XINHAO_ban = "";
                XINHAO = PP.Name.ToString();

               


                contextMenuStrip4.Show(PP, new Point(e.X, e.Y));
            }

            if (e.Button == MouseButtons.Left)
            {
                string name = PP.Name.ToString();

                XINHAO = name;

                if(XINHAO.IndexOf("1")!=-1)
                {
                    XINHAOINT = 1;
                }
                if (XINHAO.IndexOf("2") != -1)
                {
                    XINHAOINT = 2;
                }
                if (XINHAO.IndexOf("3") != -1)
                {
                    XINHAOINT = 3;
                }
               
                Form1.paiweiget.ShowDialog();
            }
        }

        private void 清除当前数据ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if(XINHAO_ban!="")
            {
                string[] nameGO = { "TOP", "JG", "MID", "ADC", "SUP" };

                string item="";
                foreach (var itemG in nameGO)
                {
                    if(XINHAO_ban.IndexOf(itemG) !=-1)
                    {
                        item = itemG;
                        break;
                    }
                }

                for (int i = 0; i < 3; i++)
                {
                    switch (item)
                    {

                        case "TOP":

                            Form1.WeiTOPban[i] = "0";
                            Form1.paiwei.TOP[i].Image = Resources._6666;
                            Util.WriteIniData("AutomaticHero", item + "BanID" + i.ToString(), "0", @".\Data\Cofing.ini");
                            break;

                        case "JG":

                            Form1.WeiJGban[i] = "0";
                            Form1.paiwei.JG[i].Image = Resources._6666;
                            Util.WriteIniData("AutomaticHero", item + "BanID" + i.ToString(), "0", @".\Data\Cofing.ini");

                            break;

                        case "MID":

                            Form1.WeiMIDban[i] = "0";
                            Form1.paiwei.MID[i].Image = Resources._6666;
                            Util.WriteIniData("AutomaticHero", item + "BanID" + i.ToString(), "0", @".\Data\Cofing.ini");

                            break;
                        case "ADC":

                            Form1.WeiADCban[i] = "0";
                            Form1.paiwei.ADC[i].Image = Resources._6666;
                            Util.WriteIniData("AutomaticHero", item + "BanID" + i.ToString(), "0", @".\Data\Cofing.ini");

                            break;
                        case "SUP":

                            Form1.WeiSUPban[i] = "0";
                            Form1.paiwei.SUP[i].Image = Resources._6666;
                            Util.WriteIniData("AutomaticHero", item + "BanID" + i.ToString(), "0", @".\Data\Cofing.ini");

                            break;
                        default:
                            break;
                    }
                }
            }
    

           


        














            if (XINHAO.IndexOf("TOP")!=-1)
            {
                Form1.WeiTOP[0] = "0";
                Form1.WeiTOP[1] = "0";
                Form1.WeiTOP[2] = "0";

                Form1.paiwei.TOP1.Image = Resources._1111;
                Form1.paiwei.TOP2.Image = Resources._1111;
                Form1.paiwei.TOP3.Image = Resources._1111;

                Util.WriteIniData("AutomaticHero", "WeiTOP1", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiTOP2", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiTOP3", "0", @".\Data\Cofing.ini");
            }
            if (XINHAO.IndexOf("JG") != -1)
            {
                Form1.WeiJG[0] = "0";
                Form1.WeiJG[1] = "0";
                Form1.WeiJG[2] = "0";

                Form1.paiwei.JG1.Image = Resources._1111;
                Form1.paiwei.JG2.Image = Resources._1111;
                Form1.paiwei.JG3.Image = Resources._1111;
                Util.WriteIniData("AutomaticHero", "WeiJG1", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiJG2", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiJG3", "0", @".\Data\Cofing.ini");
            }
            if (XINHAO.IndexOf("MID") != -1)
            {
                Form1.WeiMID[0] = "0";
                Form1.WeiMID[1] = "0";
                Form1.WeiMID[2] = "0";

                Form1.paiwei.MID1.Image = Resources._1111;
                Form1.paiwei.MID2.Image = Resources._1111;
                Form1.paiwei.MID3.Image = Resources._1111;

                Util.WriteIniData("AutomaticHero", "WeiMID1", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiMID2", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiMID3", "0", @".\Data\Cofing.ini");
            }
            if (XINHAO.IndexOf("ADC") != -1)
            {
                Form1.WeiADC[0] = "0";
                Form1.WeiADC[1] = "0";
                Form1.WeiADC[2] = "0";

                Form1.paiwei.ADC1.Image = Resources._1111;
                Form1.paiwei.ADC2.Image = Resources._1111;
                Form1.paiwei.ADC3.Image = Resources._1111;

                Util.WriteIniData("AutomaticHero", "WeiADC1", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiADC2", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiADC3", "0", @".\Data\Cofing.ini");
            }
            if (XINHAO.IndexOf("SUP") != -1)
            {
                Form1.WeiSUP[0] = "0";
                Form1.WeiSUP[1] = "0";
                Form1.WeiSUP[2] = "0";

                Form1.paiwei.SUP1.Image = Resources._1111;
                Form1.paiwei.SUP2.Image = Resources._1111;
                Form1.paiwei.SUP3.Image = Resources._1111;

                Util.WriteIniData("AutomaticHero", "WeiSUP1", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiSUP2", "0", @".\Data\Cofing.ini");
                Util.WriteIniData("AutomaticHero", "WeiSUP3", "0", @".\Data\Cofing.ini");
            }


        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            var PWHerodata = new Thread(Form1.paiweiget.Getrunninginitialization);
            PWHerodata.IsBackground = true;
            PWHerodata.Start();//获取排位英雄数据
        }
    }
}

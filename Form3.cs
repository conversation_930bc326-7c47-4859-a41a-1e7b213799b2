﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WindowsFormsApp1
{
    public partial class Form3 : Form
    {

        public Form3()
        {

            InitializeComponent();
        }

        private void Form3_Load(object sender, EventArgs e)
        {
            listView1.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView2.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView3.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView4.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView5.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
            listView6.MouseDoubleClick += new MouseEventHandler(listView_MouseDoubleClick);
        }


        public bool dddddddd()
        {

            Form1.form1.Rizhi("2正在获取英雄数据中!2");

           

            if (Form1.GetDataKun.Count > 0)
            {

                imageList1.Images.Clear();
                listView1.Items.Clear();

                imageList2.Images.Clear();
                listView2.Items.Clear();

                imageList3.Images.Clear();
                listView3.Items.Clear();

                imageList4.Images.Clear();
                listView4.Items.Clear();

                imageList5.Images.Clear();
                listView5.Items.Clear();

                imageList6.Images.Clear();
                listView6.Items.Clear();



                for (var i = 0; i < Form1.GetDataKun.Count; i++)
                {

                    var HeroID = Form1.GetDataKun[i].HeroID;

                    var HeroLEIXING = Form1.GetDataKun[i].Weizi;

                    var Heroname = Form1.GetDataKun[i].Heroname;

                    var icon = Form1.GetDataKun[i].icon;

                    if (HeroLEIXING == "TOP")
                    {
                        this.listView1.BeginUpdate();
                        this.imageList1.Images.Add(HeroID, icon);

                        this.listView1.Items.Add(Heroname, HeroID);
                        this.listView1.EndUpdate();
                    }

                    if (HeroLEIXING == "JUNGLE")
                    {

                        this.listView2.BeginUpdate();
                        this.imageList2.Images.Add(HeroID, icon);

                        this.listView2.Items.Add(Heroname, HeroID);
                        this.listView2.EndUpdate();
                    }

                    if (HeroLEIXING == "MID")
                    {

                        this.listView3.BeginUpdate();
                        this.imageList3.Images.Add(HeroID, icon);

                        this.listView3.Items.Add(Heroname, HeroID);
                        this.listView3.EndUpdate();
                    }

                    if (HeroLEIXING == "ADC")
                    {

                        this.listView4.BeginUpdate();
                        this.imageList4.Images.Add(HeroID, icon);

                        this.listView4.Items.Add(Heroname, HeroID);
                        this.listView4.EndUpdate();
                    }

                    if (HeroLEIXING == "SUPPORT")
                    {

                        this.listView5.BeginUpdate();
                        this.imageList5.Images.Add(HeroID, icon);

                        this.listView5.Items.Add(Heroname, HeroID);
                        this.listView5.EndUpdate();
                    }
                    if (HeroLEIXING == "QITA")

                    {

                        this.listView6.BeginUpdate();
                        this.imageList6.Images.Add(HeroID, icon);

                        this.listView6.Items.Add(Heroname, HeroID);
                        this.listView6.EndUpdate();
                    }

                }

                Form1.form1.Rizhi("初始化英雄数据成功!");

                Form1.form1.pictureBox2.Enabled = true;

                Form1.form1.pictureBox3.Enabled = true;

                Form1.form1.InitializationG = 2;


                return true;
            }
            else
            {
                return false;
            }
        }
        public void Getrunninginitialization()
        {
            bool wanbi = false;
            while (!wanbi)
            {


                var HeroData = Util.zi_Getwenb("/lol-champions/v1/owned-champions-minimal");

                var HeroDataJX = JSON.Jiexun_1(HeroData);

                try
                {
                    if (Form1.form1.QQQQQQQQQ || Form1.form1.EEEEEEEEEEEE)
                        break;

                    if (Form1.form1.pictureBox2.InvokeRequired)
                    {

                        Form1.form1.pictureBox2.Invoke(new Action<int>(n =>
                        {
                            Form1.form1.pictureBox2.Enabled = true;
                            var woowo0 = Util.ReadIniData("AutomaticHero", "HeroID0", "123", @".\Data\Cofing.ini");
                            var woowo1 = Util.ReadIniData("AutomaticHero", "HeroID1", "123", @".\Data\Cofing.ini");
                            //InitializationHero(1);
                            if (HeroDataJX.Count > 0)
                            {
                                for (var i = 0; i < HeroDataJX.Count; i++)
                                {

                                    if (HeroDataJX[i]["id"].ToString() == woowo0)
                                    {
                                        Form1.XuanyxSHUZU[0] = woowo0;
                                        Form1.form1.pictureBox2.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + woowo0 + ".png");

                                    }

                                    if (HeroDataJX[i]["id"].ToString() == woowo1)
                                    {
                                        Form1.XuanyxSHUZU[1] = woowo1;
                                        Form1.form1.pictureBox3.Image = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + woowo1 + ".png");

                                    }


                                }
                            }


                        }), 1);


                    }






                    Form1.form1.Rizhi("1正在获取英雄数据中!1");




                    Form1.GetDataKun.Clear();

                    if (HeroDataJX.Count > 0)
                    {





                        for (var i = 0; i < HeroDataJX.Count; i++)
                        {




                            var HeroID = HeroDataJX[i]["id"].ToString();

                            var HeroLEIXING = Util.GetheronameWEI(HeroID);

                            var Heroname = HeroDataJX[i]["name"].ToString();

                            var icon = Util.zi_GetwenbZ("/lol-game-data/assets/v1/champion-icons/" + HeroID + ".png");






                            if (HeroLEIXING.Contains("TOP"))
                            {




                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "TOP";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.GetDataKun.Add(JUCS);


                            }

                            if (HeroLEIXING.Contains("JUNGLE"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "JUNGLE";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.GetDataKun.Add(JUCS);

                            }
                            if (HeroLEIXING.Contains("MID"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "MID";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.GetDataKun.Add(JUCS);

                            }

                            if (HeroLEIXING.Contains("ADC"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "ADC";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.GetDataKun.Add(JUCS);

                            }

                            if (HeroLEIXING.Contains("SUPPORT"))
                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "SUPPORT";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.GetDataKun.Add(JUCS);
                            }
                            if (HeroLEIXING.Count() == 0)

                            {
                                var JUCS = new Form1.BanData();
                                JUCS.Weizi = "QITA";
                                JUCS.icon = icon;
                                JUCS.HeroID = HeroID;
                                JUCS.Heroname = Heroname;
                                Form1.GetDataKun.Add(JUCS);

                            }
                        }

                        if (Form1.form1.pictureBox3.InvokeRequired)
                        {

                            Form1.form1.pictureBox3.Invoke(new Action<int>(n =>
                            {
                                if (dddddddd())
                                {
                                    
                                    wanbi = true;
                                }
                                    






                            }), 1);

                        }
                        else
                        {
                            Form1.form1.pictureBox3.Enabled = false;
                            if (dddddddd())
                            {
                               
                                wanbi = true;
                            }


                        }


                    }






















                    if (wanbi)
                        break;



                }
                catch (Exception e)
                {

                    Console.WriteLine(e.Message);
                }


                Thread.Sleep(100);




            }




        }


        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {


            InitializationHero(((ListView)sender));



        }
        private void InitializationHero(ListView listViewYY /*ImageList GO*/)
        {

            if (listViewYY.SelectedItems[0].Index != -1)
            {

                var index1 = listViewYY.SelectedItems[0].Index;
                Image QQQWW = null;
                switch (listViewYY.Name)
                {

                    case "listView1":
                        QQQWW = imageList1.Images[index1];
                        break;
                    case "listView2":
                        QQQWW = imageList2.Images[index1];
                        break;
                    case "listView3":
                        QQQWW = imageList3.Images[index1];
                        break;
                    case "listView4":
                        QQQWW = imageList4.Images[index1];
                        break;
                    case "listView5":
                        QQQWW = imageList5.Images[index1];
                        break;
                    case "listView6":
                        QQQWW = imageList6.Images[index1];
                        break;
                    default:
                        break;
                }
                if (QQQWW != null)
                {


                    var HAHAID = listViewYY.Items[index1].ImageKey;

                    var XQ = Form1.XuanID;


                    if (XQ == 0 && Form1.XuanyxSHUZU[1] == HAHAID)
                    {
                        var QQ1 = Form1.form1.pictureBox2.Image;
                        var WW1 = Form1.XuanyxSHUZU[0];

                        Form1.XuanyxSHUZU[1] = WW1;
                        Form1.form1.pictureBox3.Image = QQ1;

                        Util.WriteIniData("AutomaticHero", "HeroID1", WW1, @".\Data\Cofing.ini");
                    }
                    else if (XQ == 1 && Form1.XuanyxSHUZU[0] == HAHAID)
                    {
                        var QQ2 = Form1.form1.pictureBox3.Image;
                        var WW2 = Form1.XuanyxSHUZU[1];

                        Form1.XuanyxSHUZU[0] = WW2;
                        Form1.form1.pictureBox2.Image = QQ2;
                        Util.WriteIniData("AutomaticHero", "HeroID0", WW2, @".\Data\Cofing.ini");
                    }

                    if (XQ == 0)
                    {
                        Form1.XuanyxSHUZU[0] = HAHAID;
                        Form1.form1.pictureBox2.Image = QQQWW;
                    }
                    else if (XQ == 1)
                    {

                        Form1.XuanyxSHUZU[1] = HAHAID;
                        Form1.form1.pictureBox3.Image = QQQWW;
                    }

                    Util.WriteIniData("AutomaticHero", "HeroID" + XQ.ToString(), HAHAID, @".\Data\Cofing.ini");
                    Form1.form1.YXYX = false;
                    this.Close();
                }


            }


        }
        public Bitmap WhiteAndBlack(System.Drawing.Bitmap image)
        {
            //原来图片的长度

            int width = image.Width;

            //原来图片的高度

            int height = image.Height;

            //改变色素

            //横坐标

            for (int x = 0; x < width; x++)

            {
                //纵坐标

                for (int y = 0; y < height; y++)

                {
                    //获得坐标(x,y)颜色

                    Color color = image.GetPixel(x, y);

                    //获得该颜色下的黑白色

                    int value = (color.R + color.G + color.B) / 3;

                    //设置颜色

                    image.SetPixel(x, y, Color.FromArgb(value, value, value));

                }

            }

            return image;

        }


    }
}

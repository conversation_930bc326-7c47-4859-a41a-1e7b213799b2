2025-05-23 02:12:39: 正在搜索英雄联盟客户端和Riot客户端...
2025-05-23 02:12:39: 正在获取Riot Games最新版本信息...
2025-05-23 02:12:40: 成功获取版本列表，共467个版本，最新版本为: 15.10.1
2025-05-23 02:12:40: 已获取Riot最新版本数据，当前最新版本: 15.10.1, 最低要求版本: 15.8.1
2025-05-23 02:12:40: 搜索驱动器 C:\ (深度 6)...
2025-05-23 02:12:40: 搜索驱动器 D:\ (深度 6)...
2025-05-23 02:12:40: 检测到LeagueClient.exe版本: 15.9.678.1165
2025-05-23 02:12:40: 比较客户端版本(15.9.678.1165)与最低要求版本(15.8.1)
2025-05-23 02:12:40: 客户端版本(15.9.678.1165)满足最低要求版本(15.8.1)
2025-05-23 02:12:40: 尝试查找配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-23 02:12:40: 配置文件是否存在: True
2025-05-23 02:12:40: 配置文件大小: 1260 字节
2025-05-23 02:12:40: 成功读取配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1260 字节
2025-05-23 02:12:40: 检测到游戏区域: TW2
2025-05-23 02:12:40: 检测到非PBE区域，将作为候选客户端
2025-05-23 02:12:40: 添加英雄联盟客户端 (非PBE区域) 到候选列表: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165
2025-05-23 02:12:40: 找到Riot客户端: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-23 02:12:41: 搜索驱动器 F:\ (深度 6)...
2025-05-23 02:12:42: 搜索驱动器 G:\ (深度 6)...
2025-05-23 02:12:42: 检测到LeagueClient.exe版本: 15.10.683.1368
2025-05-23 02:12:42: 比较客户端版本(15.10.683.1368)与最低要求版本(15.8.1)
2025-05-23 02:12:42: 客户端版本(15.10.683.1368)满足最低要求版本(15.8.1)
2025-05-23 02:12:42: 尝试查找配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-23 02:12:42: 配置文件是否存在: True
2025-05-23 02:12:42: 配置文件大小: 1251 字节
2025-05-23 02:12:42: 成功读取配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1251 字节
2025-05-23 02:12:42: 检测到游戏区域: TW2
2025-05-23 02:12:42: 检测到非PBE区域，将作为候选客户端
2025-05-23 02:12:42: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368
2025-05-23 02:12:42: 检测到LeagueClient.exe版本: 15.10.677.6592
2025-05-23 02:12:42: 比较客户端版本(15.10.677.6592)与最低要求版本(15.8.1)
2025-05-23 02:12:42: 客户端版本(15.10.677.6592)满足最低要求版本(15.8.1)
2025-05-23 02:12:42: 尝试查找配置文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-23 02:12:42: 配置文件是否存在: False
2025-05-23 02:12:42: 未找到LeagueClientSettings.yaml文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-23 02:12:42: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592
2025-05-23 02:12:44: 找到 3 个候选客户端，按优先级排序如下:
2025-05-23 02:12:44: 1. 路径: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-23 02:12:44: 2. 路径: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592, 区域类型: 普通
2025-05-23 02:12:44: 3. 路径: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165, 区域类型: 普通
2025-05-23 02:12:44: 选择最佳客户端: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-23 02:12:44: 搜索完成，耗时: 5.08秒
2025-05-23 02:12:44: 英雄联盟客户端路径: G:\Riot Games\League of Legends\LeagueClient.exe
2025-05-23 02:12:44: Riot客户端路径: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-23 13:34:38: 正在搜索英雄联盟客户端和Riot客户端...
2025-05-23 13:34:38: 正在获取Riot Games最新版本信息...
2025-05-23 13:34:40: 成功获取版本列表，共467个版本，最新版本为: 15.10.1
2025-05-23 13:34:40: 已获取Riot最新版本数据，当前最新版本: 15.10.1, 最低要求版本: 15.8.1
2025-05-23 13:34:40: 搜索驱动器 C:\ (深度 6)...
2025-05-23 13:34:40: 搜索驱动器 D:\ (深度 6)...
2025-05-23 13:34:40: 检测到LeagueClient.exe版本: 15.9.678.1165
2025-05-23 13:34:40: 比较客户端版本(15.9.678.1165)与最低要求版本(15.8.1)
2025-05-23 13:34:40: 客户端版本(15.9.678.1165)满足最低要求版本(15.8.1)
2025-05-23 13:34:40: 尝试查找配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-23 13:34:40: 配置文件是否存在: True
2025-05-23 13:34:40: 配置文件大小: 1260 字节
2025-05-23 13:34:40: 成功读取配置文件: D:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1260 字节
2025-05-23 13:34:40: 检测到游戏区域: TW2
2025-05-23 13:34:40: 检测到非PBE区域，将作为候选客户端
2025-05-23 13:34:40: 添加英雄联盟客户端 (非PBE区域) 到候选列表: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165
2025-05-23 13:34:40: 找到Riot客户端: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-23 13:34:41: 搜索驱动器 F:\ (深度 6)...
2025-05-23 13:34:42: 搜索驱动器 G:\ (深度 6)...
2025-05-23 13:34:42: 检测到LeagueClient.exe版本: 15.10.683.1368
2025-05-23 13:34:42: 比较客户端版本(15.10.683.1368)与最低要求版本(15.8.1)
2025-05-23 13:34:42: 客户端版本(15.10.683.1368)满足最低要求版本(15.8.1)
2025-05-23 13:34:42: 尝试查找配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml
2025-05-23 13:34:42: 配置文件是否存在: True
2025-05-23 13:34:42: 配置文件大小: 1251 字节
2025-05-23 13:34:42: 成功读取配置文件: G:\Riot Games\League of Legends\Config\LeagueClientSettings.yaml, 文件大小: 1251 字节
2025-05-23 13:34:42: 检测到游戏区域: TW2
2025-05-23 13:34:42: 检测到非PBE区域，将作为候选客户端
2025-05-23 13:34:42: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368
2025-05-23 13:34:42: 检测到LeagueClient.exe版本: 15.10.677.6592
2025-05-23 13:34:42: 比较客户端版本(15.10.677.6592)与最低要求版本(15.8.1)
2025-05-23 13:34:42: 客户端版本(15.10.677.6592)满足最低要求版本(15.8.1)
2025-05-23 13:34:42: 尝试查找配置文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-23 13:34:42: 配置文件是否存在: False
2025-05-23 13:34:42: 未找到LeagueClientSettings.yaml文件: G:\Riot Games\League of Legends (PBE)\Config\LeagueClientSettings.yaml
2025-05-23 13:34:42: 添加英雄联盟客户端 (非PBE区域) 到候选列表: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592
2025-05-23 13:34:43: 找到 3 个候选客户端，按优先级排序如下:
2025-05-23 13:34:43: 1. 路径: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-23 13:34:43: 2. 路径: G:\Riot Games\League of Legends (PBE)\LeagueClient.exe, 版本: 15.10.677.6592, 区域类型: 普通
2025-05-23 13:34:43: 3. 路径: D:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.9.678.1165, 区域类型: 普通
2025-05-23 13:34:43: 选择最佳客户端: G:\Riot Games\League of Legends\LeagueClient.exe, 版本: 15.10.683.1368, 区域类型: 普通
2025-05-23 13:34:43: 搜索完成，耗时: 4.79秒
2025-05-23 13:34:43: 英雄联盟客户端路径: G:\Riot Games\League of Legends\LeagueClient.exe
2025-05-23 13:34:43: Riot客户端路径: D:\Riot Games\Riot Client\RiotClientServices.exe
2025-05-23 13:34:49: Riot客户端快捷方式已创建
2025-05-23 13:34:49: 启动脚本已写入桌面
2025-05-23 13:34:49: 正在获取推荐网站列表...
2025-05-23 13:34:50: 获取到 3 个链接
2025-05-23 13:34:52: 已打开 3 个网站
2025-05-23 13:34:52: 开始从WebDAV服务器下载便捷文件夹...
2025-05-23 13:34:53: 下载便捷文件夹异常: 发生一个或多个错误。
2025-05-23 13:35:55: 开始从WebDAV服务器下载便捷文件夹...
2025-05-23 13:35:56: 下载便捷文件夹异常: 发生一个或多个错误。
2025-05-23 14:17:59: 开始从WebDAV服务器下载便捷文件夹...
2025-05-23 14:17:59: WebDAV服务器: www1.movemama.cn
2025-05-23 14:17:59: 目标文件夹: 便捷
2025-05-23 14:17:59: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-23 14:17:59: 本地文件夹已存在
2025-05-23 14:17:59: 已创建HttpClient，超时时间: 10分钟
2025-05-23 14:17:59: 正在获取文件夹内容列表: http://www1.movemama.cn/便捷/
2025-05-23 14:18:00: PROPFIND请求响应状态: 207
2025-05-23 14:18:00: 下载便捷文件夹异常: 发生一个或多个错误。
2025-05-23 15:06:23: 开始从WebDAV服务器下载便捷文件夹...
2025-05-23 15:06:23: WebDAV服务器: www1.movemama.cn
2025-05-23 15:06:23: 目标文件夹: 便捷
2025-05-23 15:06:23: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-23 15:06:23: 本地文件夹已存在
2025-05-23 15:06:23: 已设置WebClient认证信息
2025-05-23 15:06:23: 正在获取文件夹内容列表: http://www1.movemama.cn/便捷/
2025-05-23 15:06:24: PROPFIND请求响应状态: 207
2025-05-23 15:06:24: 成功获取文件夹内容列表
2025-05-23 15:06:24: 响应内容长度: 3096 字符
2025-05-23 15:06:24: 解析到 0 个文件
2025-05-23 15:06:24: 便捷文件夹为空或无法访问
2025-05-23 15:06:24: WebDAV下载操作完成
2025-05-23 22:08:42: 开始从WebDAV服务器下载便捷文件夹...
2025-05-23 22:08:42: WebDAV服务器: www1.movemama.cn
2025-05-23 22:08:42: 目标文件夹: 便捷
2025-05-23 22:08:42: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-23 22:08:42: 本地文件夹已存在
2025-05-23 22:08:42: 已设置WebClient认证信息
2025-05-23 22:08:42: 正在获取文件夹内容列表: http://www1.movemama.cn/便捷/
2025-05-23 22:08:43: PROPFIND请求响应状态: 207
2025-05-23 22:08:43: 成功获取文件夹内容列表
2025-05-23 22:08:43: 响应内容长度: 3096 字符
2025-05-23 22:08:43: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-23_22-08-43.txt
2025-05-23 22:08:43: 开始解析WebDAV XML响应...
2025-05-23 22:08:43: XML分割为 115 行
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/
2025-05-23 22:08:43: 跳过文件夹路径: /%e4%be%bf%e6%8d%b7/
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/%e5%85%8d%e9%87%8d%e5%90%af-%e5%90%af%e5%8a%a8VanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-23 22:08:43: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/%e9%bb%91%e5%8f%b7.txt
2025-05-23 22:08:43: 添加文件: 黑号.txt
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/%e7%bd%91%e5%90%a7%e5%85%8d%e9%87%8d%e5%90%af%e5%8a%a0%e8%bd%bdVanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-23 22:08:43: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/%e6%94%b9%e7%9a%84%e5%86%85%e5%ae%b9.txt
2025-05-23 22:08:43: 添加文件: 改的内容.txt
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-23 22:08:43: 跳过文件夹路径: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-23 22:08:43: 找到href: /%e4%be%bf%e6%8d%b7/setup.exe
2025-05-23 22:08:43: 添加文件: setup.exe
2025-05-23 22:08:43: 最终解析到 5 个文件
2025-05-23 22:08:43: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-23 22:08:43: 文件列表: 黑号.txt
2025-05-23 22:08:43: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:08:43: 文件列表: 改的内容.txt
2025-05-23 22:08:43: 文件列表: setup.exe
2025-05-23 22:08:43: 解析到 5 个文件
2025-05-23 22:08:43: 开始下载 5 个文件...
2025-05-23 22:08:43: 文件列表:
2025-05-23 22:08:43:   - 免重启-启动VanGuard驱动.bat
2025-05-23 22:08:43:   - 黑号.txt
2025-05-23 22:08:43:   - 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:08:43:   - 改的内容.txt
2025-05-23 22:08:43:   - setup.exe
2025-05-23 22:08:43: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-23 22:08:43:   源URL: http://www1.movemama.cn/便捷/%E5%85%8D%E9%87%8D%E5%90%AF-%E5%90%AF%E5%8A%A8VanGuard%E9%A9%B1%E5%8A%A8.bat
2025-05-23 22:08:43:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-23 22:08:43: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-23 22:08:43:   文件大小: 1,193 字节 (1.17 KB)
2025-05-23 22:08:43:   下载耗时: 0.34 秒
2025-05-23 22:08:43:   进度: 1/5
2025-05-23 22:08:43: 正在下载: 黑号.txt
2025-05-23 22:08:43:   源URL: http://www1.movemama.cn/便捷/%E9%BB%91%E5%8F%B7.txt
2025-05-23 22:08:43:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-23 22:08:43: 下载完成: 黑号.txt
2025-05-23 22:08:43:   文件大小: 125 字节 (0.12 KB)
2025-05-23 22:08:43:   下载耗时: 0.34 秒
2025-05-23 22:08:43:   进度: 2/5
2025-05-23 22:08:43: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:08:43:   源URL: http://www1.movemama.cn/便捷/%E7%BD%91%E5%90%A7%E5%85%8D%E9%87%8D%E5%90%AF%E5%8A%A0%E8%BD%BDVanGuard%E9%A9%B1%E5%8A%A8.bat
2025-05-23 22:08:43:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-23 22:08:44: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:08:44:   文件大小: 1,884 字节 (1.84 KB)
2025-05-23 22:08:44:   下载耗时: 0.34 秒
2025-05-23 22:08:44:   进度: 3/5
2025-05-23 22:08:44: 正在下载: 改的内容.txt
2025-05-23 22:08:44:   源URL: http://www1.movemama.cn/便捷/%E6%94%B9%E7%9A%84%E5%86%85%E5%AE%B9.txt
2025-05-23 22:08:44:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-23 22:08:44: 下载完成: 改的内容.txt
2025-05-23 22:08:44:   文件大小: 1,750 字节 (1.71 KB)
2025-05-23 22:08:44:   下载耗时: 0.34 秒
2025-05-23 22:08:44:   进度: 4/5
2025-05-23 22:08:44: 正在下载: setup.exe
2025-05-23 22:08:44:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-23 22:08:44:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-23 22:08:51: 下载完成: setup.exe
2025-05-23 22:08:51:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-23 22:08:51:   下载耗时: 7.29 秒
2025-05-23 22:08:51:   进度: 5/5
2025-05-23 22:08:51: === 下载完成统计 ===
2025-05-23 22:08:51: 总文件数: 5
2025-05-23 22:08:51: 成功下载: 5
2025-05-23 22:08:51: 下载失败: 0
2025-05-23 22:08:51: 成功率: 100.0%
2025-05-23 22:08:51: 便捷文件夹下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-23 22:08:51: WebDAV下载操作完成
2025-05-23 22:58:16: 开始从WebDAV服务器下载便捷文件夹...
2025-05-23 22:58:16: WebDAV服务器: www1.movemama.cn
2025-05-23 22:58:16: 目标文件夹: 便捷
2025-05-23 22:58:16: 本地保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-23 22:58:16: 本地文件夹已存在
2025-05-23 22:58:16: 已设置WebClient认证信息
2025-05-23 22:58:16: 正在获取文件夹内容列表: http://www1.movemama.cn/便捷/
2025-05-23 22:58:17: PROPFIND请求响应状态: 207
2025-05-23 22:58:17: 成功获取文件夹内容列表
2025-05-23 22:58:17: 响应内容长度: 3096 字符
2025-05-23 22:58:17: 开始递归下载整个文件夹结构...
2025-05-23 22:58:17: 正在获取文件夹内容列表: http://www1.movemama.cn/便捷/
2025-05-23 22:58:18: PROPFIND请求响应状态: 207
2025-05-23 22:58:18: 成功获取文件夹内容列表
2025-05-23 22:58:18: 响应内容长度: 3096 字符
2025-05-23 22:58:18: XML响应内容已保存到: C:\Users\<USER>\Desktop\WebDAV_XML_Debug_2025-05-23_22-58-18.txt
2025-05-23 22:58:18: 开始解析WebDAV XML响应...
2025-05-23 22:58:18: XML分割为 115 行
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/
2025-05-23 22:58:18: 跳过文件夹路径: /%e4%be%bf%e6%8d%b7/
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/%e5%85%8d%e9%87%8d%e5%90%af-%e5%90%af%e5%8a%a8VanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-23 22:58:18: 添加文件: 免重启-启动VanGuard驱动.bat
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/%e9%bb%91%e5%8f%b7.txt
2025-05-23 22:58:18: 添加文件: 黑号.txt
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/%e7%bd%91%e5%90%a7%e5%85%8d%e9%87%8d%e5%90%af%e5%8a%a0%e8%bd%bdVanGuard%e9%a9%b1%e5%8a%a8.bat
2025-05-23 22:58:18: 添加文件: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/%e6%94%b9%e7%9a%84%e5%86%85%e5%ae%b9.txt
2025-05-23 22:58:18: 添加文件: 改的内容.txt
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-23 22:58:18: 跳过文件夹路径: /%e4%be%bf%e6%8d%b7/%e8%84%9a%e6%9c%ac/
2025-05-23 22:58:18: 找到href: /%e4%be%bf%e6%8d%b7/setup.exe
2025-05-23 22:58:18: 添加文件: setup.exe
2025-05-23 22:58:18: 最终解析到 5 个文件
2025-05-23 22:58:18: 文件列表: 免重启-启动VanGuard驱动.bat
2025-05-23 22:58:18: 文件列表: 黑号.txt
2025-05-23 22:58:18: 文件列表: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:58:18: 文件列表: 改的内容.txt
2025-05-23 22:58:18: 文件列表: setup.exe
2025-05-23 22:58:18: 解析到 5 个文件
2025-05-23 22:58:18: 开始下载 5 个文件...
2025-05-23 22:58:18: 文件列表:
2025-05-23 22:58:18:   - 免重启-启动VanGuard驱动.bat
2025-05-23 22:58:18:   - 黑号.txt
2025-05-23 22:58:18:   - 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:58:18:   - 改的内容.txt
2025-05-23 22:58:18:   - setup.exe
2025-05-23 22:58:18: 正在下载: 免重启-启动VanGuard驱动.bat
2025-05-23 22:58:18:   源URL: http://www1.movemama.cn/便捷/%E5%85%8D%E9%87%8D%E5%90%AF-%E5%90%AF%E5%8A%A8VanGuard%E9%A9%B1%E5%8A%A8.bat
2025-05-23 22:58:18:   目标路径: C:\Users\<USER>\Desktop\便捷\免重启-启动VanGuard驱动.bat
2025-05-23 22:58:18: 下载完成: 免重启-启动VanGuard驱动.bat
2025-05-23 22:58:18:   文件大小: 1,193 字节 (1.17 KB)
2025-05-23 22:58:18:   下载耗时: 0.33 秒
2025-05-23 22:58:18:   进度: 1/0
2025-05-23 22:58:18: 正在下载: 黑号.txt
2025-05-23 22:58:18:   源URL: http://www1.movemama.cn/便捷/%E9%BB%91%E5%8F%B7.txt
2025-05-23 22:58:18:   目标路径: C:\Users\<USER>\Desktop\便捷\黑号.txt
2025-05-23 22:58:18: 下载完成: 黑号.txt
2025-05-23 22:58:18:   文件大小: 125 字节 (0.12 KB)
2025-05-23 22:58:18:   下载耗时: 0.33 秒
2025-05-23 22:58:18:   进度: 2/0
2025-05-23 22:58:18: 正在下载: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:58:18:   源URL: http://www1.movemama.cn/便捷/%E7%BD%91%E5%90%A7%E5%85%8D%E9%87%8D%E5%90%AF%E5%8A%A0%E8%BD%BDVanGuard%E9%A9%B1%E5%8A%A8.bat
2025-05-23 22:58:18:   目标路径: C:\Users\<USER>\Desktop\便捷\网吧免重启加载VanGuard驱动.bat
2025-05-23 22:58:19: 下载完成: 网吧免重启加载VanGuard驱动.bat
2025-05-23 22:58:19:   文件大小: 1,884 字节 (1.84 KB)
2025-05-23 22:58:19:   下载耗时: 0.33 秒
2025-05-23 22:58:19:   进度: 3/0
2025-05-23 22:58:19: 正在下载: 改的内容.txt
2025-05-23 22:58:19:   源URL: http://www1.movemama.cn/便捷/%E6%94%B9%E7%9A%84%E5%86%85%E5%AE%B9.txt
2025-05-23 22:58:19:   目标路径: C:\Users\<USER>\Desktop\便捷\改的内容.txt
2025-05-23 22:58:19: 下载完成: 改的内容.txt
2025-05-23 22:58:19:   文件大小: 1,750 字节 (1.71 KB)
2025-05-23 22:58:19:   下载耗时: 0.33 秒
2025-05-23 22:58:19:   进度: 4/0
2025-05-23 22:58:19: 正在下载: setup.exe
2025-05-23 22:58:19:   源URL: http://www1.movemama.cn/便捷/setup.exe
2025-05-23 22:58:19:   目标路径: C:\Users\<USER>\Desktop\便捷\setup.exe
2025-05-23 22:58:26: 下载完成: setup.exe
2025-05-23 22:58:26:   文件大小: 56,250,984 字节 (54932.60 KB)
2025-05-23 22:58:26:   下载耗时: 7.23 秒
2025-05-23 22:58:26:   进度: 5/0
2025-05-23 22:58:26: === 递归下载完成统计 ===
2025-05-23 22:58:26: 总文件夹数: 0
2025-05-23 22:58:26: 总文件数: 0
2025-05-23 22:58:26: 成功下载: 5
2025-05-23 22:58:26: 下载失败: 0
2025-05-23 22:58:26: 便捷文件夹递归下载完成，保存路径: C:\Users\<USER>\Desktop\便捷
2025-05-23 22:58:26: WebDAV下载操作完成

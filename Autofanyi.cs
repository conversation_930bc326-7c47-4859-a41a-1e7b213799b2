﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WindowsFormsApp1.Properties;

namespace WindowsFormsApp1
{
    public partial class Autofanyi : Form
    {
        private int x, y;  //显示的坐标变量
        public Autofanyi()
        {
            InitializeComponent();
        }

        private void Autofanyi_FormClosing(object sender, FormClosingEventArgs e)
        {
            Form1.form1.VVVTGO = true;
        }


        public void 加入文本(string name ,string GOtxt)
        {



           // System.DateTime currentTime = new System.DateTime();

            //currentTime = System.DateTime.Now;

           // currentTime.ToString("t");//取时间 19:20:13

            var NEIR = string.Format("{0}: {1}\r\n", name, GOtxt);


            if (this.textBox1.InvokeRequired)
            {
                this.textBox1.Invoke(new Action<int>(n =>
                {
                    this.textBox1.AppendText(NEIR);     // 追加文本，并且使得光标定位到插入地方。
                    this.textBox1.ScrollToCaret();

                    // this.textBox1.Focus();//获取焦点
                    this.textBox1.Select(this.textBox1.TextLength, 0);//光标定位到文本最后
                    this.textBox1.ScrollToCaret();//滚动到光标处


                }), 1);

            }
            else
            {
                this.textBox1.AppendText(NEIR);     // 追加文本，并且使得光标定位到插入地方。
                this.textBox1.ScrollToCaret();

                // this.textBox1.Focus();//获取焦点
                this.textBox1.Select(this.textBox1.TextLength, 0);//光标定位到文本最后
                this.textBox1.ScrollToCaret();//滚动到光标处
            }







        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            if (Form1.form1.QQQQQQQQQ || Form1.form1.TGOTGOTGO || Form1.form1.EEEEEEEEEEEE)
            {
                Form1.form1.TGOTGOTGO = false;
                this.Close();
            }

        }

        private void textBox2_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyData == Keys.Enter)
            {
                new System.Threading.Thread((System.Threading.ThreadStart)delegate
                {
                    var YW = textBox2.Text.Replace("\r\n", "");

                    if(this.textBox2.InvokeRequired)
                    {
                        this.textBox2.Invoke(new Action<int>(n =>
                        {

                            textBox2.Text = "";


                        }
                ), 1);
                    }
                    else
                    {
                        textBox2.Text = "";
                    }
                   

                    var data = Resources.聊天11;

                  

                    加入文本("我",YW);

                    var FY = Util.translationHF(YW);

                    data = data.Replace("[body]", FY);

                    var ID = "";
                    var S1 = Util.zi_Getwenb("/lol-chat/v1/conversations");

                    if (S1.IndexOf("id") != -1)
                    {
                        var S2 = JSON.Jiexun_1(S1);

                        for (int i = 0; i < S2.Count; i++)
                        {
                            var type = S2[i]["type"].ToString();

                            if (type == "postGame" || type == "championSelect")
                            {
                                ID = S2[i]["id"].ToString();
                            }
                        }

                        if (ID != "")
                        {
                            Util.zi_Sendshuju("/lol-chat/v1/conversations/" + ID + "/messages", "POST", data, Encoding.UTF8);

                        }


                    }






                }).Start();
            }


        }

        private void button1_Click(object sender, EventArgs e)
        {
            Form1.form1.TGOTGOTGO = false;
            this.Close();
        }

        private void Autofanyi_Load(object sender, EventArgs e)
        {
            int x = SystemInformation.PrimaryMonitorSize.Width - this.Width;
            int y = 0;//要让窗体往上走 只需改变 Y的坐标
            this.Location = new Point(x, y);
            this.TopMost = true;
            this.timer1.Interval = 1;

            this.timer1.Start();
        }
    }
}
